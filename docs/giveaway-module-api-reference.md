# Winners Society Giveaway Module - Admin Panel API Reference

This document provides a comprehensive reference for all API endpoints available for giveaway management in the Winners Society admin panel. Use this as a guide for implementing the giveaway management features in your Angular application.

## Table of Contents

1. [Authentication Requirements](#authentication-requirements)
2. [Giveaway Management](#giveaway-management)
3. [Prize Management](#prize-management)
4. [Entry Management](#entry-management)
5. [Winner Management](#winner-management)
6. [Statistics and Reporting](#statistics-and-reporting)

## Authentication Requirements

All admin panel endpoints require:

- **Authentication**: Valid JWT token in the Authorization header
- **Authorization**: User must have ADMIN role
- **Header Format**: `Authorization: Bearer {jwt_token}`

If authentication fails, endpoints will return a `401 Unauthorized` response.
If authorization fails, endpoints will return a `403 Forbidden` response.

## Giveaway Management

### List All Giveaways

Retrieves a paginated list of all giveaways.

- **Method**: GET
- **URL**: `/api/giveaways`
- **Query Parameters**:
  - `page` (optional): Page number (default: 1)
  - `limit` (optional): Items per page (default: 10)
  - `status` (optional): Filter by status (DRAFT, ACTIVE, COMPLETED, CANCELLED)
  - `featured` (optional): Filter by featured status (true/false)
  - `search` (optional): Search by title or description
- **Response Format**:
  ```json
  {
    "success": true,
    "data": [
      {
        "id": "uuid",
        "title": "string",
        "description": "string",
        "startDate": "timestamp",
        "endDate": "timestamp",
        "drawDate": "timestamp",
        "status": "string",
        "featuredImage": "string",
        "prizeValue": "number",
        "isActive": "boolean",
        "createdAt": "timestamp",
        "updatedAt": "timestamp"
      }
    ],
    "meta": {
      "total": "number",
      "page": "number",
      "limit": "number",
      "pages": "number"
    }
  }
  ```
- **Database Interaction**: Reads from `Giveaway` table with optional filters

### Get Giveaway Details

Retrieves detailed information about a specific giveaway.

- **Method**: GET
- **URL**: `/api/giveaways/:id`
- **Path Parameters**:
  - `id`: Giveaway ID (UUID)
- **Response Format**:
  ```json
  {
    "success": true,
    "data": {
      "id": "uuid",
      "title": "string",
      "description": "string",
      "startDate": "timestamp",
      "endDate": "timestamp",
      "drawDate": "timestamp",
      "status": "string",
      "featuredImage": "string",
      "prizeValue": "number",
      "prizeDetails": "string",
      "imageUrl": "string",
      "category": "string",
      "tags": ["string"],
      "rules": "string",
      "termsAndConditions": "string",
      "minTier": "string",
      "maxEntries": "number",
      "isActive": "boolean",
      "createdAt": "timestamp",
      "updatedAt": "timestamp",
      "prizes": [
        {
          "id": "uuid",
          "name": "string",
          "description": "string",
          "value": "number",
          "currency": "string",
          "quantity": "number",
          "images": ["string"]
        }
      ],
      "entryCount": "number"
    }
  }
  ```
- **Database Interaction**: Reads from `Giveaway`, `Prize`, and counts from `Entry` tables

### Create Giveaway

Creates a new giveaway.

- **Method**: POST
- **URL**: `/api/giveaways`
- **Request Body**:
  ```json
  {
    "title": "string",
    "description": "string",
    "startDate": "timestamp",
    "endDate": "timestamp",
    "drawDate": "timestamp",
    "status": "string",
    "featuredImage": "string",
    "prizeValue": "number",
    "prizeDetails": "string",
    "imageUrl": "string",
    "category": "string",
    "tags": ["string"],
    "rules": "string",
    "termsAndConditions": "string",
    "minTier": "string",
    "maxEntries": "number",
    "isActive": "boolean"
  }
  ```
- **Response Format**:
  ```json
  {
    "success": true,
    "data": {
      "id": "uuid",
      "title": "string",
      "status": "string",
      "createdAt": "timestamp"
    }
  }
  ```
- **Database Interaction**: Creates a new record in the `Giveaway` table

### Update Giveaway

Updates an existing giveaway.

- **Method**: PUT
- **URL**: `/api/giveaways/:id`
- **Path Parameters**:
  - `id`: Giveaway ID (UUID)
- **Request Body**: Same fields as Create Giveaway (all fields optional)
- **Response Format**:
  ```json
  {
    "success": true,
    "data": {
      "id": "uuid",
      "title": "string",
      "status": "string",
      "updatedAt": "timestamp"
    }
  }
  ```
- **Database Interaction**: Updates a record in the `Giveaway` table

### Delete Giveaway

Deletes a giveaway.

- **Method**: DELETE
- **URL**: `/api/giveaways/:id`
- **Path Parameters**:
  - `id`: Giveaway ID (UUID)
- **Response Format**:
  ```json
  {
    "success": true,
    "message": "Giveaway deleted successfully"
  }
  ```
- **Database Interaction**: Deletes a record from the `Giveaway` table and related records from `Prize` and `Entry` tables

### Publish Giveaway

Changes a giveaway's status from DRAFT to ACTIVE.

- **Method**: POST
- **URL**: `/api/giveaways/:id/publish`
- **Path Parameters**:
  - `id`: Giveaway ID (UUID)
- **Response Format**:
  ```json
  {
    "success": true,
    "data": {
      "id": "uuid",
      "title": "string",
      "status": "ACTIVE",
      "updatedAt": "timestamp"
    }
  }
  ```
- **Database Interaction**: Updates the status field in the `Giveaway` table

## Prize Management

### List Prizes for Giveaway

Retrieves all prizes for a specific giveaway.

- **Method**: GET
- **URL**: `/api/prizes/giveaway/:id`
- **Path Parameters**:
  - `id`: Giveaway ID (UUID)
- **Response Format**:
  ```json
  {
    "success": true,
    "data": [
      {
        "id": "uuid",
        "giveawayId": "uuid",
        "name": "string",
        "description": "string",
        "value": "number",
        "currency": "string",
        "quantity": "number",
        "images": ["string"],
        "specifications": "object",
        "createdAt": "timestamp",
        "updatedAt": "timestamp"
      }
    ]
  }
  ```
- **Database Interaction**: Reads from `Prize` table filtered by giveawayId

### Get Prize Details

Retrieves detailed information about a specific prize.

- **Method**: GET
- **URL**: `/api/prizes/:id`
- **Path Parameters**:
  - `id`: Prize ID (UUID)
- **Response Format**:
  ```json
  {
    "success": true,
    "data": {
      "id": "uuid",
      "giveawayId": "uuid",
      "name": "string",
      "description": "string",
      "value": "number",
      "currency": "string",
      "quantity": "number",
      "images": ["string"],
      "specifications": "object",
      "createdAt": "timestamp",
      "updatedAt": "timestamp",
      "giveaway": {
        "id": "uuid",
        "title": "string"
      }
    }
  }
  ```
- **Database Interaction**: Reads from `Prize` table with join to `Giveaway` table

### Create Prize

Creates a new prize for a giveaway.

- **Method**: POST
- **URL**: `/api/prizes/giveaway/:giveawayId`
- **Path Parameters**:
  - `giveawayId`: Giveaway ID (UUID)
- **Request Body**:
  ```json
  {
    "name": "string",
    "description": "string",
    "value": "number",
    "currency": "string",
    "quantity": "number",
    "images": ["string"],
    "specifications": "object"
  }
  ```
- **Response Format**:
  ```json
  {
    "success": true,
    "data": {
      "id": "uuid",
      "giveawayId": "uuid",
      "name": "string",
      "createdAt": "timestamp"
    }
  }
  ```
- **Database Interaction**: Creates a new record in the `Prize` table

### Update Prize

Updates an existing prize.

- **Method**: PUT
- **URL**: `/api/prizes/:id`
- **Path Parameters**:
  - `id`: Prize ID (UUID)
- **Request Body**: Same fields as Create Prize (all fields optional)
- **Response Format**:
  ```json
  {
    "success": true,
    "data": {
      "id": "uuid",
      "name": "string",
      "updatedAt": "timestamp"
    }
  }
  ```
- **Database Interaction**: Updates a record in the `Prize` table

### Delete Prize

Deletes a prize.

- **Method**: DELETE
- **URL**: `/api/prizes/:id`
- **Path Parameters**:
  - `id`: Prize ID (UUID)
- **Response Format**:
  ```json
  {
    "success": true,
    "message": "Prize deleted successfully"
  }
  ```
- **Database Interaction**: Deletes a record from the `Prize` table

## Entry Management

### Get Entries for Giveaway

Retrieves all entries for a specific giveaway.

- **Method**: GET
- **URL**: `/api/giveaways/:id/entries`
- **Path Parameters**:
  - `id`: Giveaway ID (UUID)
- **Query Parameters**:
  - `page` (optional): Page number (default: 1)
  - `limit` (optional): Items per page (default: 10)
- **Response Format**:
  ```json
  {
    "success": true,
    "data": {
      "totalEntries": "number",
      "entries": [
        {
          "id": "uuid",
          "userId": "uuid",
          "giveawayId": "uuid",
          "membershipId": "uuid",
          "entryDate": "timestamp",
          "entryMethod": "string",
          "referenceId": "string",
          "quantity": "number",
          "isWinner": "boolean",
          "user": {
            "id": "uuid",
            "email": "string",
            "firstName": "string",
            "lastName": "string"
          }
        }
      ]
    },
    "meta": {
      "total": "number",
      "page": "number",
      "limit": "number",
      "pages": "number"
    }
  }
  ```
- **Database Interaction**: Reads from `Entry` table with join to `User` table

### Get Entry Count for Giveaway

Retrieves the total number of entries for a specific giveaway.

- **Method**: GET
- **URL**: `/api/giveaways/:id/entries/count`
- **Path Parameters**:
  - `id`: Giveaway ID (UUID)
- **Response Format**:
  ```json
  {
    "success": true,
    "data": {
      "totalEntries": "number"
    }
  }
  ```
- **Database Interaction**: Counts records in the `Entry` table for the specified giveaway

### Get User Entries for Giveaway

Retrieves all entries for a specific user in a giveaway.

- **Method**: GET
- **URL**: `/api/giveaways/:id/entries/user/:userId`
- **Path Parameters**:
  - `id`: Giveaway ID (UUID)
  - `userId`: User ID (UUID)
- **Response Format**:
  ```json
  {
    "success": true,
    "data": {
      "entries": [
        {
          "id": "uuid",
          "userId": "uuid",
          "giveawayId": "uuid",
          "entryDate": "timestamp",
          "entryMethod": "string",
          "quantity": "number",
          "isWinner": "boolean"
        }
      ],
      "totalEntries": "number"
    }
  }
  ```
- **Database Interaction**: Reads from `Entry` table filtered by giveawayId and userId

## Winner Management

### Conduct Draw

Conducts the draw for a giveaway and selects winners.

- **Method**: POST
- **URL**: `/api/giveaways/:id/draw`
- **Path Parameters**:
  - `id`: Giveaway ID (UUID)
- **Request Body**:
  ```json
  {
    "numberOfWinners": "number"
  }
  ```
- **Response Format**:
  ```json
  {
    "success": true,
    "data": {
      "drawResult": {
        "giveawayId": "uuid",
        "drawCompleted": true,
        "winners": [
          {
            "userId": "uuid",
            "entryId": "uuid",
            "prizeId": "uuid",
            "isAlternate": false
          }
        ]
      },
      "winners": [
        {
          "id": "uuid",
          "userId": "uuid",
          "giveawayId": "uuid",
          "prizeId": "uuid",
          "entryId": "uuid",
          "selectionDate": "timestamp",
          "status": "string",
          "user": {
            "id": "uuid",
            "email": "string",
            "firstName": "string",
            "lastName": "string"
          },
          "prize": {
            "id": "uuid",
            "name": "string",
            "value": "number"
          }
        }
      ]
    }
  }
  ```
- **Database Interaction**:
  - Reads from `Entry` table to select random entries
  - Creates records in the `Winner` table
  - Updates the `Giveaway` table status to COMPLETED

### Get Winners for Giveaway

Retrieves all winners for a specific giveaway.

- **Method**: GET
- **URL**: `/api/winners/giveaway/:id`
- **Path Parameters**:
  - `id`: Giveaway ID (UUID)
- **Response Format**:
  ```json
  {
    "success": true,
    "data": [
      {
        "id": "uuid",
        "userId": "uuid",
        "giveawayId": "uuid",
        "prizeId": "uuid",
        "entryId": "uuid",
        "selectionDate": "timestamp",
        "status": "string",
        "claimDate": "timestamp",
        "shippingDetails": "object",
        "user": {
          "id": "uuid",
          "email": "string",
          "firstName": "string",
          "lastName": "string"
        },
        "prize": {
          "id": "uuid",
          "name": "string",
          "value": "number"
        }
      }
    ]
  }
  ```
- **Database Interaction**: Reads from `Winner` table with joins to `User` and `Prize` tables

### Get Winner Details

Retrieves detailed information about a specific winner.

- **Method**: GET
- **URL**: `/api/winners/:id`
- **Path Parameters**:
  - `id`: Winner ID (UUID)
- **Response Format**:
  ```json
  {
    "success": true,
    "data": {
      "id": "uuid",
      "userId": "uuid",
      "giveawayId": "uuid",
      "prizeId": "uuid",
      "entryId": "uuid",
      "selectionDate": "timestamp",
      "status": "string",
      "claimDate": "timestamp",
      "shippingDetails": "object",
      "user": {
        "id": "uuid",
        "email": "string",
        "firstName": "string",
        "lastName": "string",
        "profileImage": "string"
      },
      "prize": {
        "id": "uuid",
        "name": "string",
        "description": "string",
        "value": "number",
        "currency": "string"
      },
      "giveaway": {
        "id": "uuid",
        "title": "string"
      }
    }
  }
  ```
- **Database Interaction**: Reads from `Winner` table with joins to `User`, `Prize`, and `Giveaway` tables

### Update Winner Status

Updates a winner's status.

- **Method**: PUT
- **URL**: `/api/winners/:id/status`
- **Path Parameters**:
  - `id`: Winner ID (UUID)
- **Request Body**:
  ```json
  {
    "status": "string"
  }
  ```
- **Response Format**:
  ```json
  {
    "success": true,
    "data": {
      "id": "uuid",
      "status": "string",
      "updatedAt": "timestamp"
    }
  }
  ```
- **Database Interaction**: Updates the status field in the `Winner` table

## Statistics and Reporting

### Get Giveaway Statistics

Retrieves statistics about giveaways.

- **Method**: GET
- **URL**: `/api/admin/dashboard/stats/giveaways`
- **Query Parameters**:
  - `startDate` (optional): Start date for filtering statistics (ISO format)
  - `endDate` (optional): End date for filtering statistics (ISO format)
  - `dateRange` (optional): Predefined date range ('day', 'week', 'month', 'year', 'all')
- **Response Format**:
  ```json
  {
    "success": true,
    "data": {
      "totalGiveaways": "number",
      "activeGiveaways": "number",
      "completedGiveaways": "number",
      "giveawaysByStatus": {
        "DRAFT": "number",
        "ACTIVE": "number",
        "COMPLETED": "number",
        "CANCELLED": "number"
      },
      "totalEntries": "number",
      "totalWinners": "number"
    }
  }
  ```
- **Database Interaction**:
  - Reads from `Giveaway` table with counts by status
  - Counts from `Entry` and `Winner` tables

### Generate Giveaway Report

Generates a report about giveaways.

- **Method**: POST
- **URL**: `/api/admin/dashboard/reports`
- **Request Body**:
  ```json
  {
    "type": "GIVEAWAY_REPORT",
    "format": "CSV",
    "filters": {
      "startDate": "timestamp",
      "endDate": "timestamp",
      "status": "string"
    }
  }
  ```
- **Response Format**:
  ```json
  {
    "success": true,
    "data": {
      "reportId": "uuid",
      "type": "GIVEAWAY_REPORT",
      "format": "CSV",
      "url": "string",
      "generatedAt": "timestamp",
      "expiresAt": "timestamp"
    }
  }
  ```
- **Database Interaction**:
  - Reads from `Giveaway`, `Prize`, `Entry`, and `Winner` tables
  - Creates a report record in the database

### Generate Entry Report

Generates a report about entries for a specific giveaway.

- **Method**: POST
- **URL**: `/api/admin/dashboard/reports`
- **Request Body**:
  ```json
  {
    "type": "ENTRY_REPORT",
    "format": "CSV",
    "filters": {
      "giveawayId": "uuid"
    }
  }
  ```
- **Response Format**: Same as Generate Giveaway Report
- **Database Interaction**:
  - Reads from `Entry` table with joins to `User` and `Giveaway` tables
  - Creates a report record in the database

### Generate Winner Report

Generates a report about winners.

- **Method**: POST
- **URL**: `/api/admin/dashboard/reports`
- **Request Body**:
  ```json
  {
    "type": "WINNER_REPORT",
    "format": "CSV",
    "filters": {
      "giveawayId": "uuid",
      "status": "string"
    }
  }
  ```
- **Response Format**: Same as Generate Giveaway Report
- **Database Interaction**:
  - Reads from `Winner` table with joins to `User`, `Prize`, and `Giveaway` tables
  - Creates a report record in the database
