# Service Testing Utilities

This document provides guidance on how to use the service testing utilities to write effective and comprehensive tests for your service implementations.

## Overview

The service testing utilities provide:
- Mock implementations of service interfaces
- Mock implementations of repository dependencies
- Factory functions for test data generation
- Setup and teardown utilities
- Test helpers for validating enum usage
- Type-safe mock data generators
- Error handling test case generators

## Quick Start

Here's a basic example of how to set up a test for a service:

```typescript
import { 
  setupServiceTest,
  createMockUsers,
  testComprehensiveErrorHandling
} from '../src/services/testing/service-test-utils.js';

import { MyServiceImpl } from '../src/services/my-service/my-service.js';

describe('MyService', () => {
  let context;
  let myService;
  
  beforeEach(() => {
    // Setup test context with mock data
    context = setupServiceTest({
      users: createMockUsers(3)
      // Add more mock data as needed
    });
    
    // Create service instance with mock repositories
    myService = new MyServiceImpl(
      context.repositories.user,
      // Add other dependencies
    );
  });
  
  afterEach(() => {
    // Clean up mocks
    context.cleanup();
  });
  
  // Your tests here
});
```

## Mock Data Generation

The utilities provide factory functions for generating test data:

```typescript
// Create a single mock user
const user = createMockUser({ 
  firstName: 'Jane',
  lastName: 'Doe'
});

// Create multiple mock users
const users = createMockUsers(5, { 
  role: Role.USER 
});

// Create mock winners
const winners = createMockWinners(3, {
  userId: 'user-123',
  giveawayId: 'giveaway-123'
});
```

## Testing Error Handling

Test comprehensive error handling with minimal code:

```typescript
describe('get', () => {
  it('should return user by id', async () => {
    // Regular test implementation
  });
  
  // Test all error scenarios automatically
  testComprehensiveErrorHandling(
    () => userService.get('test-id'),
    jest.spyOn(context.repositories.user, 'findById'),
    ['test-id']
  );
});
```

## Enum Validation

Test enum validation easily:

```typescript
describe('enum validation', () => {
  it('should validate UserRole enum values', () => {
    expect(isValidEnum(UserRole, UserRole.ADMIN)).toBe(true);
    expect(isValidEnum(UserRole, 'INVALID_ROLE')).toBe(false);
  });
});
```

## Model Schema Validation

Validate model schemas with predefined tests:

```typescript
testModelValidation(
  'User',
  () => createMockUser(),
  {
    id: 'string',
    email: /^.+@.+\..+$/,
    firstName: 'string',
    lastName: 'string',
    role: 'string',
    createdAt: 'date',
    updatedAt: 'date'
  }
);
```

## Setup Service Test

The `setupServiceTest` function provides a complete environment for testing:

```typescript
const context = setupServiceTest({
  users: createMockUsers(3),
  giveaways: createMockGiveaways(2),
  prizes: createMockPrizes(4, { 
    giveawayId: 'giveaway-1' 
  }),
  winners: createMockWinners(2),
  setupServices: true // Automatically create service implementations
});

// Access repositories
const userRepo = context.repositories.user;

// Access mock data
const users = context.mockData.users;

// Access pre-configured services
const winnerService = context.services.winner;
```

## Best Practices

1. Use the AAA pattern (Arrange, Act, Assert) in your tests
2. Test happy paths and error scenarios
3. Use `testComprehensiveErrorHandling` to ensure all error types are handled
4. Reset mocks between tests with `context.cleanup()`
5. Use the factory functions to create consistent test data
6. Test service methods in isolation
7. Mock external dependencies to isolate test units
8. Use descriptive test names that indicate what's being tested

## Example Test

See `tests/services/winner.service.test.ts` for a complete example of how to test a service implementation using these utilities. 