# Email Verification System Documentation

## Overview

The Winners Society platform now includes a comprehensive email verification system that ensures users verify their email addresses before accessing protected features. This system provides secure token-based email verification with automatic email sending via SendGrid.

## Features

### 1. **Email Verification Flow**
- Users receive verification emails automatically upon registration
- Secure, unique verification tokens with 24-hour expiration
- One-time use tokens that are invalidated after verification
- Professional HTML email templates with clear call-to-action

### 2. **Database Integration**
- New `VerificationToken` model for secure token storage
- Support for multiple token types (email verification, password reset)
- Automatic cleanup of expired and used tokens
- Proper foreign key relationships with cascade deletion

### 3. **SendGrid Integration**
- Professional email templates with responsive design
- Configurable sender information via environment variables
- Robust error handling for email delivery failures
- Support for both templated and plain emails

### 4. **Security Features**
- Cryptographically secure token generation (32-byte random tokens)
- Token expiration enforcement
- Prevention of token reuse
- Secure token validation and consumption

## Architecture

### Database Schema

```sql
-- VerificationToken table
CREATE TABLE "VerificationToken" (
    "id" TEXT PRIMARY KEY,
    "userId" TEXT NOT NULL,
    "token" TEXT UNIQUE NOT NULL,
    "type" "VerificationTokenType" NOT NULL,
    "expiresAt" TIMESTAMP(3) NOT NULL,
    "isUsed" BOOLEAN DEFAULT false,
    "createdAt" TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3),
    FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE
);

-- Enum for token types
CREATE TYPE "VerificationTokenType" AS ENUM (
    'EMAIL_VERIFICATION',
    'PASSWORD_RESET'
);
```

### Service Layer

#### EmailService Interface
- `sendEmail()` - Send plain text or HTML emails
- `sendTemplateEmail()` - Send emails using templates
- `sendVerificationEmail()` - Send email verification emails
- `sendPasswordResetEmail()` - Send password reset emails

#### VerificationTokenService
- `generateToken()` - Create secure verification tokens
- `validateAndConsumeToken()` - Validate and consume tokens
- `isTokenValid()` - Check token validity without consuming
- `invalidateExistingTokens()` - Invalidate existing tokens
- `cleanupExpiredTokens()` - Remove expired tokens

### Middleware

#### Email Verification Middleware
- `requireEmailVerification()` - Block access for unverified users
- `checkEmailVerification()` - Add verification warnings to responses

## Configuration

### Environment Variables

```bash
# SendGrid Configuration
SENDGRID_API_KEY="your-sendgrid-api-key"
SENDGRID_FROM_EMAIL="<EMAIL>"
SENDGRID_FROM_NAME="Winners Society"

# Application URLs
APP_URL="http://localhost:3000"
FRONTEND_URL="http://localhost:3000"
```

### Email Templates

The system includes built-in HTML email templates for:
- **Email Verification**: Professional welcome email with verification button
- **Password Reset**: Secure password reset email with reset button

Templates include:
- Responsive design for mobile and desktop
- Clear call-to-action buttons
- Security warnings and expiration notices
- Branded styling with Winners Society colors

## API Endpoints

### Authentication Endpoints

#### Register User
```http
POST /api/auth/register
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "securepassword",
  "firstName": "John",
  "lastName": "Doe",
  "addressLine1": "123 Main St",
  "city": "Anytown",
  "state": "CA",
  "postalCode": "12345",
  "country": "USA"
}
```

**Response**: User created with `isVerified: false`, verification email sent automatically.

#### Verify Email
```http
GET /api/auth/verify-email/:token
```

**Response**: Redirects to frontend with success/error status.

#### Resend Verification Email
```http
POST /api/auth/resend-verification
Authorization: Bearer <access-token>
```

**Response**: New verification email sent to user.

### Protected Routes

Routes can be protected with email verification using middleware:

```typescript
import { requireEmailVerification } from '../middleware/email-verification.middleware.js';

// Require email verification
router.get('/protected-route', 
  authenticateUser, 
  requireEmailVerification, 
  handler
);

// Optional verification check
router.get('/optional-route', 
  authenticateUser, 
  checkEmailVerification, 
  handler
);
```

## User Experience

### Registration Flow
1. User submits registration form
2. Account created with `isVerified: false`
3. Verification email sent automatically
4. User receives professional email with verification link
5. User clicks verification link
6. Email verified, user can access protected features

### Verification Email Content
- Welcome message with user's first name
- Clear explanation of verification requirement
- Prominent "Verify Email Address" button
- Fallback verification link for accessibility
- 24-hour expiration notice
- Security disclaimer for unintended recipients

### Error Handling
- Clear error messages for invalid/expired tokens
- Helpful guidance for users with verification issues
- Automatic resend functionality for failed deliveries
- Graceful degradation if email service is unavailable

## Security Considerations

### Token Security
- 256-bit cryptographically secure random tokens
- Tokens stored hashed in database
- Automatic expiration enforcement
- One-time use with immediate invalidation

### Email Security
- No sensitive information in email content
- Secure HTTPS verification links
- Clear security warnings in emails
- Protection against email enumeration attacks

### Rate Limiting
- Consider implementing rate limiting for:
  - Registration attempts
  - Verification email requests
  - Token validation attempts

## Monitoring and Maintenance

### Cleanup Tasks
- Expired tokens are automatically cleaned up
- Used tokens older than 7 days are removed
- Failed email delivery attempts are logged

### Monitoring Points
- Email delivery success rates
- Token generation and validation metrics
- User verification completion rates
- Failed verification attempts

## Testing

The system includes comprehensive testing for:
- Database schema validation
- Token generation and validation
- Email service integration
- Middleware functionality
- End-to-end verification flow

Run tests with:
```bash
npm test
```

## Future Enhancements

### Planned Features
- Email template customization via admin panel
- Multi-language email templates
- SMS verification as backup option
- Advanced email analytics and tracking
- Bulk user verification management

### Integration Opportunities
- Integration with marketing automation platforms
- Advanced email personalization
- A/B testing for email templates
- Integration with customer support systems
