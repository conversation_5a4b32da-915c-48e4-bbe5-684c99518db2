# Membership Purchase & Payment Flow Documentation

This document describes the backend process and API endpoints for purchasing a membership plan, handling payments via Stripe, and activating user memberships in the Winners Society platform.

---

## Overview

The membership purchase flow is designed to be secure, idempotent, and user-friendly. It ensures that users can select a membership plan, pay for it using Stripe, and have their membership activated automatically upon successful payment. The backend handles all critical steps, including payment intent creation, payment processing, webhook handling, and membership activation.

---

## 1. Membership Purchase Flow (Backend)

### Step-by-Step Process

1. **User selects a membership plan** from the available options (fetched from `/api/membership-tiers`).
2. **Frontend requests a Stripe payment intent** for the selected plan by calling `POST /api/payments/payment-intent` with the `planId` and `userId`.
3. **Backend fetches plan details** from the database, ensuring the price and currency are correct and not client-supplied.
4. **Stripe payment intent is created** and the client secret is returned to the frontend.
5. **User enters payment details** (via Stripe.js) and confirms the payment.
6. **Frontend calls `POST /api/payments/process`** with the `paymentIntentId` and `paymentMethodId` to confirm the payment.
7. **Backend confirms the payment intent** with <PERSON><PERSON>, creates a transaction record, and (if payment is successful) activates the user's membership for the selected plan.
8. **Stripe sends webhook events** (e.g., `payment_intent.succeeded`) to `/api/payments/webhook` for additional idempotent processing and membership activation.
9. **User's membership status is updated** in the database and can be checked via `/api/membership`.

---

## 2. Key Backend Endpoints

### 2.1. Get Membership Plans
- **Endpoint:** `GET /api/membership-tiers`
- **Description:** Fetch all available membership plans.
- **Response:** Array of plan objects (id, name, price, currency, etc.)
- **DTO Requirements:**
  - **Response DTO:** `MembershipTierResponseDto[]`
  ```typescript
  interface MembershipTierResponseDto {
    id: string;
    name: string;
    description: string;
    price: number;
    currency: string;
    billingPeriod: string;
    entryAllocation: number;
    duration: number;
    features: any; // JSON array of included features
    benefits: string[];
    isActive: boolean;
    displayOrder: number;
    createdAt: Date;
    updatedAt: Date;
  }
  ```

### 2.2. Create Payment Intent
- **Endpoint:** `POST /api/payments/payment-intent`
- **Description:** Create a Stripe payment intent for a specific plan.
- **Request:** `{ userId, planId }`
- **Response:** `{ id, clientSecret, amount, currency, status }`
- **DTO Requirements:**
  - **Request DTO:** `PaymentIntentCreateDto`
  ```typescript
  interface PaymentIntentCreateDto {
    planId: string;
    description?: string;
    metadata?: Record<string, unknown>;
  }
  ```
  - **Response DTO:** `PaymentIntentResponseDto`
  ```typescript
  interface PaymentIntentResponseDto {
    clientSecret: string;
    intentId: string;
    amount: number;
    currency: string;
  }
  ```

### 2.3. Save Payment Method (Optional)
- **Endpoint:** `POST /api/payments/payment-methods`
- **Description:** Save a Stripe payment method for the user.
- **Request:** `{ stripePaymentMethodId, isDefault }`
- **Response:** Payment method object
- **DTO Requirements:**
  - **Request DTO:** `PaymentMethodCreateDto`
  ```typescript
  interface PaymentMethodCreateDto {
    type: PaymentType; // Enum: CREDIT_CARD, etc.
    stripePaymentMethodId: string; // From Stripe.js
    isDefault?: boolean;
  }
  ```
  - **Response DTO:** `PaymentMethodResponseDto`
  ```typescript
  interface PaymentMethodResponseDto {
    id: string;
    userId: string;
    type: PaymentType;
    stripePaymentMethodId: string;
    last4?: string;
    brand?: string;
    expiryMonth?: number;
    expiryYear?: number;
    holderName?: string;
    isDefault: boolean;
    createdAt: Date;
    updatedAt: Date;
  }
  ```

### 2.4. Process Payment
- **Endpoint:** `POST /api/payments/process`
- **Description:** Confirm a payment intent and activate membership if successful.
- **Request:** `{ paymentIntentId, paymentMethodId }`
- **Response:** Transaction object
- **DTO Requirements:**
  - **Request DTO:** `PaymentProcessDto`
  ```typescript
  interface PaymentProcessDto {
    paymentIntentId: string;
    paymentMethodId: string;
  }
  ```
  - **Response DTO:** `TransactionResponseDto`
  ```typescript
  interface TransactionResponseDto {
    id: string;
    userId: string;
    membershipId?: string;
    amount: number;
    currency: string;
    status: PaymentStatus; // Enum: PENDING, COMPLETED, FAILED, REFUNDED
    paymentMethod: string;
    paymentIntentId?: string;
    description?: string;
    metadata?: Record<string, unknown>;
    createdAt: Date;
    updatedAt: Date;
  }
  ```

### 2.5. Get Membership Status
- **Endpoint:** `GET /api/membership`
- **Description:** Get the current user's membership status.
- **Response:** Membership object
- **DTO Requirements:**
  - **Response DTO:**
  ```typescript
  interface MembershipResponseDto {
    id: string;
    userId: string;
    tierId: string;
    tierName: string;
    startDate: Date;
    endDate: Date;
    status: MembershipStatus; // Enum: ACTIVE, EXPIRED, CANCELLED
    autoRenew: boolean;
    entriesRemaining: number;
    createdAt: Date;
    updatedAt: Date;
  }
  ```

### 2.6. (Optional) Get User Payment Methods
- **Endpoint:** `GET /api/payments/payment-methods`
- **Description:** List all saved payment methods for the user.
- **DTO Requirements:**
  - **Response DTO:** `PaymentMethodResponseDto[]` (Array of payment methods, see 2.3)

### 2.7. Stripe Webhook Endpoint
- **Endpoint:** `POST /api/payments/webhook`
- **Description:** Stripe calls this endpoint to notify the backend of payment events. Backend ensures idempotent membership activation.
- **Request:** Raw webhook payload with Stripe signature header
- **Response:** `{ received: true, eventType: string }`
- **Security Note:** This endpoint validates requests using the Stripe signature

---

## 3. Backend Logic Highlights

- **Plan Validation:** Only valid, active plans from the DB can be purchased. Price/currency are never client-supplied.
- **Idempotency:** Membership activation is idempotent. The backend checks if the user already has an active membership for the plan before creating a new one.
- **Membership Activation:** On successful payment (via processPayment or webhook), the backend:
  - Checks if the user already has an active membership for the plan.
  - If not, calls the membership service to subscribe the user to the plan.
- **Error Handling:** All errors are handled with clear messages and proper status codes.
- **Security:** All sensitive operations require authentication and server-side validation.

---

## 4. Example Sequence

1. **GET /api/membership-tiers** → Show plans to user
2. **POST /api/payments/payment-intent** → Get Stripe client secret
3. **User enters card details in Stripe.js**
4. **POST /api/payments/process** → Confirm payment and activate membership
5. **GET /api/membership** → Show updated membership status

---

## 5. Notes for UI Developers

- Always use the planId from `/api/membership-tiers` when creating a payment intent.
- Never allow the client to specify the amount or currency directly.
- After payment, always check `/api/membership` to confirm membership activation.
- Handle errors gracefully and display clear messages to the user.

---

## 6. References
- See `src/services/payment/stripe-payment.service.ts` for implementation details.
- See `src/controllers/payment.controller.ts` and `src/controllers/membership.controller.ts` for endpoint wiring.

---

## 7. DTO Validation Requirements

### Validation Approach

For all DTOs used in the payment flow, the backend implements thorough validation to ensure data integrity and security:

1. **Schema Validation**: All request DTOs are validated using Zod schemas in the `validation.ts` utility:
   ```typescript
   export const paymentSchemas = {
     createIntent: z.object({
       planId: Fields.id,
       description: z.string().optional(),
       metadata: z.record(z.string()).optional(),
     }),
     process: z.object({
       paymentIntentId: z.string(),
       paymentMethodId: z.string(),
     }),
   };
   ```

2. **Server-Side Pricing**: When creating payment intents, the server always fetches the price from the database rather than trusting client-supplied values.

3. **Idempotency**: All payment operations implement idempotency to prevent duplicate processing, using both transaction IDs and payment intent IDs.

4. **Data Sanitization**: All incoming data is sanitized to prevent injection attacks.

### Common DTO Patterns

The payment flow follows these DTO patterns:

1. **Request/Response Separation**: Each endpoint has distinct DTOs for requests and responses.

2. **Base DTO Inheritance**: Response DTOs extend the `BaseResponseDto` which includes common fields like `id`, `createdAt`, and `updatedAt`.

3. **Optional Fields Handling**: Optional fields in DTOs are explicitly marked using TypeScript's optional property syntax.

4. **Type Safety**: Enums are used for status fields and other constrained values to ensure type safety.

5. **Metadata Support**: Most DTOs include a `metadata` field to allow for flexible, structured additional data.

For more detailed DTO implementations, see the corresponding files in the `src/dtos/` directory.

---

For questions or further details, contact the backend team.
