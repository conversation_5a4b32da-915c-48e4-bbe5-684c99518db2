# Step-by-Step Angular UI Development Guide with GitHub Copilot

This guide provides a structured approach to building the frontend for the membership-based giveaway platform using the latest Angular framework, Angular Material, and GitHub Copilot. It follows the UI modules, components, and user flows previously defined, incorporating role-based differentiation.

**Assumptions:**
*   Using the latest Angular version.
*   Using Angular CLI for generation.
*   Using standalone components primarily.
*   Using Angular Material for UI components.
*   Using TypeScript.
*   Using SCSS for styling.
*   Using RxJS for asynchronous operations.
*   Using a state management approach (e.g., services with BehaviorSubjects, NgRx, Elf).
*   Using Angular's built-in HttpClient or a wrapper service for API calls.

## Phase 1: Project Setup & Foundation (Day 1-2)

**Goal:** Initialize the Angular project, add Angular Material, set up basic configurations, and establish project structure.

1.  **Initialize Angular Project:**
    *   **Prompt:** `// Use Angular CLI to create a new project named 'membership-platform-ui'. Use SCSS for styling and enable standalone components.`
    *   **Action:** Run `ng new membership-platform-ui --style=scss --standalone` (follow CLI prompts, enable routing).
2.  **Add Angular Material:**
    *   **Prompt:** `// Add Angular Material to the project using ng add.`
    *   **Action:** Run `ng add @angular/material` (select theme, typography options).
3.  **Set Up Project Structure:**
    *   **Prompt:** `// Create the recommended folder structure within src/app: core (guards, interceptors, models, services), features (auth, dashboard, giveaways, membership, profile, admin), layout (toolbar, footer, sidenav), pages (home, not-found), shared (components, directives, pipes).`
    *   **Action:** Create folders manually or using terminal commands.
4.  **Install Core Dependencies (Optional):**
    *   **Prompt:** `// Install additional dependencies if needed: date-fns (for date manipulation), zod (for validation, optional), ngx-stripe (if using Stripe).`
    *   **Action:** Run `npm install date-fns zod ngx-stripe` (as needed).
5.  **Configure Global Styles:**
    *   **Prompt:** `// Add global styles in src/styles.scss. Include basic resets, theme overrides, and utility classes if necessary.`
    *   **Action:** Edit `src/styles.scss`.
6.  **Basic Environment Variables:**
    *   **Prompt:** `// Set up environment variables for API base URL in src/environments/environment.ts and environment.prod.ts.`
    *   **Action:** Define `apiBaseUrl` in environment files.
7.  **Generate Core Services/Interceptors:**
    *   **Prompt:** `// Generate core services: ApiService (for HttpClient wrapper), NotificationService (using MatSnackBar). Generate core interceptors: AuthInterceptor (adds token), ErrorInterceptor (handles HTTP errors). Use Angular CLI.`
    *   **Action:** Run `ng g s core/services/api`, `ng g s core/services/notification`, `ng g interceptor core/interceptors/auth`, `ng g interceptor core/interceptors/error`. Remember to provide interceptors in `app.config.ts`.

## Phase 2: Core Layout & Shared Components (Day 2-4)

**Goal:** Build the main application layout using Angular Material and create reusable shared components.

1.  **Implement App Component Layout:**
    *   **Prompt:** `// Modify src/app/app.component.html to include the main layout structure using Angular Material: <mat-toolbar>, <mat-sidenav-container>, <mat-sidenav>, <mat-sidenav-content> containing <router-outlet>, and <app-footer>.`
    *   **Action:** Update `app.component.html` and `app.component.ts`.
2.  **Generate & Implement Toolbar Component:**
    *   **Prompt:** `// Generate a standalone ToolbarComponent in src/app/layout/toolbar. Implement the <mat-toolbar> with title, navigation buttons (<button mat-button routerLink="...">), and conditional logic (*ngIf) for Login/Register buttons vs. User Menu based on AuthService.isAuthenticated$. Inject AuthService.`
    *   **Action:** Run `ng g c layout/toolbar --standalone`. Implement HTML and TS.
3.  **Generate & Implement Footer Component:**
    *   **Prompt:** `// Generate a standalone FooterComponent in src/app/layout/footer. Implement a simple footer with copyright and links using <mat-toolbar> or a standard footer tag.`
    *   **Action:** Run `ng g c layout/footer --standalone`. Implement HTML and TS.
4.  **Generate & Implement User Menu Component:**
    *   **Prompt:** `// Generate a standalone UserMenuComponent in src/app/layout/user-menu. Use <button mat-icon-button [matMenuTriggerFor]="userMenu"> and <mat-menu #userMenu>. Include menu items (<button mat-menu-item>) for Profile, Settings, Logout. Use *ngIf="authService.isAdmin$ | async" for the Admin Dashboard link. Inject AuthService, Router.`
    *   **Action:** Run `ng g c layout/user-menu --standalone`. Implement HTML and TS.
5.  **Integrate Layout Components:**
    *   **Prompt:** `// Import and use ToolbarComponent and FooterComponent within AppComponent's template.`
    *   **Action:** Update `app.component.ts` (imports array for standalone) and `app.component.html`.
6.  **Generate Shared Components (Example: Spinner):**
    *   **Prompt:** `// Generate a standalone SpinnerComponent in src/app/shared/components/spinner. Use <mat-progress-spinner> and an *ngIf input to control visibility.`
    *   **Action:** Run `ng g c shared/components/spinner --standalone`. Implement.
7.  **(Continue for other shared components):** Generate components like `ConfirmationDialogComponent` (using `MatDialog`), `DataTableComponent` (using `MatTable`), `PaginatorComponent` (using `MatPaginator`), etc., as needed.

## Phase 3: Authentication Module (Day 5-7)

**Goal:** Implement user registration, login, password reset, auth state management, and routing.

1.  **Generate Auth Components:**
    *   **Prompt:** `// Generate standalone components for Login, Register, ForgotPassword, ResetPassword, VerifyEmail within src/app/features/auth.`
    *   **Action:** Run `ng g c features/auth/login --standalone`, `ng g c features/auth/register --standalone`, etc.
2.  **Generate AuthService:**
    *   **Prompt:** `// Generate AuthService in src/app/core/services. Implement methods for login, register, logout, verifyEmail, forgotPassword, resetPassword using ApiService. Manage authentication state (token, user data, isAuthenticated$, isAdmin$) using BehaviorSubjects.`
    *   **Action:** Run `ng g s core/services/auth`. Implement service logic.
3.  **Implement Auth Routing:**
    *   **Prompt:** `// Define routes for auth components in app.routes.ts (or a separate auth.routes.ts).`
    *   **Action:** Add paths like `{ path: 'login', component: LoginComponent }`.
4.  **Implement Login Component:**
    *   **Prompt:** `// Implement LoginComponent. Create a reactive form using FormBuilder. Use Angular Material form fields (<mat-form-field>, <input matInput>). Implement onSubmit() to call AuthService.login(), handle loading state, display errors (using MatSnackBar or form errors), and navigate on success using Router.`
    *   **Action:** Implement `login.component.ts` and `login.component.html`.
5.  **Implement Register Component:**
    *   **Prompt:** `// Implement RegisterComponent similar to Login. Include fields for name, email, password, confirm password. Add password matching validator. Call AuthService.register() on submit.`
    *   **Action:** Implement `register.component.ts` and `register.component.html`.
6.  **(Implement other Auth components):** Implement ForgotPassword, ResetPassword, VerifyEmail components similarly, handling route parameters (`ActivatedRoute`) where needed.
7.  **Generate Auth Guards:**
    *   **Prompt:** `// Generate AuthGuard and AdminGuard (functional guards preferred) in src/app/core/guards. Implement logic using AuthService to check authentication and admin status, redirecting using Router if checks fail.`
    *   **Action:** Run `ng g guard core/guards/auth --functional`, `ng g guard core/guards/admin --functional`. Implement guard logic.
8.  **Apply Guards to Routes:**
    *   **Prompt:** `// Update app.routes.ts to apply AuthGuard and AdminGuard to relevant routes using the canActivate property.`
    *   **Action:** Edit `app.routes.ts`.

## Phase 4: Core Feature Modules (Giveaways, Membership) (Day 8-12)

**Goal:** Build the UI for browsing giveaways, viewing details, entering, and managing memberships.

1.  **Generate Feature Services:**
    *   **Prompt:** `// Generate services for Giveaways, Membership, Payment, Entry in src/app/core/services. Implement methods to fetch data (list, details) and perform actions (subscribe, enter) using ApiService.`
    *   **Action:** Run `ng g s core/services/giveaway`, `ng g s core/services/membership`, etc.
2.  **Generate Feature Components:**
    *   **Prompt:** `// Generate standalone components for GiveawayList, GiveawayDetail, MembershipTiers, SubscriptionCheckout, MembershipManagement within src/app/features/giveaways and src/app/features/membership.`
    *   **Action:** Run `ng g c features/giveaways/giveaway-list --standalone`, etc.
3.  **Implement Feature Routing:**
    *   **Prompt:** `// Define routes for these components in app.routes.ts (or feature-specific route files), applying AuthGuard where necessary.`
    *   **Action:** Add feature routes.
4.  **Implement Giveaway List Component:**
    *   **Prompt:** `// Implement GiveawayListComponent. Fetch data using GiveawayService. Display giveaways using *ngFor with <mat-card> or a custom GiveawayCardComponent. Include admin-only 'Create' button (*ngIf="authService.isAdmin$ | async"). Handle loading/empty states.`
    *   **Action:** Implement TS and HTML.
5.  **Implement Giveaway Detail Component:**
    *   **Prompt:** `// Implement GiveawayDetailComponent. Fetch details using GiveawayService based on route slug (ActivatedRoute). Display details, prize, countdown. Include member-only entry status/button (*ngIf="!isAdmin && isAuthenticated") and admin-only management links (*ngIf="isAdmin"). Implement enterGiveaway() method calling EntryService.`
    *   **Action:** Implement TS and HTML.
6.  **Implement Membership Tiers Component:**
    *   **Prompt:** `// Implement MembershipTiersComponent. Fetch tiers using MembershipService. Display using <mat-card> or grid. Include conditional CTA buttons based on auth status, current plan, and admin role (*ngIf).`
    *   **Action:** Implement TS and HTML.
7.  **(Implement other feature components):** Implement SubscriptionCheckout (integrating Stripe/payment provider), MembershipManagement similarly, fetching data, handling forms, and user interactions.

## Phase 5: User Profile & Dashboard (Day 13-15)

**Goal:** Build the user dashboard and profile management sections.

1.  **Generate Profile/Dashboard Components:**
    *   **Prompt:** `// Generate standalone components for Dashboard, Profile, Settings, EntryHistory, WinHistory, TransactionHistory within src/app/features/dashboard and src/app/features/profile.`
    *   **Action:** Run `ng g c features/dashboard/dashboard --standalone`, `ng g c features/profile/profile --standalone`, etc.
2.  **Generate UserService:**
    *   **Prompt:** `// Generate UserService in src/app/core/services. Implement methods for fetching/updating profile, changing password, fetching history data.`
    *   **Action:** Run `ng g s core/services/user`. Implement.
3.  **Implement Profile/Dashboard Routing:**
    *   **Prompt:** `// Define routes for dashboard and profile sections in app.routes.ts, protected by AuthGuard. Use child routes for profile sections.`
    *   **Action:** Add dashboard/profile routes.
4.  **Implement Dashboard Component:**
    *   **Prompt:** `// Implement DashboardComponent. Fetch summary data (e.g., from UserService or dedicated DashboardService). Display using widgets (custom components or <mat-card>).`
    *   **Action:** Implement TS and HTML.
5.  **Implement Profile Component:**
    *   **Prompt:** `// Implement ProfileComponent. Create reactive form for editable fields (name). Fetch initial data using UserService. Implement onSubmit() to call updateProfile().`
    *   **Action:** Implement TS and HTML.
6.  **Implement Settings Component:**
    *   **Prompt:** `// Implement SettingsComponent. Use <mat-tab-group>. Create forms for password change and notification preferences. Call relevant UserService methods on submit.`
    *   **Action:** Implement TS and HTML.
7.  **Implement History Components:**
    *   **Prompt:** `// Implement history components (EntryHistory, etc.). Use Angular Material Table (<table mat-table>) with MatTableDataSource to display paginated data fetched from UserService. Use <mat-paginator>.`
    *   **Action:** Implement TS and HTML for history views.

## Phase 6: Content Pages (Day 16)

**Goal:** Create static content pages.

1.  **Generate Content Components:**
    *   **Prompt:** `// Generate standalone components for StaticPage, Winners within src/app/features/content.`
    *   **Action:** Run `ng g c features/content/static-page --standalone`, etc.
2.  **Generate ContentService:**
    *   **Prompt:** `// Generate ContentService in src/app/core/services. Implement methods to fetch content by slug (for static pages) and fetch winners.`
    *   **Action:** Run `ng g s core/services/content`. Implement.
3.  **Implement Content Routing:**
    *   **Prompt:** `// Define routes for static pages (using data property for slug) and winners page in app.routes.ts.`
    *   **Action:** Add content routes.
4.  **Implement Static Page Component:**
    *   **Prompt:** `// Implement StaticPageComponent. Fetch content using ContentService based on route data (ActivatedRoute). Display title and content (using [innerHTML] with DomSanitizer). Include admin-only 'Edit' button (*ngIf="isAdmin").`
    *   **Action:** Implement TS and HTML.
5.  **Implement Winners Component:**
    *   **Prompt:** `// Implement WinnersComponent. Fetch winners using ContentService. Display using <mat-card> grid. Include admin-only 'Manage' link (*ngIf="isAdmin").`
    *   **Action:** Implement TS and HTML.

## Phase 7: Admin Module (Day 17-20)

**Goal:** Build the administrative interface using lazy loading.

1.  **Generate Admin Module & Routing:**
    *   **Prompt:** `// Generate the Admin feature module with routing, configured for lazy loading. Generate AdminLayoutComponent.`
    *   **Action:** Run `ng g module features/admin --routing`. Configure lazy loading in `app.routes.ts`. Generate `ng g c features/admin/layout/admin-layout --standalone` (or within module).
2.  **Generate Admin Components & Services:**
    *   **Prompt:** `// Generate necessary admin components (AdminDashboard, UserList, GiveawayList, GiveawayForm, etc.) and admin-specific services (AdminUserService, AdminGiveawayService) within the admin module.`
    *   **Action:** Use `ng g c features/admin/users/user-list --standalone` (or `--module=admin`), `ng g s features/admin/services/admin-user`, etc.
3.  **Implement Admin Routing (admin.routes.ts):**
    *   **Prompt:** `// Define child routes within admin.routes.ts, using AdminLayoutComponent as a wrapper. Protect all child routes.`
    *   **Action:** Set up admin child routes.
4.  **Implement Admin List Components:**
    *   **Prompt:** `// Implement admin list components (UserList, etc.). Use MatTable with sorting, pagination, filtering. Fetch data using admin services. Include 'Add New' button and action buttons (Edit, Delete) per row, often opening MatDialog.`
    *   **Action:** Implement admin list views.
5.  **Implement Admin Form Components (often in Dialogs):**
    *   **Prompt:** `// Implement admin form components (GiveawayForm, etc.), often designed to be used within MatDialog. Use reactive forms, Angular Material controls. Inject MAT_DIALOG_DATA for editing. Return data on dialog close.`
    *   **Action:** Implement admin forms.

## Phase 8: Styling, State Management, API Integration Refinement (Ongoing)

**Goal:** Polish the UI, ensure consistent state management, and robust API handling.

1.  **Refine Material Theme & SCSS:** Customize theme in `styles.scss`. Add component-specific styles.
2.  **Global State:** Ensure consistent use of services/stores for auth, user profile.
3.  **Error Handling:** Refine `ErrorInterceptor` and component-level error display (`MatSnackBar`).
4.  **Loading States:** Use `*ngIf` with async pipe or boolean flags to show `<mat-progress-spinner>` or `<mat-progress-bar>`.
5.  **Accessibility (A11y):** Review Material component usage and custom elements for ARIA attributes, keyboard navigation.

## Phase 9: Testing & Deployment Prep (Day 21+)

**Goal:** Ensure application quality and prepare for deployment.

1.  **Unit Testing:**
    *   **Prompt:** `// Write unit tests (.spec.ts) for services and components using Angular testing utilities (TestBed) and Jasmine/Karma. Mock dependencies.`
    *   **Action:** Implement unit tests.
2.  **Integration Testing (Component Interaction):**
    *   **Prompt:** `// Write component integration tests focusing on parent-child communication and template interactions.`
    *   **Action:** Implement integration tests.
3.  **End-to-End Testing (Optional):**
    *   **Prompt:** `// Set up E2E tests using Cypress or Protractor (though Cypress is often preferred) to test critical user flows.`
    *   **Action:** Implement E2E tests.
4.  **Build Optimization:** Run production build (`ng build --configuration production`). Analyze bundle sizes.
5.  **Deployment Configuration:** Configure `angular.json` for different environments. Prepare Dockerfile or server configurations.

This step-by-step guide provides a structured path for Angular development using Copilot. Remember to review suggestions, adapt prompts, and test thoroughly.
