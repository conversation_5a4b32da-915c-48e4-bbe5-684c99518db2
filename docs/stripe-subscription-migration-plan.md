# Stripe Subscription Migration Plan

## Overview

This document outlines the migration from one-time payments to recurring Stripe subscriptions for the Winners Society membership platform.

## Key Principle: Stripe as Source of Truth

**Stripe Manages:**
- ✅ Subscription lifecycle (active, past_due, canceled, etc.)
- ✅ Billing cycles and automatic charges
- ✅ Payment retries and dunning management
- ✅ Proration for upgrades/downgrades
- ✅ Invoice generation
- ✅ Tax calculation (with Stripe Tax)

**Our Database Stores:**
- ✅ Minimal subscription reference data (subscription ID, customer ID)
- ✅ User-facing preferences
- ✅ Local business logic requirements
- ✅ Performance cache (current period end date)

## Phase 1: Database Schema Updates ✅

### Updated Models:

**User Model:**
- Added `stripeCustomerId` for Stripe customer reference

**MembershipTier Model:**
- Added `stripeProductId` for Stripe product reference
- Added `stripePriceId` for Stripe price reference

**Membership Model:**
- Added `stripeSubscriptionId` for subscription reference
- Added `stripeCustomerId` for customer reference
- Added `currentPeriodEnd` for performance caching
- Added `cancelAtPeriodEnd` for cancellation management

**PaymentMethod Model:**
- Added `billingAddress` for address storage
- Added `isBackup` for backup payment method functionality

## Phase 2: Backend Implementation

### 2.1 Stripe Product and Price Setup

Create Stripe products and prices for each membership tier:

```typescript
// Create Stripe products for each membership tier
const stripeProduct = await stripe.products.create({
  name: tier.name,
  description: tier.description,
  metadata: {
    tierName: tier.name,
    entryAllocation: tier.entryAllocation.toString()
  }
});

// Create recurring price for the product
const stripePrice = await stripe.prices.create({
  product: stripeProduct.id,
  unit_amount: Math.round(tier.price * 100), // Convert to cents
  currency: tier.currency.toLowerCase(),
  recurring: {
    interval: 'month',
    interval_count: 1
  },
  metadata: {
    tierName: tier.name
  }
});
```

### 2.2 Subscription Service Implementation

**New Methods Required:**
- `createSubscription(userId, priceId, paymentMethodId)`
- `updateSubscription(subscriptionId, newPriceId)`
- `cancelSubscription(subscriptionId, cancelAtPeriodEnd)`
- `resumeSubscription(subscriptionId)`
- `getSubscriptionStatus(subscriptionId)`

### 2.3 Customer Management

**Stripe Customer Creation:**
```typescript
const customer = await stripe.customers.create({
  email: user.email,
  name: `${user.firstName} ${user.lastName}`,
  metadata: {
    userId: user.id
  }
});
```

### 2.4 Webhook Event Handling

**Critical Webhook Events:**
- `customer.subscription.created`
- `customer.subscription.updated`
- `customer.subscription.deleted`
- `invoice.payment_succeeded`
- `invoice.payment_failed`
- `customer.subscription.trial_will_end`

## Phase 3: Billing Address Requirements

### Research Findings:

**Stripe Requirements:**
- Billing address is **not mandatory** for subscription payments
- Required for tax compliance in certain jurisdictions
- Recommended for fraud prevention
- Required for Stripe Tax automatic calculation

**Implementation Strategy:**
- Make billing address **optional** initially
- Add collection form with validation
- Enable Stripe Tax for automatic tax calculation
- Support international addresses with proper validation

### Billing Address Form Fields:
```typescript
interface BillingAddress {
  line1: string;
  line2?: string;
  city: string;
  state?: string;
  postal_code: string;
  country: string;
}
```

## Phase 4: Frontend Implementation

### 4.1 Subscription Flow Updates

**Current Flow:**
1. Select plan → Create payment intent → Process one-time payment

**New Flow:**
1. Select plan → Create/retrieve customer → Attach payment method → Create subscription

### 4.2 Payment Form Updates

**New Features:**
- Billing address collection (optional)
- Subscription terms and conditions
- Auto-renewal preferences
- Payment method as default for future charges

### 4.3 Subscription Management UI

**New Components:**
- Subscription status display
- Next billing date
- Cancel subscription
- Update payment method
- Upgrade/downgrade plans
- Billing history

## Phase 5: Migration Strategy

### 5.1 Backward Compatibility

**Existing Users:**
- Keep current one-time payment memberships
- Migrate to subscriptions on next renewal
- Provide migration incentives

### 5.2 Feature Flags

**Gradual Rollout:**
- Feature flag for subscription vs one-time payments
- A/B testing for conversion rates
- Rollback capability

## Phase 6: Error Handling & User Experience

### 6.1 Failed Payment Scenarios

**Stripe's Built-in Dunning:**
- Automatic retry logic (3 attempts over 3 weeks)
- Email notifications to customers
- Subscription status updates

**Our Implementation:**
- Display payment failure notifications
- Provide easy payment method update
- Grace period before access restriction

### 6.2 User Notifications

**Email Notifications:**
- Subscription created
- Payment succeeded
- Payment failed
- Subscription canceled
- Payment method expiring

## Phase 7: Testing Strategy

### 7.1 Stripe Test Mode

**Test Scenarios:**
- Successful subscription creation
- Failed payment scenarios
- Subscription cancellation
- Plan upgrades/downgrades
- Webhook event handling

### 7.2 Test Cards

Use Stripe test cards for various scenarios:
- `****************` - Successful payments
- `****************` - Card declined
- `****************` - Insufficient funds

## Implementation Timeline

**Week 1-2:** Database schema updates and migration
**Week 3-4:** Backend subscription service implementation
**Week 5-6:** Webhook handlers and error handling
**Week 7-8:** Frontend subscription flow implementation
**Week 9-10:** Testing and bug fixes
**Week 11-12:** Gradual rollout and monitoring

## Success Metrics

- Subscription conversion rate
- Payment failure rate
- Customer retention rate
- Support ticket reduction
- Revenue predictability improvement
