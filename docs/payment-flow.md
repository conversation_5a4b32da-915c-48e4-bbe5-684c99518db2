# Payment Processing Flow Documentation

## Overview

The Winners Society platform uses a two-step payment process with Stripe:

1. **Create a Payment Intent**: This reserves the payment and returns a client secret
2. **Process the Payment**: This confirms the payment using the payment intent ID and payment method ID

## Payment Flow

### Step 1: Create a Payment Intent

```
POST /api/payments/payment-intent
{
  "planId": "membership-tier-id",
  "description": "Membership subscription"
}
```

Response:
```json
{
  "success": true,
  "data": {
    "id": "pi_3RPxTmFd99NEHyJZ1HdWM2Wb",
    "clientSecret": "pi_3RPxTmFd99NEHyJZ1HdWM2Wb_secret_3ZUxgZnNlko2UAQNM31KzuqDK",
    "amount": 49,
    "currency": "usd",
    "status": "requires_payment_method"
  }
}
```

### Step 2: Process the Payment

```
POST /api/payments/process
{
  "paymentIntentId": "pi_3RPxTmFd99NEHyJZ1HdWM2Wb",
  "paymentMethodId": "pm_1RPxHnFd99NEHyJZ8QXQB142"
}
```

Response:
```json
{
  "success": true,
  "data": {
    "id": "transaction-id",
    "userId": "user-id",
    "amount": 49,
    "currency": "usd",
    "status": "COMPLETED",
    "paymentMethod": "pm_1RPxHnFd99NEHyJZ8QXQB142",
    "paymentIntentId": "pi_3RPxTmFd99NEHyJZ1HdWM2Wb",
    "description": "Membership subscription",
    "createdAt": "2023-05-15T12:34:56.789Z",
    "updatedAt": "2023-05-15T12:34:56.789Z"
  }
}
```

## Important Notes

1. **Security**: The payment amount is determined server-side based on the membership plan. Never allow clients to specify the amount directly.

2. **Required Parameters**: Both `paymentIntentId` and `paymentMethodId` are required when processing a payment.

3. **Order of Operations**: Always create a payment intent first, then process the payment.

4. **Idempotency**: The system is designed to be idempotent. If a payment has already been processed, the system will return an error.

5. **Membership Activation**: Upon successful payment, the system automatically activates the user's membership.

6. **Redirect-Free Payments**: The system is configured to never use redirect-based payment methods, ensuring a seamless in-app experience.

## Frontend Implementation

When implementing the payment flow in your frontend application:

1. Fetch available membership tiers from `/api/membership-tiers/public`
2. Create a payment intent for the selected tier
3. Use Stripe.js to collect payment details and get a payment method ID
4. Process the payment with both IDs
5. Verify membership activation with `/api/memberships`

## Error Handling

Common errors to handle:

- Missing payment intent ID: "Payment intent ID is required. Please create a payment intent first."
- Missing payment method ID: "Payment method ID is required."
- Already processed: "Payment has already been processed."
- Invalid payment intent: "Payment intent not found or invalid."
- Redirect required: "This PaymentIntent is configured to accept payment methods enabled in your Dashboard. Because some of these payment methods might redirect your customer off of your page, you must provide a `return_url`." (This should not occur with our current configuration, but if it does, it indicates a configuration issue.)

## Webhook Support

The system also supports Stripe webhooks for additional payment event handling:

```
POST /api/payments/webhook
```

This endpoint requires a valid Stripe signature in the `stripe-signature` header.
