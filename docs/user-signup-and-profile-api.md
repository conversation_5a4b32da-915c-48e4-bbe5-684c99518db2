# User Signup and Profile API Documentation

## 1. User Signup (Registration)

**Endpoint:**
`POST /api/auth/register`

**Description:**
Registers a new user and creates their profile (including address fields) in a single call.

**Request Body:**
Send as JSON. All fields are required unless marked optional.

| Field         | Type   | Required | Description                |
|---------------|--------|----------|----------------------------|
| email         | string | Yes      | User's email address       |
| password      | string | Yes      | User's password            |
| firstName     | string | Yes      | User's first name          |
| lastName      | string | Yes      | User's last name           |
| addressLine1  | string | Yes      | Address line 1             |
| city          | string | Yes      | City                       |
| state         | string | Yes      | State or province          |
| postalCode    | string | Yes      | Postal/ZIP code            |
| country       | string | Yes      | Country                    |
| profileImage  | string | No       | URL to profile image       |

**Password Requirements:**
- Minimum 8 characters
- At least one uppercase letter
- At least one lowercase letter
- At least one number
- At least one special character

**Example Request:**
```json
{
  "email": "<EMAIL>",
  "password": "StrongPassword123!",
  "firstName": "John",
  "lastName": "Doe",
  "addressLine1": "123 Main St",
  "city": "New York",
  "state": "NY",
  "postalCode": "10001",
  "country": "USA",
  "profileImage": "https://example.com/profile.jpg"
}
```

**Success Response:**
- `201 Created`
```json
{
  "success": true,
  "data": {
    "user": {
      "id": "user-uuid",
      "email": "<EMAIL>",
      "firstName": "John",
      "lastName": "Doe",
      "role": "USER",
      "isVerified": true,
      "createdAt": "2025-05-18T12:00:00.000Z",
      "updatedAt": "2025-05-18T12:00:00.000Z"
    },
    "accessToken": "jwt-access-token",
    "refreshToken": "jwt-refresh-token",
    "expiresIn": 3600
  }
}
```

**Error Responses:**
- `400 Bad Request` if required fields are missing or invalid.
- `409 Conflict` if the email is already registered.

---

## 2. Get User Profile (for Profile Page)

**Endpoint:**
`GET /api/user/full-profile`

**Authentication:**
Requires Bearer token (JWT) in the `Authorization` header.

**Description:**
Returns both the User and Profile (address and personal info) for the currently authenticated user.

**Success Response:**
- `200 OK`
```json
{
  "success": true,
  "data": {
    "user": {
      "id": "user-uuid",
      "email": "<EMAIL>",
      "firstName": "John",
      "lastName": "Doe",
      "role": "USER",
      "isVerified": true,
      "createdAt": "2025-05-18T12:00:00.000Z",
      "updatedAt": "2025-05-18T12:00:00.000Z"
    },
    "profile": {
      "userId": "user-uuid",
      "address": "123 Main St",
      "city": "New York",
      "state": "NY",
      "zipCode": "10001",
      "country": "USA",
      "bio": null,
      "avatar": "https://example.com/profile.jpg",
      "phone": null,
      "birthDate": null
    }
  }
}
```

**Error Responses:**
- `401 Unauthorized` if the token is missing or invalid.

---

## 3. Update User Profile

**Endpoint:**
`PUT /api/user/profile`

**Authentication:**
Requires Bearer token (JWT).

**Description:**
Allows the user to update their profile and address information.

**Request Body Example:**
```json
{
  "firstName": "Jane",
  "lastName": "Smith",
  "profileImage": "https://example.com/new-profile.jpg",
  "addressLine1": "456 Elm St",
  "city": "Los Angeles",
  "state": "CA",
  "postalCode": "90001",
  "country": "USA"
}
```

**Success Response:**
- `200 OK`
```json
{
  "success": true,
  "data": {
    // Updated user and/or profile fields
  }
}
```

---

## Notes for FE Developers

- Always use the `Authorization: Bearer <accessToken>` header for authenticated endpoints.
- After signup, store the `accessToken` and `refreshToken` for future authenticated requests.
- Use `/api/user/full-profile` to fetch all user and address info for the profile page.
- To update profile/address, use the update endpoint with the relevant fields.

---
