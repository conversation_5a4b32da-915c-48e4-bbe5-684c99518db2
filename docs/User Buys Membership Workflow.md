# User Buys Membership Workflow

This document outlines the typical workflow for a user purchasing a membership on a giveaway platform, building upon the user signup process and competitor analysis.

## Flow Diagram (Conceptual)

```mermaid
graph TD
    A{User is Logged In} --> B[User navigates to Membership Plans Page (e.g., /membership)];
    B --> C[User views available Membership Tiers (e.g., Bronze, Silver, Gold)];
    C --> D[User selects a desired Membership Tier];
    D --> E{Plan Details & Price Confirmation Page};
    E --> F[User clicks "Proceed to Payment" / "Subscribe"];
    F --> G[Payment Form Presented (e.g., Stripe Elements, PayPal)];
    G --> H[User enters Payment Details (Card Info, Billing Address)];
    H --> I{Backend: Validate Payment Form Input (client-side first)};
    I -- Valid --> J[User clicks "Confirm Payment" / "Pay Now"];
    J --> K{Backend: Create Payment Intent with Payment Gateway (e.g., Stripe)};
    K --> L{Backend: Process Payment with Payment Gateway};
    L -- Payment Successful --> M[Backend: Payment Gateway sends Success Confirmation (Webhook/Callback)];
    M --> N[Backend: Create Transaction Record (status: COMPLETED)];
    N --> O[Backend: Create/Update User's Membership Record];
    O --> P[Backend: Allocate Giveaway Entries based on new Tier];
    P --> Q[Backend: Send Membership Confirmation Email to User];
    Q --> R[Frontend: Display "Membership Activated!" Success Page/Message];
    R --> S[Redirect to User Dashboard or Giveaways Page];
    L -- Payment Failed --> T[Backend: Payment Gateway sends Failure Confirmation];
    T --> U[Backend: Create Transaction Record (status: FAILED)];
    U --> V[Frontend: Display "Payment Failed. Please try again or use a different card." Error Message];
    V --> G;
    I -- Invalid --> W[Frontend: Display validation errors on payment form];
```

## Detailed Steps

1.  **Initiation & Tier Selection**:
    *   **Logged-in User**: The user is already logged in. If not, they would typically be prompted to log in or sign up first.
    *   **Navigation**: The user navigates to the membership plans page (e.g., via a "Membership," "Upgrade," or "Join Our Club" link).
    *   **Tier Review**: The user reviews the available `MembershipTier` options, their prices, `billingPeriod` (MONTHLY, YEARLY), `entryAllocation`, `features`, and `benefits`.
    *   **Selection**: The user clicks a "Select Plan," "Choose This Tier," or "Subscribe" button for their chosen tier.

2.  **Plan Confirmation & Checkout Initiation**:
    *   The user might be taken to a confirmation page summarizing the selected tier, its price, and billing frequency.
    *   The user clicks a button like "Proceed to Checkout," "Confirm Subscription," or "Pay Now."

3.  **Payment Form Presentation**:
    *   A secure payment form is displayed. This is often an embedded form from a payment gateway provider (e.g., Stripe Elements, PayPal SDK).
    *   **Fields Typically Include**:
        *   Cardholder Name
        *   Credit/Debit Card Number
        *   Card Expiry Date (MM/YY)
        *   Card CVC/CVV
        *   Billing Address (Street, City, State, Postal Code, Country)
        *   Option to save payment method for future renewals (if applicable, with user consent).

4.  **Payment Information Submission**:
    *   The user fills in their payment and billing details.
    *   Client-side validation (e.g., card number format, date validity) should occur before submission.
    *   The user clicks "Confirm Payment," "Subscribe," or "Pay Securely."

5.  **Backend Payment Processing**:
    *   **Tokenization (if applicable)**: Payment details might be tokenized by the payment gateway on the client-side to avoid sending raw card data to the application server.
    *   **Payment Intent Creation**: The backend communicates with the payment gateway API to create a payment intent or charge, passing the amount, currency, and customer information (or token).
    *   **Payment Execution**: The payment gateway attempts to process the payment.

6.  **Payment Outcome Handling**:
    *   **Payment Successful**:
        1.  The payment gateway confirms the successful transaction (often via a webhook to the backend, or a direct API response).
        2.  **Transaction Record**: A new record is created in the `Transaction` table with details like `userId`, `membershipTierId` (if applicable, or a more general `productId`), `amount`, `currency`, `paymentGateway` (e.g., STRIPE), `gatewayTransactionId`, `status` (e.g., `COMPLETED`), and `paymentMethodDetails`.
        3.  **Membership Activation/Update**: A new record is created in the `Membership` table (or an existing one is updated if it's a renewal/upgrade). This record links the `User` to the chosen `MembershipTier` and includes `startDate`, `endDate` (calculated based on `billingPeriod` and `duration` from `MembershipTier`), `status` (e.g., `ACTIVE`), and `autoRenew` status.
        4.  **Entry Allocation**: Based on the `entryAllocation` field of the purchased `MembershipTier`, the corresponding number of entries are credited to the user. This might involve creating records in an `Entry` table linking the user, the giveaway(s) they are eligible for, and the number of entries.
        5.  **Confirmation Email**: A confirmation email is sent to the user detailing their new membership, payment, and benefits.
        6.  **Frontend Update**: The frontend displays a success message (e.g., "Membership Activated! Welcome aboard!") and typically redirects the user to their dashboard or the main giveaways page.
    *   **Payment Failed**:
        1.  The payment gateway communicates the failure (e.g., insufficient funds, card declined, fraud suspected).
        2.  **Transaction Record (Optional but Recommended)**: A record might still be created in the `Transaction` table with a `status` of `FAILED` for auditing purposes.
        3.  **Frontend Update**: The frontend displays an error message (e.g., "Payment failed. Please check your card details or try a different payment method."). The user is usually returned to the payment form to retry.

7.  **Recurring Billing Setup (if applicable)**:
    *   If the membership is a recurring subscription, the payment gateway and the backend system store the necessary information (e.g., customer ID, payment method ID from the gateway) to handle automatic renewals according to the `billingPeriod`.

## Database Tables Touched (based on provided Prisma schema context)

*   **`User`**: Read to confirm user identity and potentially update last activity.
*   **`MembershipTier`**: Read to get details of the selected tier (price, benefits, entry allocation, duration, billingPeriod).
*   **`Membership`**: A new record is created or an existing one is updated. Fields like `userId`, `membershipTierId`, `startDate`, `endDate`, `status` (`ACTIVE`), `autoRenew`, `paymentGatewaySubscriptionId` are populated.
*   **`Transaction`**: A new record is created. Fields like `userId`, `membershipId` (linking to the new/updated membership), `amount`, `currency`, `status` (`COMPLETED` or `FAILED`), `paymentGateway`, `gatewayTransactionId`, `type` (`SUBSCRIPTION_PURCHASE`) are populated.
*   **`Entry`** (Conceptual, assuming it exists for tracking entries per giveaway):
    *   New records might be created linking the `userId` to specific active `Giveaway` records, with the number of entries derived from `MembershipTier.entryAllocation`.
    *   Alternatively, `User.totalEntries` or a similar field might be updated if entries are generic and not per-giveaway initially.
*   **`Giveaway`**: Read to identify active giveaways for entry allocation if entries are specific to current draws.

## Key Validations

*   **Frontend Validations**:
    *   Payment form fields (card number format, expiry date, CVC).
    *   Billing address fields (if applicable).
*   **Backend Validations**:
    *   User authentication and authorization (ensure the logged-in user is making the purchase for themselves).
    *   `MembershipTier` ID is valid and the tier is active and purchasable.
    *   Payment amount matches the selected tier's price.
    *   Prevention of duplicate subscription creation if one is already active (unless it's an upgrade flow).
    *   Validation of payment gateway responses/webhooks (e.g., signature verification for webhooks).
    *   Sufficient stock/availability if the membership tier has limits (not typical for digital subscriptions but possible).

This workflow focuses on a new membership purchase. Upgrade/downgrade scenarios would involve additional logic for proration, changing `endDate` of the old plan, and adjusting entry allocations.
