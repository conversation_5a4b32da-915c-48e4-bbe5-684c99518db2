# Angular UI Design & Component Prompts (with Role Differentiation)

This document provides detailed prompts for generating Angular component structures, templates (HTML), and potentially TypeScript logic using GitHub Copilot. It focuses on the latest Angular features (standalone components, signals if applicable), Angular Material components, and includes explicit role differentiation (Admin vs. Member/Client User).

**General Design Style:** Modern, clean, trustworthy, engaging, using Angular Material Design principles. Consistent color palette and typography.

**Instructions for AI Assistant:** For each prompt, generate:
1.  **Angular Component Structure:** Suggest the necessary `@Component` decorator setup (selector, `standalone: true`, `imports`, `templateUrl`, `styleUrl`). Include necessary `Input`s, `Output`s, and injected services (e.g., `AuthService`, `ApiService`).
2.  **Component Template (HTML):** Generate the HTML template using Angular Material components (`<mat-card>`, `<mat-toolbar>`, `<mat-button>`, `<mat-form-field>`, `<mat-table>`, etc.) and Angular directives (`*ngIf`, `*ngFor`). **Explicitly show or describe how elements should differ for Admin vs. Member roles using `*ngIf="isAdmin"` or similar logic.** Include placeholders for dynamic data binding (`{{ variable }}` or `[property]`).
3.  **Component TypeScript (Key Logic):** Suggest key TypeScript logic within the component class, especially for handling role-based visibility, fetching data, and event handling.
4.  **SCSS/CSS Suggestions:** Suggest component-specific styling needs beyond Angular Material defaults.

## 1. Core Layout

**Prompt (App Component, Toolbar, Sidenav - Role Aware):**
"Generate the main `AppComponent` structure for an Angular application using Angular Material. Include:
1.  **Toolbar (`<mat-toolbar>`):** Display the application title/logo. Include navigation buttons (`<button mat-button>`) for Giveaways, Membership, Winners. Conditionally display Login/Register buttons OR a user menu button based on authentication status (`AuthService.isAuthenticated()`).
2.  **User Menu (`<mat-menu>`):** Triggered by the user menu button. Include items for Profile, Settings, My Entries, My Wins, Billing, Logout. **If Admin (`AuthService.isAdmin()`):** Also include a distinct "Admin Dashboard" menu item routing to `/admin`.
3.  **Sidenav (`<mat-sidenav-container>`, `<mat-sidenav>`):** Optional for main navigation, especially on mobile. Can be toggled.
4.  **Main Content Area:** Use `<router-outlet>` within `<mat-sidenav-content>` to render routed components.
5.  **Footer:** A simple footer component placeholder `<app-footer>`.

*Component Structure:* `AppComponent` (standalone), inject `AuthService`, `Router`.
*TypeScript Logic:* Methods for `logout()`, checking `isAdmin`, handling navigation.
*SCSS:* Customize toolbar color, sidenav width."

**Prompt (Footer Component):**
"Generate a simple `FooterComponent` (standalone) using Angular Material. Display copyright text and links (`<a mat-button>`) for Terms of Service and Privacy Policy within a `<mat-toolbar>` or a simple `<footer>` tag styled with Material theme."

## 2. Authentication Module

**Prompt (Login Component - `/login`):**
"Generate the `LoginComponent` (standalone) for the `/login` route using Angular Material.
1.  **Template:** Center a `<mat-card>` (`class="login-card"`). Include `<mat-card-header>` with title "Login". Inside `<mat-card-content>`, create a reactive form (`[formGroup]`) with `<mat-form-field>` for Email and Password (type="password"). Use `<mat-error>` for validation messages. Include `<mat-checkbox>` for "Remember me". Add a submit button (`<button mat-raised-button color="primary" type="submit">`). Include links (`<a>`) below for "Forgot Password?" and "Register".
2.  **TypeScript:** Inject `FormBuilder`, `AuthService`, `Router`. Create `loginForm` using `FormBuilder` with validators (`Validators.required`, `Validators.email`). Implement `onSubmit()` method to call `AuthService.login()`, handle errors (e.g., display in `<mat-error>` or snackbar), and navigate to `/dashboard` on success.

*SCSS:* Style the `login-card` width and centering."

**Prompt (Register Component - `/register`):**
"Generate the `RegisterComponent` (standalone) similar to `LoginComponent`. The reactive form should include fields for First Name, Last Name, Email, Password, Confirm Password. Implement custom validator for password matching. `onSubmit()` should call `AuthService.register()`, handle success (e.g., show message about email verification), and errors."

**Prompt (Forgot Password Component - `/forgot-password`):**
"Generate the `ForgotPasswordComponent` (standalone). Centered `<mat-card>`. Reactive form with a single Email field (`<mat-form-field>`). `onSubmit()` calls `AuthService.forgotPassword()`. Show confirmation message on success."

**Prompt (Reset Password Component - `/reset-password/[token]`):**
"Generate the `ResetPasswordComponent` (standalone). Centered `<mat-card>`. Reactive form with New Password and Confirm Password fields. Implement password matching validator. Get token from route parameters (`ActivatedRoute`). `onSubmit()` calls `AuthService.resetPassword()` with token and new password."

## 3. Membership Module

**Prompt (Membership Tiers Component - `/membership` - Role Aware):**
"Generate the `MembershipTiersComponent` (standalone) for the `/membership` route.
1.  **Template:** Use Angular Material grid list (`<mat-grid-list>`) or Flex Layout (`fxLayout`) to display membership tiers side-by-side. Each tier is a `<mat-card>`.
2.  **Tier Card:** Display Tier Name (`<mat-card-title>`), Price (`<mat-card-subtitle>`), Features list (`<ul>` with `<mat-icon>`).
3.  **CTA Button (`<button mat-raised-button>` - Conditional):**
    *   Use `*ngIf="!authService.isAuthenticated()"` for "Choose Plan" (routes to login/subscribe).
    *   Use `*ngIf="authService.isAuthenticated() && !isCurrentPlan(tier) && !isAdmin()"` for "Switch Plan".
    *   Use `*ngIf="isCurrentPlan(tier)"` to show "Current Plan" text/badge (`<mat-chip>`).
    *   Use `*ngIf="isAdmin()"` for "Edit Tier" button (routes to admin).
4.  **TypeScript:** Inject `MembershipService`, `AuthService`. Fetch tiers using `MembershipService.getTiers()`. Implement `isCurrentPlan(tier)` logic based on user's membership status from `AuthService` or another service.

*SCSS:* Style tier cards, highlight recommended plan."

**Prompt (Subscription Checkout Component - `/membership/subscribe/[tierId]`):**
"Generate the `SubscriptionCheckoutComponent` (standalone).
1.  **Template:** Use a two-column layout (Flex Layout or CSS Grid). Left column shows Order Summary (`<mat-card>`) with tier details. Right column contains Payment Form (`<mat-card>`) with placeholders for Stripe Elements (`<div id="stripe-card-element">`). Include "Confirm & Pay" button (`<button mat-raised-button color="accent">`).
2.  **TypeScript:** Inject `ActivatedRoute`, `MembershipService`, `PaymentService`, `Router`. Get `tierId` from route. Fetch tier details. Initialize Stripe Elements in `ngOnInit` or `ngAfterViewInit`. Implement payment submission logic calling `PaymentService.createSubscription()`."

**Prompt (Membership Management Component - `/membership/manage` - Role Aware):**
"Generate the `MembershipManagementComponent` (standalone) for Members.
1.  **Template:** Display current membership details in `<mat-card>` (Tier, Status with `<mat-chip>`, Renewal Date, Entries). Include action buttons:
    *   `*ngIf="!isAdmin()"`: "Cancel Subscription" (`<button mat-stroked-button color="warn">`), "Change Plan" (`<button mat-raised-button color="primary">`).
    *   `*ngIf="isAdmin()"`: Add admin-specific buttons like "Process Refund", "Manually Extend".
2.  **TypeScript:** Inject `MembershipService`, `AuthService`. Fetch user's membership details. Implement methods for cancel/change plan actions (potentially opening modals using `MatDialog`). Admin actions call relevant admin service methods."

## 4. Giveaways Module

**Prompt (Giveaway List Component - `/giveaways` - Role Aware):**
"Generate the `GiveawayListComponent` (standalone).
1.  **Template:** Page title "Current Giveaways".
    *   `*ngIf="isAdmin()"`: "Create New Giveaway" button (`<button mat-fab color="accent">` with add icon).
    *   Use `<mat-grid-list>` or Flex Layout to display giveaways in `<mat-card>`s.
    *   **Giveaway Card:** Include Image (`<img mat-card-image>`), Title (`<mat-card-title>`), Countdown timer, Description (`<mat-card-content>`).
    *   **Card Actions (`<mat-card-actions>` - Conditional):**
        *   `*ngIf="!isAdmin()"`: "View Details" button (`<button mat-button color="primary">`).
        *   `*ngIf="isAdmin()"`: "Manage" button (`<button mat-button color="accent">`).
2.  **TypeScript:** Inject `GiveawayService`, `AuthService`. Fetch active giveaways. Handle navigation based on role.

*SCSS:* Style giveaway cards, countdown timer."

**Prompt (Giveaway Detail Component - `/giveaways/[slug]` - Role Aware):**
"Generate the `GiveawayDetailComponent` (standalone).
1.  **Template:** Giveaway Title (`<h1>`).
    *   `*ngIf="isAdmin()"`: "Edit Giveaway" button (`<button mat-stroked-button>`).
    *   Use two-column layout. Left: Image Carousel (e.g., using a third-party library or simple `<mat-card>`), Description, T&Cs. Right: Countdown Timer (`<mat-card>`), Prize Details, Entry Status (`<mat-card>` with `<mat-progress-bar>`), Entry Button.
    *   **Entry Status/Button (`*ngIf="!isAdmin() && isAuthenticated()"`):** Show "Your entries: X / Y". Show "Enter Now" button (`<button mat-raised-button color="accent" [disabled]="!canEnter">`).
    *   **Admin Links (`*ngIf="isAdmin()"`):** Links to "View Entries", "Conduct Draw".
2.  **TypeScript:** Inject `ActivatedRoute`, `GiveawayService`, `AuthService`, `EntryService`. Fetch giveaway details by slug. Fetch user's entry status. Implement `canEnter` logic. Implement `enterGiveaway()` method calling `EntryService`.

*SCSS:* Style countdown, entry status, progress bar."

## 5. User Profile Module

**Prompt (Profile Component - `/profile`):**
"Generate the `ProfileComponent` (standalone).
1.  **Template:** Page Title "My Profile". Use `<mat-card>` with a reactive form (`[formGroup]`). Include `<mat-form-field>` for editable First Name, Last Name. Display non-editable Email. Include file input for Profile Image (`<input type="file">` styled or use a component). "Save Changes" button (`<button mat-raised-button color="primary">`).
2.  **TypeScript:** Inject `FormBuilder`, `UserService`, `AuthService`. Initialize form with user data from `AuthService`. Implement `onSubmit()` to call `UserService.updateProfile()`."

**Prompt (Settings Component - `/profile/settings`):**
"Generate the `SettingsComponent` (standalone).
1.  **Template:** Page Title "Account Settings". Use `<mat-tab-group>` for sections.
    *   **Password Tab:** `<mat-card>` with reactive form for Current/New/Confirm Password. "Change Password" button.
    *   **Notifications Tab:** `<mat-card>` with list of notification types using `<mat-slide-toggle>` or `<mat-checkbox>`. "Save Preferences" button.
2.  **TypeScript:** Inject `FormBuilder`, `UserService`. Implement forms and `onSubmit()` methods for each section."

**Prompt (History Components - `/profile/entries`, etc.):**
"Generate a history component (e.g., `EntryHistoryComponent`, standalone).
1.  **Template:** Page Title. Use Angular Material Table (`<table mat-table [dataSource]="dataSource">`) to display history records. Define columns (`<ng-container matColumnDef>`). Use `<mat-paginator>` for pagination.
2.  **TypeScript:** Inject relevant service (e.g., `EntryService`). Fetch data. Set up `MatTableDataSource` and connect `MatPaginator`."

## 6. Content Pages

**Prompt (Static Page Component - e.g., `/about` - Role Aware):**
"Generate a `StaticPageComponent` (standalone).
1.  **Template:** Use `<mat-card>` or simple container.
    *   `*ngIf="isAdmin()"`: "Edit Page" button (`<button mat-icon-button>`).
    *   Page Title (`<h1>`).
    *   Content area (`<div [innerHTML]="content">` - ensure sanitization).
2.  **TypeScript:** Inject `ActivatedRoute`, `ContentService`, `AuthService`, `DomSanitizer`. Fetch content based on route/slug. Sanitize HTML content before rendering."

**Prompt (Winners Component - `/winners` - Role Aware):**
"Generate the `WinnersComponent` (standalone).
1.  **Template:** Page Title "Our Winners".
    *   `*ngIf="isAdmin()"`: Link/Button to "Manage Winners".
    *   Display winners using `<mat-grid-list>` or Flex Layout with `<mat-card>` for each winner (Name, Prize, Image/Testimonial).
2.  **TypeScript:** Inject `WinnerService`, `AuthService`. Fetch winners data."

## 7. Admin Module

**Prompt (Admin Layout Component):**
"Generate the `AdminLayoutComponent` (standalone).
1.  **Template:** Use `<mat-sidenav-container>`. Sidenav (`<mat-sidenav mode="side" opened>`) contains `<mat-nav-list>` with links (`<a mat-list-item routerLink="...">`) for Dashboard, Users, Giveaways, etc., using `<mat-icon>`. Main content area uses `<router-outlet>`.
2.  **TypeScript:** Basic component structure.

*SCSS:* Style sidenav width, list items."

**Prompt (Admin List Component - e.g., `/admin/users`):**
"Generate an admin list component (e.g., `UserListComponent`, standalone).
1.  **Template:** Page Title. "Add New" button (`<button mat-raised-button color="primary">`). Optional filter controls (`<mat-form-field>`). Use Angular Material Table (`<table mat-table>`) with columns for data and an 'actions' column. Actions column contains Edit/Delete buttons (`<button mat-icon-button>`). Use `<mat-paginator>`.
2.  **TypeScript:** Inject relevant admin service, `MatDialog`. Fetch data. Set up `MatTableDataSource`, `MatPaginator`, `MatSort`. Implement methods for `addUser()`, `editUser(user)`, `deleteUser(user)` (often opening dialogs for forms/confirmation).

**Prompt (Admin Form Component - e.g., in a Dialog):**
"Generate an admin form component (e.g., `GiveawayFormComponent`, standalone, often used within `MatDialog`).
1.  **Template:** Use `<form [formGroup]="form">`. Use `<mat-dialog-title>`, `<mat-dialog-content>`, `<mat-dialog-actions>`. Inside content, use `<mat-form-field>` for inputs (Title, Dates using `<mat-datepicker>`), `<textarea matInput>` for description, etc. Actions have Save (`<button mat-raised-button color="primary" [mat-dialog-close]="form.value">`) and Cancel (`<button mat-button mat-dialog-close>`) buttons.
2.  **TypeScript:** Inject `MAT_DIALOG_DATA`, `MatDialogRef`, `FormBuilder`, relevant admin service. Initialize form, potentially patching with `data` if editing. Implement save logic (often handled by the component that opened the dialog based on `dialogRef.afterClosed()`)."

This provides a comprehensive set of prompts tailored for Angular and Angular Material, including role differentiation.
