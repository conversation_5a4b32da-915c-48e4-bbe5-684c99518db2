# Membership-Based Giveaway Platform: Architecture Diagram Documentation

## 1. Introduction

### 1.1 Purpose
This document provides a detailed architecture diagram for the membership-based giveaway platform, outlining the system components, their interactions, and the overall structure of the application.

### 1.2 Scope
This document covers the backend architecture for the MVP implementation, focusing on the Node.js, Express, Prisma, and PostgreSQL technology stack.

## 2. System Architecture Overview

The platform follows a layered architecture pattern with clear separation of concerns:

```
+-----------------------------------------------------------------------+
|                           Client Applications                          |
|  (Web Browsers, Mobile Apps, Admin Dashboards, External Integrations)  |
+-----------------------------------------------------------------------+
                                   |
                                   | HTTP/HTTPS
                                   v
+-----------------------------------------------------------------------+
|                             API Gateway                                |
|                     (Rate Limiting, Caching, Routing)                  |
+-----------------------------------------------------------------------+
                                   |
                                   v
+-----------------------------------------------------------------------+
|                           Express.js Server                            |
+-----------------------------------------------------------------------+
         |                 |                  |                |
         v                 v                  v                v
+----------------+ +----------------+ +----------------+ +-------------+
|   Controllers  | |    Services    | |   Middleware   | |   Routes    |
+----------------+ +----------------+ +----------------+ +-------------+
         |                 |                  |
         |                 v                  |
         |        +----------------+          |
         +------->|  Repositories  |<---------+
                  +----------------+
                          |
                          v
+-----------------------------------------------------------------------+
|                        Prisma ORM Layer                               |
+-----------------------------------------------------------------------+
                          |
                          v
+-----------------------------------------------------------------------+
|                      PostgreSQL Database                              |
+-----------------------------------------------------------------------+
         |                 |                  |                |
         v                 v                  v                v
+----------------+ +----------------+ +----------------+ +-------------+
|     Users      | |  Memberships   | |   Giveaways    | |   Other     |
|    Tables      | |    Tables      | |    Tables      | |   Tables    |
+----------------+ +----------------+ +----------------+ +-------------+
```

## 3. Component Details

### 3.1 Client Applications
- Web browsers accessing the frontend application
- Admin dashboards for platform management
- Potential mobile applications (future)
- External integrations via API

### 3.2 API Gateway
- Handles rate limiting to prevent abuse
- Provides caching for improved performance
- Routes requests to appropriate endpoints
- Manages CORS and security headers

### 3.3 Express.js Server
- Core application server built on Node.js
- Handles HTTP requests and responses
- Implements RESTful API endpoints
- Manages authentication and authorization

### 3.4 Controllers
- Handle incoming HTTP requests
- Validate request data
- Coordinate with services to fulfill requests
- Format and return responses

### 3.5 Services
- Implement business logic
- Coordinate complex operations
- Manage transactions and data integrity
- Handle error conditions

### 3.6 Middleware
- Authenticate and authorize requests
- Log request and response data
- Handle cross-cutting concerns
- Provide error handling

### 3.7 Routes
- Define API endpoints
- Map HTTP methods to controller actions
- Group related endpoints
- Apply middleware to endpoint groups

### 3.8 Repositories
- Provide data access abstraction
- Implement CRUD operations
- Handle database-specific logic
- Manage entity relationships

### 3.9 Prisma ORM Layer
- Provides type-safe database access
- Manages database schema and migrations
- Handles query building and execution
- Optimizes database operations

### 3.10 PostgreSQL Database
- Stores all application data
- Enforces data integrity through constraints
- Provides ACID-compliant transactions
- Supports complex queries and indexing

## 4. Folder Structure

The application follows a well-organized folder structure that reflects the architecture:

```
/
├── src/
│   ├── config/                 # Configuration files
│   │   ├── database.js         # Database configuration
│   │   ├── server.js           # Server configuration
│   │   └── ...
│   │
│   ├── controllers/            # Request handlers
│   │   ├── auth.controller.js
│   │   ├── user.controller.js
│   │   ├── membership.controller.js
│   │   ├── giveaway.controller.js
│   │   └── ...
│   │
│   ├── services/               # Business logic
│   │   ├── auth.service.js
│   │   ├── user.service.js
│   │   ├── membership.service.js
│   │   ├── giveaway.service.js
│   │   └── ...
│   │
│   ├── repositories/           # Data access layer
│   │   ├── base.repository.js  # Base repository with common methods
│   │   ├── user.repository.js
│   │   ├── membership.repository.js
│   │   ├── giveaway.repository.js
│   │   └── ...
│   │
│   ├── middleware/             # Express middleware
│   │   ├── auth.middleware.js
│   │   ├── error.middleware.js
│   │   ├── validation.middleware.js
│   │   └── ...
│   │
│   ├── routes/                 # API routes
│   │   ├── auth.routes.js
│   │   ├── user.routes.js
│   │   ├── membership.routes.js
│   │   ├── giveaway.routes.js
│   │   └── ...
│   │
│   ├── utils/                  # Utility functions
│   │   ├── logger.js
│   │   ├── validator.js
│   │   ├── helpers.js
│   │   └── ...
│   │
│   ├── models/                 # Data models and DTOs
│   │   ├── user.model.js
│   │   ├── membership.model.js
│   │   ├── giveaway.model.js
│   │   └── ...
│   │
│   ├── validators/             # Request validators
│   │   ├── auth.validator.js
│   │   ├── user.validator.js
│   │   ├── membership.validator.js
│   │   └── ...
│   │
│   ├── app.js                  # Express application setup
│   └── server.js               # Server entry point
│
├── prisma/                     # Prisma ORM files
│   ├── schema.prisma           # Database schema
│   ├── migrations/             # Database migrations
│   └── seed.js                 # Database seeding
│
├── tests/                      # Test files
│   ├── unit/                   # Unit tests
│   ├── integration/            # Integration tests
│   └── e2e/                    # End-to-end tests
│
├── .env                        # Environment variables
├── .env.example                # Example environment variables
├── package.json                # Project dependencies
└── README.md                   # Project documentation
```

## 5. Request Flow

The following diagram illustrates the flow of a typical request through the system:

```
Client → Request → API Gateway → Routes → Middleware → Controller → Service → Repository → Prisma → Database
                                                                       ↑
                                                                       |
Response ← API Gateway ← Routes ← Controller ← Service ← Repository ← Prisma ← Database
```

### 5.1 Example: User Registration Flow

1. Client sends a POST request to `/api/auth/register`
2. API Gateway validates the request and forwards it
3. Routes direct the request to the AuthController
4. AuthMiddleware processes the request
5. AuthController validates the request data
6. AuthController calls AuthService.register()
7. AuthService performs business logic validation
8. AuthService calls UserRepository.create()
9. UserRepository uses Prisma to create the user
10. Prisma executes the SQL query in the database
11. Result propagates back up the chain
12. AuthController formats the response
13. Response is sent back to the client

## 6. Controller Layer

The controller layer is responsible for handling HTTP requests and responses. Each controller focuses on a specific domain area:

### 6.1 Controller Structure

```javascript
// Example controller structure
class UserController {
  constructor(userService) {
    this.userService = userService;
  }

  async getProfile(req, res, next) {
    try {
      const userId = req.user.id;
      const profile = await this.userService.getUserProfile(userId);
      return res.status(200).json({
        success: true,
        data: profile
      });
    } catch (error) {
      next(error);
    }
  }

  // Other controller methods...
}

module.exports = UserController;
```

### 6.2 Controller Responsibilities

- Parse and validate request parameters
- Extract data from request body
- Call appropriate service methods
- Handle success responses
- Delegate error handling to middleware
- Format response data

## 7. Service Layer

The service layer implements the business logic of the application:

### 7.1 Service Structure

```javascript
// Example service structure
class MembershipService {
  constructor(membershipRepository, userRepository, paymentService) {
    this.membershipRepository = membershipRepository;
    this.userRepository = userRepository;
    this.paymentService = paymentService;
  }

  async upgradeMembership(userId, newTierId, paymentMethod) {
    // Get current membership
    const currentMembership = await this.membershipRepository.findByUserId(userId);
    if (!currentMembership) {
      throw new BusinessError('User has no active membership');
    }

    // Get membership tiers
    const currentTier = await this.membershipRepository.getTierById(currentMembership.tierId);
    const newTier = await this.membershipRepository.getTierById(newTierId);

    // Calculate price difference
    const priceDifference = this.calculatePriceDifference(currentTier, newTier, currentMembership);

    // Process payment if needed
    if (priceDifference > 0) {
      await this.paymentService.processPayment(userId, priceDifference, paymentMethod, 'membership_upgrade');
    }

    // Store current membership in history
    const membershipHistory = currentMembership.membershipHistory || [];
    membershipHistory.push({
      tierId: currentMembership.tierId,
      tierName: currentTier.name,
      startDate: currentMembership.startDate,
      endDate: new Date(),
      price: currentTier.price,
      currency: currentTier.currency,
      changeReason: 'upgrade',
      changeDate: new Date()
    });

    // Update membership
    const updatedMembership = await this.membershipRepository.update(currentMembership.id, {
      tierId: newTierId,
      membershipHistory
    });

    return updatedMembership;
  }

  // Other service methods...
}

module.exports = MembershipService;
```

### 7.2 Service Responsibilities

- Implement business rules and logic
- Coordinate operations across multiple repositories
- Manage transactions and ensure data consistency
- Validate business constraints
- Handle business-level errors
- Implement complex business processes

## 8. Repository Layer

The repository layer provides an abstraction over data access:

### 8.1 Repository Structure

```javascript
// Example repository structure
class GiveawayRepository extends BaseRepository {
  constructor(prisma) {
    super(prisma, 'giveaway');
  }

  async findActiveGiveaways(page = 1, limit = 10) {
    const skip = (page - 1) * limit;
    const [giveaways, total] = await Promise.all([
      this.prisma.giveaway.findMany({
        where: {
          status: 'active',
          startDate: { lte: new Date() },
          endDate: { gte: new Date() }
        },
        include: {
          prizes: true
        },
        skip,
        take: limit,
        orderBy: {
          endDate: 'asc'
        }
      }),
      this.prisma.giveaway.count({
        where: {
          status: 'active',
          startDate: { lte: new Date() },
          endDate: { gte: new Date() }
        }
      })
    ]);

    return {
      data: giveaways,
      meta: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    };
  }

  // Other repository methods...
}

module.exports = GiveawayRepository;
```

### 8.2 Repository Responsibilities

- Provide CRUD operations for entities
- Abstract database access details
- Handle database-specific logic
- Implement query filters and pagination
- Manage entity relationships
- Optimize database queries

## 9. Unit of Work Pattern

The application implements the Unit of Work pattern to manage transactions and ensure data consistency:

### 9.1 Unit of Work Structure

```javascript
// Example Unit of Work implementation
class UnitOfWork {
  constructor(prisma) {
    this.prisma = prisma;
    this.userRepository = new UserRepository(prisma);
    this.membershipRepository = new MembershipRepository(prisma);
    this.giveawayRepository = new GiveawayRepository(prisma);
    this.entryRepository = new EntryRepository(prisma);
    this.transactionRepository = new TransactionRepository(prisma);
  }

  async executeTransaction(callback) {
    return this.prisma.$transaction(async (tx) => {
      const txUow = new UnitOfWork(tx);
      return callback(txUow);
    });
  }
}

module.exports = UnitOfWork;
```

### 9.2 Unit of Work Usage

```javascript
// Example usage in a service
async createGiveawayWithPrizes(giveawayData, prizesData) {
  return this.unitOfWork.executeTransaction(async (uow) => {
    // Create giveaway
    const giveaway = await uow.giveawayRepository.create(giveawayData);
    
    // Create prizes
    const prizes = await Promise.all(
      prizesData.map(prizeData => 
        uow.prizeRepository.create({
          ...prizeData,
          giveawayId: giveaway.id
        })
      )
    );
    
    return { giveaway, prizes };
  });
}
```

## 10. Middleware Implementation

The application uses middleware for cross-cutting concerns:

### 10.1 Authentication Middleware

```javascript
// Example authentication middleware
const authMiddleware = (req, res, next) => {
  const authHeader = req.headers.authorization;
  
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return res.status(401).json({
      success: false,
      error: {
        message: 'Authentication required'
      }
    });
  }
  
  const token = authHeader.split(' ')[1];
  
  try {
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    req.user = decoded;
    next();
  } catch (error) {
    return res.status(401).json({
      success: false,
      error: {
        message: 'Invalid or expired token'
      }
    });
  }
};
```

### 10.2 Role-Based Authorization Middleware

```javascript
// Example role-based authorization middleware
const authorize = (roles = []) => {
  if (typeof roles === 'string') {
    roles = [roles];
  }
  
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        error: {
          message: 'Authentication required'
        }
      });
    }
    
    if (roles.length && !roles.includes(req.user.role)) {
      return res.status(403).json({
        success: false,
        error: {
          message: 'Access forbidden'
        }
      });
    }
    
    next();
  };
};
```

## 11. Error Handling

The application implements a centralized error handling approach:

### 11.1 Error Middleware

```javascript
// Example error handling middleware
const errorMiddleware = (err, req, res, next) => {
  const logger = new Logger();
  
  // Log the error
  logger.error(err);
  
  // Default error status and message
  let statusCode = 500;
  let message = 'Internal server error';
  let details = undefined;
  
  // Handle specific error types
  if (err instanceof ValidationError) {
    statusCode = 400;
    message = 'Validation error';
    details = err.details;
  } else if (err instanceof AuthenticationError) {
    statusCode = 401;
    message = err.message;
  } else if (err instanceof AuthorizationError) {
    statusCode = 403;
    message = err.message;
  } else if (err instanceof ResourceNotFoundError) {
    statusCode = 404;
    message = err.message;
  } else if (err instanceof BusinessError) {
    statusCode = 422;
    message = err.message;
  }
  
  // Send error response
  res.status(statusCode).json({
    success: false,
    error: {
      message,
      details,
      code: err.code
    }
  });
};
```

## 12. Dependency Injection

The application uses a simple dependency injection approach to manage component dependencies:

### 12.1 Dependency Injection Container

```javascript
// Example dependency injection container
class Container {
  constructor() {
    this.services = {};
  }
  
  register(name, instance) {
    this.services[name] = instance;
  }
  
  resolve(name) {
    if (!this.services[name]) {
      throw new Error(`Service ${name} not registered`);
    }
    return this.services[name];
  }
}

// Usage
const container = new Container();

// Register services
const prisma = new PrismaClient();
const unitOfWork = new UnitOfWork(prisma);

container.register('prisma', prisma);
container.register('unitOfWork', unitOfWork);
container.register('userRepository', new UserRepository(prisma));
container.register('membershipRepository', new MembershipRepository(prisma));
container.register('userService', new UserService(container.resolve('userRepository')));
container.register('membershipService', new MembershipService(
  container.resolve('membershipRepository'),
  container.resolve('userRepository')
));
container.register('userController', new UserController(container.resolve('userService')));
container.register('membershipController', new MembershipController(container.resolve('membershipService')));
```

## 13. API Documentation

The application uses Swagger/OpenAPI for API documentation:

### 13.1 Swagger Integration

```javascript
// Example Swagger integration
const swaggerJsdoc = require('swagger-jsdoc');
const swaggerUi = require('swagger-ui-express');

const swaggerOptions = {
  definition: {
    openapi: '3.0.0',
    info: {
      title: 'Membership-Based Giveaway Platform API',
      version: '1.0.0',
      description: 'API documentation for the Membership-Based Giveaway Platform'
    },
    servers: [
      {
        url: 'http://localhost:3000/api',
        description: 'Development server'
      }
    ],
    components: {
      securitySchemes: {
        bearerAuth: {
          type: 'http',
          scheme: 'bearer',
          bearerFormat: 'JWT'
        }
      }
    },
    security: [
      {
        bearerAuth: []
      }
    ]
  },
  apis: ['./src/routes/*.js']
};

const swaggerSpec = swaggerJsdoc(swaggerOptions);

app.use('/api-docs', swaggerUi.serve, swaggerUi.setup(swaggerSpec));
```

## 14. Conclusion

This document provides a comprehensive overview of the architecture for the membership-based giveaway platform. The architecture follows a layered approach with clear separation of concerns, making the system modular, maintainable, and scalable.

The key architectural components include:

1. **Controllers**: Handle HTTP requests and responses
2. **Services**: Implement business logic
3. **Repositories**: Provide data access abstraction
4. **Middleware**: Handle cross-cutting concerns
5. **Prisma ORM**: Manage database interactions
6. **PostgreSQL Database**: Store application data

This architecture supports all the functionality outlined in the requirements specification while providing a solid foundation for future enhancements. The modular design allows for independent development and testing of components, as well as easier maintenance and scalability.
