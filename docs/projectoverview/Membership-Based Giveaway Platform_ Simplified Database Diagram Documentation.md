# Membership-Based Giveaway Platform: Simplified Database Diagram Documentation

## 1. Introduction

### 1.1 Purpose
This document provides a detailed database schema design for the membership-based giveaway platform using PostgreSQL. It outlines the tables, relationships, constraints, and indexing strategies to support the application requirements.

### 1.2 Scope
This document covers the complete database structure for the MVP implementation, including all necessary tables to support user management, membership subscriptions, giveaways, entries, payments, and content management.

### 1.3 Database Technology
The database will be implemented using PostgreSQL 15.x with Prisma as the ORM layer.

## 2. Entity Relationship Diagram

```
+----------------+       +-------------------+       +----------------+
|     Users      |       |    Memberships    |       |   Giveaways    |
+----------------+       +-------------------+       +----------------+
| PK id          |<----->| PK id             |       | PK id          |
|    email       |       | FK userId         |       |    title       |
|    password    |       | FK tierID         |       |    description |
|    firstName   |       |    status         |       |    startDate   |
|    lastName    |       |    startDate      |       |    endDate     |
|    phoneNumber |       |    endDate        |       |    drawDate    |
|    birthDate   |       |    renewalDate    |       |    status      |
|    address     |       |    paymentMethod  |       |    featured    |
|    role        |       |    autoRenew      |       |    categories  |
|    status      |       |    membershipHistory|     |    createdAt   |
|    createdAt   |       |    createdAt      |       |    updatedAt   |
|    updatedAt   |       |    updatedAt      |       +----------------+
+----------------+       +-------------------+              ^
        ^                        ^                          |
        |                        |                          |
        |                        v                          |
        |                +-------------------+              |
        |                | MembershipTiers   |              |
        |                +-------------------+              |
        |                | PK id             |              |
        |                |    name           |              |
        |                |    description    |              |
        |                |    price          |              |
        |                |    currency       |              |
        |                |    billingCycle   |              |
        |                |    entryAllocation|              |
        |                |    benefits       |              |
        |                |    isActive       |              |
        |                |    displayOrder   |              |
        |                |    createdAt      |              |
        |                |    updatedAt      |              |
        |                +-------------------+              |
        v                                                   v
+----------------+       +-------------------+       +----------------+
|   Entries      |       |   Transactions    |       |    Prizes      |
+----------------+       +-------------------+       +----------------+
| PK id          |       | PK id             |       | PK id          |
| FK userId      |       | FK userId         |       | FK giveawayId  |
| FK giveawayId  |       |    type           |       |    name        |
| FK membershipId|       |    amount         |       |    description |
|    quantity    |       |    currency       |       |    value       |
|    source      |       |    status         |       |    quantity    |
|    createdAt   |       |    description    |       |    images      |
|    expiresAt   |       |    paymentMethod  |       |    cashAlt     |
+----------------+       |    reference      |       |    cashAltValue|
                         |    createdAt      |       |    createdAt   |
                         +-------------------+       |    updatedAt   |
                                                     +----------------+
```

## 3. Table Definitions

### 3.1 Users Table

**Purpose**: Stores user account information and authentication details.

**Schema**:
```sql
CREATE TABLE "Users" (
    "id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    "email" VARCHAR(255) UNIQUE NOT NULL,
    "password" VARCHAR(255) NOT NULL,
    "firstName" VARCHAR(100),
    "lastName" VARCHAR(100),
    "phoneNumber" VARCHAR(20),
    "birthDate" DATE,
    "address" JSONB,
    "role" VARCHAR(20) NOT NULL DEFAULT 'member',
    "status" VARCHAR(20) NOT NULL DEFAULT 'active',
    "emailVerified" BOOLEAN NOT NULL DEFAULT false,
    "phoneVerified" BOOLEAN NOT NULL DEFAULT false,
    "preferences" JSONB DEFAULT '{}',
    "referredBy" UUID REFERENCES "Users"("id"),
    "referralCode" VARCHAR(20) UNIQUE,
    "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP
);
```

**Indexes**:
- Primary Key: `id`
- Unique Index: `email`
- Unique Index: `referralCode`
- Index: `role`
- Index: `status`
- Index: `referredBy`

**Constraints**:
- Email must be unique
- Role must be one of: 'visitor', 'member', 'admin', 'superadmin'
- Status must be one of: 'active', 'inactive', 'suspended'

### 3.2 MembershipTiers Table

**Purpose**: Stores the master data for membership tiers available in the system.

**Schema**:
```sql
CREATE TABLE "MembershipTiers" (
    "id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    "name" VARCHAR(50) NOT NULL,
    "description" TEXT,
    "price" DECIMAL(10, 2) NOT NULL,
    "currency" VARCHAR(3) NOT NULL DEFAULT 'USD',
    "billingCycle" VARCHAR(20) NOT NULL DEFAULT 'monthly',
    "entryAllocation" JSONB NOT NULL,
    "benefits" JSONB NOT NULL DEFAULT '[]',
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "displayOrder" INTEGER NOT NULL DEFAULT 0,
    "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP
);
```

**Indexes**:
- Primary Key: `id`
- Index: `name`
- Index: `isActive`
- Index: `displayOrder`

**Constraints**:
- Name must be unique
- Price must be greater than 0
- Billing cycle must be one of: 'monthly', 'quarterly', 'annual'

### 3.3 Memberships Table

**Purpose**: Stores user subscription information and membership details, with support for tracking membership history and upgrades.

**Schema**:
```sql
CREATE TABLE "Memberships" (
    "id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    "userId" UUID NOT NULL REFERENCES "Users"("id") ON DELETE CASCADE,
    "tierId" UUID NOT NULL REFERENCES "MembershipTiers"("id"),
    "status" VARCHAR(20) NOT NULL DEFAULT 'active',
    "startDate" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "endDate" TIMESTAMP WITH TIME ZONE,
    "renewalDate" TIMESTAMP WITH TIME ZONE,
    "paymentMethod" JSONB,
    "autoRenew" BOOLEAN NOT NULL DEFAULT true,
    "cancellationReason" TEXT,
    "pauseHistory" JSONB DEFAULT '[]',
    "membershipHistory" JSONB NOT NULL DEFAULT '[]',
    "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT "unique_user_membership" UNIQUE ("userId")
);
```

**Indexes**:
- Primary Key: `id`
- Unique Index: `userId`
- Index: `tierId`
- Index: `status`
- Index: `renewalDate`

**Constraints**:
- Each user can have only one active membership
- Status must be one of: 'active', 'cancelled', 'paused', 'expired'

### 3.4 Giveaways Table

**Purpose**: Stores information about giveaway campaigns.

**Schema**:
```sql
CREATE TABLE "Giveaways" (
    "id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    "title" VARCHAR(255) NOT NULL,
    "description" TEXT,
    "shortDescription" VARCHAR(255),
    "startDate" TIMESTAMP WITH TIME ZONE NOT NULL,
    "endDate" TIMESTAMP WITH TIME ZONE NOT NULL,
    "drawDate" TIMESTAMP WITH TIME ZONE NOT NULL,
    "drawCompleted" BOOLEAN NOT NULL DEFAULT false,
    "status" VARCHAR(20) NOT NULL DEFAULT 'draft',
    "entryAllocation" JSONB NOT NULL,
    "totalEntries" INTEGER NOT NULL DEFAULT 0,
    "featured" BOOLEAN NOT NULL DEFAULT false,
    "categories" VARCHAR(50)[] DEFAULT '{}',
    "tags" VARCHAR(50)[] DEFAULT '{}',
    "termsAndConditions" TEXT,
    "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP
);
```

**Indexes**:
- Primary Key: `id`
- Index: `status`
- Index: `endDate`
- Index: `drawDate`
- Index: `featured`
- Index: `categories` (GIN index for array)

**Constraints**:
- End date must be after start date
- Draw date must be after end date
- Status must be one of: 'draft', 'active', 'completed', 'cancelled'

### 3.5 Prizes Table

**Purpose**: Stores information about prizes associated with giveaways.

**Schema**:
```sql
CREATE TABLE "Prizes" (
    "id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    "giveawayId" UUID NOT NULL REFERENCES "Giveaways"("id") ON DELETE CASCADE,
    "name" VARCHAR(255) NOT NULL,
    "description" TEXT,
    "value" DECIMAL(10, 2) NOT NULL,
    "currency" VARCHAR(3) NOT NULL DEFAULT 'USD',
    "quantity" INTEGER NOT NULL DEFAULT 1,
    "images" TEXT[] DEFAULT '{}',
    "cashAlternative" BOOLEAN NOT NULL DEFAULT false,
    "cashAlternativeValue" DECIMAL(10, 2),
    "specifications" JSONB DEFAULT '{}',
    "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP
);
```

**Indexes**:
- Primary Key: `id`
- Index: `giveawayId`

**Constraints**:
- Value must be greater than 0
- Quantity must be greater than 0
- If cash alternative is true, cash alternative value must be provided

### 3.6 Entries Table

**Purpose**: Tracks user entries for giveaways.

**Schema**:
```sql
CREATE TABLE "Entries" (
    "id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    "userId" UUID NOT NULL REFERENCES "Users"("id") ON DELETE CASCADE,
    "giveawayId" UUID NOT NULL REFERENCES "Giveaways"("id") ON DELETE CASCADE,
    "membershipId" UUID NOT NULL REFERENCES "Memberships"("id") ON DELETE CASCADE,
    "quantity" INTEGER NOT NULL DEFAULT 1,
    "source" VARCHAR(20) NOT NULL DEFAULT 'membership',
    "expiresAt" TIMESTAMP WITH TIME ZONE,
    "metadata" JSONB DEFAULT '{}',
    "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP
);
```

**Indexes**:
- Primary Key: `id`
- Index: `userId`
- Index: `giveawayId`
- Composite Index: `(userId, giveawayId)`
- Index: `source`
- Index: `createdAt`

**Constraints**:
- Quantity must be greater than 0
- Source must be one of: 'membership', 'bonus', 'referral', 'purchase'

### 3.7 Winners Table

**Purpose**: Records winners of giveaways and prize claim status.

**Schema**:
```sql
CREATE TABLE "Winners" (
    "id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    "userId" UUID NOT NULL REFERENCES "Users"("id") ON DELETE CASCADE,
    "giveawayId" UUID NOT NULL REFERENCES "Giveaways"("id") ON DELETE CASCADE,
    "prizeId" UUID NOT NULL REFERENCES "Prizes"("id") ON DELETE CASCADE,
    "selectedPrize" VARCHAR(20) NOT NULL DEFAULT 'physical',
    "claimed" BOOLEAN NOT NULL DEFAULT false,
    "claimedDate" TIMESTAMP WITH TIME ZONE,
    "shippingDetails" JSONB DEFAULT '{}',
    "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT "unique_user_giveaway_prize" UNIQUE ("userId", "giveawayId", "prizeId")
);
```

**Indexes**:
- Primary Key: `id`
- Index: `userId`
- Index: `giveawayId`
- Index: `prizeId`
- Composite Index: `(userId, giveawayId)`

**Constraints**:
- Selected prize must be one of: 'physical', 'cash'
- A user can win a specific prize in a giveaway only once

### 3.8 Transactions Table

**Purpose**: Records payment transactions for subscriptions and other purchases.

**Schema**:
```sql
CREATE TABLE "Transactions" (
    "id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    "userId" UUID NOT NULL REFERENCES "Users"("id") ON DELETE CASCADE,
    "type" VARCHAR(20) NOT NULL,
    "status" VARCHAR(20) NOT NULL DEFAULT 'pending',
    "amount" DECIMAL(10, 2) NOT NULL,
    "currency" VARCHAR(3) NOT NULL DEFAULT 'USD',
    "paymentMethod" JSONB,
    "description" TEXT,
    "reference" JSONB,
    "gateway" JSONB,
    "billingAddress" JSONB,
    "invoice" JSONB,
    "metadata" JSONB DEFAULT '{}',
    "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP
);
```

**Indexes**:
- Primary Key: `id`
- Index: `userId`
- Index: `type`
- Index: `status`
- Index: `createdAt`
- Index: `((reference->>'id')::uuid)` (for finding transactions related to specific entities)

**Constraints**:
- Amount must be greater than 0
- Type must be one of: 'subscription', 'one-time', 'refund'
- Status must be one of: 'pending', 'completed', 'failed', 'refunded'

## 4. Relationships

### 4.1 One-to-One Relationships

- **User to Membership**: Each user can have only one active membership.

### 4.2 One-to-Many Relationships

- **User to Entries**: A user can have multiple entries across different giveaways.
- **User to Winners**: A user can win multiple prizes across different giveaways.
- **User to Transactions**: A user can have multiple payment transactions.
- **User to Notifications**: A user can have multiple notifications.
- **User to Content**: A user (author) can create multiple content items.
- **Giveaway to Prizes**: A giveaway can have multiple prizes.
- **Giveaway to Entries**: A giveaway can have multiple entries from different users.
- **Giveaway to Winners**: A giveaway can have multiple winners.
- **Membership to Entries**: A membership can generate multiple entries.
- **MembershipTier to Memberships**: A membership tier can be associated with multiple user memberships.

### 4.3 Many-to-Many Relationships

- **Users to Giveaways** (through Entries): Users can enter multiple giveaways, and giveaways can have multiple user entries.
- **Users to Prizes** (through Winners): Users can win multiple prizes, and prizes can be won by multiple users (in case of multiple quantities).

## 5. Prisma Schema

The following is the Prisma schema representation of the database design:

```prisma
generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id             String         @id @default(uuid()) @db.Uuid
  email          String         @unique
  password       String
  firstName      String?
  lastName       String?
  phoneNumber    String?
  birthDate      DateTime?      @db.Date
  address        Json?
  role           String         @default("member")
  status         String         @default("active")
  emailVerified  Boolean        @default(false)
  phoneVerified  Boolean        @default(false)
  preferences    Json?          @default("{}")
  referredBy     String?        @db.Uuid
  referralCode   String?        @unique
  createdAt      DateTime       @default(now()) @db.Timestamptz
  updatedAt      DateTime       @default(now()) @db.Timestamptz
  
  // Relations
  referrer       User?          @relation("UserReferrals", fields: [referredBy], references: [id])
  referrals      User[]         @relation("UserReferrals")
  membership     Membership?
  entries        Entry[]
  winners        Winner[]
  transactions   Transaction[]
  notifications  Notification[]
  authoredContent Content[]
  
  @@index([role])
  @@index([status])
  @@index([referredBy])
}

model MembershipTier {
  id                 String       @id @default(uuid()) @db.Uuid
  name               String       @unique
  description        String?
  price              Decimal      @db.Decimal(10, 2)
  currency           String       @default("USD") @db.VarChar(3)
  billingCycle       String       @default("monthly")
  entryAllocation    Json
  benefits           Json         @default("[]")
  isActive           Boolean      @default(true)
  displayOrder       Int          @default(0)
  createdAt          DateTime     @default(now()) @db.Timestamptz
  updatedAt          DateTime     @default(now()) @db.Timestamptz
  
  // Relations
  memberships        Membership[]
  
  @@index([name])
  @@index([isActive])
  @@index([displayOrder])
}

model Membership {
  id                   String         @id @default(uuid()) @db.Uuid
  userId               String         @unique @db.Uuid
  tierId               String         @db.Uuid
  status               String         @default("active")
  startDate            DateTime       @default(now()) @db.Timestamptz
  endDate              DateTime?      @db.Timestamptz
  renewalDate          DateTime?      @db.Timestamptz
  paymentMethod        Json?
  autoRenew            Boolean        @default(true)
  cancellationReason   String?
  pauseHistory         Json?          @default("[]")
  membershipHistory    Json           @default("[]")
  createdAt            DateTime       @default(now()) @db.Timestamptz
  updatedAt            DateTime       @default(now()) @db.Timestamptz
  
  // Relations
  user                 User           @relation(fields: [userId], references: [id], onDelete: Cascade)
  tier                 MembershipTier @relation(fields: [tierId], references: [id])
  entries              Entry[]
  
  @@index([tierId])
  @@index([status])
  @@index([renewalDate])
}

model Giveaway {
  id               String    @id @default(uuid()) @db.Uuid
  title            String
  description      String?
  shortDescription String?
  startDate        DateTime  @db.Timestamptz
  endDate          DateTime  @db.Timestamptz
  drawDate         DateTime  @db.Timestamptz
  drawCompleted    Boolean   @default(false)
  status           String    @default("draft")
  entryAllocation  Json
  totalEntries     Int       @default(0)
  featured         Boolean   @default(false)
  categories       String[]  @default([])
  tags             String[]  @default([])
  termsAndConditions String?
  createdAt        DateTime  @default(now()) @db.Timestamptz
  updatedAt        DateTime  @default(now()) @db.Timestamptz
  
  // Relations
  prizes           Prize[]
  entries          Entry[]
  winners          Winner[]
  
  @@index([status])
  @@index([endDate])
  @@index([drawDate])
  @@index([featured])
  @@index([categories], type: Gin)
}

model Prize {
  id                  String    @id @default(uuid()) @db.Uuid
  giveawayId          String    @db.Uuid
  name                String
  description         String?
  value               Decimal   @db.Decimal(10, 2)
  currency            String    @default("USD") @db.VarChar(3)
  quantity            Int       @default(1)
  images              String[]  @default([])
  cashAlternative     Boolean   @default(false)
  cashAlternativeValue Decimal?  @db.Decimal(10, 2)
  specifications      Json?     @default("{}")
  createdAt           DateTime  @default(now()) @db.Timestamptz
  updatedAt           DateTime  @default(now()) @db.Timestamptz
  
  // Relations
  giveaway            Giveaway  @relation(fields: [giveawayId], references: [id], onDelete: Cascade)
  winners             Winner[]
  
  @@index([giveawayId])
}

model Entry {
  id           String    @id @default(uuid()) @db.Uuid
  userId       String    @db.Uuid
  giveawayId   String    @db.Uuid
  membershipId String    @db.Uuid
  quantity     Int       @default(1)
  source       String    @default("membership")
  expiresAt    DateTime? @db.Timestamptz
  metadata     Json?     @default("{}")
  createdAt    DateTime  @default(now()) @db.Timestamptz
  
  // Relations
  user         User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  giveaway     Giveaway  @relation(fields: [giveawayId], references: [id], onDelete: Cascade)
  membership   Membership @relation(fields: [membershipId], references: [id], onDelete: Cascade)
  
  @@index([userId])
  @@index([giveawayId])
  @@index([userId, giveawayId])
  @@index([source])
  @@index([createdAt])
}

model Winner {
  id              String    @id @default(uuid()) @db.Uuid
  userId          String    @db.Uuid
  giveawayId      String    @db.Uuid
  prizeId         String    @db.Uuid
  selectedPrize   String    @default("physical")
  claimed         Boolean   @default(false)
  claimedDate     DateTime? @db.Timestamptz
  shippingDetails Json?     @default("{}")
  createdAt       DateTime  @default(now()) @db.Timestamptz
  updatedAt       DateTime  @default(now()) @db.Timestamptz
  
  // Relations
  user            User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  giveaway        Giveaway  @relation(fields: [giveawayId], references: [id], onDelete: Cascade)
  prize           Prize     @relation(fields: [prizeId], references: [id], onDelete: Cascade)
  
  @@unique([userId, giveawayId, prizeId])
  @@index([userId])
  @@index([giveawayId])
  @@index([prizeId])
  @@index([userId, giveawayId])
}

model Transaction {
  id             String    @id @default(uuid()) @db.Uuid
  userId         String    @db.Uuid
  type           String
  status         String    @default("pending")
  amount         Decimal   @db.Decimal(10, 2)
  currency       String    @default("USD") @db.VarChar(3)
  paymentMethod  Json?
  description    String?
  reference      Json?
  gateway        Json?
  billingAddress Json?
  invoice        Json?
  metadata       Json?     @default("{}")
  createdAt      DateTime  @default(now()) @db.Timestamptz
  updatedAt      DateTime  @default(now()) @db.Timestamptz
  
  // Relations
  user           User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  @@index([userId])
  @@index([type])
  @@index([status])
  @@index([createdAt])
}

model Content {
  id             String    @id @default(uuid()) @db.Uuid
  title          String
  slug           String    @unique
  content        String?
  summary        String?
  authorId       String?   @db.Uuid
  featuredImage  String?
  gallery        String[]  @default([])
  publishDate    DateTime? @db.Timestamptz
  status         String    @default("draft")
  categories     String[]  @default([])
  tags           String[]  @default([])
  accessLevel    String    @default("public")
  relatedContent String[]  @db.Uuid @default([])
  metadata       Json?     @default("{}")
  seo            Json?     @default("{}")
  createdAt      DateTime  @default(now()) @db.Timestamptz
  updatedAt      DateTime  @default(now()) @db.Timestamptz
  
  // Relations
  author         User?     @relation(fields: [authorId], references: [id])
  
  @@index([authorId])
  @@index([publishDate])
  @@index([status])
  @@index([accessLevel])
  @@index([categories], type: Gin)
  @@index([tags], type: Gin)
}

model Notification {
  id        String    @id @default(uuid()) @db.Uuid
  userId    String    @db.Uuid
  type      String
  title     String
  content   String
  status    String    @default("unread")
  readAt    DateTime? @db.Timestamptz
  reference Json?
  createdAt DateTime  @default(now()) @db.Timestamptz
  
  // Relations
  user      User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  @@index([userId])
  @@index([type])
  @@index([status])
  @@index([createdAt])
}

model Setting {
  key         String    @id
  value       Json
  description String?
  category    String
  isPublic    Boolean   @default(false)
  createdAt   DateTime  @default(now()) @db.Timestamptz
  updatedAt   DateTime  @default(now()) @db.Timestamptz
  
  @@index([category])
  @@index([isPublic])
}
```

## 6. Membership History Tracking

### 6.1 Purpose

The membership history tracking feature enables:

1. **Membership Upgrades**: Track when users upgrade from one tier to another
2. **Membership Changes**: Record changes in membership status, pricing, or benefits
3. **Audit Trail**: Maintain a complete history of a user's membership journey
4. **Reporting**: Enable analytics on membership changes and upgrade patterns

### 6.2 Implementation Details

Membership history is implemented through:

1. **membershipHistory JSONB field in Memberships table**:
   - Stores an array of historical membership records
   - Each record contains previous tier information, dates, and reason for change

2. **Structure of membershipHistory records**:
```json
[
  {
    "tierId": "uuid-of-previous-tier",
    "tierName": "Previous Tier Name",
    "startDate": "2025-01-01T00:00:00Z",
    "endDate": "2025-04-01T00:00:00Z",
    "price": 9.99,
    "currency": "USD",
    "changeReason": "upgrade",
    "changeDate": "2025-04-01T00:00:00Z"
  },
  {
    "tierId": "uuid-of-another-tier",
    "tierName": "Another Tier Name",
    "startDate": "2024-10-01T00:00:00Z",
    "endDate": "2025-01-01T00:00:00Z",
    "price": 4.99,
    "currency": "USD",
    "changeReason": "upgrade",
    "changeDate": "2025-01-01T00:00:00Z"
  }
]
```

### 6.3 Use Cases

1. **Membership Upgrade**:
   - When a user upgrades from a basic to premium tier
   - Current membership details are added to membershipHistory
   - New tier information is set as the current membership

2. **Membership Downgrade**:
   - When a user moves to a lower tier
   - Current membership details are added to membershipHistory
   - New tier information is set as the current membership

3. **Membership Renewal with Price Change**:
   - When a membership renews but the price has changed
   - Current membership details are added to membershipHistory
   - Updated price information is set for the current membership

4. **Membership Cancellation and Reactivation**:
   - When a user cancels and later reactivates their membership
   - Complete history is maintained for reporting and analysis

## 7. Database Migration and Versioning

For database migrations and versioning, we will use Prisma Migrate to manage schema changes:

```bash
# Generate migration files
npx prisma migrate dev --name init

# Apply migrations to production
npx prisma migrate deploy
```

## 8. Performance Considerations

1. **Indexing Strategy**:
   - All foreign keys are indexed
   - Frequently queried fields have dedicated indexes
   - Composite indexes for common query patterns
   - GIN indexes for array fields

2. **Query Optimization**:
   - Use of JSONB for flexible data that doesn't require frequent filtering
   - Denormalization where appropriate for performance
   - Careful use of array fields with proper indexing

3. **Scaling Considerations**:
   - Partitioning strategy for large tables (e.g., Entries, Transactions)
   - Read replicas for reporting and analytics
   - Connection pooling configuration

## 9. Security Considerations

1. **Data Protection**:
   - Passwords stored with bcrypt hashing
   - Sensitive data in JSONB fields can be encrypted at the application level
   - Row-level security policies for multi-tenant isolation

2. **Access Control**:
   - Database roles with least privilege principles
   - Application-level access control through the role system
   - Audit logging for sensitive operations

## 10. Conclusion

This simplified database design provides a comprehensive structure for the membership-based giveaway platform, with special attention to membership history tracking and upgrades. The design supports all the required functionality while maintaining flexibility for future enhancements and scalability for growth.
