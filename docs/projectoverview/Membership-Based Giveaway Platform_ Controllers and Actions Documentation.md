# Membership-Based Giveaway Platform: Controllers and Actions Documentation

## 1. Introduction

### 1.1 Purpose
This document outlines the controllers and their actions for the membership-based giveaway platform, including role-based access control for each action.

### 1.2 Scope
This document covers all controllers required for the MVP implementation, detailing their actions, request/response formats, and access control rules.

## 2. Role-Based Access Control Overview

### 2.1 User Roles
The platform implements the following user roles with increasing levels of access:

1. **Visitor**: Unauthenticated user with limited access to public information
2. **Member**: Authenticated user with an active membership subscription
3. **Admin**: Staff with management privileges for platform content and operations
4. **SuperAdmin**: User with full system access and configuration capabilities

### 2.2 Access Control Matrix

| Feature Area        | Visitor | Member | Admin | SuperAdmin |
|---------------------|---------|--------|-------|------------|
| Public Content      | ✓       | ✓      | ✓     | ✓          |
| Member Content      | ✗       | ✓      | ✓     | ✓          |
| Account Management  | Limited | ✓      | ✓     | ✓          |
| Giveaway Entry      | ✗       | ✓      | ✓     | ✓          |
| Content Management  | ✗       | ✗      | ✓     | ✓          |
| User Management     | ✗       | ✗      | ✓     | ✓          |
| System Configuration| ✗       | ✗      | ✗     | ✓          |

## 3. Authentication Controller

**Purpose**: Handles user authentication and security operations.

### 3.1 Register Action

**Endpoint**: `POST /api/auth/register`

**Description**: Registers a new user account.

**Request Body**:
```json
{
  "email": "string",
  "password": "string",
  "firstName": "string",
  "lastName": "string",
  "phoneNumber": "string (optional)"
}
```

**Response**:
```json
{
  "success": true,
  "data": {
    "id": "uuid",
    "email": "string",
    "firstName": "string",
    "lastName": "string",
    "role": "member",
    "createdAt": "timestamp"
  }
}
```

**Access Control**:
- Visitor: ✓
- Member: ✓
- Admin: ✓
- SuperAdmin: ✓

### 3.2 Login Action

**Endpoint**: `POST /api/auth/login`

**Description**: Authenticates a user and returns a JWT token.

**Request Body**:
```json
{
  "email": "string",
  "password": "string",
  "rememberMe": "boolean (optional)"
}
```

**Response**:
```json
{
  "success": true,
  "data": {
    "token": "string",
    "user": {
      "id": "uuid",
      "email": "string",
      "firstName": "string",
      "lastName": "string",
      "role": "string"
    }
  }
}
```

**Access Control**:
- Visitor: ✓
- Member: ✓
- Admin: ✓
- SuperAdmin: ✓

### 3.3 Verify Email Action

**Endpoint**: `GET /api/auth/verify-email/:token`

**Description**: Verifies a user's email address using a token.

**Response**:
```json
{
  "success": true,
  "message": "Email verified successfully"
}
```

**Access Control**:
- Visitor: ✓
- Member: ✓
- Admin: ✓
- SuperAdmin: ✓

### 3.4 Forgot Password Action

**Endpoint**: `POST /api/auth/forgot-password`

**Description**: Initiates the password reset process.

**Request Body**:
```json
{
  "email": "string"
}
```

**Response**:
```json
{
  "success": true,
  "message": "Password reset instructions sent to email"
}
```

**Access Control**:
- Visitor: ✓
- Member: ✓
- Admin: ✓
- SuperAdmin: ✓

### 3.5 Reset Password Action

**Endpoint**: `POST /api/auth/reset-password`

**Description**: Resets a user's password using a token.

**Request Body**:
```json
{
  "token": "string",
  "password": "string",
  "confirmPassword": "string"
}
```

**Response**:
```json
{
  "success": true,
  "message": "Password reset successfully"
}
```

**Access Control**:
- Visitor: ✓
- Member: ✓
- Admin: ✓
- SuperAdmin: ✓

### 3.6 Refresh Token Action

**Endpoint**: `POST /api/auth/refresh-token`

**Description**: Refreshes an expired JWT token.

**Request Body**:
```json
{
  "refreshToken": "string"
}
```

**Response**:
```json
{
  "success": true,
  "data": {
    "token": "string"
  }
}
```

**Access Control**:
- Member: ✓
- Admin: ✓
- SuperAdmin: ✓

### 3.7 Logout Action

**Endpoint**: `POST /api/auth/logout`

**Description**: Invalidates the current user's token.

**Response**:
```json
{
  "success": true,
  "message": "Logged out successfully"
}
```

**Access Control**:
- Member: ✓
- Admin: ✓
- SuperAdmin: ✓

## 4. User Controller

**Purpose**: Manages user profiles and account settings.

### 4.1 Get Profile Action

**Endpoint**: `GET /api/users/profile`

**Description**: Retrieves the current user's profile.

**Response**:
```json
{
  "success": true,
  "data": {
    "id": "uuid",
    "email": "string",
    "firstName": "string",
    "lastName": "string",
    "phoneNumber": "string",
    "birthDate": "date",
    "address": "object",
    "role": "string",
    "status": "string",
    "emailVerified": "boolean",
    "phoneVerified": "boolean",
    "preferences": "object",
    "createdAt": "timestamp"
  }
}
```

**Access Control**:
- Member: ✓
- Admin: ✓
- SuperAdmin: ✓

### 4.2 Update Profile Action

**Endpoint**: `PUT /api/users/profile`

**Description**: Updates the current user's profile information.

**Request Body**:
```json
{
  "firstName": "string",
  "lastName": "string",
  "phoneNumber": "string",
  "birthDate": "date",
  "address": "object"
}
```

**Response**:
```json
{
  "success": true,
  "data": {
    "id": "uuid",
    "firstName": "string",
    "lastName": "string",
    "phoneNumber": "string",
    "birthDate": "date",
    "address": "object",
    "updatedAt": "timestamp"
  }
}
```

**Access Control**:
- Member: ✓
- Admin: ✓
- SuperAdmin: ✓

### 4.3 Change Password Action

**Endpoint**: `PUT /api/users/change-password`

**Description**: Changes the current user's password.

**Request Body**:
```json
{
  "currentPassword": "string",
  "newPassword": "string",
  "confirmPassword": "string"
}
```

**Response**:
```json
{
  "success": true,
  "message": "Password changed successfully"
}
```

**Access Control**:
- Member: ✓
- Admin: ✓
- SuperAdmin: ✓

### 4.4 Update Preferences Action

**Endpoint**: `PUT /api/users/preferences`

**Description**: Updates the current user's preferences.

**Request Body**:
```json
{
  "emailNotifications": "boolean",
  "marketingEmails": "boolean",
  "theme": "string",
  "language": "string"
}
```

**Response**:
```json
{
  "success": true,
  "data": {
    "preferences": "object",
    "updatedAt": "timestamp"
  }
}
```

**Access Control**:
- Member: ✓
- Admin: ✓
- SuperAdmin: ✓

### 4.5 Get User Entries Action

**Endpoint**: `GET /api/users/entries`

**Description**: Retrieves the current user's giveaway entries.

**Query Parameters**:
- `page`: Page number (default: 1)
- `limit`: Items per page (default: 10)
- `status`: Entry status filter (optional)

**Response**:
```json
{
  "success": true,
  "data": [
    {
      "id": "uuid",
      "giveawayId": "uuid",
      "giveawayTitle": "string",
      "quantity": "number",
      "source": "string",
      "createdAt": "timestamp"
    }
  ],
  "meta": {
    "pagination": {
      "page": "number",
      "limit": "number",
      "total": "number",
      "pages": "number"
    }
  }
}
```

**Access Control**:
- Member: ✓
- Admin: ✓
- SuperAdmin: ✓

### 4.6 Get User Wins Action

**Endpoint**: `GET /api/users/wins`

**Description**: Retrieves the current user's giveaway wins.

**Query Parameters**:
- `page`: Page number (default: 1)
- `limit`: Items per page (default: 10)
- `claimed`: Filter by claimed status (optional)

**Response**:
```json
{
  "success": true,
  "data": [
    {
      "id": "uuid",
      "giveawayId": "uuid",
      "giveawayTitle": "string",
      "prizeId": "uuid",
      "prizeName": "string",
      "prizeValue": "number",
      "claimed": "boolean",
      "claimedDate": "timestamp",
      "createdAt": "timestamp"
    }
  ],
  "meta": {
    "pagination": {
      "page": "number",
      "limit": "number",
      "total": "number",
      "pages": "number"
    }
  }
}
```

**Access Control**:
- Member: ✓
- Admin: ✓
- SuperAdmin: ✓

## 5. Membership Controller

**Purpose**: Manages membership subscriptions and tiers.

### 5.1 Get Membership Tiers Action

**Endpoint**: `GET /api/memberships/tiers`

**Description**: Retrieves all available membership tiers.

**Response**:
```json
{
  "success": true,
  "data": [
    {
      "id": "uuid",
      "name": "string",
      "description": "string",
      "price": "number",
      "currency": "string",
      "billingCycle": "string",
      "entryAllocation": "object",
      "benefits": "array",
      "isActive": "boolean",
      "displayOrder": "number"
    }
  ]
}
```

**Access Control**:
- Visitor: ✓
- Member: ✓
- Admin: ✓
- SuperAdmin: ✓

### 5.2 Get Current Membership Action

**Endpoint**: `GET /api/memberships/current`

**Description**: Retrieves the current user's membership details.

**Response**:
```json
{
  "success": true,
  "data": {
    "id": "uuid",
    "tier": {
      "id": "uuid",
      "name": "string",
      "description": "string",
      "price": "number",
      "currency": "string",
      "billingCycle": "string",
      "entryAllocation": "object",
      "benefits": "array"
    },
    "status": "string",
    "startDate": "timestamp",
    "endDate": "timestamp",
    "renewalDate": "timestamp",
    "autoRenew": "boolean",
    "membershipHistory": "array"
  }
}
```

**Access Control**:
- Member: ✓
- Admin: ✓
- SuperAdmin: ✓

### 5.3 Subscribe Action

**Endpoint**: `POST /api/memberships/subscribe`

**Description**: Subscribes the current user to a membership tier.

**Request Body**:
```json
{
  "tierId": "uuid",
  "paymentMethod": "object",
  "autoRenew": "boolean"
}
```

**Response**:
```json
{
  "success": true,
  "data": {
    "id": "uuid",
    "tierId": "uuid",
    "status": "active",
    "startDate": "timestamp",
    "endDate": "timestamp",
    "renewalDate": "timestamp",
    "autoRenew": "boolean",
    "transactionId": "uuid"
  }
}
```

**Access Control**:
- Visitor: ✓ (with authentication)
- Member: ✓
- Admin: ✓
- SuperAdmin: ✓

### 5.4 Cancel Membership Action

**Endpoint**: `POST /api/memberships/cancel`

**Description**: Cancels the current user's membership subscription.

**Request Body**:
```json
{
  "cancellationReason": "string (optional)"
}
```

**Response**:
```json
{
  "success": true,
  "data": {
    "id": "uuid",
    "status": "cancelled",
    "endDate": "timestamp"
  }
}
```

**Access Control**:
- Member: ✓
- Admin: ✓
- SuperAdmin: ✓

### 5.5 Update Auto-Renew Action

**Endpoint**: `PUT /api/memberships/auto-renew`

**Description**: Updates the auto-renew setting for the current user's membership.

**Request Body**:
```json
{
  "autoRenew": "boolean"
}
```

**Response**:
```json
{
  "success": true,
  "data": {
    "id": "uuid",
    "autoRenew": "boolean",
    "updatedAt": "timestamp"
  }
}
```

**Access Control**:
- Member: ✓
- Admin: ✓
- SuperAdmin: ✓

### 5.6 Upgrade Membership Action

**Endpoint**: `POST /api/memberships/upgrade`

**Description**: Upgrades the current user's membership to a higher tier.

**Request Body**:
```json
{
  "tierId": "uuid",
  "paymentMethod": "object (optional)"
}
```

**Response**:
```json
{
  "success": true,
  "data": {
    "id": "uuid",
    "tierId": "uuid",
    "status": "active",
    "startDate": "timestamp",
    "endDate": "timestamp",
    "renewalDate": "timestamp",
    "transactionId": "uuid",
    "membershipHistory": "array"
  }
}
```

**Access Control**:
- Member: ✓
- Admin: ✓
- SuperAdmin: ✓

### 5.7 Get Membership History Action

**Endpoint**: `GET /api/memberships/history`

**Description**: Retrieves the current user's membership history.

**Response**:
```json
{
  "success": true,
  "data": {
    "currentMembership": {
      "id": "uuid",
      "tierId": "uuid",
      "tierName": "string",
      "startDate": "timestamp",
      "endDate": "timestamp",
      "price": "number",
      "currency": "string"
    },
    "history": [
      {
        "tierId": "uuid",
        "tierName": "string",
        "startDate": "timestamp",
        "endDate": "timestamp",
        "price": "number",
        "currency": "string",
        "changeReason": "string",
        "changeDate": "timestamp"
      }
    ]
  }
}
```

**Access Control**:
- Member: ✓
- Admin: ✓
- SuperAdmin: ✓

## 6. MembershipTier Controller

**Purpose**: Manages membership tier definitions.

### 6.1 Get All Tiers Action

**Endpoint**: `GET /api/membership-tiers`

**Description**: Retrieves all membership tiers, including inactive ones for admins.

**Query Parameters**:
- `includeInactive`: Include inactive tiers (admin only)

**Response**:
```json
{
  "success": true,
  "data": [
    {
      "id": "uuid",
      "name": "string",
      "description": "string",
      "price": "number",
      "currency": "string",
      "billingCycle": "string",
      "entryAllocation": "object",
      "benefits": "array",
      "isActive": "boolean",
      "displayOrder": "number",
      "createdAt": "timestamp",
      "updatedAt": "timestamp"
    }
  ]
}
```

**Access Control**:
- Visitor: ✓ (active tiers only)
- Member: ✓ (active tiers only)
- Admin: ✓ (all tiers)
- SuperAdmin: ✓ (all tiers)

### 6.2 Get Tier By ID Action

**Endpoint**: `GET /api/membership-tiers/:id`

**Description**: Retrieves a specific membership tier by ID.

**Response**:
```json
{
  "success": true,
  "data": {
    "id": "uuid",
    "name": "string",
    "description": "string",
    "price": "number",
    "currency": "string",
    "billingCycle": "string",
    "entryAllocation": "object",
    "benefits": "array",
    "isActive": "boolean",
    "displayOrder": "number",
    "createdAt": "timestamp",
    "updatedAt": "timestamp"
  }
}
```

**Access Control**:
- Visitor: ✓ (active tiers only)
- Member: ✓ (active tiers only)
- Admin: ✓ (all tiers)
- SuperAdmin: ✓ (all tiers)

### 6.3 Create Tier Action

**Endpoint**: `POST /api/membership-tiers`

**Description**: Creates a new membership tier.

**Request Body**:
```json
{
  "name": "string",
  "description": "string",
  "price": "number",
  "currency": "string",
  "billingCycle": "string",
  "entryAllocation": "object",
  "benefits": "array",
  "isActive": "boolean",
  "displayOrder": "number"
}
```

**Response**:
```json
{
  "success": true,
  "data": {
    "id": "uuid",
    "name": "string",
    "description": "string",
    "price": "number",
    "currency": "string",
    "billingCycle": "string",
    "entryAllocation": "object",
    "benefits": "array",
    "isActive": "boolean",
    "displayOrder": "number",
    "createdAt": "timestamp",
    "updatedAt": "timestamp"
  }
}
```

**Access Control**:
- Admin: ✓
- SuperAdmin: ✓

### 6.4 Update Tier Action

**Endpoint**: `PUT /api/membership-tiers/:id`

**Description**: Updates an existing membership tier.

**Request Body**:
```json
{
  "name": "string",
  "description": "string",
  "price": "number",
  "currency": "string",
  "billingCycle": "string",
  "entryAllocation": "object",
  "benefits": "array",
  "isActive": "boolean",
  "displayOrder": "number"
}
```

**Response**:
```json
{
  "success": true,
  "data": {
    "id": "uuid",
    "name": "string",
    "description": "string",
    "price": "number",
    "currency": "string",
    "billingCycle": "string",
    "entryAllocation": "object",
    "benefits": "array",
    "isActive": "boolean",
    "displayOrder": "number",
    "updatedAt": "timestamp"
  }
}
```

**Access Control**:
- Admin: ✓
- SuperAdmin: ✓

### 6.5 Delete Tier Action

**Endpoint**: `DELETE /api/membership-tiers/:id`

**Description**: Deletes a membership tier (soft delete).

**Response**:
```json
{
  "success": true,
  "message": "Membership tier deleted successfully"
}
```

**Access Control**:
- SuperAdmin: ✓

## 7. Giveaway Controller

**Purpose**: Manages giveaway campaigns and entries.

### 7.1 Get All Giveaways Action

**Endpoint**: `GET /api/giveaways`

**Description**: Retrieves all active giveaways.

**Query Parameters**:
- `page`: Page number (default: 1)
- `limit`: Items per page (default: 10)
- `status`: Filter by status (optional)
- `featured`: Filter by featured status (optional)
- `category`: Filter by category (optional)

**Response**:
```json
{
  "success": true,
  "data": [
    {
      "id": "uuid",
      "title": "string",
      "description": "string",
      "shortDescription": "string",
      "startDate": "timestamp",
      "endDate": "timestamp",
      "drawDate": "timestamp",
      "status": "string",
      "featured": "boolean",
      "categories": "array",
      "prizes": [
        {
          "id": "uuid",
          "name": "string",
          "value": "number",
          "images": "array"
        }
      ],
      "entryAllocation": "object"
    }
  ],
  "meta": {
    "pagination": {
      "page": "number",
      "limit": "number",
      "total": "number",
      "pages": "number"
    }
  }
}
```

**Access Control**:
- Visitor: ✓ (limited details)
- Member: ✓
- Admin: ✓
- SuperAdmin: ✓

### 7.2 Get Giveaway By ID Action

**Endpoint**: `GET /api/giveaways/:id`

**Description**: Retrieves a specific giveaway by ID.

**Response**:
```json
{
  "success": true,
  "data": {
    "id": "uuid",
    "title": "string",
    "description": "string",
    "shortDescription": "string",
    "startDate": "timestamp",
    "endDate": "timestamp",
    "drawDate": "timestamp",
    "status": "string",
    "featured": "boolean",
    "categories": "array",
    "tags": "array",
    "prizes": [
      {
        "id": "uuid",
        "name": "string",
        "description": "string",
        "value": "number",
        "currency": "string",
        "quantity": "number",
        "images": "array",
        "cashAlternative": "boolean",
        "cashAlternativeValue": "number"
      }
    ],
    "entryAllocation": "object",
    "termsAndConditions": "string",
    "totalEntries": "number",
    "userEntries": "number (for authenticated users)",
    "createdAt": "timestamp",
    "updatedAt": "timestamp"
  }
}
```

**Access Control**:
- Visitor: ✓ (limited details)
- Member: ✓
- Admin: ✓
- SuperAdmin: ✓

### 7.3 Create Giveaway Action

**Endpoint**: `POST /api/giveaways`

**Description**: Creates a new giveaway.

**Request Body**:
```json
{
  "title": "string",
  "description": "string",
  "shortDescription": "string",
  "startDate": "timestamp",
  "endDate": "timestamp",
  "drawDate": "timestamp",
  "status": "string",
  "featured": "boolean",
  "categories": "array",
  "tags": "array",
  "entryAllocation": "object",
  "termsAndConditions": "string",
  "prizes": [
    {
      "name": "string",
      "description": "string",
      "value": "number",
      "currency": "string",
      "quantity": "number",
      "images": "array",
      "cashAlternative": "boolean",
      "cashAlternativeValue": "number"
    }
  ]
}
```

**Response**:
```json
{
  "success": true,
  "data": {
    "id": "uuid",
    "title": "string",
    "status": "string",
    "createdAt": "timestamp"
  }
}
```

**Access Control**:
- Admin: ✓
- SuperAdmin: ✓

### 7.4 Update Giveaway Action

**Endpoint**: `PUT /api/giveaways/:id`

**Description**: Updates an existing giveaway.

**Request Body**:
```json
{
  "title": "string",
  "description": "string",
  "shortDescription": "string",
  "startDate": "timestamp",
  "endDate": "timestamp",
  "drawDate": "timestamp",
  "status": "string",
  "featured": "boolean",
  "categories": "array",
  "tags": "array",
  "entryAllocation": "object",
  "termsAndConditions": "string"
}
```

**Response**:
```json
{
  "success": true,
  "data": {
    "id": "uuid",
    "title": "string",
    "status": "string",
    "updatedAt": "timestamp"
  }
}
```

**Access Control**:
- Admin: ✓
- SuperAdmin: ✓

### 7.5 Delete Giveaway Action

**Endpoint**: `DELETE /api/giveaways/:id`

**Description**: Deletes a giveaway (soft delete).

**Response**:
```json
{
  "success": true,
  "message": "Giveaway deleted successfully"
}
```

**Access Control**:
- Admin: ✓
- SuperAdmin: ✓

### 7.6 Enter Giveaway Action

**Endpoint**: `POST /api/giveaways/:id/entries`

**Description**: Creates entries for the current user in a giveaway.

**Request Body**:
```json
{
  "quantity": "number (optional, defaults to membership allocation)"
}
```

**Response**:
```json
{
  "success": true,
  "data": {
    "entries": "number",
    "totalEntries": "number",
    "giveawayId": "uuid"
  }
}
```

**Access Control**:
- Member: ✓
- Admin: ✓
- SuperAdmin: ✓

### 7.7 Get Giveaway Entries Action

**Endpoint**: `GET /api/giveaways/:id/entries`

**Description**: Retrieves entries for a specific giveaway (admin only).

**Query Parameters**:
- `page`: Page number (default: 1)
- `limit`: Items per page (default: 10)
- `userId`: Filter by user ID (optional)

**Response**:
```json
{
  "success": true,
  "data": [
    {
      "id": "uuid",
      "userId": "uuid",
      "user": {
        "id": "uuid",
        "email": "string",
        "firstName": "string",
        "lastName": "string"
      },
      "quantity": "number",
      "source": "string",
      "createdAt": "timestamp"
    }
  ],
  "meta": {
    "pagination": {
      "page": "number",
      "limit": "number",
      "total": "number",
      "pages": "number"
    }
  }
}
```

**Access Control**:
- Admin: ✓
- SuperAdmin: ✓

### 7.8 Conduct Draw Action

**Endpoint**: `POST /api/giveaways/:id/draw`

**Description**: Conducts the draw for a giveaway and selects winners.

**Response**:
```json
{
  "success": true,
  "data": {
    "giveawayId": "uuid",
    "drawCompleted": true,
    "winners": [
      {
        "userId": "uuid",
        "user": {
          "email": "string",
          "firstName": "string",
          "lastName": "string"
        },
        "prizeId": "uuid",
        "prize": {
          "name": "string",
          "value": "number"
        }
      }
    ]
  }
}
```

**Access Control**:
- Admin: ✓
- SuperAdmin: ✓

## 8. Prize Controller

**Purpose**: Manages prizes associated with giveaways.

### 8.1 Get All Prizes Action

**Endpoint**: `GET /api/giveaways/:giveawayId/prizes`

**Description**: Retrieves all prizes for a specific giveaway.

**Response**:
```json
{
  "success": true,
  "data": [
    {
      "id": "uuid",
      "name": "string",
      "description": "string",
      "value": "number",
      "currency": "string",
      "quantity": "number",
      "images": "array",
      "cashAlternative": "boolean",
      "cashAlternativeValue": "number",
      "createdAt": "timestamp",
      "updatedAt": "timestamp"
    }
  ]
}
```

**Access Control**:
- Visitor: ✓
- Member: ✓
- Admin: ✓
- SuperAdmin: ✓

### 8.2 Get Prize By ID Action

**Endpoint**: `GET /api/prizes/:id`

**Description**: Retrieves a specific prize by ID.

**Response**:
```json
{
  "success": true,
  "data": {
    "id": "uuid",
    "giveawayId": "uuid",
    "name": "string",
    "description": "string",
    "value": "number",
    "currency": "string",
    "quantity": "number",
    "images": "array",
    "cashAlternative": "boolean",
    "cashAlternativeValue": "number",
    "specifications": "object",
    "createdAt": "timestamp",
    "updatedAt": "timestamp"
  }
}
```

**Access Control**:
- Visitor: ✓
- Member: ✓
- Admin: ✓
- SuperAdmin: ✓

### 8.3 Create Prize Action

**Endpoint**: `POST /api/giveaways/:giveawayId/prizes`

**Description**: Creates a new prize for a giveaway.

**Request Body**:
```json
{
  "name": "string",
  "description": "string",
  "value": "number",
  "currency": "string",
  "quantity": "number",
  "images": "array",
  "cashAlternative": "boolean",
  "cashAlternativeValue": "number",
  "specifications": "object"
}
```

**Response**:
```json
{
  "success": true,
  "data": {
    "id": "uuid",
    "giveawayId": "uuid",
    "name": "string",
    "value": "number",
    "createdAt": "timestamp"
  }
}
```

**Access Control**:
- Admin: ✓
- SuperAdmin: ✓

### 8.4 Update Prize Action

**Endpoint**: `PUT /api/prizes/:id`

**Description**: Updates an existing prize.

**Request Body**:
```json
{
  "name": "string",
  "description": "string",
  "value": "number",
  "currency": "string",
  "quantity": "number",
  "images": "array",
  "cashAlternative": "boolean",
  "cashAlternativeValue": "number",
  "specifications": "object"
}
```

**Response**:
```json
{
  "success": true,
  "data": {
    "id": "uuid",
    "name": "string",
    "value": "number",
    "updatedAt": "timestamp"
  }
}
```

**Access Control**:
- Admin: ✓
- SuperAdmin: ✓

### 8.5 Delete Prize Action

**Endpoint**: `DELETE /api/prizes/:id`

**Description**: Deletes a prize.

**Response**:
```json
{
  "success": true,
  "message": "Prize deleted successfully"
}
```

**Access Control**:
- Admin: ✓
- SuperAdmin: ✓

## 9. Winner Controller

**Purpose**: Manages giveaway winners and prize claiming.

### 9.1 Get All Winners Action

**Endpoint**: `GET /api/giveaways/:giveawayId/winners`

**Description**: Retrieves all winners for a specific giveaway.

**Query Parameters**:
- `page`: Page number (default: 1)
- `limit`: Items per page (default: 10)
- `claimed`: Filter by claimed status (optional)

**Response**:
```json
{
  "success": true,
  "data": [
    {
      "id": "uuid",
      "userId": "uuid",
      "user": {
        "firstName": "string",
        "lastName": "string"
      },
      "prizeId": "uuid",
      "prize": {
        "name": "string",
        "value": "number"
      },
      "selectedPrize": "string",
      "claimed": "boolean",
      "claimedDate": "timestamp",
      "createdAt": "timestamp"
    }
  ],
  "meta": {
    "pagination": {
      "page": "number",
      "limit": "number",
      "total": "number",
      "pages": "number"
    }
  }
}
```

**Access Control**:
- Visitor: ✓ (limited details)
- Member: ✓
- Admin: ✓
- SuperAdmin: ✓

### 9.2 Get Winner By ID Action

**Endpoint**: `GET /api/winners/:id`

**Description**: Retrieves a specific winner by ID.

**Response**:
```json
{
  "success": true,
  "data": {
    "id": "uuid",
    "userId": "uuid",
    "user": {
      "firstName": "string",
      "lastName": "string",
      "email": "string (admin only)"
    },
    "giveawayId": "uuid",
    "giveaway": {
      "title": "string"
    },
    "prizeId": "uuid",
    "prize": {
      "name": "string",
      "description": "string",
      "value": "number",
      "images": "array"
    },
    "selectedPrize": "string",
    "claimed": "boolean",
    "claimedDate": "timestamp",
    "shippingDetails": "object (admin only)",
    "createdAt": "timestamp",
    "updatedAt": "timestamp"
  }
}
```

**Access Control**:
- Member: ✓ (own wins only)
- Admin: ✓
- SuperAdmin: ✓

### 9.3 Claim Prize Action

**Endpoint**: `POST /api/winners/:id/claim`

**Description**: Claims a prize won by the current user.

**Request Body**:
```json
{
  "selectedPrize": "string (physical/cash)",
  "shippingDetails": "object (required for physical prizes)",
  "paymentDetails": "object (required for cash prizes)"
}
```

**Response**:
```json
{
  "success": true,
  "data": {
    "id": "uuid",
    "claimed": true,
    "claimedDate": "timestamp",
    "selectedPrize": "string",
    "updatedAt": "timestamp"
  }
}
```

**Access Control**:
- Member: ✓ (own wins only)
- Admin: ✓
- SuperAdmin: ✓

### 9.4 Update Winner Action

**Endpoint**: `PUT /api/winners/:id`

**Description**: Updates winner information (admin only).

**Request Body**:
```json
{
  "claimed": "boolean",
  "claimedDate": "timestamp",
  "shippingDetails": "object",
  "notes": "string"
}
```

**Response**:
```json
{
  "success": true,
  "data": {
    "id": "uuid",
    "claimed": "boolean",
    "updatedAt": "timestamp"
  }
}
```

**Access Control**:
- Admin: ✓
- SuperAdmin: ✓

## 10. Payment Controller

**Purpose**: Manages payment processing and transactions.

### 10.1 Create Payment Intent Action

**Endpoint**: `POST /api/payments/create-intent`

**Description**: Creates a payment intent for subscription or other purchases.

**Request Body**:
```json
{
  "amount": "number",
  "currency": "string",
  "description": "string",
  "metadata": "object"
}
```

**Response**:
```json
{
  "success": true,
  "data": {
    "clientSecret": "string",
    "amount": "number",
    "currency": "string"
  }
}
```

**Access Control**:
- Member: ✓
- Admin: ✓
- SuperAdmin: ✓

### 10.2 Process Payment Action

**Endpoint**: `POST /api/payments/process`

**Description**: Processes a payment after client confirmation.

**Request Body**:
```json
{
  "paymentIntentId": "string",
  "paymentMethodId": "string",
  "type": "string",
  "reference": "object"
}
```

**Response**:
```json
{
  "success": true,
  "data": {
    "transactionId": "uuid",
    "status": "string",
    "amount": "number",
    "currency": "string"
  }
}
```

**Access Control**:
- Member: ✓
- Admin: ✓
- SuperAdmin: ✓

### 10.3 Get Transactions Action

**Endpoint**: `GET /api/payments/transactions`

**Description**: Retrieves the current user's payment transactions.

**Query Parameters**:
- `page`: Page number (default: 1)
- `limit`: Items per page (default: 10)
- `type`: Transaction type filter (optional)
- `status`: Transaction status filter (optional)

**Response**:
```json
{
  "success": true,
  "data": [
    {
      "id": "uuid",
      "type": "string",
      "status": "string",
      "amount": "number",
      "currency": "string",
      "description": "string",
      "createdAt": "timestamp"
    }
  ],
  "meta": {
    "pagination": {
      "page": "number",
      "limit": "number",
      "total": "number",
      "pages": "number"
    }
  }
}
```

**Access Control**:
- Member: ✓ (own transactions only)
- Admin: ✓
- SuperAdmin: ✓

### 10.4 Get Transaction By ID Action

**Endpoint**: `GET /api/payments/transactions/:id`

**Description**: Retrieves a specific transaction by ID.

**Response**:
```json
{
  "success": true,
  "data": {
    "id": "uuid",
    "userId": "uuid",
    "type": "string",
    "status": "string",
    "amount": "number",
    "currency": "string",
    "paymentMethod": "object",
    "description": "string",
    "reference": "object",
    "gateway": "object",
    "createdAt": "timestamp",
    "updatedAt": "timestamp"
  }
}
```

**Access Control**:
- Member: ✓ (own transactions only)
- Admin: ✓
- SuperAdmin: ✓

### 10.5 Process Refund Action

**Endpoint**: `POST /api/payments/refund`

**Description**: Processes a refund for a transaction (admin only).

**Request Body**:
```json
{
  "transactionId": "uuid",
  "amount": "number (optional, defaults to full amount)",
  "reason": "string"
}
```

**Response**:
```json
{
  "success": true,
  "data": {
    "refundId": "uuid",
    "originalTransactionId": "uuid",
    "amount": "number",
    "status": "string",
    "createdAt": "timestamp"
  }
}
```

**Access Control**:
- Admin: ✓
- SuperAdmin: ✓

## 11. Content Controller

**Purpose**: Manages content pages and articles.

### 11.1 Get All Content Action

**Endpoint**: `GET /api/content`

**Description**: Retrieves all published content.

**Query Parameters**:
- `page`: Page number (default: 1)
- `limit`: Items per page (default: 10)
- `category`: Filter by category (optional)
- `tag`: Filter by tag (optional)
- `accessLevel`: Filter by access level (optional)

**Response**:
```json
{
  "success": true,
  "data": [
    {
      "id": "uuid",
      "title": "string",
      "slug": "string",
      "summary": "string",
      "featuredImage": "string",
      "publishDate": "timestamp",
      "categories": "array",
      "tags": "array",
      "accessLevel": "string",
      "createdAt": "timestamp"
    }
  ],
  "meta": {
    "pagination": {
      "page": "number",
      "limit": "number",
      "total": "number",
      "pages": "number"
    }
  }
}
```

**Access Control**:
- Visitor: ✓ (public content only)
- Member: ✓ (based on membership tier)
- Admin: ✓
- SuperAdmin: ✓

### 11.2 Get Content By Slug Action

**Endpoint**: `GET /api/content/:slug`

**Description**: Retrieves a specific content item by slug.

**Response**:
```json
{
  "success": true,
  "data": {
    "id": "uuid",
    "title": "string",
    "slug": "string",
    "content": "string",
    "summary": "string",
    "author": {
      "id": "uuid",
      "firstName": "string",
      "lastName": "string"
    },
    "featuredImage": "string",
    "gallery": "array",
    "publishDate": "timestamp",
    "categories": "array",
    "tags": "array",
    "accessLevel": "string",
    "relatedContent": "array",
    "createdAt": "timestamp",
    "updatedAt": "timestamp"
  }
}
```

**Access Control**:
- Visitor: ✓ (public content only)
- Member: ✓ (based on membership tier)
- Admin: ✓
- SuperAdmin: ✓

### 11.3 Create Content Action

**Endpoint**: `POST /api/content`

**Description**: Creates a new content item.

**Request Body**:
```json
{
  "title": "string",
  "slug": "string",
  "content": "string",
  "summary": "string",
  "featuredImage": "string",
  "gallery": "array",
  "publishDate": "timestamp",
  "status": "string",
  "categories": "array",
  "tags": "array",
  "accessLevel": "string",
  "relatedContent": "array",
  "seo": "object"
}
```

**Response**:
```json
{
  "success": true,
  "data": {
    "id": "uuid",
    "title": "string",
    "slug": "string",
    "status": "string",
    "createdAt": "timestamp"
  }
}
```

**Access Control**:
- Admin: ✓
- SuperAdmin: ✓

### 11.4 Update Content Action

**Endpoint**: `PUT /api/content/:id`

**Description**: Updates an existing content item.

**Request Body**:
```json
{
  "title": "string",
  "slug": "string",
  "content": "string",
  "summary": "string",
  "featuredImage": "string",
  "gallery": "array",
  "publishDate": "timestamp",
  "status": "string",
  "categories": "array",
  "tags": "array",
  "accessLevel": "string",
  "relatedContent": "array",
  "seo": "object"
}
```

**Response**:
```json
{
  "success": true,
  "data": {
    "id": "uuid",
    "title": "string",
    "slug": "string",
    "status": "string",
    "updatedAt": "timestamp"
  }
}
```

**Access Control**:
- Admin: ✓
- SuperAdmin: ✓

### 11.5 Delete Content Action

**Endpoint**: `DELETE /api/content/:id`

**Description**: Deletes a content item (soft delete).

**Response**:
```json
{
  "success": true,
  "message": "Content deleted successfully"
}
```

**Access Control**:
- Admin: ✓
- SuperAdmin: ✓

## 12. Notification Controller

**Purpose**: Manages user notifications.

### 12.1 Get All Notifications Action

**Endpoint**: `GET /api/notifications`

**Description**: Retrieves all notifications for the current user.

**Query Parameters**:
- `page`: Page number (default: 1)
- `limit`: Items per page (default: 10)
- `status`: Filter by status (optional)
- `type`: Filter by type (optional)

**Response**:
```json
{
  "success": true,
  "data": [
    {
      "id": "uuid",
      "type": "string",
      "title": "string",
      "content": "string",
      "status": "string",
      "reference": "object",
      "readAt": "timestamp",
      "createdAt": "timestamp"
    }
  ],
  "meta": {
    "pagination": {
      "page": "number",
      "limit": "number",
      "total": "number",
      "pages": "number"
    },
    "unreadCount": "number"
  }
}
```

**Access Control**:
- Member: ✓
- Admin: ✓
- SuperAdmin: ✓

### 12.2 Mark Notification As Read Action

**Endpoint**: `PUT /api/notifications/:id/read`

**Description**: Marks a notification as read.

**Response**:
```json
{
  "success": true,
  "data": {
    "id": "uuid",
    "status": "read",
    "readAt": "timestamp"
  }
}
```

**Access Control**:
- Member: ✓ (own notifications only)
- Admin: ✓
- SuperAdmin: ✓

### 12.3 Mark All Notifications As Read Action

**Endpoint**: `PUT /api/notifications/read-all`

**Description**: Marks all notifications as read for the current user.

**Response**:
```json
{
  "success": true,
  "message": "All notifications marked as read",
  "data": {
    "count": "number"
  }
}
```

**Access Control**:
- Member: ✓
- Admin: ✓
- SuperAdmin: ✓

### 12.4 Delete Notification Action

**Endpoint**: `DELETE /api/notifications/:id`

**Description**: Deletes a notification.

**Response**:
```json
{
  "success": true,
  "message": "Notification deleted successfully"
}
```

**Access Control**:
- Member: ✓ (own notifications only)
- Admin: ✓
- SuperAdmin: ✓

## 13. Admin Controller

**Purpose**: Provides administrative functionality for platform management.

### 13.1 Get Dashboard Stats Action

**Endpoint**: `GET /api/admin/dashboard`

**Description**: Retrieves dashboard statistics.

**Response**:
```json
{
  "success": true,
  "data": {
    "users": {
      "total": "number",
      "active": "number",
      "newToday": "number",
      "newThisWeek": "number",
      "newThisMonth": "number"
    },
    "memberships": {
      "total": "number",
      "byTier": [
        {
          "tier": "string",
          "count": "number"
        }
      ],
      "newToday": "number",
      "expiringThisWeek": "number"
    },
    "giveaways": {
      "active": "number",
      "upcoming": "number",
      "completed": "number",
      "totalEntries": "number"
    },
    "revenue": {
      "today": "number",
      "thisWeek": "number",
      "thisMonth": "number",
      "total": "number"
    }
  }
}
```

**Access Control**:
- Admin: ✓
- SuperAdmin: ✓

### 13.2 Get All Users Action

**Endpoint**: `GET /api/admin/users`

**Description**: Retrieves all users with filtering and pagination.

**Query Parameters**:
- `page`: Page number (default: 1)
- `limit`: Items per page (default: 10)
- `search`: Search term for email or name (optional)
- `role`: Filter by role (optional)
- `status`: Filter by status (optional)
- `membershipTier`: Filter by membership tier (optional)

**Response**:
```json
{
  "success": true,
  "data": [
    {
      "id": "uuid",
      "email": "string",
      "firstName": "string",
      "lastName": "string",
      "role": "string",
      "status": "string",
      "membership": {
        "tier": "string",
        "status": "string",
        "endDate": "timestamp"
      },
      "createdAt": "timestamp"
    }
  ],
  "meta": {
    "pagination": {
      "page": "number",
      "limit": "number",
      "total": "number",
      "pages": "number"
    }
  }
}
```

**Access Control**:
- Admin: ✓
- SuperAdmin: ✓

### 13.3 Get User By ID Action

**Endpoint**: `GET /api/admin/users/:id`

**Description**: Retrieves detailed information for a specific user.

**Response**:
```json
{
  "success": true,
  "data": {
    "id": "uuid",
    "email": "string",
    "firstName": "string",
    "lastName": "string",
    "phoneNumber": "string",
    "birthDate": "date",
    "address": "object",
    "role": "string",
    "status": "string",
    "emailVerified": "boolean",
    "phoneVerified": "boolean",
    "preferences": "object",
    "membership": {
      "id": "uuid",
      "tier": {
        "id": "uuid",
        "name": "string"
      },
      "status": "string",
      "startDate": "timestamp",
      "endDate": "timestamp",
      "renewalDate": "timestamp",
      "membershipHistory": "array"
    },
    "stats": {
      "totalEntries": "number",
      "totalWins": "number",
      "totalSpent": "number"
    },
    "createdAt": "timestamp",
    "updatedAt": "timestamp"
  }
}
```

**Access Control**:
- Admin: ✓
- SuperAdmin: ✓

### 13.4 Update User Action

**Endpoint**: `PUT /api/admin/users/:id`

**Description**: Updates a user's information.

**Request Body**:
```json
{
  "firstName": "string",
  "lastName": "string",
  "phoneNumber": "string",
  "birthDate": "date",
  "address": "object",
  "role": "string",
  "status": "string",
  "emailVerified": "boolean",
  "phoneVerified": "boolean"
}
```

**Response**:
```json
{
  "success": true,
  "data": {
    "id": "uuid",
    "email": "string",
    "firstName": "string",
    "lastName": "string",
    "role": "string",
    "status": "string",
    "updatedAt": "timestamp"
  }
}
```

**Access Control**:
- Admin: ✓ (cannot change roles to SuperAdmin)
- SuperAdmin: ✓

### 13.5 Get All Transactions Action

**Endpoint**: `GET /api/admin/transactions`

**Description**: Retrieves all transactions with filtering and pagination.

**Query Parameters**:
- `page`: Page number (default: 1)
- `limit`: Items per page (default: 10)
- `userId`: Filter by user ID (optional)
- `type`: Filter by transaction type (optional)
- `status`: Filter by status (optional)
- `startDate`: Filter by start date (optional)
- `endDate`: Filter by end date (optional)

**Response**:
```json
{
  "success": true,
  "data": [
    {
      "id": "uuid",
      "userId": "uuid",
      "user": {
        "email": "string",
        "firstName": "string",
        "lastName": "string"
      },
      "type": "string",
      "status": "string",
      "amount": "number",
      "currency": "string",
      "description": "string",
      "createdAt": "timestamp"
    }
  ],
  "meta": {
    "pagination": {
      "page": "number",
      "limit": "number",
      "total": "number",
      "pages": "number"
    },
    "summary": {
      "totalAmount": "number",
      "count": "number"
    }
  }
}
```

**Access Control**:
- Admin: ✓
- SuperAdmin: ✓

### 13.6 Generate Report Action

**Endpoint**: `POST /api/admin/reports`

**Description**: Generates various administrative reports.

**Request Body**:
```json
{
  "type": "string",
  "parameters": "object",
  "format": "string"
}
```

**Response**:
```json
{
  "success": true,
  "data": {
    "reportId": "uuid",
    "type": "string",
    "format": "string",
    "url": "string",
    "expiresAt": "timestamp",
    "createdAt": "timestamp"
  }
}
```

**Access Control**:
- Admin: ✓
- SuperAdmin: ✓

## 14. Settings Controller

**Purpose**: Manages system-wide configuration settings.

### 14.1 Get All Settings Action

**Endpoint**: `GET /api/settings`

**Description**: Retrieves all public settings or all settings for admins.

**Query Parameters**:
- `category`: Filter by category (optional)

**Response**:
```json
{
  "success": true,
  "data": [
    {
      "key": "string",
      "value": "any",
      "category": "string",
      "isPublic": "boolean"
    }
  ]
}
```

**Access Control**:
- Visitor: ✓ (public settings only)
- Member: ✓ (public settings only)
- Admin: ✓ (all settings)
- SuperAdmin: ✓ (all settings)

### 14.2 Get Setting By Key Action

**Endpoint**: `GET /api/settings/:key`

**Description**: Retrieves a specific setting by key.

**Response**:
```json
{
  "success": true,
  "data": {
    "key": "string",
    "value": "any",
    "description": "string",
    "category": "string",
    "isPublic": "boolean",
    "updatedAt": "timestamp"
  }
}
```

**Access Control**:
- Visitor: ✓ (public settings only)
- Member: ✓ (public settings only)
- Admin: ✓ (all settings)
- SuperAdmin: ✓ (all settings)

### 14.3 Update Setting Action

**Endpoint**: `PUT /api/settings/:key`

**Description**: Updates a specific setting.

**Request Body**:
```json
{
  "value": "any",
  "description": "string (optional)",
  "isPublic": "boolean (optional)"
}
```

**Response**:
```json
{
  "success": true,
  "data": {
    "key": "string",
    "value": "any",
    "updatedAt": "timestamp"
  }
}
```

**Access Control**:
- SuperAdmin: ✓

### 14.4 Create Setting Action

**Endpoint**: `POST /api/settings`

**Description**: Creates a new setting.

**Request Body**:
```json
{
  "key": "string",
  "value": "any",
  "description": "string",
  "category": "string",
  "isPublic": "boolean"
}
```

**Response**:
```json
{
  "success": true,
  "data": {
    "key": "string",
    "value": "any",
    "category": "string",
    "isPublic": "boolean",
    "createdAt": "timestamp"
  }
}
```

**Access Control**:
- SuperAdmin: ✓

## 15. Conclusion

This document provides a comprehensive overview of the controllers and actions required for the membership-based giveaway platform, including their endpoints, request/response formats, and role-based access control. The controllers are designed to support all the functionality outlined in the requirements specification while maintaining proper security and access control.

The role-based access control system ensures that users can only access the functionality appropriate for their role, with increasing levels of access from Visitor to SuperAdmin. This helps maintain the security and integrity of the platform while providing a seamless user experience.

By implementing these controllers and actions, the platform will provide a robust API for the frontend application to interact with, supporting all the core functionality required for the MVP.
