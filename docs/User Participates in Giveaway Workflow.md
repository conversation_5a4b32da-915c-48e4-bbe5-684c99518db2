# User Participates in Giveaway Workflow

This document outlines the typical workflow for a member participating in a giveaway, referencing the provided Prisma schema and common practices from competitor sites.

## Flow Diagram (Conceptual - Member Participation)

```mermaid
graph TD
    A{User is Logged In & has Active Membership} --> B[User navigates to Giveaways Page (e.g., /giveaways)];
    B --> C[User views list of Active Giveaways];
    C --> D{User selects a specific Giveaway to view details};
    D --> E[Giveaway Detail Page Shown: Prize, Rules, End Date];
    E --> F{System checks User Status, Membership, & Giveaway Eligibility};
    F -- Eligible & Automatic Entries --> G[Display: "You have X entries from your [TierName] membership for this giveaway!"];
    F -- Eligible & Option for Bonus Entries --> H[Display: "Want more chances? Earn Bonus Entries!" (e.g., complete task, refer friend)];
    H --> I[User performs action for bonus entries (if available)];
    I --> J{Backend: Validate bonus action & award bonus entries};
    J --> K[Backend: Create/Update `Entry` Record for User & Giveaway];
    K --> G;
    F -- Not Eligible (e.g., tier restriction, giveaway status) --> L[Display: "Your current membership does not qualify" or "This giveaway is not active/available"];
    G --> M{Giveaway `status` is `ACTIVE` / `endDate` not passed?};
    M -- Yes --> N[User can see their current entry count for this giveaway];
    M -- No (Closed/Ended) --> O[Display: "This giveaway has ended. Winner: [WinnerName]" or "Winner to be announced"];
    N --> P[User waits for draw date];

    subgraph "Automatic Entry Allocation (Backend Process)"
        Q[New Giveaway Launched OR Member Renews/Upgrades OR New Member Joins] --> R{System identifies eligible members for the specific giveaway based on `Giveaway.eligibleMembershipTierIds` and `Membership.status`};
        R --> S[For each eligible member, retrieve `MembershipTier.entryAllocation`];
        S --> T[Backend: Create `Entry` Record(s) in `Entry` table: `userId`, `giveawayId`, `numberOfEntries`, `source`=\'MEMBERSHIP\', `entryDate`=NOW()];
        T --> U[Update User's visible entry count for that giveaway (e.g., via a summary field or query)];
    end
```

## Detailed Steps

1.  **User Accesses Giveaways**:
    *   The user, who is logged in and has an `ACTIVE` `Membership`, navigates to the section of the platform listing available giveaways (e.g., a `/giveaways` page).
    *   The frontend fetches and displays a list of `Giveaway` records where `status` is `ACTIVE` and `endDate` is in the future.

2.  **Viewing Giveaway Details**:
    *   The user clicks on a specific `Giveaway` from the list.
    *   The frontend navigates to a detail page for that `Giveaway`, displaying its `name`, `description`, `prizeId` (which is then used to fetch and display `Prize` details), `startDate`, `endDate`, `rules`, etc.

3.  **Displaying User-Specific Entry Information (for Members)**:
    *   On the giveaway detail page, the system determines the user's participation status for *this specific* `Giveaway`.
    *   **Automatic Entries**: The system checks if the user's current active `Membership.membershipTierId` is included in the `Giveaway.eligibleMembershipTierIds` (if this field is used and populated). It then queries the `Entry` table for existing entries for this `userId` and `giveawayId` that originated from their membership (`source = 'MEMBERSHIP'`).
    *   The number of entries (e.g., sum of `numberOfEntries` from relevant `Entry` records) is displayed to the user (e.g., "You have X entries from your membership!").

4.  **Bonus Entry Opportunities (Optional Feature)**:
    *   If the `Giveaway` offers ways to earn bonus entries (e.g., `Giveaway.bonusEntryMechanisms` field might store this configuration):
        *   The UI presents these options (e.g., "Share on Social Media for +5 Entries").
        *   If the user completes an action: The frontend sends a request to a backend endpoint.
        *   The backend validates the action (e.g., verifies social share via API if possible, or marks task as complete).
        *   If valid, a new `Entry` record is created with `userId`, `giveawayId`, the bonus `numberOfEntries`, and `source` (e.g., `BONUS_SOCIAL_SHARE`, `BONUS_REFERRAL`).
        *   The user's displayed entry count for the giveaway is updated.

5.  **Non-Member or Ineligible Member View**:
    *   If the user is not logged in, does not have an `ACTIVE` membership, or their `MembershipTier` is not eligible for this specific `Giveaway`, the page should display a prominent Call to Action to join/upgrade their membership to participate.

6.  **Giveaway Lifecycle & Entry Validity**:
    *   Entries are typically only valid if the `Giveaway.status` is `ACTIVE` and the current date is between `Giveaway.startDate` and `Giveaway.endDate`.
    *   If a user's `Membership` lapses (`status` becomes `INACTIVE` or `EXPIRED`), their eligibility for future automatic entries or participation might cease, depending on business rules. Existing entries for already-entered giveaways usually remain valid.

7.  **Winner Selection (Backend Process)**:
    *   After `Giveaway.endDate` passes, the `Giveaway.status` is updated to `CLOSED` (or `DRAWING_PENDING`).
    *   A backend process (e.g., a scheduled job or manual trigger) selects a winner(s) by randomly choosing from all valid `Entry` records associated with that `giveawayId`.
        *   The randomness should be fair and auditable.
        *   The `numberOfEntries` per `Entry` record should proportionally increase a user's chance if one `Entry` record can represent multiple chances.
    *   A new record is created in the `Winner` table with `userId`, `giveawayId`, `prizeId`, and `announcedAt`.
    *   The `Giveaway.status` might be updated to `WINNER_ANNOUNCED`.
    *   The winner is notified through appropriate channels (email, in-app notification).

## Database Tables Touched (Based on User's Provided Prisma Schema)

*   **`User`**: Read `id` (from authenticated session), check `status`, `emailVerified`.
*   **`Membership`**: Read for the current user (`userId`) to find their `ACTIVE` membership, get `membershipTierId`, check `status`, `startDate`, `endDate`.
*   **`MembershipTier`**: Read using `Membership.membershipTierId` to get `entryAllocation` (for automatic entries) and `name`.
*   **`Giveaway`**: Read to list active giveaways and display details. Key fields: `id`, `name`, `description`, `startDate`, `endDate`, `status`, `prizeId`, `eligibleMembershipTierIds`, `rules`.
    *   Updated: `status` changes (e.g., `ACTIVE` -> `CLOSED` -> `WINNER_ANNOUNCED`).
*   **`Prize`**: Read using `Giveaway.prizeId` to display prize information.
*   **`Entry`**: This is the central table for participation.
    *   **Created**: When automatic membership entries are allocated, or when bonus entries are earned.
        *   `userId`: Foreign key to `User`.
        *   `giveawayId`: Foreign key to `Giveaway`.
        *   `numberOfEntries`: Number of entries this record represents (e.g., 1, or more if entries are bundled).
        *   `entryDate`: Timestamp of when the entry was made/allocated.
        *   `source`: String indicating how the entry was obtained (e.g., `MEMBERSHIP_BRONZE`, `BONUS_SHARE`, `MANUAL_ADMIN_ENTRY`).
    *   **Read**: To display user's current entries for a giveaway, and to select a winner.
*   **`Winner`**: A new record is created when a winner is drawn.
    *   `userId`: Foreign key to `User` (the winner).
    *   `giveawayId`: Foreign key to `Giveaway`.
    *   `prizeId`: Foreign key to `Prize`.
    *   `announcedAt`: Timestamp of announcement.
    *   `claimedAt`: Timestamp when/if the prize is claimed (initially null).
    *   `claimDetails`: Any notes about the claim process.

## Key Validations

*   **User State**: User must be logged in, `emailVerified`, and have an `ACTIVE` `status`.
*   **Membership State**: User must have an `ACTIVE` `Membership`. `Membership.endDate` should be valid (not past, unless rules allow entries from recently expired memberships for draws they were eligible for when active).
*   **Giveaway State**: `Giveaway.status` must be `ACTIVE`. Current date/time must be within `Giveaway.startDate` and `Giveaway.endDate` for new entries or bonus entry actions.
*   **Tier Eligibility**: If `Giveaway.eligibleMembershipTierIds` is used, the user's `Membership.membershipTierId` must be in the allowed list.
*   **Entry Uniqueness/Limits**:
    *   Prevent duplicate *automatic* entries for the same user for the same giveaway from the same membership benefit period. (e.g., if a user has a Gold monthly plan, they get X entries for Giveaway Y for that month; they shouldn't get them twice for the same month's benefit).
    *   Enforce any limits on bonus entries (e.g., 
