# Angular UI Modules and Components List

This document outlines the detailed UI modules, components (pages and reusable elements), and routing structure required for the frontend of the membership-based giveaway platform, designed for an Angular application (using standalone components where appropriate).

## 1. Core Angular Modules / Feature Areas

The frontend can be organized into the following feature areas or modules (using Angular Modules or simply folder structure with standalone components):

1.  **Core**: Provides essential services, guards, interceptors, and the main App component/layout.
2.  **Auth**: Handles user login, registration, password reset, and email verification.
3.  **Layout**: Contains main layout components (Toolbar, Footer, Sidenav).
4.  **Dashboard**: User-specific area for members.
5.  **Membership**: Displays membership tiers, handles subscription, upgrades, and management.
6.  **Giveaways**: Lists active and past giveaways, shows details, and handles entry.
7.  **Content**: Displays static pages (About, FAQ, Terms) and potentially blog/articles.
8.  **User Profile**: Allows users to manage their account details and preferences.
9.  **Admin**: Dedicated section for platform administration (lazy-loaded module recommended).
10. **Shared**: Contains reusable UI components, pipes, and directives used across multiple modules.

## 2. Components and Routing by Feature Area

*(Note: Using standalone components simplifies imports, but traditional NgModule structure is also possible)*

### 2.1 Core Area

*   **Components:**
    *   `AppComponent`: The root component, likely containing the main layout structure (Toolbar, Sidenav container, Router Outlet).
*   **Services:**
    *   `ApiService`: Base service for making HTTP requests.
    *   `NotificationService`: Service for displaying snackbars/toasts.
*   **Guards:**
    *   `AuthGuard`: Protects routes requiring authentication.
    *   `AdminGuard`: Protects admin routes, checks for admin role.
*   **Interceptors:**
    *   `AuthInterceptor`: Attaches JWT token to outgoing requests.
    *   `ErrorInterceptor`: Handles global HTTP errors.

### 2.2 Auth Area (`/auth` route prefix or separate module)

*   **Routing:** `auth-routing.module.ts` or defined in `app.routes.ts`.
*   **Components (Pages):**
    *   `LoginComponent` (`/login`)
    *   `RegisterComponent` (`/register`)
    *   `ForgotPasswordComponent` (`/forgot-password`)
    *   `ResetPasswordComponent` (`/reset-password/:token`)
    *   `VerifyEmailComponent` (`/verify-email/:token`)
*   **Services:**
    *   `AuthService`: Handles all authentication logic (login, register, logout, token management, user state).
*   **Components (Reusable):**
    *   `SocialLoginButtonsComponent` (Optional)

### 2.3 Layout Area

*   **Components:**
    *   `ToolbarComponent`: Top navigation bar with logo, links, user menu/login button.
    *   `FooterComponent`: Bottom section with copyright, links.
    *   `UserMenuComponent`: Dropdown for logged-in users (Profile, Settings, Logout).
    *   `SidenavComponent`: Optional side navigation, especially for mobile or admin.

### 2.4 Dashboard Area (`/dashboard` route)

*   **Routing:** Defined in `app.routes.ts` or a `dashboard.routes.ts`, protected by `AuthGuard`.
*   **Components (Page):**
    *   `DashboardComponent` (`/dashboard`)
*   **Components (Widgets/Reusable):**
    *   `DashboardSummaryComponent`: Overview widgets.
    *   `ActiveGiveawaysWidgetComponent`: Quick view of giveaways.
    *   `MembershipStatusCardComponent`: Displays current tier info.

### 2.5 Membership Area (`/membership` route prefix)

*   **Routing:** `membership.routes.ts`, protected by `AuthGuard` where necessary.
*   **Components (Pages):**
    *   `MembershipTiersComponent` (`/membership`)
    *   `SubscriptionCheckoutComponent` (`/membership/subscribe/:tierId`, protected by `AuthGuard`)
    *   `MembershipManagementComponent` (`/membership/manage`, protected by `AuthGuard`)
*   **Services:**
    *   `MembershipService`: Fetches tier info, manages user subscription state.
    *   `PaymentService`: Handles interaction with payment gateway (e.g., Stripe).
*   **Components (Reusable):**
    *   `MembershipTierCardComponent`: Displays details of a single tier.
    *   `SubscriptionFormComponent`: Integrates with payment gateway.
    *   `CancelSubscriptionDialogComponent`: Confirmation dialog (using `MatDialog`).

### 2.6 Giveaways Area (`/giveaways` route prefix)

*   **Routing:** `giveaways.routes.ts`.
*   **Components (Pages):**
    *   `GiveawayListComponent` (`/giveaways`)
    *   `GiveawayDetailComponent` (`/giveaways/:slug`)
*   **Services:**
    *   `GiveawayService`: Fetches giveaway list and details.
    *   `EntryService`: Handles entering giveaways.
*   **Components (Reusable):**
    *   `GiveawayCardComponent`: Summary card for lists.
    *   `PrizeShowcaseComponent`: Displays prize information.
    *   `EntryButtonComponent`: Button to enter giveaway.
    *   `EntryStatusComponent`: Shows user's entry status.
    *   `CountdownTimerComponent`: Displays time remaining.
    *   `WinnerDisplayComponent`: Shows winner(s).

### 2.7 Content Area

*   **Routing:** Defined in `app.routes.ts`.
*   **Components (Pages):**
    *   `StaticPageComponent` (e.g., `/about`, `/faq`, `/terms-of-service`, `/privacy-policy` - potentially using a single component driven by route data or slug).
    *   `WinnersComponent` (`/winners`)
    *   `BlogListComponent` (Optional, `/blog`)
    *   `BlogPostComponent` (Optional, `/blog/:slug`)
*   **Services:**
    *   `ContentService`: Fetches static page or blog content.
*   **Components (Reusable):**
    *   `FaqAccordionComponent`: For FAQ page.
    *   `WinnerCardComponent`: For winners page.
    *   `ContentRendererComponent`: Renders markdown/HTML securely.

### 2.8 User Profile Area (`/profile` route prefix)

*   **Routing:** `profile.routes.ts`, protected by `AuthGuard`.
*   **Components (Pages):**
    *   `ProfileComponent` (`/profile`)
    *   `SettingsComponent` (`/profile/settings`)
    *   `EntryHistoryComponent` (`/profile/entries`)
    *   `WinHistoryComponent` (`/profile/wins`)
    *   `TransactionHistoryComponent` (`/profile/transactions`)
*   **Services:**
    *   `UserService`: Manages user profile data, password changes, fetches history.
*   **Components (Reusable):**
    *   `ProfileFormComponent`: Edits profile details.
    *   `ChangePasswordFormComponent`: Handles password change.
    *   `NotificationPreferencesComponent`: Manages notification settings.
    *   `HistoryTableComponent`: Generic table for displaying history data (Entries, Wins, Transactions).

### 2.9 Admin Area (Lazy-loaded module at `/admin`)

*   **Routing:** `admin-routing.module.ts` (defines child routes within `/admin`), protected by `AuthGuard` and `AdminGuard`.
*   **Components (Layout):**
    *   `AdminLayoutComponent`: Wrapper for admin section, includes sidebar.
    *   `AdminSidebarComponent`: Navigation for admin sections.
*   **Components (Pages):**
    *   `AdminDashboardComponent` (`/admin/dashboard`)
    *   `UserListComponent` (`/admin/users`)
    *   `UserDetailComponent` (`/admin/users/:userId`)
    *   `MembershipListComponent` (`/admin/memberships`)
    *   `TierListComponent` (`/admin/memberships/tiers`)
    *   `GiveawayListComponent` (`/admin/giveaways`)
    *   `GiveawayFormComponent` (`/admin/giveaways/new`, `/admin/giveaways/:giveawayId/edit`)
    *   `GiveawayDrawComponent` (`/admin/giveaways/:giveawayId/draw`)
    *   `WinnerListComponent` (`/admin/winners`)
    *   `ContentListComponent` (`/admin/content`)
    *   `ContentFormComponent` (`/admin/content/new`, `/admin/content/:contentId/edit`)
    *   `TransactionListComponent` (`/admin/transactions`)
    *   `SettingsComponent` (`/admin/settings`)
*   **Services (Admin Specific):**
    *   `AdminUserService`, `AdminGiveawayService`, `AdminContentService`, etc.
*   **Components (Reusable):**
    *   `AdminStatsCardComponent`: For dashboard metrics.
    *   `AdminDataTableComponent`: Generic table for admin lists.
    *   `ConfirmationDialogComponent`: Reusable confirmation dialog.

### 2.10 Shared Area

*   **Components (Reusable UI Elements):**
    *   `ButtonComponent` (if extending `mat-button`)
    *   `SpinnerComponent` (e.g., using `MatProgressSpinner`)
    *   `AlertDialogComponent` (using `MatDialog`)
    *   `PaginatorComponent` (if customizing `MatPaginator`)
    *   `FormComponent` (potential wrapper)
*   **Pipes:**
    *   `DateFormatPipe` (if specific formatting needed beyond Angular's `DatePipe`)
    *   `CurrencyFormatPipe` (if specific formatting needed beyond Angular's `CurrencyPipe`)
*   **Directives:**
    *   `RoleBasedDisplayDirective` (`*appHasRole="'ADMIN'"` - custom structural directive for role checks).

This structure provides a modular and maintainable foundation for the Angular frontend, leveraging standalone components and clear feature areas.
