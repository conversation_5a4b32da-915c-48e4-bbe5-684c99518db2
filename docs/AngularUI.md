--- /dev/null
+++ b/docs/ANGULAR_UI_DEVELOPMENT_GUIDE.md
@@ -0,0 +1,411 @@
+# Winners Society - Angular UI Development Guide
+
+## 1. Introduction
+
+### 1.1 Purpose
+This guide provides a structured approach, steps, and prompts to assist developers in creating the Angular frontend application for the **Winners Society** platform. It complements the existing backend documentation and assumes the backend API is available as specified.
+
+### 1.2 Scope
+This document covers the setup, core structure, authentication implementation, feature module development (Giveaways, Membership, User Profile, Admin), and refinement strategies for the Angular UI.
+
+### 1.3 Assumptions
+*   You have Node.js and npm/yarn installed.
+*   You have the Angular CLI installed (`npm install -g @angular/cli`).
+*   The backend API is running and accessible (e.g., at `http://localhost:3000/api`).
+
+## 2. Suggested Technology Stack
+
+*   **Framework:** Angular (Latest stable version, e.g., v17+)
+*   **Language:** TypeScript
+*   **Styling:** SCSS (or your preferred CSS preprocessor)
+*   **UI Library:** Angular Material (or Bootstrap/ngx-bootstrap, Tailwind CSS, etc.)
+*   **HTTP Client:** Angular `HttpClient`
+*   **Routing:** Angular Router
+*   **Forms:** Angular Reactive Forms
+*   **State Management (Optional but Recommended):** NgRx or NGXS (Alternatively, start with services and RxJS `BehaviorSubject`)
+*   **Reactive Programming:** RxJS
+
+## 3. Phase 1: Project Setup & Core Structure
+
+### Step 3.1: Create the Angular Project
+
+Open your terminal in the desired parent directory (e.g., `Workspace`) and run:
+
+```bash
+ng new winners-society-ui --routing --style=scss
+cd winners-society-ui
+```
+*   `--routing`: Sets up the Angular Router.
+*   `--style=scss`: Configures SCSS for styling (adjust if needed).
+
+### Step 3.2: Install UI Library (Example: Angular Material)
+
+Run the following command and follow the prompts:
+
+```bash
+ng add @angular/material
+```
+*(If using Bootstrap, install `bootstrap` and `ngx-bootstrap` via npm and add Bootstrap CSS to `angular.json`)*
+
+### Step 3.3: Basic Folder Structure
+
+Use the Angular CLI to generate the core modules and components:
+
+```bash
+# Core Module (for singleton services, guards, interceptors)
+ng generate module core --flat --module=app
+
+# Shared Module (for reusable components, directives, pipes)
+ng generate module shared
+
+# Layout Components
+ng generate component layouts/header --module=shared --export
+ng generate component layouts/footer --module=shared --export
+ng generate component layouts/main-layout # Will contain header/footer/router-outlet
+
+# Feature Modules (with routing)
+ng generate module features/auth --routing
+ng generate module features/giveaways --routing
+ng generate module features/membership --routing
+ng generate module features/user --routing
+ng generate module features/admin --routing
+```
+
+### Step 3.4: Setup Environment Variables
+
+Configure API endpoint URLs in `src/environments/`:
+
+```typescript
+// src/environments/environment.ts
+export const environment = {
+  production: false,
+  apiUrl: 'http://localhost:3000/api' // Your backend API URL
+};
+
+// src/environments/environment.prod.ts
+export const environment = {
+  production: true,
+  apiUrl: 'https://your-production-api.com/api' // Your production backend API URL
+};
+```
+
+### Step 3.5: Configure Main Layout and Routing
+
+1.  **Update Main Layout HTML:**
+    *   Edit `src/app/layouts/main-layout/main-layout.component.html`.
+    *   Include the header, footer, and the main content area with `<router-outlet>`.
+    ```html
+    <app-header></app-header>
+    <main>
+      <router-outlet></router-outlet>
+    </main>
+    <app-footer></app-footer>
+    ```
+
+2.  **Configure App Routing:**
+    *   Edit `src/app/app-routing.module.ts`.
+    *   Set up lazy loading for the feature modules within the `MainLayoutComponent`.
+
+    **Prompt for `app-routing.module.ts`:**
+    ```typescript
+    /*
+    Update app-routing.module.ts:
+    1. Import MainLayoutComponent.
+    2. Define routes:
+       - A base route '' that redirects to a default path (e.g., '/giveaways').
+       - A route using MainLayoutComponent as the parent for feature modules.
+       - Children routes within MainLayoutComponent that lazy-load the feature modules:
+         {
+           path: '',
+           component: MainLayoutComponent,
+           children: [
+             { path: 'auth', loadChildren: () => import('./features/auth/auth.module').then(m => m.AuthModule) },
+             { path: 'giveaways', loadChildren: () => import('./features/giveaways/giveaways.module').then(m => m.GiveawaysModule) },
+             { path: 'membership', loadChildren: () => import('./features/membership/membership.module').then(m => m.MembershipModule) },
+             { path: 'user', loadChildren: () => import('./features/user/user.module').then(m => m.UserModule) },
+             { path: 'admin', loadChildren: () => import('./features/admin/admin.module').then(m => m.AdminModule) },
+             // Add a default child route if needed, e.g., redirect '' to 'giveaways'
+             { path: '', redirectTo: 'giveaways', pathMatch: 'full' }
+           ]
+         },
+       - A wildcard route '**' for a 404 Not Found component.
+    */
+    ```
+
+## 4. Phase 2: Authentication
+
+### Step 4.1: Create Auth Components
+
+Generate components for login, registration, etc., within the `auth` feature module:
+
+```bash
+ng generate component features/auth/login --module=features/auth
+ng generate component features/auth/register --module=features/auth
+ng generate component features/auth/forgot-password --module=features/auth
+ng generate component features/auth/reset-password --module=features/auth
+```
+
+### Step 4.2: Create Auth Service
+
+Generate a singleton service in the `core` module:
+
+```bash
+ng generate service core/auth/auth
+```
+
+**Prompt for `src/app/core/auth/auth.service.ts`:**
+```typescript
+/*
+Implement the AuthService in src/app/core/auth/auth.service.ts:
+1. Inject HttpClient from '@angular/common/http' and Router from '@angular/router'.
+2. Import environment variables.
+3. Import necessary DTOs (UserLoginDto, UserRegistrationDto, AuthResponseDto, TokenRefreshDto, TokenResponseDto, etc.) - create these interfaces based on backend documentation if they don't exist.
+4. Import RxJS BehaviorSubject and Observable.
+5. Define private BehaviorSubject for authentication state: `private isAuthenticatedSubject = new BehaviorSubject<boolean>(this.hasToken());`
+6. Expose public Observable for auth state: `public isAuthenticated$ = this.isAuthenticatedSubject.asObservable();`
+7. Define methods for:
+   - login(credentials: UserLoginDto): Observable<AuthResponseDto> (POST `${environment.apiUrl}/auth/login`)
+   - register(userData: UserRegistrationDto): Observable<AuthResponseDto> (POST `${environment.apiUrl}/auth/register`)
+   - forgotPassword(email: string): Observable<void> (POST `${environment.apiUrl}/auth/forgot-password`)
+   - resetPassword(data: PasswordResetDto): Observable<void> (POST `${environment.apiUrl}/auth/reset-password`)
+   - refreshToken(): Observable<TokenResponseDto> (POST `${environment.apiUrl}/auth/refresh-token` using stored refresh token)
+   - logout(): void (Clear tokens, update auth state, navigate to login)
+8. Implement token storage using localStorage:
+   - `saveTokens(tokens: AuthResponseDto | TokenResponseDto)`: Store access and refresh tokens.
+   - `getAccessToken(): string | null`
+   - `getRefreshToken(): string | null`
+   - `clearTokens(): void`
+   - `hasToken(): boolean`: Check if an access token exists.
+9. Update `isAuthenticatedSubject` in `login`, `saveTokens`, and `logout`/`clearTokens`.
+10. Handle token saving and auth state update within `login` and `refreshToken` success callbacks (`tap` operator).
+*/
+```
+
+### Step 4.3: Implement Login/Register Components
+
+Use Angular Reactive Forms for handling login and registration forms.
+
+**Prompt for `src/app/features/auth/login/login.component.ts`:**
+```typescript
+/*
+Implement LoginComponent:
+1. Inject AuthService, Router, and FormBuilder.
+2. Create a loginForm: FormGroup using FormBuilder with 'email' (Validators.required, Validators.email) and 'password' (Validators.required) controls.
+3. Implement an `onSubmit()` method:
+   - If `loginForm.invalid`, mark all fields as touched and return.
+   - Call `authService.login(this.loginForm.value)`.
+   - Use `.subscribe()` to handle the response:
+     - On success: Navigate to a default authenticated route (e.g., '/giveaways' or '/user/profile'). The service should handle token saving and state update.
+     - On error: Display an appropriate error message to the user (e.g., "Invalid credentials").
+*/
+```
+
+**Prompt for `src/app/features/auth/register/register.component.ts`:**
+```typescript
+/*
+Implement RegisterComponent:
+1. Inject AuthService, Router, and FormBuilder.
+2. Create a registerForm: FormGroup for registration fields (email, password, confirmPassword, firstName, lastName) with appropriate validators.
+3. Add a custom validator to check if 'password' and 'confirmPassword' match.
+4. Implement `onSubmit()`:
+   - If `registerForm.invalid`, mark all fields as touched and return.
+   - Call `authService.register(this.registerForm.value)`.
+   - Use `.subscribe()`:
+     - On success: Navigate to the login page or show a success message prompting email verification.
+     - On error: Display specific error messages (e.g., "Email already exists", "Password mismatch").
+*/
+```
+
+### Step 4.4: Create Auth Guard
+
+Generate a functional guard to protect routes:
+
+```bash
+ng generate guard core/auth/auth --functional
+```
+
+**Prompt for `src/app/core/auth/auth.guard.ts`:**
+```typescript
+/*
+Implement the functional AuthGuard (`authGuard`):
+1. Use `inject()` to get instances of AuthService and Router.
+2. In the guard function logic (`CanActivateFn`):
+   - Check `authService.isAuthenticatedSubject.value` or `authService.hasToken()`.
+   - If authenticated, return `true`.
+   - If not authenticated, use `router.navigate(['/auth/login'])` and return `false`.
+*/
+```
+*Apply this guard to routes requiring authentication in your routing modules.*
+
+### Step 4.5: Create HTTP Interceptor for Tokens
+
+Generate a functional interceptor to add the JWT to outgoing requests:
+
+```bash
+ng generate interceptor core/auth/token --functional
+```
+
+**Prompt for `src/app/core/auth/token.interceptor.ts`:**
+```typescript
+/*
+Implement the functional TokenInterceptor (`tokenInterceptor`):
+1. Use `inject()` to get an instance of AuthService.
+2. In the intercept function logic (`HttpInterceptorFn`):
+   - Get the access token using `authService.getAccessToken()`.
+   - If a token exists:
+     - Clone the request: `req.clone({ setHeaders: { Authorization: `Bearer ${token}` } })`.
+     - Pass the cloned request to `next(clonedReq)`.
+   - If no token exists, pass the original request: `next(req)`.
+3. Consider adding error handling for 401 Unauthorized responses (e.g., attempt token refresh or logout). Use `catchError` operator from RxJS.
+*/
+```
+
+**Register the Interceptor:**
+Provide the interceptor in `src/app/app.config.ts` (for standalone setup) or `src/app/app.module.ts` (for module-based setup):
+
+```typescript
+// Example for app.config.ts
+import { provideHttpClient, withInterceptors } from '@angular/common/http';
+import { tokenInterceptor } from './core/auth/token.interceptor';
+
+export const appConfig: ApplicationConfig = {
+  providers: [
+    provideHttpClient(withInterceptors([tokenInterceptor])),
+    // ... other providers
+  ]
+};
+```
+
+## 5. Phase 3: Feature Implementation
+
+Follow a similar pattern for each feature module (Giveaways, Membership, User, Admin):
+
+### Step 5.1: Create Models/Interfaces
+
+Define TypeScript interfaces in `src/app/core/models/` corresponding to your backend DTOs.
+
+**Prompt for `src/app/core/models/giveaway.model.ts`:**
+```typescript
+/*
+Create TypeScript interfaces based on the backend GiveawayResponseDto, PrizeResponseDto, etc.
+Example:
+export interface Prize {
+  id: string;
+  name: string;
+  value: number;
+  currency: string;
+  quantity: number;
+  images: string[];
+  // ... other fields from PrizeResponseDto
+}
+
+export interface Giveaway {
+  id: string;
+  title: string;
+  description: string;
+  startDate: string; // Use string for dates from API, convert in service/component
+  endDate: string;
+  status: string; // Consider using an enum matching the backend
+  featuredImage?: string;
+  prizes?: Prize[];
+  entryCount?: number;
+  // ... other fields from GiveawayResponseDto
+}
+
+export interface PaginatedGiveaways { // Example for paginated response
+  data: Giveaway[];
+  meta: {
+    total: number;
+    page: number;
+    limit: number;
+    hasNext: boolean;
+    hasPrevious: boolean;
+  };
+}
+*/
+```
+
+### Step 5.2: Create Feature Services
+
+Generate services within each feature module.
+
+```bash
+ng generate service features/giveaways/giveaway
+ng generate service features/membership/membership
+# ... and so on
+```
+
+**Prompt for `src/app/features/giveaways/giveaway.service.ts`:**
+```typescript
+/*
+Implement GiveawayService:
+1. Inject HttpClient.
+2. Import environment variables and necessary models/DTOs.
+3. Define methods to interact with the backend giveaway endpoints:
+   - getGiveaways(params: { page?: number, limit?: number, status?: string }): Observable<PaginatedGiveaways> (GET `${environment.apiUrl}/giveaways` with query params)
+   - getGiveawayDetails(id: string): Observable<Giveaway> (GET `${environment.apiUrl}/giveaways/${id}`)
+   - enterGiveaway(id: string): Observable<any> (POST `${environment.apiUrl}/giveaways/${id}/enter`) - Adjust payload if needed.
+   - (Admin methods if needed) createGiveaway, updateGiveaway, etc.
+4. Handle potential errors using `catchError`.
+*/
+```
+
+### Step 5.3: Implement Feature Components
+
+Generate components needed for the feature (e.g., list, detail, card).
+
+```bash
+ng generate component features/giveaways/giveaway-list --module=features/giveaways
+ng generate component features/giveaways/giveaway-detail --module=features/giveaways
+ng generate component features/giveaways/giveaway-card --module=features/giveaways # Reusable card
+```
+
+**Prompt for `src/app/features/giveaways/giveaway-list/giveaway-list.component.ts`:**
+```typescript
+/*
+Implement GiveawayListComponent:
+1. Inject GiveawayService.
+2. Define properties to hold giveaways (e.g., `giveaways$: Observable<Giveaway[]>`) and pagination state.
+3. In `ngOnInit`, call `giveawayService.getGiveaways()` to fetch initial data. Use async pipe (`| async`) in the template or subscribe manually.
+4. Implement methods for handling pagination changes (e.g., `onPageChange(page: number)`).
+5. In the template (`.html`):
+   - Use `*ngIf` to check if giveaways data is available.
+   - Use `*ngFor` to loop through giveaways.
+   - Use the `app-giveaway-card` component to display each giveaway (`[giveaway]="item"`).
+   - Add pagination controls (e.g., using Angular Material Paginator).
+*/
+```
+
+**Prompt for `src/app/features/giveaways/giveaway-detail/giveaway-detail.component.ts`:**
+```typescript
+/*
+Implement GiveawayDetailComponent:
+1. Inject GiveawayService, ActivatedRoute, AuthService, Router.
+2. In `ngOnInit`:
+   - Get the giveaway ID from route parameters using `ActivatedRoute.paramMap`. Use RxJS `switchMap` to fetch details when ID changes.
+   - Call `giveawayService.getGiveawayDetails()` with the ID. Store the result in a property (e.g., `giveaway$: Observable<Giveaway>`).
+3. Check authentication status using `AuthService.isAuthenticated$`.
+4. In the template (`.html`):
+   - Use `*ngIf` with the async pipe to display giveaway details when available.
+   - Show an "Enter Giveaway" button only if the user is authenticated (`AuthService.isAuthenticated$ | async`) and the giveaway is active (check status/dates).
+5. Implement an `enterGiveaway()` method:
+   - Call `giveawayService.enterGiveaway(giveawayId)`.
+   - Handle success (e.g., show a confirmation message) and errors (e.g., "Already entered", "Not eligible").
+*/
+```
+
+*Repeat Steps 5.1 - 5.3 for Membership, User Profile, and Admin features, adapting the prompts based on the specific backend endpoints and required functionality.*
+
+## 6. Phase 4: Refinement & Testing
+
+*   **Styling:** Apply consistent styling using your chosen UI library (e.g., Angular Material themes) or custom SCSS. Create global styles and component-specific styles.
+*   **Error Handling:**
+    *   Implement a global HTTP error interceptor (`HttpErrorInterceptor`) to catch API errors.
+    *   Display user-friendly messages using a notification/snackbar service (e.g., Angular Material Snackbar).
+    *   Provide specific feedback in components for form validation errors.
+*   **Loading States:**
+    *   Use loading indicators (e.g., `mat-spinner` or custom spinners) while data is being fetched.
+    *   Employ skeleton loaders for a better perceived performance.
+    *   Manage loading state within components or services (e.g., using a boolean flag or a dedicated state property).
+*   **State Management (If using NgRx/NGXS):**
+    *   Define state slices for features (e.g., `AuthState`, `GiveawayState`).
+    *   Create actions for events (e.g., `[Auth API] Login Success`).
+    *   Implement reducers (NgRx) or state classes (NGXS) to handle state transitions.
+    *   Use effects (NgRx) or actions handlers (NGXS) to manage side effects like API calls.
+    *   Create selectors to query specific parts of the state.
+*   **Testing:**
+    *   **Unit Tests (`.spec.ts`):** Use Angular's `TestBed`, Jasmine, and Karma. Mock dependencies (services, HttpClient) using spies or mock classes. Test component logic, service methods, pipes, and guards in isolation.
+    *   **Integration Tests:** Test interactions between components (e.g., parent-child communication).
+    *   **End-to-End (E2E) Tests:** Use frameworks like Cypress or Playwright to simulate user interactions across the application. Test critical user flows (login, registration, entering giveaways).
+
+## 7. Conclusion
+
+This guide provides a roadmap for building the Angular UI. Remember to adapt the prompts and steps based on your specific requirements and chosen libraries. Break down complex features into smaller, manageable components and services. Leverage the Angular CLI and focus on creating a maintainable, testable, and user-friendly application.
+
+```

This guide, `docs/ANGULAR_UI_DEVELOPMENT_GUIDE.md`, has been created in your project folder. It contains the detailed steps and prompts to help you build the Angular frontend.
