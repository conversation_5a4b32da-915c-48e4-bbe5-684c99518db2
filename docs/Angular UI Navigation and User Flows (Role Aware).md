# Angular UI Navigation and User Flows (Role Aware)

This document outlines the primary navigation structure using Angular Router, route guards, and key user flows for the membership-based giveaway platform frontend, designed for Angular.

## 1. Routing Structure (`app.routes.ts` / Feature Routes)

```typescript
// Example app.routes.ts (using standalone component routing)
import { Routes } from '@angular/router';
import { AuthGuard } from './core/guards/auth.guard';
import { AdminGuard } from './core/guards/admin.guard';
import { HomeComponent } from './pages/home/<USER>'; // Assuming a simple home page
import { LoginComponent } from './auth/login/login.component';
import { RegisterComponent } from './auth/register/register.component';
import { ForgotPasswordComponent } from './auth/forgot-password/forgot-password.component';
import { ResetPasswordComponent } from './auth/reset-password/reset-password.component';
import { VerifyEmailComponent } from './auth/verify-email/verify-email.component';
import { MembershipTiersComponent } from './membership/membership-tiers/membership-tiers.component';
import { SubscriptionCheckoutComponent } from './membership/subscription-checkout/subscription-checkout.component';
import { MembershipManagementComponent } from './membership/membership-management/membership-management.component';
import { GiveawayListComponent } from './giveaways/giveaway-list/giveaway-list.component';
import { GiveawayDetailComponent } from './giveaways/giveaway-detail/giveaway-detail.component';
import { DashboardComponent } from './dashboard/dashboard.component';
import { ProfileComponent } from './user-profile/profile/profile.component';
import { SettingsComponent } from './user-profile/settings/settings.component';
import { EntryHistoryComponent } from './user-profile/entry-history/entry-history.component';
import { WinHistoryComponent } from './user-profile/win-history/win-history.component';
import { TransactionHistoryComponent } from './user-profile/transaction-history/transaction-history.component';
import { StaticPageComponent } from './content/static-page/static-page.component';
import { WinnersComponent } from './content/winners/winners.component';
import { NotFoundComponent } from './pages/not-found/not-found.component'; // Generic 404

export const routes: Routes = [
  { path: '', component: HomeComponent, pathMatch: 'full' }, // Or redirect to giveaways/dashboard

  // Auth Routes
  { path: 'login', component: LoginComponent },
  { path: 'register', component: RegisterComponent },
  { path: 'forgot-password', component: ForgotPasswordComponent },
  { path: 'reset-password/:token', component: ResetPasswordComponent },
  { path: 'verify-email/:token', component: VerifyEmailComponent },

  // Membership Routes
  { path: 'membership', component: MembershipTiersComponent },
  {
    path: 'membership/subscribe/:tierId',
    component: SubscriptionCheckoutComponent,
    canActivate: [AuthGuard] // Must be logged in to subscribe
  },
  {
    path: 'membership/manage',
    component: MembershipManagementComponent,
    canActivate: [AuthGuard] // Must be logged in to manage
  },

  // Giveaways Routes
  { path: 'giveaways', component: GiveawayListComponent },
  { path: 'giveaways/:slug', component: GiveawayDetailComponent },

  // Dashboard (Member Area)
  {
    path: 'dashboard',
    component: DashboardComponent,
    canActivate: [AuthGuard] // Must be logged in
  },

  // User Profile Routes
  {
    path: 'profile',
    canActivate: [AuthGuard], // Must be logged in
    children: [
      { path: '', component: ProfileComponent, pathMatch: 'full' },
      { path: 'settings', component: SettingsComponent },
      { path: 'entries', component: EntryHistoryComponent },
      { path: 'wins', component: WinHistoryComponent },
      { path: 'transactions', component: TransactionHistoryComponent },
    ]
  },

  // Content Routes
  { path: 'about', component: StaticPageComponent, data: { pageSlug: 'about' } },
  { path: 'faq', component: StaticPageComponent, data: { pageSlug: 'faq' } },
  { path: 'terms-of-service', component: StaticPageComponent, data: { pageSlug: 'terms-of-service' } },
  { path: 'privacy-policy', component: StaticPageComponent, data: { pageSlug: 'privacy-policy' } },
  { path: 'winners', component: WinnersComponent },

  // Admin Routes (Lazy Loaded)
  {
    path: 'admin',
    loadChildren: () => import('./admin/admin.routes').then(m => m.ADMIN_ROUTES),
    canActivate: [AuthGuard, AdminGuard] // Must be logged in AND be an Admin
  },

  // Wildcard route for 404
  { path: '**', component: NotFoundComponent }
];

// Example admin.routes.ts (for lazy loading)
import { Routes } from '@angular/router';
import { AdminLayoutComponent } from './admin-layout/admin-layout.component';
import { AdminDashboardComponent } from './admin-dashboard/admin-dashboard.component';
// ... import other admin components

export const ADMIN_ROUTES: Routes = [
  {
    path: '',
    component: AdminLayoutComponent, // Wrapper layout with sidebar
    children: [
      { path: '', redirectTo: 'dashboard', pathMatch: 'full' },
      { path: 'dashboard', component: AdminDashboardComponent },
      // ... other admin child routes (users, giveaways, content, etc.)
    ]
  }
];
```

## 2. Navigation Logic (within `ToolbarComponent`)

*   Use `*ngIf="!(authService.isAuthenticated$ | async)"` to show Login/Register buttons.
*   Use `*ngIf="authService.isAuthenticated$ | async"` to show the User Menu button.
*   Inside the User Menu (`<mat-menu>`), use `*ngIf="authService.isAdmin$ | async"` to show the "Admin Dashboard" link.
*   Main navigation links (Giveaways, Membership, etc.) can be always visible or conditionally hidden based on auth status if needed.

## 3. Route Guards

*   **`AuthGuard` (`CanActivateFn` or Class-based `CanActivate`):**
    *   Inject `AuthService` and `Router`.
    *   Check `authService.isAuthenticated()`.
    *   If authenticated, return `true`.
    *   If not authenticated, navigate to `/login` (potentially storing the intended URL for redirect after login) and return `false`.
*   **`AdminGuard` (`CanActivateFn` or Class-based `CanActivate`):**
    *   Inject `AuthService` and `Router`.
    *   Check `authService.isAuthenticated()` AND `authService.isAdmin()`.
    *   If both are true, return `true`.
    *   If not, navigate to `/dashboard` or `/` (or show an unauthorized page) and return `false`.

## 4. Key User Flows (Angular Context)

### 4.1 User Registration Flow

1.  User navigates to `/register`.
2.  `RegisterComponent` displays the form.
3.  User submits form.
4.  `RegisterComponent` calls `AuthService.register(formData)`.
5.  `AuthService` makes API call.
6.  On success, `RegisterComponent` shows a success message (e.g., using `MatSnackBar`) prompting email check.
7.  User clicks email link, navigates to `/verify-email/:token`.
8.  `VerifyEmailComponent` extracts token from `ActivatedRoute`.
9.  `VerifyEmailComponent` calls `AuthService.verifyEmail(token)`.
10. On success, `AuthService` might auto-login the user (optional), `VerifyEmailComponent` shows success message and navigates to `/login` or `/dashboard` using `Router.navigate()`.

### 4.2 User Login Flow

1.  User navigates to `/login`.
2.  `LoginComponent` displays the form.
3.  User submits form.
4.  `LoginComponent` calls `AuthService.login(credentials)`.
5.  `AuthService` makes API call, stores token and user data on success, updates authentication state (e.g., BehaviorSubject `isAuthenticated$`).
6.  `LoginComponent` navigates to `/dashboard` (or intended redirect URL) using `Router.navigate()`.
7.  `ToolbarComponent` updates automatically based on `isAuthenticated$` observable.

### 4.3 Membership Subscription Flow

1.  User navigates to `/membership`.
2.  `MembershipTiersComponent` fetches and displays tiers.
3.  User clicks "Choose Plan" on a tier.
4.  Button click handler checks authentication via `AuthService`. If not logged in, navigates to `/login` (perhaps with `?redirect=/membership/subscribe/[tierId]`). If logged in, navigates to `/membership/subscribe/[tierId]`.
5.  `SubscriptionCheckoutComponent` loads, protected by `AuthGuard`.
6.  Component fetches tier details based on `ActivatedRoute` param.
7.  User interacts with Stripe Elements (or other payment form).
8.  User submits payment.
9.  Component calls `PaymentService.createSubscription()`.
10. On success, `PaymentService` or `AuthService` updates user's membership status. Component shows success message (`MatSnackBar`) and navigates to `/dashboard` or `/membership/manage`.

### 4.4 Entering a Giveaway Flow

1.  Member navigates to `/giveaways`.
2.  `GiveawayListComponent` displays cards.
3.  Member clicks "View Details", navigates to `/giveaways/:slug`.
4.  `GiveawayDetailComponent` loads, fetches giveaway details and user's entry status for this giveaway (e.g., `EntryService.getEntryStatus(slug)`).
5.  Template uses `*ngIf` to show entry status and enable/disable the "Enter Now" button based on eligibility.
6.  Member clicks "Enter Now".
7.  Component calls `EntryService.enterGiveaway(slug)`.
8.  On success, `EntryService` might update local state or refetch entry status. Component shows success message (`MatSnackBar`).

### 4.5 Admin Creating a Giveaway Flow

1.  Admin logs in, navigates to `/admin` (protected by `AuthGuard`, `AdminGuard`).
2.  `AdminLayoutComponent` with `AdminSidebarComponent` is displayed.
3.  Admin navigates to `/admin/giveaways` via sidebar.
4.  `GiveawayListComponent` (Admin version/view) displays "Create New Giveaway" button (`*ngIf="isAdmin()"`).
5.  Admin clicks button, navigates to `/admin/giveaways/new`.
6.  `GiveawayFormComponent` loads (potentially in a dialog opened by the list component, or as a separate route).
7.  Admin fills out the reactive form.
8.  Admin submits form.
9.  Component calls `AdminGiveawayService.createGiveaway(formData)`.
10. On success, navigate back to `/admin/giveaways` or show success message.

### 4.6 Admin Conducting a Draw Flow

1.  Admin navigates to `/admin/giveaways`.
2.  Selects a completed giveaway, navigates to `/admin/giveaways/:giveawayId` (Admin detail view or management page).
3.  Clicks "Conduct Draw" button (`*ngIf="giveaway.status === 'COMPLETED'"`).
4.  Navigates to `/admin/giveaways/:giveawayId/draw`.
5.  `GiveawayDrawComponent` loads, fetches eligible entries and prize details.
6.  Admin clicks "Select Winners" button.
7.  Component calls `AdminGiveawayService.conductDraw(giveawayId)`.
8.  Backend performs draw.
9.  On success, component displays winners, updates giveaway status locally or refetches. Shows success message.

## 5. Considerations

*   **State Management:** Use services with `BehaviorSubject` or libraries like NgRx/Akita/Elf for managing global state (auth status, user profile, membership details).
*   **Error Handling:** Use Angular's `HttpInterceptor` for global error handling. Display user-friendly errors using `MatSnackBar` or dedicated error components.
*   **Loading Indicators:** Use `*ngIf` with async pipe (`| async`) or component properties (e.g., `isLoading`) to show loading indicators (`<mat-progress-spinner>`) during data fetching.
*   **Lazy Loading:** Implement lazy loading for feature modules (especially Admin) to improve initial load time.
