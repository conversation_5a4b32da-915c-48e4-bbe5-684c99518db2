# Admin User Profile Documentation

## Viewing User Profiles

### User List Endpoint

**Endpoint**: `GET /api/admin/dashboard/stats/users`

**Description**: Retrieves a paginated list of all users with optional filtering.

**Authentication Required**: Yes (Admin access only)

### Request Parameters

#### Pagination Parameters
| Parameter | Type | Required | Default | Description |
|-----------|------|----------|---------|-------------|
| `page` | Number | No | 1 | Page number to retrieve |
| `limit` | Number | No | 10 | Number of users per page |

#### Filter Parameters
| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `role` | String | No | Filter by user role (e.g., "USER", "ADMIN", "MODERATOR") |
| `email` | String | No | Filter by email (partial match, case insensitive) |
| `membershipStatus` | String | No | Filter by membership status (e.g., "ACTIVE", "EXPIRED", "CANCELED") |
| `hasActiveMembership` | Boolean | No | Filter users with/without active memberships |
| `isVerified` | Boolean | No | Filter by email verification status |
| `createdAfter` | Date | No | Filter users created after this date (ISO format) |
| `createdBefore` | Date | No | Filter users created before this date (ISO format) |

### Response Format

```json
{
  "success": true,
  "data": [
    {
      "id": "uuid",
      "email": "<EMAIL>",
      "firstName": "John",
      "lastName": "Doe",
      "role": "USER",
      "status": "ACTIVE",
      "membership": {
        "tier": "PREMIUM",
        "status": "ACTIVE",
        "endDate": "2023-12-31T23:59:59Z"
      },
      "createdAt": "2023-01-15T12:30:45Z"
    },
    // More user objects...
  ],
  "meta": {
    "pagination": {
      "page": 1,
      "limit": 10,
      "total": 45,
      "pages": 5
    }
  }
}
```

## Individual User Profile Details

**Endpoint**: `GET /api/admin/users/:id`

**Description**: Retrieves detailed information for a specific user.

**Authentication Required**: Yes (Admin access only)

**Response Format**:
```json
{
  "success": true,
  "data": {
    "id": "uuid",
    "email": "string",
    "firstName": "string",
    "lastName": "string",
    "phoneNumber": "string",
    "birthDate": "date",
    "address": "object",
    "role": "string",
    "status": "string",
    "emailVerified": "boolean",
    "phoneVerified": "boolean",
    "preferences": "object",
    "membership": {
      "id": "uuid",
      "tier": {
        "id": "uuid",
        "name": "string"
      },
      "status": "string",
      "startDate": "timestamp",
      "endDate": "timestamp",
      "renewalDate": "timestamp",
      "membershipHistory": "array"
    },
    "stats": {
      "totalEntries": "number",
      "totalWins": "number",
      "totalSpent": "number"
    },
    "createdAt": "timestamp",
    "updatedAt": "timestamp"
  }
}
```

## Admin Actions on User Profiles

### 1. Update User Information

**Endpoint**: `PUT /api/admin/users/:id`

**Description**: Updates a user's information and status.

**Request Body**:
```json
{
  "firstName": "string",
  "lastName": "string",
  "phoneNumber": "string",
  "birthDate": "date",
  "address": "object",
  "role": "string",
  "status": "string", 
  "emailVerified": "boolean",
  "phoneVerified": "boolean"
}
```

**Response**:
```json
{
  "success": true,
  "data": {
    "id": "uuid",
    "email": "string",
    "firstName": "string",
    "lastName": "string",
    "role": "string",
    "status": "string",
    "updatedAt": "timestamp"
  }
}
```

### 2. Delete User Account

**Endpoint**: `DELETE /api/admin/users/:id`

**Description**: Permanently deletes a user account. Cannot delete your own account or another admin account.

**Response**:
```json
{
  "success": true,
  "data": {
    "id": "uuid",
    "email": "string",
    "firstName": "string",
    "lastName": "string"
  }
}
```

## Adding a New User (Two-Step Process)

Since there's no direct admin endpoint for user creation, use this two-step process:

### Step 1: Register the user

**Endpoint:** `POST /api/auth/register`

**Description:** Registers a new user in the system with default USER role.

**Authentication Required:** No

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "securePassword123",
  "firstName": "John",
  "lastName": "Doe",
  "profileImage": "https://example.com/path/to/image.jpg" // Optional
}
```

**Response Format:**
```json
{
  "success": true,
  "data": {
    "user": {
      "id": "uuid",
      "email": "<EMAIL>",
      "firstName": "John",
      "lastName": "Doe",
      "role": "USER",
      "isVerified": true,
      "createdAt": "2023-07-15T12:30:45Z",
      "updatedAt": "2023-07-15T12:30:45Z"
    },
    "accessToken": "jwt-access-token",
    "refreshToken": "jwt-refresh-token",
    "expiresIn": 3600
  }
}
```

### Step 2: Update the user's role (if needed)

**Endpoint:** `PUT /api/admin/users/:id`

**Description:** Updates a user's information, including role.

**Authentication Required:** Yes (Admin access only)

**Request Body:**
```json
{
  "role": "ADMIN", // Or any other role
  "status": "ACTIVE",
  "emailVerified": true
}
```

## User-Related Information Tabs

When viewing a user profile, admins should be able to see additional tabs with related information:

### 1. Membership Information
- Current membership status (ACTIVE, CANCELLED, EXPIRED)
- Membership tier details
- Start date, end date, renewal date
- Auto-renewal status
- Membership history

### 2. Transaction History
- List of all transactions
- Payment status
- Amount
- Date
- Transaction type

### 3. Giveaway Entries
- All giveaways entered
- Entry quantity
- Entry method
- Entry date

### 4. Wins History
- Giveaways won
- Prize details
- Claim status
- Win date

## Admin Actions UI Components

Your admin UI should include the following components:

1. **User Status Toggle**: Change user status between active/inactive
2. **Role Assignment Dropdown**: Change user role (USER, ADMIN, MODERATOR)
3. **Edit Profile Button**: Open form to edit user information
4. **Delete Account Button**: With confirmation dialog
5. **Email Verification Toggle**: Mark email as verified/unverified
6. **View Transactions Button**: Navigate to user's transaction history
7. **Membership Actions**: 
   - Extend membership
   - Cancel membership
   - Change membership tier
8. **Activity Log**: Recent actions and logins

## Implementation Notes

- All actions require admin authentication
- SuperAdmin role has additional privileges
- Deleted user accounts cannot be recovered
- Some actions (like deleting another admin) have restrictions
- Include proper confirmation dialogs for destructive actions
- Password requirements:
  - Minimum 8 characters
  - Contains at least one uppercase letter
  - Contains at least one lowercase letter
  - Contains at least one number
  - Contains at least one special character

## Error Responses

### Unauthorized (401)
```json
{
  "success": false,
  "message": "Authentication required"
}
```

### Forbidden (403)
```json
{
  "success": false,
  "message": "Admin access required"
}
```

### Bad Request (400)
```json
{
  "success": false,
  "message": "Error message details"
}
```

### Not Found (404)
```json
{
  "success": false,
  "message": "User with ID {id} not found"
}
``` 