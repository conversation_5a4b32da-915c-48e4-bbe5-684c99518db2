# Giveaway System - Automatic Enrollment Process Requirements

## Overview

This document outlines the requirements for implementing an automatic enrollment process for the Winners Society giveaway system. The system should automatically enroll eligible members into active giveaways based on their membership status and tier, eliminating the need for manual entry.

## Table of Contents

1. [Business Requirements](#business-requirements)
2. [Functional Requirements](#functional-requirements)
3. [Technical Requirements](#technical-requirements)
4. [Database Changes](#database-changes)
5. [API Endpoints](#api-endpoints)
6. [Implementation Guidelines](#implementation-guidelines)
7. [Testing Requirements](#testing-requirements)

## Business Requirements

### Automatic Enrollment Goals

1. **Streamline User Experience**: Eliminate the need for members to manually enter each giveaway
2. **Increase Participation**: Ensure all eligible members are automatically included in giveaways
3. **Reward Membership Tiers**: Allocate entries based on membership tier benefits
4. **Maintain Fairness**: Apply consistent rules for entry allocation across all members

### User Scenarios

1. **Existing Members**: When a new giveaway becomes active, all eligible existing members should be automatically entered
2. **New Members**: When a user purchases a membership, they should be automatically entered into all currently active giveaways for which they are eligible
3. **Membership Upgrades**: When a member upgrades their membership tier, they should receive additional entries for active giveaways based on their new tier's allocation

## Functional Requirements

### Eligibility Criteria

For a member to be automatically enrolled in a giveaway, they must meet ALL of the following criteria:

1. Have an **ACTIVE** membership status
2. Have a membership tier that is included in the giveaway's `eligibleMembershipTierIds` list
3. Have a verified email address
4. Not have been previously entered into the giveaway (to prevent duplicates)

### Entry Allocation

1. **Single Entry Record with Weight**: Each eligible member receives a single entry record with a `numberOfEntries` field that corresponds to their membership tier's `entryAllocation` value
2. For example, if Basic tier has 29 entries, Premium has 49, and VIP has 99, a VIP member would have one entry record with `numberOfEntries = 99`
3. Each entry should be recorded with the source "MEMBERSHIP" to distinguish from other entry methods
4. The entry date should be the current timestamp when the entry is created
5. If a user already has an entry and becomes eligible for additional entries (e.g., through tier upgrade), the existing entry's `numberOfEntries` should be updated rather than creating a new record

### Enrollment Triggers

The automatic enrollment process should be triggered by the following events:

#### For Existing Members:

1. When a giveaway status changes to **ACTIVE**
   - A background job should identify all eligible members
   - Create entry records in bulk for all eligible members

#### For New Members:

1. When a user completes signup and activates a membership:
   - System should identify all currently **ACTIVE** giveaways
   - Filter for giveaways where the user's new membership tier is eligible
   - Create entry records for each eligible giveaway

#### For Membership Upgrades:

1. When a member upgrades their membership tier:
   - System should identify all currently **ACTIVE** giveaways
   - For each giveaway where the new tier has a higher `entryAllocation` than the previous tier
   - Create additional entry records to match the new tier's allocation

## Technical Requirements

### Background Job for Giveaway Activation

When a giveaway is activated (status changes to **ACTIVE**), a background job should:

1. Query all users with:
   - `membership.status = 'ACTIVE'`
   - `membership.tierId` in giveaway's `eligibleMembershipTierIds`
   - `user.isEmailVerified = true`

2. For each eligible user:
   - Check if an entry already exists for this user and giveaway
   - If no entry exists, create a new entry with:
     - `userId`: User's ID
     - `giveawayId`: Activated giveaway's ID
     - `numberOfEntries`: User's membership tier's `entryAllocation` value (e.g., 29, 49, or 99)
     - `source`: "MEMBERSHIP"
     - `entryDate`: Current timestamp
   - If an entry already exists (e.g., from a previous manual entry), update the existing entry:
     - Set `numberOfEntries` to the maximum of current value or the tier's `entryAllocation` value
     - Update `source` to "MEMBERSHIP_UPDATED" if it was previously a different source

3. Process in batches to avoid memory issues with large user bases
4. Implement retry logic for failed entries
5. Log completion statistics (users processed, entries created, errors)

### Membership Activation/Upgrade Handler

When a membership is activated or upgraded, a handler should:

1. Query all **ACTIVE** giveaways where:
   - The user's new `membership.tierId` is in giveaway's `eligibleMembershipTierIds`

2. For each eligible giveaway:
   - Check if an entry already exists for this user and giveaway
   - If no entry exists, create a new entry with:
     - `userId`: User's ID
     - `giveawayId`: Giveaway ID
     - `numberOfEntries`: User's new membership tier's `entryAllocation` value
     - `source`: "MEMBERSHIP"
     - `entryDate`: Current timestamp
   - If an entry already exists and this is an upgrade:
     - Update the existing entry's `numberOfEntries` to the new tier's allocation value
     - Add a record to the audit log indicating the entry was upgraded
     - Update the `source` field to "MEMBERSHIP_UPGRADED" to track the upgrade

3. This process should be part of the membership activation/upgrade transaction
4. Implement error handling to ensure membership activation succeeds even if giveaway entry fails

## Database Changes

### Giveaway Table Updates

Add the following fields to the `Giveaway` table:

1. `eligibleMembershipTierIds` (Array of Strings)
   - Purpose: Store the IDs of membership tiers eligible for this giveaway
   - Default: Empty array (no restrictions, all tiers eligible)
   - Example: `["tier_basic", "tier_premium", "tier_vip"]`

2. `autoEnrollment` (Boolean)
   - Purpose: Flag to enable/disable automatic enrollment for this giveaway
   - Default: `true`
   - Note: This allows administrators to make some giveaways manual-entry only

3. `lastEnrollmentJobId` (String)
   - Purpose: Reference to the last enrollment job run for this giveaway
   - Used to check job status and retrieve error information

### Entry Table Updates

Ensure the `Entry` table has the following fields to support the single-entry-with-weight model:

1. `userId` (String)
   - Purpose: Reference to the user
   - Constraint: Unique combination with `giveawayId` to ensure one entry per user per giveaway

2. `giveawayId` (String)
   - Purpose: Reference to the giveaway
   - Constraint: Unique combination with `userId` to ensure one entry per user per giveaway

3. `numberOfEntries` (Integer)
   - Purpose: Store the weight/number of entries allocated to this user
   - Default: Based on membership tier's `entryAllocation`
   - Example values: 29 for Basic tier, 49 for Premium tier, 99 for VIP tier
   - This field is the key to implementing the weighted random selection

4. `source` (String)
   - Purpose: Track the source of the entry
   - Possible values: "MEMBERSHIP", "MEMBERSHIP_UPGRADED", "MEMBERSHIP_UPDATED", "MANUAL", "REFERRAL", etc.
   - Default: "MEMBERSHIP" for auto-enrolled entries

5. `entryDate` (DateTime)
   - Purpose: When the entry was created
   - Used for sorting and filtering entries

6. `lastUpdatedDate` (DateTime)
   - Purpose: When the entry was last updated (e.g., due to tier upgrade)
   - Helps track entry modifications

7. `createdByUserId` (String, nullable)
   - Purpose: Track which admin created the entry (for manual entries)
   - Null for automatic entries

8. `notes` (String, nullable)
   - Purpose: Admin notes for manual entries
   - Useful for audit and explaining why manual enrollment was needed

9. `membershipTierId` (String, nullable)
   - Purpose: Reference to the membership tier that determined the entry allocation
   - Helps track which tier was used for allocation

### MembershipTier Table Updates

Ensure the `MembershipTier` table has:

1. `entryAllocation` (Integer)
   - Purpose: Define how many entries members of this tier receive for giveaways
   - Example values: Basic: 1, Premium: 5, VIP: 10

### New Tables

#### FailedEnrollment Table

Create a new table to track failed enrollment attempts:

1. `id` (String) - Primary key
2. `giveawayId` (String) - Reference to the giveaway
3. `userId` (String) - Reference to the user
4. `membershipTierId` (String) - Reference to the membership tier
5. `jobId` (String) - Reference to the enrollment job
6. `errorMessage` (String) - Detailed error message
7. `errorCode` (String) - Error code for categorization
8. `attemptDate` (DateTime) - When the enrollment was attempted
9. `status` (String) - Status of the failed enrollment (PENDING, RETRIED, MANUALLY_RESOLVED)
10. `resolvedDate` (DateTime, nullable) - When the issue was resolved
11. `resolvedByUserId` (String, nullable) - Admin who resolved the issue
12. `resolutionMethod` (String, nullable) - How the issue was resolved (RETRY, MANUAL_ENROLLMENT, SKIPPED)

#### EnrollmentAuditLog Table

Create a new table for audit logging of enrollment activities:

1. `id` (String) - Primary key
2. `giveawayId` (String) - Reference to the giveaway
3. `userId` (String, nullable) - Reference to the user (null for batch operations)
4. `adminId` (String, nullable) - Reference to the admin who performed the action
5. `action` (String) - The action performed (AUTO_ENROLLMENT, MANUAL_ENROLLMENT, RETRY_ENROLLMENT)
6. `details` (JSON) - Additional details about the action
7. `timestamp` (DateTime) - When the action occurred
8. `affectedUserCount` (Integer, default: 1) - Number of users affected (for batch operations)
9. `status` (String) - Outcome of the action (SUCCESS, PARTIAL_SUCCESS, FAILURE)

## API Endpoints

### New Admin Endpoints

1. **Configure Eligible Tiers for Giveaway**
   - **Method**: PUT
   - **URL**: `/api/admin/giveaways/:id/eligible-tiers`
   - **Request Body**:
     ```json
     {
       "eligibleMembershipTierIds": ["tier_id_1", "tier_id_2"]
     }
     ```
   - **Response**: Updated giveaway object

2. **Toggle Auto-Enrollment for Giveaway**
   - **Method**: PUT
   - **URL**: `/api/admin/giveaways/:id/auto-enrollment`
   - **Request Body**:
     ```json
     {
       "autoEnrollment": true|false
     }
     ```
   - **Response**: Updated giveaway object

3. **Manually Trigger Enrollment Process for All Eligible Users**
   - **Method**: POST
   - **URL**: `/api/admin/giveaways/:id/trigger-enrollment`
   - **Response**: Job status and summary of entries created

4. **Manually Enroll Specific User**
   - **Method**: POST
   - **URL**: `/api/admin/giveaways/:id/entries`
   - **Request Body**:
     ```json
     {
       "userId": "user_id",
       "membershipTierId": "tier_id",
       "numberOfEntries": 49,
       "updateStrategy": "REPLACE_OR_CREATE",
       "source": "MANUAL",
       "notes": "Manually enrolled by admin due to system error"
     }
     ```
   - **Notes**:
     - `updateStrategy` can be:
       - `REPLACE_OR_CREATE`: Replace existing entry or create new one
       - `ADD_TO_EXISTING`: Add to existing entry weight if one exists
       - `ONLY_IF_NOT_EXISTS`: Only create if user doesn't already have an entry
   - **Response**: Created or updated entry object with current weight value

5. **Get Failed Enrollments**
   - **Method**: GET
   - **URL**: `/api/admin/giveaways/:id/failed-enrollments`
   - **Query Parameters**:
     - `page` (optional): Page number
     - `limit` (optional): Items per page
     - `errorType` (optional): Filter by error type
     - `membershipTierId` (optional): Filter by membership tier
   - **Response**: List of failed enrollments with error details

6. **Retry Failed Enrollments**
   - **Method**: POST
   - **URL**: `/api/admin/giveaways/:id/retry-failed-enrollments`
   - **Request Body**:
     ```json
     {
       "failedEnrollmentIds": ["id1", "id2", "id3"]
     }
     ```
   - **Response**: Job status and summary of retried enrollments

7. **Get Enrollment Audit Log**
   - **Method**: GET
   - **URL**: `/api/admin/giveaways/:id/enrollment-logs`
   - **Query Parameters**:
     - `page` (optional): Page number
     - `limit` (optional): Items per page
     - `startDate` (optional): Filter by start date
     - `endDate` (optional): Filter by end date
     - `enrollmentType` (optional): Filter by type (AUTO, MANUAL)
     - `userId` (optional): Filter by user ID
   - **Response**: List of enrollment log entries

8. **Export Enrollment Audit Log**
   - **Method**: GET
   - **URL**: `/api/admin/giveaways/:id/enrollment-logs/export`
   - **Query Parameters**: Same as Get Enrollment Audit Log
   - **Response**: CSV file download

### Updated Endpoints

1. **Create Giveaway**
   - Update to include `eligibleMembershipTierIds` and `autoEnrollment` fields

2. **Update Giveaway**
   - Update to allow modification of `eligibleMembershipTierIds` and `autoEnrollment` fields

3. **Get Giveaway Entries**
   - Update to include filter for entry source (MEMBERSHIP, MANUAL, etc.)
   - Add filter for user information (email, name, ID)
   - Include membership tier information in response

## Winner Selection Algorithm

To support the single-entry-with-weight model, the winner selection process must use a weighted random selection algorithm that properly accounts for the `numberOfEntries` field.

### Weighted Random Selection

1. **Single Winner Selection**:
   ```typescript
   function selectWinner(entries: Entry[]): Entry {
     // Calculate total weight (sum of all numberOfEntries)
     const totalWeight = entries.reduce((sum, entry) => sum + entry.numberOfEntries, 0);

     // Generate a random number between 0 and totalWeight
     const randomNum = Math.random() * totalWeight;

     // Find the winning entry
     let weightSum = 0;
     for (const entry of entries) {
       weightSum += entry.numberOfEntries;
       if (randomNum < weightSum) {
         return entry;
       }
     }

     // Fallback (should never reach here if implemented correctly)
     return entries[entries.length - 1];
   }
   ```

2. **Multiple Winner Selection**:
   For selecting multiple winners, use a weighted reservoir sampling algorithm:

   ```typescript
   function selectMultipleWinners(entries: Entry[], count: number): Entry[] {
     // Handle edge cases
     if (entries.length <= count) return [...entries];
     if (count <= 0) return [];

     // Initialize reservoir with first 'count' entries
     const reservoir: Entry[] = [];
     const weights: number[] = [];

     // Fill reservoir initially
     for (let i = 0; i < count; i++) {
       reservoir.push(entries[i]);
       weights.push(entries[i].numberOfEntries);
     }

     let weightSum = weights.reduce((a, b) => a + b, 0);

     // Process remaining entries
     for (let i = count; i < entries.length; i++) {
       weightSum += entries[i].numberOfEntries;

       // Randomly decide whether to include this entry in reservoir
       const randomWeight = Math.random() * weightSum;
       let weightSoFar = 0;

       for (let j = 0; j < count; j++) {
         weightSoFar += weights[j];
         if (randomWeight < weightSoFar) {
           // Replace this entry in the reservoir
           reservoir[j] = entries[i];
           weights[j] = entries[i].numberOfEntries;
           break;
         }
       }
     }

     return reservoir;
   }
   ```

3. **Database Implementation**:
   ```typescript
   async function conductDraw(giveawayId: string, winnerCount: number): Promise<Winner[]> {
     // Use a database transaction to ensure consistency
     return await db.transaction(async (tx) => {
       // Get all eligible entries
       const entries = await tx.entry.findMany({
         where: {
           giveawayId,
           isDisqualified: false
         },
         include: { user: true }
       });

       // Select winners using weighted algorithm
       const winningEntries = selectMultipleWinners(entries, winnerCount);

       // Create winner records
       const winners = await Promise.all(
         winningEntries.map(entry =>
           tx.winner.create({
             data: {
               giveawayId,
               userId: entry.userId,
               entryId: entry.id,
               selectionDate: new Date(),
               status: 'SELECTED'
             }
           })
         )
       );

       // Update giveaway status
       await tx.giveaway.update({
         where: { id: giveawayId },
         data: { status: 'COMPLETED' }
       });

       return winners;
     });
   }
   ```

## Implementation Guidelines

### Background Job Implementation

1. **Technology**:
   - Use a reliable job queue system (e.g., Bull, Agenda, or native cloud services)
   - Ensure jobs are persistent and can survive server restarts

2. **Performance Considerations**:
   - Process users in batches (e.g., 100-500 users per batch)
   - Implement database transactions for batch inserts
   - Add indexes to improve query performance:
     - `membership.status`
     - `membership.tierId`
     - `user.isEmailVerified`
     - Unique compound index on `userId` and `giveawayId` in the Entry table to enforce single entry per user

3. **Database Optimization for Single-Entry-With-Weight Model**:
   - Create the following indexes to optimize the weighted selection process:
     - Index on `Entry.giveawayId` for quick filtering of entries by giveaway
     - Index on `Entry.numberOfEntries` to optimize sorting and aggregation
     - Index on `Entry.source` for filtering by entry source
     - Composite index on `(giveawayId, userId, numberOfEntries)` for optimized winner selection queries
   - Implement database-level constraints:
     - Unique constraint on `(userId, giveawayId)` to enforce single entry per user per giveaway
     - Check constraint to ensure `numberOfEntries > 0`
   - Consider partitioning large entry tables by giveaway ID for very large-scale implementations

4. **Error Handling**:
   - Implement retry logic with exponential backoff
   - Log detailed error information
   - Create a separate error queue for failed entries that need manual review
   - Send notifications to administrators for systemic failures

### Membership Integration

1. **Membership Activation Hook**:
   - Add a hook in the membership activation process to trigger giveaway enrollment
   - This should run after the membership is successfully activated
   - Implement as an event listener or direct service call

2. **Membership Upgrade Hook**:
   - Add a hook in the membership upgrade process to trigger additional entries
   - Compare old and new tier's `entryAllocation` values
   - Only create additional entries if the new allocation is higher

3. **Membership Cancellation/Expiration**:
   - When a membership is cancelled or expires, entries should remain valid
   - Future giveaways should not include this user until they reactivate

### Admin UI Updates

1. **Giveaway Creation/Edit Form**:
   - Add multi-select field for eligible membership tiers
   - Add toggle for auto-enrollment
   - Add help text explaining the automatic enrollment process

2. **Giveaway Details View**:
   - Add section showing eligible tiers and auto-enrollment status
   - Add button to manually trigger enrollment process for all eligible users
   - Show statistics on auto-enrolled entries vs. manually enrolled entries
   - Display enrollment job history with status and error information

3. **Entry Management View**:
   - Add filter for entry source (MEMBERSHIP vs. other sources)
   - Display entry source in the entries table
   - Add column prominently showing entry weight (`numberOfEntries`) for each user
   - Include visual representation of relative entry weight (e.g., progress bar)
   - Display percentage of total entries for each user
   - Include user membership tier information
   - Show clear statistics:
     - Total number of unique users entered
     - Total number of weighted entries across all users
     - Average entries per user
     - Distribution of entries by membership tier (chart)
   - Implement sorting by entry weight
   - Add tooltip explaining the single-entry-with-weight model

4. **Manual Enrollment Interface**:
   - Add "Manually Enroll User" button to giveaway details page
   - Create a modal/form for manual enrollment with:
     - User search field (by email, name, or ID)
     - Dropdown to select membership tier to use for entry allocation
     - Field to specify the number of entries (weight) to allocate:
       - Default to the selected tier's `entryAllocation` value
       - Allow admin to override with custom value
     - Clear explanation that this creates/updates a single weighted entry
     - Warning if user already has an entry (showing current entry weight)
     - Option to replace or add to existing entry weight
     - Notes field for admin comments on manual enrollment
   - Display confirmation before submitting
   - Show success/failure notification after submission

5. **Failed Enrollments Management**:
   - Create a dedicated section for viewing failed automatic enrollments
   - List users who failed to be automatically enrolled with error reasons
   - Provide bulk action to retry failed enrollments
   - Add button to manually enroll individual failed users
   - Include filtering by error type, date, and membership tier

6. **Enrollment Audit Log**:
   - Add a log view showing all enrollment activities
   - Include automatic and manual enrollment events
   - Show administrator who performed manual enrollments
   - Allow filtering by date range, enrollment type, and user
   - Enable export of audit log to CSV

## Testing Requirements

### Unit Tests

1. Test the eligibility determination logic:
   - Test with various combinations of membership status, tier, and email verification
   - Test edge cases (no eligible tiers, all tiers eligible)

2. Test the entry allocation logic:
   - Verify correct number of entries based on membership tier
   - Test with various tier configurations

### Integration Tests

1. Test the giveaway activation flow:
   - Verify background job is triggered when giveaway status changes to ACTIVE
   - Verify correct entries are created for eligible users

2. Test the membership activation flow:
   - Verify entries are created when a new membership is activated
   - Test with various giveaway configurations

3. Test the membership upgrade flow:
   - Verify additional entries are created when a membership is upgraded
   - Test with various tier combinations

### Performance Tests

1. Test with large user bases:
   - Simulate 10,000+ users with active memberships
   - Measure time to complete enrollment process
   - Verify system stability under load

2. Test batch processing:
   - Verify batching works correctly with large datasets
   - Test memory usage during batch processing

## Monitoring and Maintenance

1. **Monitoring Requirements**:
   - Track job execution time and success rate
   - Monitor database performance during batch operations
   - Set up alerts for job failures or excessive execution time

2. **Maintenance Tasks**:
   - Regularly review and clean up failed jobs
   - Optimize database indexes based on query patterns
   - Archive old entry data for completed giveaways

## Implementation Phases

1. **Phase 1: Database Schema Updates**
   - Add new fields to Giveaway, Entry, and MembershipTier tables
   - Create necessary indexes
   - Update API models and validation

2. **Phase 2: Background Job Implementation**
   - Implement job queue infrastructure
   - Create enrollment job for giveaway activation
   - Add admin endpoints for manual triggering

3. **Phase 3: Membership Integration**
   - Implement hooks in membership activation/upgrade processes
   - Add entry creation logic for new members

4. **Phase 4: Admin UI Updates**
   - Update giveaway creation/edit forms
   - Add UI elements for managing auto-enrollment
   - Update entry management views

5. **Phase 5: Testing and Optimization**
   - Conduct all required tests
   - Optimize performance based on test results
   - Deploy to production
