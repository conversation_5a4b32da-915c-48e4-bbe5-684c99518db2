# Service Layer Prompts for GitHub Copilot

These prompts are designed to help you create the service layer for your membership-based giveaway platform. Each prompt focuses on a specific service implementation, following best practices for working with GitHub Copilot.

## Implementation Guidelines

When implementing services, follow these guidelines to ensure robust, type-safe code:

```
// Key guidelines for all service implementations:
// 1. ENUM USAGE:
//    - Always import and use enum types directly from Prisma models (e.g., Role.USER not 'USER')
//    - Never use string literals or type assertions for enum values
//
// 2. TYPE SAFETY:
//    - Avoid using 'any' or unsafe type assertions (as any)
//    - Use proper type assertions with explicit types: as unknown as SpecificType
//    - Define complete interfaces/types for all DTOs
//
// 3. DATABASE CONSISTENCY:
//    - Ensure field names match between DTOs, service interfaces, and database schema
//    - Convert nullable fields properly (e.g., userData.profileImage || null)
//
// 4. ERROR HANDLING:
//    - Implement try/catch blocks for all database operations
//    - Use specific error types (NotFoundError, ValidationError, etc.)
//    - Log detailed errors but return appropriate user-facing messages
//
// 5. VALIDATION:
//    - Validate all input data before database operations
//    - Check for required fields early in methods
//    - Add proper guards against null/undefined values
//
// 6. REAL IMPLEMENTATION:
//    - Avoid using mock data in production services
//    - Implement actual database operations instead of stub methods
//    - Use repository pattern consistently
```

### Error Handling Patterns

To avoid code duplication while maintaining robust error handling, implement these patterns:

```
// Common error handling patterns to implement:
//
// 1. CENTRALIZED ERROR CLASSES:
//    Define all error types in a central utils/errors.ts file:
//    - ValidationError: For invalid input data
//    - NotFoundError: For resources that don't exist
//    - AuthenticationError: For auth failures
//    - AuthorizationError: For permission issues
//    - BusinessLogicError: For business rule violations
//    - RepositoryError: For database operation failures
//
// 2. ERROR HANDLER UTILITIES:
//    Create helper functions to standardize error handling:
//    ```
//    // utils/db-error-handler.ts
//    export function handleDatabaseError(error: any, entityName: string): Error {
//      if (error.code === 'P2002') 
//        return new ValidationError(`${entityName} with this identifier already exists`);
//      if (error.code === 'P2025') 
//        return new NotFoundError(`${entityName} not found`);
//      return error;
//    }
//    ```
//
// 3. REPOSITORY ABSTRACTION:
//    Handle common DB errors in the base repository class:
//    ```
//    // Base repository implementation
//    protected async executeQuery<T>(
//      query: () => Promise<T>,
//      errorMessage: string
//    ): Promise<T> {
//      try {
//        return await query();
//      } catch (error) {
//        this.handleError(error, errorMessage);
//      }
//    }
//    ```
//
// 4. HIGHER-ORDER FUNCTIONS:
//    Use wrapper functions for operations with standard error handling:
//    ```
//    async function withErrorHandling<T>(
//      operation: () => Promise<T>, 
//      entityName: string
//    ): Promise<T> {
//      try {
//        return await operation();
//      } catch (error) {
//        throw handleDatabaseError(error, entityName);
//      }
//    }
//    ```
//
// 5. ERRORS TO HANDLE:
//    At minimum, handle these error types in each service:
//    - Database errors: Unique constraints, foreign keys, not found
//    - Validation errors: Before database operations
//    - Authentication/authorization errors: For secure operations
//    - Business logic errors: Application-specific rules
//    - External service errors: For third-party integrations
```

## 1. Create Base Service Interface

```
// I need to create a generic service interface for my membership platform.
// This will define the standard operations that all services should implement.
// Please create a src/services/base.service.ts file with:
// - A generic interface for basic service operations
// - Type parameters for the entity and DTO types
// - Methods for get, list, create, update, and delete
// - Support for pagination and filtering
```

## 2. Authentication Service Interface

```
// I need to create an authentication service interface.
// Please create a src/services/auth/auth-service.interface.ts file with:
// - Methods for user registration
// - Methods for user login with JWT token generation
// - Methods for password reset
// - Methods for email verification
// - Methods for token refresh
// - Methods for logout (token invalidation)
```

## 3. Authentication Service Implementation

```
// I need to implement the authentication service.
// Please create a src/services/auth/auth.service.ts file that:
// - Implements the auth service interface
// - Uses the user repository for data access
// - Implements password hashing with bcrypt
// - Generates JWT tokens for authentication
// - Handles email verification
// - Includes proper validation and error handling
// - Uses proper Prisma enums (e.g., Role.USER) instead of string literals
// - Avoids unsafe type assertions and uses proper type safety
// - Implements try/catch blocks for all database operations
// - Validates all required input fields before processing
// - Converts nullable fields like profileImage properly (value || null)
// - Uses actual database operations instead of mock data
```

## 4. User Service Interface

```
// I need to create a user service interface.
// Please create a src/services/user/user-service.interface.ts file with:
// - Methods for getting user profile
// - Methods for updating user profile
// - Methods for changing password
// - Methods for managing user preferences
// - Methods for retrieving user's entries and wins
```

## 5. User Service Implementation

```
// I need to implement the user service.
// Please create a src/services/user/user.service.ts file that:
// - Implements the user service interface
// - Uses the user repository for data access
// - Includes validation for all user operations
// - Handles profile image uploads (if applicable)
// - Implements proper error handling
// - Uses proper Prisma enums (e.g., Role.USER) instead of string literals
// - Avoids unsafe type assertions and uses explicit types
// - Implements try/catch blocks for all database operations
// - Validates all required input fields before processing
// - Handles JSON fields like preferences with proper type assertions
```

## 6. Membership Service Interface

```
// I need to create a membership service interface.
// Please create a src/services/membership/membership-service.interface.ts file with:
// - Methods for getting available membership tiers
// - Methods for subscribing to a tier
// - Methods for canceling membership
// - Methods for updating auto-renew settings
// - Methods for upgrading membership
// - Methods for getting membership history
```

## 7. Membership Service Implementation

```
// I need to implement the membership service.
// Please create a src/services/membership/membership.service.ts file that:
// - Implements the membership service interface
// - Uses the membership and membership tier repositories
// - Integrates with the payment service for processing payments
// - Handles membership history tracking
// - Implements proper validation and error handling
// - Uses proper Prisma enums instead of string literals
// - Implements try/catch blocks for all database operations with specific error types
// - Validates all required fields before processing requests
// - Uses proper type safety with no 'any' type assertions
```

## 8. Giveaway Service Interface

```
// I need to create a giveaway service interface.
// Please create a src/services/giveaway/giveaway-service.interface.ts file with:
// - Methods for listing active, upcoming, and past giveaways
// - Methods for getting giveaway details
// - Methods for creating and updating giveaways (admin)
// - Methods for entering giveaways
// - Methods for conducting draws and selecting winners (admin)
```

## 9. Giveaway Service Implementation

```
// I need to implement the giveaway service.
// Please create a src/services/giveaway/giveaway.service.ts file that:
// - Implements the giveaway service interface
// - Uses the giveaway, prize, entry, and winner repositories
// - Implements entry allocation based on membership tier
// - Includes a fair algorithm for winner selection
// - Handles validation for entry eligibility
// - Implements proper error handling
// - Uses proper Prisma enums for status and type fields
// - Validates all required input fields before processing
// - Implements try/catch blocks with specific error types
// - Uses proper type safety with no unsafe type assertions
```

## 10. Prize Service Interface

```
// I need to create a prize service interface.
// Please create a src/services/prize/prize-service.interface.ts file with:
// - Methods for getting prizes for a giveaway
// - Methods for getting prize details
// - Methods for creating and updating prizes (admin)
// - Methods for managing prize inventory
```

## 11. Prize Service Implementation

```
// I need to implement the prize service.
// Please create a src/services/prize/prize.service.ts file that:
// - Implements the prize service interface
// - Uses the prize repository for data access
// - Handles prize image uploads (if applicable)
// - Implements inventory management
// - Includes proper validation and error handling
// - Uses proper Prisma enums for prize types/categories
// - Validates all required fields before processing
// - Implements try/catch blocks for all database operations
// - Uses proper type safety with explicit type assertions when needed
```

## 12. Winner Service Interface

```
// I need to create a winner service interface.
// Please create a src/services/winner/winner-service.interface.ts file with:
// - Methods for getting winners for a giveaway
// - Methods for getting winner details
// - Methods for claiming prizes
// - Methods for updating winner status (admin)
```

## 13. Winner Service Implementation

```
// I need to implement the winner service.
// Please create a src/services/winner/winner.service.ts file that:
// - Implements the winner service interface
// - Uses the winner, prize, and user repositories
// - Handles the prize claiming process
// - Integrates with notification service for winner notifications
// - Implements proper validation and error handling
// - Uses proper Prisma enums for status fields
// - Validates all required input fields before processing
// - Implements try/catch blocks with specific error types
// - Uses repository pattern consistently and avoids mock data
```

## 14. Payment Service Interface

```
// I need to create a payment service interface.
// Please create a src/services/payment/payment-service.interface.ts file with:
// - Methods for creating payment intents
// - Methods for processing payments
// - Methods for handling webhooks from payment provider
// - Methods for getting transaction history
// - Methods for processing refunds (admin)
```

## 15. Payment Service Implementation

```
// I need to implement the payment service with Stripe.
// Please create a src/services/payment/stripe-payment.service.ts file that:
// - Implements the payment service interface
// - Integrates with Stripe API for payment processing
// - Uses the transaction repository for storing payment records
// - Handles webhook events from Stripe
// - Implements proper error handling for payment failures
// - Uses proper Prisma enums for payment status and types
// - Implements try/catch blocks for all third-party API calls
// - Validates all required input fields before processing
// - Uses proper type safety with explicit typings for Stripe objects
```

## 16. Content Service Interface

```
// I need to create a content service interface.
// Please create a src/services/content/content-service.interface.ts file with:
// - Methods for getting content by slug
// - Methods for listing content by category
// - Methods for creating and updating content (admin)
// - Methods for managing content access levels
```

## 17. Content Service Implementation

```
// I need to implement the content service.
// Please create a src/services/content/content.service.ts file that:
// - Implements the content service interface
// - Uses the content repository for data access
// - Handles content access control based on membership
// - Implements proper validation and error handling
// - Uses proper Prisma enums for content types and status
// - Validates all required fields before processing
// - Implements try/catch blocks with specific error types
// - Uses proper type safety with no 'any' type assertions
```

## 18. Notification Service Interface

```
// I need to create a notification service interface.
// Please create a src/services/notification/notification-service.interface.ts file with:
// - Methods for creating notifications
// - Methods for getting user notifications
// - Methods for marking notifications as read
// - Methods for sending email notifications
```

## 19. Notification Service Implementation

```
// I need to implement the notification service.
// Please create a src/services/notification/notification.service.ts file that:
// - Implements the notification service interface
// - Uses the notification repository for data access
// - Integrates with an email service for sending emails
// - Implements templating for notification messages
// - Handles notification preferences
// - Uses proper Prisma enums for notification types and status
// - Implements try/catch blocks for all email service calls
// - Validates all required fields before sending notifications
// - Uses proper type safety with explicit typings
```

## 20. Admin Service Interface

```
// I need to create an admin service interface.
// Please create a src/services/admin/admin-service.interface.ts file with:
// - Methods for getting dashboard statistics
// - Methods for user management
// - Methods for transaction management
// - Methods for generating reports
```

## 21. Admin Service Implementation

```
// I need to implement the admin service.
// Please create a src/services/admin/admin.service.ts file that:
// - Implements the admin service interface
// - Uses various repositories for data access
// - Implements dashboard statistics calculation
// - Includes report generation functionality
// - Implements proper validation and error handling
// - Uses proper Prisma enums for all status and role fields
// - Validates admin permissions before processing requests
// - Implements try/catch blocks with specific error types
// - Uses proper type safety with explicit typings for complex objects
```

## 22. Service Module

```
// I need to create a module to register all services for dependency injection.
// Please create a src/services/index.ts file that:
// - Exports all service interfaces and implementations
// - Provides a factory function to create all services
// - Sets up dependency injection for services
// - Configures service dependencies
```

## 23. Service Error Handling

```
// I need to create a robust error handling system for service operations.
// Please create a src/utils/errors.ts file with:
// - A ServiceError base class with proper stack trace preservation
// - Specific error subclasses:
//   - ValidationError for input validation failures
//   - NotFoundError for resources that don't exist
//   - AuthenticationError for auth failures
//   - AuthorizationError for permission issues
//   - BusinessLogicError for business rule violations
//   - RepositoryError for database operation failures
// - Error codes for client-side error handling
// - Type safety for error parameters
// - Functions to convert errors to HTTP responses
// 
// Also create a src/utils/error-handlers.ts file with:
// - A handleDatabaseError utility to map database errors to domain errors
// - Higher-order functions to wrap database operations with standard error handling
// - Specialized handlers for common error patterns (unique constraint, not found, etc.)
// - Utility to extract user-friendly messages from technical errors
// - Structured logging helpers for error reporting
// 
// In the src/repositories/base/prisma-base.repository.ts file, implement:
// - A base executeQuery method that wraps Prisma operations with try/catch
// - Common error handling for standard Prisma error codes
// - Consistent error transformation for all repository methods
```

## 24. Service Testing Utilities

```
// I need utilities for testing services.
// Please create a src/services/testing/service-test-utils.ts file with:
// - Mock implementations of all service interfaces
// - Mock implementations of repository dependencies
// - Factory functions to create test data with proper types
// - Setup and teardown functions for service tests
// - Test helpers for validating enum usage
// - Type-safe mock data generators
// - Test cases for error handling scenarios
```

## 25. Type Definitions and Validation

```
// I need to create robust type definitions and validation utilities.
// Please create a src/utils/validation.ts file with:
// - Zod schemas for validating all service inputs
// - Helper functions for common validation patterns
// - Type guards for runtime type checking
// - Mapping functions between DTOs and entity types
// - Utility functions for handling nullable fields
// - Type-safe enum validation helpers
```