# Detailed Model Implementation Prompts for GitHub Copilot

These prompts provide specific implementation details for each model in your membership-based giveaway platform. Each prompt focuses on a single model with complete implementation details using Prisma schema.

## 1. User Model Implementation

```prisma
// I need to implement the User model for my membership platform.
// Please create or update the Prisma schema with a detailed User model:

// User model with complete fields and relationships
model User {
  id            String      @id @default(uuid())
  email         String      @unique
  password      String
  firstName     String
  lastName      String
  profileImage  String?
  role          UserRole    @default(USER)
  isVerified    Boolean     @default(false)
  verificationToken String?
  resetPasswordToken String?
  resetPasswordExpires DateTime?
  lastLoginAt   DateTime?
  
  // Preferences stored as JSON
  preferences   Json?       @default("{}")
  
  // Timestamps
  createdAt     DateTime    @default(now())
  updatedAt     DateTime    @updatedAt
  
  // Relationships
  memberships   Membership[]
  entries       Entry[]
  wins          Winner[]
  transactions  Transaction[]
  authoredContent Content[]
  notifications Notification[]
  
  // Indexes
  @@index([email])
  @@index([role])
}

// User role enum
enum UserRole {
  USER
  ADMIN
}
```

## 2. MembershipTier Model Implementation

```prisma
// I need to implement the MembershipTier model for my membership platform.
// Please create or update the Prisma schema with a detailed MembershipTier model:

// MembershipTier model with complete fields and relationships
model MembershipTier {
  id              String      @id @default(uuid())
  name            String      @unique
  description     String
  price           Decimal     @db.Decimal(10, 2)
  currency        String      @default("USD")
  billingPeriod   BillingPeriod
  entryAllocation Int
  features        Json        @default("[]")
  isActive        Boolean     @default(true)
  displayOrder    Int
  
  // Timestamps
  createdAt       DateTime    @default(now())
  updatedAt       DateTime    @updatedAt
  
  // Relationships
  memberships     Membership[]
  
  // Indexes
  @@index([isActive])
  @@index([displayOrder])
}

// Billing period enum
enum BillingPeriod {
  MONTHLY
  YEARLY
}
```

## 3. Membership Model Implementation

```prisma
// I need to implement the Membership model for my membership platform.
// Please create or update the Prisma schema with a detailed Membership model:

// Membership model with complete fields and relationships
model Membership {
  id                String            @id @default(uuid())
  userId            String
  tierId            String
  startDate         DateTime
  endDate           DateTime
  status            MembershipStatus
  autoRenew         Boolean           @default(true)
  membershipHistory Json              @default("[]")
  paymentMethodId   String?
  
  // Timestamps
  createdAt         DateTime          @default(now())
  updatedAt         DateTime          @updatedAt
  
  // Relationships
  user              User              @relation(fields: [userId], references: [id])
  tier              MembershipTier    @relation(fields: [tierId], references: [id])
  entries           Entry[]
  transactions      Transaction[]
  
  // Indexes
  @@index([userId])
  @@index([tierId])
  @@index([status])
  @@index([endDate])
}

// Membership status enum
enum MembershipStatus {
  ACTIVE
  CANCELLED
  EXPIRED
}
```

## 4. Giveaway Model Implementation

```prisma
// I need to implement the Giveaway model for my membership platform.
// Please create or update the Prisma schema with a detailed Giveaway model:

// Giveaway model with complete fields and relationships
model Giveaway {
  id                 String          @id @default(uuid())
  title              String
  description        String          @db.Text
  startDate          DateTime
  endDate            DateTime
  status             GiveawayStatus
  featuredImage      String?
  maxEntries         Int?
  category           String?
  tags               String[]
  termsAndConditions String          @db.Text
  
  // Timestamps
  createdAt          DateTime        @default(now())
  updatedAt          DateTime        @updatedAt
  
  // Relationships
  prizes             Prize[]
  entries            Entry[]
  winners            Winner[]
  
  // Indexes
  @@index([status])
  @@index([startDate, endDate])
  @@index([category])
}

// Giveaway status enum
enum GiveawayStatus {
  DRAFT
  ACTIVE
  COMPLETED
  CANCELLED
}
```

## 5. Prize Model Implementation

```prisma
// I need to implement the Prize model for my membership platform.
// Please create or update the Prisma schema with a detailed Prize model:

// Prize model with complete fields and relationships
model Prize {
  id             String    @id @default(uuid())
  giveawayId     String
  name           String
  description    String    @db.Text
  value          Decimal   @db.Decimal(10, 2)
  currency       String    @default("USD")
  quantity       Int       @default(1)
  images         String[]
  specifications Json?
  
  // Timestamps
  createdAt      DateTime  @default(now())
  updatedAt      DateTime  @updatedAt
  
  // Relationships
  giveaway       Giveaway  @relation(fields: [giveawayId], references: [id])
  winners        Winner[]
  
  // Indexes
  @@index([giveawayId])
}
```

## 6. Entry Model Implementation

```prisma
// I need to implement the Entry model for my membership platform.
// Please create or update the Prisma schema with a detailed Entry model:

// Entry model with complete fields and relationships
model Entry {
  id           String      @id @default(uuid())
  giveawayId   String
  userId       String
  membershipId String?
  entryDate    DateTime    @default(now())
  entryMethod  EntryMethod
  referenceId  String?
  
  // Timestamps
  createdAt    DateTime    @default(now())
  updatedAt    DateTime    @updatedAt
  
  // Relationships
  giveaway     Giveaway    @relation(fields: [giveawayId], references: [id])
  user         User        @relation(fields: [userId], references: [id])
  membership   Membership? @relation(fields: [membershipId], references: [id])
  winner       Winner?
  
  // Indexes
  @@index([giveawayId])
  @@index([userId])
  @@index([membershipId])
  @@index([entryMethod])
}

// Entry method enum
enum EntryMethod {
  MEMBERSHIP
  REFERRAL
  SOCIAL_SHARE
  BONUS
}
```

## 7. Winner Model Implementation

```prisma
// I need to implement the Winner model for my membership platform.
// Please create or update the Prisma schema with a detailed Winner model:

// Winner model with complete fields and relationships
model Winner {
  id             String       @id @default(uuid())
  giveawayId     String
  prizeId        String
  userId         String
  entryId        String       @unique
  selectionDate  DateTime
  status         WinnerStatus
  claimDate      DateTime?
  shippingDetails Json?
  
  // Timestamps
  createdAt      DateTime     @default(now())
  updatedAt      DateTime     @updatedAt
  
  // Relationships
  giveaway       Giveaway     @relation(fields: [giveawayId], references: [id])
  prize          Prize        @relation(fields: [prizeId], references: [id])
  user           User         @relation(fields: [userId], references: [id])
  entry          Entry        @relation(fields: [entryId], references: [id])
  
  // Indexes
  @@index([giveawayId])
  @@index([prizeId])
  @@index([userId])
  @@index([status])
}

// Winner status enum
enum WinnerStatus {
  SELECTED
  NOTIFIED
  CLAIMED
  FORFEITED
}
```

## 8. Transaction Model Implementation

```prisma
// I need to implement the Transaction model for my membership platform.
// Please create or update the Prisma schema with a detailed Transaction model:

// Transaction model with complete fields and relationships
model Transaction {
  id              String            @id @default(uuid())
  userId          String
  membershipId    String?
  amount          Decimal           @db.Decimal(10, 2)
  currency        String            @default("USD")
  status          TransactionStatus
  paymentMethod   String
  paymentIntentId String?
  description     String?
  metadata        Json?
  
  // Timestamps
  createdAt       DateTime          @default(now())
  updatedAt       DateTime          @updatedAt
  
  // Relationships
  user            User              @relation(fields: [userId], references: [id])
  membership      Membership?       @relation(fields: [membershipId], references: [id])
  
  // Indexes
  @@index([userId])
  @@index([membershipId])
  @@index([status])
  @@index([createdAt])
}

// Transaction status enum
enum TransactionStatus {
  PENDING
  COMPLETED
  FAILED
  REFUNDED
}
```

## 9. Content Model Implementation

```prisma
// I need to implement the Content model for my membership platform.
// Please create or update the Prisma schema with a detailed Content model:

// Content model with complete fields and relationships
model Content {
  id           String        @id @default(uuid())
  title        String
  slug         String        @unique
  content      String        @db.Text
  excerpt      String?
  featuredImage String?
  authorId     String
  status       ContentStatus
  publishDate  DateTime?
  category     String?
  tags         String[]
  accessLevel  AccessLevel   @default(PUBLIC)
  
  // Timestamps
  createdAt    DateTime      @default(now())
  updatedAt    DateTime      @updatedAt
  
  // Relationships
  author       User          @relation(fields: [authorId], references: [id])
  
  // Indexes
  @@index([slug])
  @@index([status])
  @@index([category])
  @@index([accessLevel])
}

// Content status enum
enum ContentStatus {
  DRAFT
  PUBLISHED
  ARCHIVED
}

// Access level enum
enum AccessLevel {
  PUBLIC
  MEMBERS_ONLY
  PREMIUM_MEMBERS
}
```

## 10. Notification Model Implementation

```prisma
// I need to implement the Notification model for my membership platform.
// Please create or update the Prisma schema with a detailed Notification model:

// Notification model with complete fields and relationships
model Notification {
  id        String             @id @default(uuid())
  userId    String
  title     String
  message   String             @db.Text
  type      NotificationType
  status    NotificationStatus @default(UNREAD)
  link      String?
  metadata  Json?
  
  // Timestamps
  createdAt DateTime           @default(now())
  updatedAt DateTime           @updatedAt
  
  // Relationships
  user      User               @relation(fields: [userId], references: [id])
  
  // Indexes
  @@index([userId])
  @@index([status])
  @@index([type])
  @@index([createdAt])
}

// Notification type enum
enum NotificationType {
  SYSTEM
  GIVEAWAY
  MEMBERSHIP
  PAYMENT
  WINNER
}

// Notification status enum
enum NotificationStatus {
  UNREAD
  READ
}
```

## 11. Setting Model Implementation

```prisma
// I need to implement the Setting model for my membership platform.
// Please create or update the Prisma schema with a detailed Setting model:

// Setting model with complete fields
model Setting {
  id          String   @id @default(uuid())
  key         String   @unique
  value       Json
  description String?
  isPublic    Boolean  @default(false)
  
  // Timestamps
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  // Indexes
  @@index([key])
  @@index([isPublic])
}
```

## 12. Complete Prisma Schema Implementation

```prisma
// I need to implement the complete Prisma schema for my membership platform.
// Please create a prisma/schema.prisma file with:

// Prisma schema configuration
generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// All models and enums defined above, combined into a single schema file
// Include all relationships and indexes
```

## 13. Prisma Schema Migration

```
// I need to create and apply the initial migration for my Prisma schema.
// Please provide the commands to:

// 1. Generate the initial migration
// 2. Apply the migration to the database
// 3. Generate the Prisma client
```

## 14. Prisma Client Usage Examples

```typescript
// I need examples of how to use the Prisma client with my models.
// Please create a src/examples/prisma-usage.ts file with examples of:

// 1. Creating a new user
// 2. Finding a user by email
// 3. Creating a membership with relationships
// 4. Querying giveaways with prizes
// 5. Creating an entry with relationships
// 6. Selecting a winner with transactions
// 7. Using Prisma transactions for complex operations
```

## 15. Data Transfer Objects (DTOs)

```typescript
// I need to create DTOs for my models to use in the service and controller layers.
// Please create a src/dtos/index.ts file with:

// 1. User DTOs (Create, Update, Response)
// 2. Membership DTOs (Create, Update, Response)
// 3. Giveaway DTOs (Create, Update, Response)
// 4. Prize DTOs (Create, Update, Response)
// 5. Entry DTOs (Create, Response)
// 6. Winner DTOs (Create, Update, Response)
// 7. Transaction DTOs (Create, Response)
// 8. Content DTOs (Create, Update, Response)
// 9. Notification DTOs (Create, Update, Response)
```
