# Repository Layer Prompts for GitHub Copilot

These prompts are designed to help you create the repository layer for your membership-based giveaway platform. Each prompt focuses on a specific repository implementation, following best practices for working with GitHub Copilot.

src/
  repositories/
    interfaces/                # Contains base repository interface
      base.repository.ts       # Base repository interface
    base/                      # Contains base repository implementation
      prisma-base.repository.ts # Base Prisma implementation
    user/
      user-repository.interface.ts
      prisma-user.repository.ts
    membership-tier/
      membership-tier-repository.interface.ts
      prisma-membership-tier.repository.ts
    membership/
      ... and so on for other entities

## 1. Create Base Repository Interface

```
// I need to create a generic repository interface for my membership platform.
// This will define the standard CRUD operations that all repositories should implement.
// Please create a src/repositories/base.repository.ts file with:
// - A generic interface for basic CRUD operations
// - Type parameters for the entity and ID types
// - Methods for findById, findMany, create, update, and delete
// - Support for pagination and filtering
```

## 2. Implement Base Repository

```
// Now I need to implement the base repository interface with Prisma.
// Please create a src/repositories/prisma-base.repository.ts file that:
// - Implements the base repository interface
// - Uses Prisma for database operations
// - Handles pagination with skip/take parameters
// - Supports basic filtering
// - Includes error handling for common database errors
// - Uses generics to work with any Prisma model
```

## 3. User Repository Interface

```
// I need to create a user repository interface that extends the base repository.
// Please create a src/repositories/user/user-repository.interface.ts file with:
// - Extension of the base repository interface for User entity
// - Additional methods specific to users:
//   - findByEmail
//   - findByUsername (if applicable)
//   - updatePassword
//   - verifyEmail
//   - findWithMemberships (to include membership data)
```

## 4. User Repository Implementation

```
// I need to implement the user repository interface with Prisma.
// Please create a src/repositories/user/prisma-user.repository.ts file that:
// - Extends the base Prisma repository
// - Implements the user repository interface
// - Includes all the specific user methods
// - Handles user-specific error cases
// - Uses Prisma's include for related data
```

## 5. Membership Tier Repository

```
// I need to create a repository for membership tiers.
// Please create the interface and implementation files:
// - src/repositories/membership-tier/membership-tier-repository.interface.ts
// - src/repositories/membership-tier/prisma-membership-tier.repository.ts
// Include methods for:
// - findActive (only active tiers)
// - findByDisplayOrder (sorted by display order)
// - findWithFeatures (including detailed feature data)
```

## 6. Membership Repository

```
// I need to create a repository for user memberships.
// Please create the interface and implementation files:
// - src/repositories/membership/membership-repository.interface.ts
// - src/repositories/membership/prisma-membership.repository.ts
// Include methods for:
// - findByUserId
// - findActiveByUserId (only active memberships)
// - updateMembershipHistory (to track changes)
// - findWithTierDetails (including tier information)
// - findExpiringSoon (memberships about to expire)
```

## 7. Giveaway Repository

```
// I need to create a repository for giveaways.
// Please create the interface and implementation files:
// - src/repositories/giveaway/giveaway-repository.interface.ts
// - src/repositories/giveaway/prisma-giveaway.repository.ts
// Include methods for:
// - findActive (currently active giveaways)
// - findUpcoming (future giveaways)
// - findPast (completed giveaways)
// - findByCategory
// - findWithPrizes (including prize details)
// - findWithEntryCount (including count of entries)
```

## 8. Prize Repository

```
// I need to create a repository for prizes.
// Please create the interface and implementation files:
// - src/repositories/prize/prize-repository.interface.ts
// - src/repositories/prize/prisma-prize.repository.ts
// Include methods for:
// - findByGiveawayId
// - findAvailable (prizes with quantity > 0)
// - updateQuantity (when prizes are claimed)
```

## 9. Entry Repository

```
// I need to create a repository for giveaway entries.
// Please create the interface and implementation files:
// - src/repositories/entry/entry-repository.interface.ts
// - src/repositories/entry/prisma-entry.repository.ts
// Include methods for:
// - findByGiveawayAndUser
// - countByGiveaway
// - countByUser
// - findWithUserDetails (including user information)
// - createBatch (for creating multiple entries at once)
```

## 10. Winner Repository

```
// I need to create a repository for giveaway winners.
// Please create the interface and implementation files:
// - src/repositories/winner/winner-repository.interface.ts
// - src/repositories/winner/prisma-winner.repository.ts
// Include methods for:
// - findByGiveaway
// - findByUser
// - findPending (winners who haven't claimed prizes)
// - findWithDetails (including user, prize, and giveaway details)
// - updateStatus (for tracking claim status)
```

## 11. Transaction Repository

```
// I need to create a repository for payment transactions.
// Please create the interface and implementation files:
// - src/repositories/transaction/transaction-repository.interface.ts
// - src/repositories/transaction/prisma-transaction.repository.ts
// Include methods for:
// - findByUser
// - findByMembership
// - findByStatus
// - findByDateRange
// - sumByDateRange (for reporting)
```

## 12. Content Repository

```
// I need to create a repository for content pages and articles.
// Please create the interface and implementation files:
// - src/repositories/content/content-repository.interface.ts
// - src/repositories/content/prisma-content.repository.ts
// Include methods for:
// - findBySlug
// - findPublished
// - findByCategory
// - findByAccessLevel
// - findWithAuthor (including author details)
```

## 13. Notification Repository

```
// I need to create a repository for user notifications.
// Please create the interface and implementation files:
// - src/repositories/notification/notification-repository.interface.ts
// - src/repositories/notification/prisma-notification.repository.ts
// Include methods for:
// - findByUser
// - findUnreadByUser
// - markAsRead
// - markAllAsRead
// - deleteOlderThan (for cleanup)
```

## 14. Setting Repository

```
// I need to create a repository for system settings.
// Please create the interface and implementation files:
// - src/repositories/setting/setting-repository.interface.ts
// - src/repositories/setting/prisma-setting.repository.ts
// Include methods for:
// - findByKey
// - findPublic (only public settings)
// - upsert (update if exists, create if not)
// - findWithCache (with caching support)
```

## 15. Unit of Work Pattern

```
// I need to implement the Unit of Work pattern to manage transactions.
// Please create a src/repositories/unit-of-work.ts file that:
// - Provides a way to perform operations in a transaction
// - Exposes all repositories
// - Ensures all repositories use the same Prisma client instance
// - Has methods to begin, commit, and rollback transactions
// - Includes a factory function to create the unit of work
```

## 16. Repository Module

```
// I need to create a module to register all repositories for dependency injection.
// Please create a src/repositories/index.ts file that:
// - Exports all repository interfaces and implementations
// - Provides a factory function to create all repositories
// - Sets up dependency injection for repositories
// - Configures the unit of work
```

## 17. Repository Error Handling

```
// I need to create custom error classes for repository operations.
// Please create a src/repositories/errors.ts file with:
// - NotFoundError for when entities aren't found
// - DuplicateError for unique constraint violations
// - ForeignKeyError for foreign key violations
// - RepositoryError as a base class
// - Functions to convert Prisma errors to these custom errors
```

## 18. Repository Testing Utilities

```
// I need utilities for testing repositories.
// Please create a src/repositories/testing/repository-test-utils.ts file with:
// - Mock implementations of all repository interfaces
// - Factory functions to create test data
// - Utilities to compare entity objects
// - Setup and teardown functions for repository tests
```
