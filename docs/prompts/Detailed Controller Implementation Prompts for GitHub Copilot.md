# Detailed Controller Implementation Prompts for GitHub Copilot

These prompts provide specific implementation details for each controller in your membership-based giveaway platform. Each prompt focuses on a single controller with complete implementation details.

## 1. Authentication Controller Implementation

```typescript
// I need to implement the authentication controller for my membership platform.
// This controller should handle user registration, login, email verification, and password reset.
// Please create a src/controllers/auth.controller.ts file with the following:

// 1. Import necessary dependencies:
//    - Express Request, Response, NextFunction
//    - Authentication service interface
//    - Validation schemas (using Zod)
//    - HTTP status codes
//    - Custom error types

// 2. Create an AuthController class with:
//    - Constructor that accepts the auth service as a dependency
//    - Register method that:
//      - Validates the request body against a registration schema
//      - Calls the auth service to register the user
//      - Returns a 201 Created response with the user (excluding password)
//      - <PERSON>les validation errors and duplicate email errors
//    - Login method that:
//      - Validates the request body against a login schema
//      - Calls the auth service to authenticate the user
//      - Returns a 200 OK response with the JWT token and user info
//      - <PERSON><PERSON> invalid credentials errors
//    - VerifyEmail method that:
//      - Extracts the verification token from the request params
//      - Calls the auth service to verify the email
//      - Redirects to the frontend with a success parameter
//      - Handles invalid or expired token errors
//    - ForgotPassword method that:
//      - Validates the request body for email
//      - Calls the auth service to initiate password reset
//      - Returns a 200 OK response (even if email not found for security)
//    - ResetPassword method that:
//      - Validates the request body for token and new password
//      - Calls the auth service to reset the password
//      - Returns a 200 OK response on success
//      - Handles invalid or expired token errors
//    - RefreshToken method that:
//      - Extracts the refresh token from the request body
//      - Calls the auth service to generate a new access token
//      - Returns a 200 OK response with the new token
//      - Handles invalid or expired refresh token errors
//    - Logout method that:
//      - Extracts the token from the request body
//      - Calls the auth service to invalidate the token
//      - Returns a 204 No Content response

// 3. Create route handler factory functions that:
//    - Wrap each controller method with try/catch
//    - Pass errors to the next function for central error handling
//    - Use proper typing for request and response objects

// 4. Export the controller class and a factory function to create it
```

## 2. User Controller Implementation

```typescript
// I need to implement the user controller for my membership platform.
// This controller should handle user profile management and related operations.
// Please create a src/controllers/user.controller.ts file with the following:

// 1. Import necessary dependencies:
//    - Express Request, Response, NextFunction
//    - User service interface
//    - Validation schemas (using Zod)
//    - HTTP status codes
//    - Custom error types
//    - Authentication types (for the authenticated user in request)

// 2. Create a UserController class with:
//    - Constructor that accepts the user service as a dependency
//    - GetProfile method that:
//      - Extracts the user ID from the authenticated request
//      - Calls the user service to get the user profile
//      - Returns a 200 OK response with the user profile
//      - Handles user not found errors
//    - UpdateProfile method that:
//      - Extracts the user ID from the authenticated request
//      - Validates the request body against a profile update schema
//      - Calls the user service to update the profile
//      - Returns a 200 OK response with the updated profile
//      - Handles validation errors
//    - ChangePassword method that:
//      - Extracts the user ID from the authenticated request
//      - Validates the request body for current and new password
//      - Calls the user service to change the password
//      - Returns a 204 No Content response on success
//      - Handles incorrect current password errors
//    - UpdatePreferences method that:
//      - Extracts the user ID from the authenticated request
//      - Validates the request body against a preferences schema
//      - Calls the user service to update preferences
//      - Returns a 200 OK response with the updated preferences
//    - GetEntries method that:
//      - Extracts the user ID from the authenticated request
//      - Validates query parameters for pagination and filtering
//      - Calls the user service to get the user's entries
//      - Returns a 200 OK response with paginated entries
//    - GetWins method that:
//      - Extracts the user ID from the authenticated request
//      - Validates query parameters for pagination and filtering
//      - Calls the user service to get the user's wins
//      - Returns a 200 OK response with paginated wins

// 3. Create route handler factory functions that:
//    - Wrap each controller method with try/catch
//    - Pass errors to the next function for central error handling
//    - Use proper typing for request and response objects

// 4. Export the controller class and a factory function to create it
```

## 3. Membership Controller Implementation

```typescript
// I need to implement the membership controller for my membership platform.
// This controller should handle membership subscription, cancellation, and management.
// Please create a src/controllers/membership.controller.ts file with the following:

// 1. Import necessary dependencies:
//    - Express Request, Response, NextFunction
//    - Membership service interface
//    - Validation schemas (using Zod)
//    - HTTP status codes
//    - Custom error types
//    - Authentication types (for the authenticated user in request)

// 2. Create a MembershipController class with:
//    - Constructor that accepts the membership service as a dependency
//    - GetMembershipTiers method that:
//      - Validates query parameters for filtering (active only, etc.)
//      - Calls the membership service to get available tiers
//      - Returns a 200 OK response with the tiers
//    - GetCurrentMembership method that:
//      - Extracts the user ID from the authenticated request
//      - Calls the membership service to get the user's current membership
//      - Returns a 200 OK response with the membership details
//      - Returns a 404 Not Found if the user has no active membership
//    - Subscribe method that:
//      - Extracts the user ID from the authenticated request
//      - Validates the request body for tier ID and payment details
//      - Calls the membership service to process the subscription
//      - Returns a 201 Created response with the new membership
//      - Handles payment processing errors
//    - CancelMembership method that:
//      - Extracts the user ID from the authenticated request
//      - Validates the request body for cancellation reason (optional)
//      - Calls the membership service to cancel the membership
//      - Returns a 200 OK response with the updated membership
//      - Handles errors if no active membership exists
//    - UpdateAutoRenew method that:
//      - Extracts the user ID from the authenticated request
//      - Validates the request body for the autoRenew flag
//      - Calls the membership service to update the setting
//      - Returns a 200 OK response with the updated membership
//      - Handles errors if no active membership exists
//    - UpgradeMembership method that:
//      - Extracts the user ID from the authenticated request
//      - Validates the request body for new tier ID and payment details
//      - Calls the membership service to process the upgrade
//      - Returns a 200 OK response with the updated membership
//      - Handles payment processing errors
//    - GetMembershipHistory method that:
//      - Extracts the user ID from the authenticated request
//      - Validates query parameters for pagination
//      - Calls the membership service to get the history
//      - Returns a 200 OK response with paginated history

// 3. Create route handler factory functions that:
//    - Wrap each controller method with try/catch
//    - Pass errors to the next function for central error handling
//    - Use proper typing for request and response objects

// 4. Export the controller class and a factory function to create it
```

## 4. Giveaway Controller Implementation

```typescript
// I need to implement the giveaway controller for my membership platform.
// This controller should handle giveaway listing, details, entries, and admin operations.
// Please create a src/controllers/giveaway.controller.ts file with the following:

// 1. Import necessary dependencies:
//    - Express Request, Response, NextFunction
//    - Giveaway service interface
//    - Validation schemas (using Zod)
//    - HTTP status codes
//    - Custom error types
//    - Authentication and authorization types

// 2. Create a GiveawayController class with:
//    - Constructor that accepts the giveaway service as a dependency
//    - ListGiveaways method that:
//      - Validates query parameters for pagination, filtering, and sorting
//      - Calls the giveaway service to get giveaways based on filters
//      - Returns a 200 OK response with paginated giveaways
//    - GetGiveawayDetails method that:
//      - Extracts the giveaway ID from the request params
//      - Calls the giveaway service to get the giveaway details
//      - Returns a 200 OK response with the giveaway details
//      - Handles giveaway not found errors
//    - CreateGiveaway method (admin only) that:
//      - Validates the request body against a giveaway creation schema
//      - Calls the giveaway service to create the giveaway
//      - Returns a 201 Created response with the new giveaway
//      - Handles validation errors
//    - UpdateGiveaway method (admin only) that:
//      - Extracts the giveaway ID from the request params
//      - Validates the request body against a giveaway update schema
//      - Calls the giveaway service to update the giveaway
//      - Returns a 200 OK response with the updated giveaway
//      - Handles giveaway not found errors
//    - DeleteGiveaway method (admin only) that:
//      - Extracts the giveaway ID from the request params
//      - Calls the giveaway service to delete the giveaway
//      - Returns a 204 No Content response on success
//      - Handles giveaway not found errors
//    - EnterGiveaway method that:
//      - Extracts the giveaway ID from the request params
//      - Extracts the user ID from the authenticated request
//      - Validates the request body for entry method details
//      - Calls the giveaway service to process the entry
//      - Returns a 201 Created response with the entry details
//      - Handles errors for ineligible entries or giveaway not active
//    - GetEntries method (admin only) that:
//      - Extracts the giveaway ID from the request params
//      - Validates query parameters for pagination and filtering
//      - Calls the giveaway service to get entries
//      - Returns a 200 OK response with paginated entries
//    - ConductDraw method (admin only) that:
//      - Extracts the giveaway ID from the request params
//      - Validates the request body for draw parameters
//      - Calls the giveaway service to conduct the draw
//      - Returns a 200 OK response with the winners
//      - Handles errors if giveaway is not ready for drawing

// 3. Create route handler factory functions that:
//    - Wrap each controller method with try/catch
//    - Pass errors to the next function for central error handling
//    - Use proper typing for request and response objects

// 4. Export the controller class and a factory function to create it
```

## 5. Prize Controller Implementation

```typescript
// I need to implement the prize controller for my membership platform.
// This controller should handle prize management for giveaways.
// Please create a src/controllers/prize.controller.ts file with the following:

// 1. Import necessary dependencies:
//    - Express Request, Response, NextFunction
//    - Prize service interface
//    - Validation schemas (using Zod)
//    - HTTP status codes
//    - Custom error types
//    - Authentication and authorization types

// 2. Create a PrizeController class with:
//    - Constructor that accepts the prize service as a dependency
//    - GetPrizes method that:
//      - Extracts the giveaway ID from the request params
//      - Calls the prize service to get prizes for the giveaway
//      - Returns a 200 OK response with the prizes
//      - Handles giveaway not found errors
//    - GetPrizeDetails method that:
//      - Extracts the prize ID from the request params
//      - Calls the prize service to get the prize details
//      - Returns a 200 OK response with the prize details
//      - Handles prize not found errors
//    - CreatePrize method (admin only) that:
//      - Extracts the giveaway ID from the request params
//      - Validates the request body against a prize creation schema
//      - Calls the prize service to create the prize
//      - Returns a 201 Created response with the new prize
//      - Handles validation errors and giveaway not found errors
//    - UpdatePrize method (admin only) that:
//      - Extracts the prize ID from the request params
//      - Validates the request body against a prize update schema
//      - Calls the prize service to update the prize
//      - Returns a 200 OK response with the updated prize
//      - Handles prize not found errors
//    - DeletePrize method (admin only) that:
//      - Extracts the prize ID from the request params
//      - Calls the prize service to delete the prize
//      - Returns a 204 No Content response on success
//      - Handles prize not found errors and constraints (e.g., if winners exist)

// 3. Create route handler factory functions that:
//    - Wrap each controller method with try/catch
//    - Pass errors to the next function for central error handling
//    - Use proper typing for request and response objects

// 4. Export the controller class and a factory function to create it
```

## 6. Winner Controller Implementation

```typescript
// I need to implement the winner controller for my membership platform.
// This controller should handle winner management and prize claiming.
// Please create a src/controllers/winner.controller.ts file with the following:

// 1. Import necessary dependencies:
//    - Express Request, Response, NextFunction
//    - Winner service interface
//    - Validation schemas (using Zod)
//    - HTTP status codes
//    - Custom error types
//    - Authentication and authorization types

// 2. Create a WinnerController class with:
//    - Constructor that accepts the winner service as a dependency
//    - GetWinners method that:
//      - Extracts the giveaway ID from the request params
//      - Validates query parameters for filtering
//      - Calls the winner service to get winners for the giveaway
//      - Returns a 200 OK response with the winners
//      - Handles giveaway not found errors
//    - GetWinnerDetails method that:
//      - Extracts the winner ID from the request params
//      - Calls the winner service to get the winner details
//      - Returns a 200 OK response with the winner details
//      - Handles winner not found errors
//    - ClaimPrize method that:
//      - Extracts the winner ID from the request params
//      - Extracts the user ID from the authenticated request
//      - Validates the request body for shipping details
//      - Calls the winner service to process the claim
//      - Returns a 200 OK response with the updated winner
//      - Handles errors if user is not the winner or prize already claimed
//    - UpdateWinnerStatus method (admin only) that:
//      - Extracts the winner ID from the request params
//      - Validates the request body for status update
//      - Calls the winner service to update the status
//      - Returns a 200 OK response with the updated winner
//      - Handles winner not found errors

// 3. Create route handler factory functions that:
//    - Wrap each controller method with try/catch
//    - Pass errors to the next function for central error handling
//    - Use proper typing for request and response objects

// 4. Export the controller class and a factory function to create it
```

## 7. Payment Controller Implementation

```typescript
// I need to implement the payment controller for my membership platform.
// This controller should handle payment processing and transaction management.
// Please create a src/controllers/payment.controller.ts file with the following:

// 1. Import necessary dependencies:
//    - Express Request, Response, NextFunction
//    - Payment service interface
//    - Validation schemas (using Zod)
//    - HTTP status codes
//    - Custom error types
//    - Authentication and authorization types

// 2. Create a PaymentController class with:
//    - Constructor that accepts the payment service as a dependency
//    - CreatePaymentIntent method that:
//      - Extracts the user ID from the authenticated request
//      - Validates the request body for amount, currency, and description
//      - Calls the payment service to create a payment intent
//      - Returns a 200 OK response with the client secret and intent ID
//      - Handles payment service errors
//    - ProcessPayment method that:
//      - Extracts the user ID from the authenticated request
//      - Validates the request body for payment details
//      - Calls the payment service to process the payment
//      - Returns a 200 OK response with the transaction details
//      - Handles payment processing errors
//    - GetTransactions method that:
//      - Extracts the user ID from the authenticated request
//      - Validates query parameters for pagination and filtering
//      - Calls the payment service to get transactions
//      - Returns a 200 OK response with paginated transactions
//    - GetTransactionDetails method that:
//      - Extracts the transaction ID from the request params
//      - Extracts the user ID from the authenticated request
//      - Calls the payment service to get the transaction details
//      - Returns a 200 OK response with the transaction details
//      - Handles transaction not found errors or unauthorized access
//    - ProcessRefund method (admin only) that:
//      - Validates the request body for transaction ID and refund amount
//      - Calls the payment service to process the refund
//      - Returns a 200 OK response with the refund details
//      - Handles refund processing errors
//    - HandleWebhook method that:
//      - Extracts the webhook signature from the request headers
//      - Passes the raw request body to the payment service
//      - Returns a 200 OK response on success
//      - Handles webhook verification errors

// 3. Create route handler factory functions that:
//    - Wrap each controller method with try/catch
//    - Pass errors to the next function for central error handling
//    - Use proper typing for request and response objects

// 4. Export the controller class and a factory function to create it
```

## 8. Content Controller Implementation

```typescript
// I need to implement the content controller for my membership platform.
// This controller should handle content pages and articles.
// Please create a src/controllers/content.controller.ts file with the following:

// 1. Import necessary dependencies:
//    - Express Request, Response, NextFunction
//    - Content service interface
//    - Validation schemas (using Zod)
//    - HTTP status codes
//    - Custom error types
//    - Authentication and authorization types

// 2. Create a ContentController class with:
//    - Constructor that accepts the content service as a dependency
//    - GetContentBySlug method that:
//      - Extracts the slug from the request params
//      - Extracts the user ID from the authenticated request (if available)
//      - Calls the content service to get the content
//      - Returns a 200 OK response with the content
//      - Handles content not found errors or access level restrictions
//    - ListContentByCategory method that:
//      - Extracts the category from the request params
//      - Validates query parameters for pagination and filtering
//      - Extracts the user ID from the authenticated request (if available)
//      - Calls the content service to list content by category
//      - Returns a 200 OK response with paginated content
//    - CreateContent method (admin only) that:
//      - Validates the request body against a content creation schema
//      - Extracts the user ID from the authenticated request (as author)
//      - Calls the content service to create the content
//      - Returns a 201 Created response with the new content
//      - Handles validation errors and duplicate slug errors
//    - UpdateContent method (admin only) that:
//      - Extracts the content ID from the request params
//      - Validates the request body against a content update schema
//      - Calls the content service to update the content
//      - Returns a 200 OK response with the updated content
//      - Handles content not found errors
//    - DeleteContent method (admin only) that:
//      - Extracts the content ID from the request params
//      - Calls the content service to delete the content
//      - Returns a 204 No Content response on success
//      - Handles content not found errors

// 3. Create route handler factory functions that:
//    - Wrap each controller method with try/catch
//    - Pass errors to the next function for central error handling
//    - Use proper typing for request and response objects

// 4. Export the controller class and a factory function to create it
```

## 9. Notification Controller Implementation

```typescript
// I need to implement the notification controller for my membership platform.
// This controller should handle user notifications.
// Please create a src/controllers/notification.controller.ts file with the following:

// 1. Import necessary dependencies:
//    - Express Request, Response, NextFunction
//    - Notification service interface
//    - Validation schemas (using Zod)
//    - HTTP status codes
//    - Custom error types
//    - Authentication types

// 2. Create a NotificationController class with:
//    - Constructor that accepts the notification service as a dependency
//    - GetNotifications method that:
//      - Extracts the user ID from the authenticated request
//      - Validates query parameters for pagination, filtering, and status
//      - Calls the notification service to get notifications
//      - Returns a 200 OK response with paginated notifications
//    - GetNotificationDetails method that:
//      - Extracts the notification ID from the request params
//      - Extracts the user ID from the authenticated request
//      - Calls the notification service to get the notification details
//      - Returns a 200 OK response with the notification details
//      - Handles notification not found errors or unauthorized access
//    - MarkAsRead method that:
//      - Extracts the notification ID from the request params
//      - Extracts the user ID from the authenticated request
//      - Calls the notification service to mark the notification as read
//      - Returns a 200 OK response with the updated notification
//      - Handles notification not found errors or unauthorized access
//    - MarkAllAsRead method that:
//      - Extracts the user ID from the authenticated request
//      - Calls the notification service to mark all notifications as read
//      - Returns a 200 OK response with the count of updated notifications

// 3. Create route handler factory functions that:
//    - Wrap each controller method with try/catch
//    - Pass errors to the next function for central error handling
//    - Use proper typing for request and response objects

// 4. Export the controller class and a factory function to create it
```

## 10. Admin Dashboard Controller Implementation

```typescript
// I need to implement the admin dashboard controller for my membership platform.
// This controller should handle admin dashboard statistics and reports.
// Please create a src/controllers/admin/dashboard.controller.ts file with the following:

// 1. Import necessary dependencies:
//    - Express Request, Response, NextFunction
//    - Admin service interface
//    - Validation schemas (using Zod)
//    - HTTP status codes
//    - Custom error types
//    - Authentication and authorization types

// 2. Create an AdminDashboardController class with:
//    - Constructor that accepts the admin service as a dependency
//    - GetDashboardStats method that:
//      - Validates query parameters for date range
//      - Calls the admin service to get dashboard statistics
//      - Returns a 200 OK response with the statistics
//    - GetUserStats method that:
//      - Validates query parameters for date range and filters
//      - Calls the admin service to get user statistics
//      - Returns a 200 OK response with the user statistics
//    - GetMembershipStats method that:
//      - Validates query parameters for date range and filters
//      - Calls the admin service to get membership statistics
//      - Returns a 200 OK response with the membership statistics
//    - GetGiveawayStats method that:
//      - Validates query parameters for date range and filters
//      - Calls the admin service to get giveaway statistics
//      - Returns a 200 OK response with the giveaway statistics
//    - GetRevenueStats method that:
//      - Validates query parameters for date range and filters
//      - Calls the admin service to get revenue statistics
//      - Returns a 200 OK response with the revenue statistics

// 3. Create route handler factory functions that:
//    - Wrap each controller method with try/catch
//    - Pass errors to the next function for central error handling
//    - Use proper typing for request and response objects

// 4. Export the controller class and a factory function to create it
```
