# Project Setup Prompts for GitHub Copilot

These prompts are designed to help you set up the initial project structure for your membership-based giveaway platform using Node.js, Express, Prisma, and PostgreSQL. Each prompt is focused, conversational, and follows best practices for working with GitHub Copilot.

## 1. Initialize Project Structure

```
// I'm creating a membership-based giveaway platform using Node.js, Express, Prisma, and PostgreSQL.
// Let's start by setting up the basic project structure.
// Please create a package.json file with the necessary dependencies and scripts.
```

## 2. Install Core Dependencies

```
// Now I need to install the core dependencies for my project.
// I'll be using Express for the web server, <PERSON>risma for database access, and PostgreSQL as the database.
// Please create an npm install command with all the necessary dependencies.
```

## 3. Configure TypeScript

```
// I want to use TypeScript for this project.
// Please create a tsconfig.json file with appropriate settings for a Node.js Express application.
// I want to use ES modules and target Node.js 18.
```

## 4. Set Up Environment Variables

```
// I need to set up environment variables for my project.
// Please create a .env file and a .env.example file with the necessary variables for:
// - Database connection (PostgreSQL)
// - JWT secret for authentication
// - Server port
// - Node environment
```

## 5. Create Project Directory Structure

```
// I need to create a directory structure for my project.
// Please generate a script to create the following folders:
// - src/ (for source code)
//   - config/ (for configuration files)
//   - controllers/ (for API controllers)
//   - services/ (for business logic)
//   - repositories/ (for data access)
//   - middleware/ (for Express middleware)
//   - routes/ (for API routes)
//   - utils/ (for utility functions)
//   - models/ (for data models)
// - prisma/ (for Prisma schema and migrations)
// - tests/ (for tests)
```

## 6. Initialize Prisma

```
// I need to initialize Prisma for my PostgreSQL database.
// Please create a command to initialize Prisma and set up the initial schema.prisma file.
```

## 7. Create Express Server Entry Point

```
// I need to create the main entry point for my Express server.
// Please create a src/app.ts file that sets up an Express application with:
// - JSON body parsing
// - CORS support
// - Basic security headers
// - Error handling middleware
// - Health check endpoint
```

## 8. Create Server Startup File

```
// I need a separate file to start the Express server.
// Please create a src/server.ts file that imports the app from app.ts and starts the server on the configured port.
```

## 9. Set Up Basic Logging

```
// I need to set up logging for my application.
// Please create a src/utils/logger.ts file using Winston for logging with:
// - Console transport for development
// - File transport for production
// - Different log levels (error, warn, info, debug)
```

## 10. Create Error Handling Utilities

```
// I need to create error handling utilities for my application.
// Please create a src/utils/errors.ts file with:
// - A base error class extending Error
// - Specific error classes for different types of errors (ValidationError, AuthenticationError, etc.)
// - A function to convert errors to HTTP responses
```

## 11. Set Up Git Repository

```
// I need to set up a Git repository for my project.
// Please create a .gitignore file for a Node.js project and commands to initialize the repository.
```

## 12. Create README File

```
// I need a README.md file for my project.
// Please create a README.md file with:
// - Project title and description
// - Setup instructions
// - Available scripts
// - Environment variables
// - Project structure
```

## 13. Set Up ESLint and Prettier

```
// I need to set up ESLint and Prettier for code quality.
// Please create configuration files for ESLint and Prettier with:
// - TypeScript support
// - Airbnb style guide
// - Prettier integration
```

## 14. Create NPM Scripts

```
// I need to update the package.json scripts for development.
// Please create scripts for:
// - Starting the development server with hot reload
// - Building the project
// - Running tests
// - Linting and formatting code
// - Generating Prisma client
```

## 15. Set Up Jest for Testing

```
// I need to set up Jest for testing.
// Please create a jest.config.js file with:
// - TypeScript support
// - Coverage reporting
// - Test environment configuration
```
