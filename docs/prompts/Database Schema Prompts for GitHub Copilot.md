# Database Schema Prompts for GitHub Copilot

These prompts are designed to help you create the database schema for your membership-based giveaway platform using Prisma and PostgreSQL. Each prompt is focused on a specific table or relationship, following best practices for working with GitHub Copilot.

## 1. Create Base Prisma Schema

```
// I'm building a membership-based giveaway platform with PostgreSQL and Prisma.
// Let's start by creating the basic Prisma schema configuration.
// Please set up the datasource and generator blocks for PostgreSQL.
```

## 2. User Model

```
// I need to create the User model for my membership platform.
// This should store user account information with the following fields:
// - id (UUID primary key)
// - email (unique, required)
// - password (hashed, required)
// - firstName and lastName
// - profileImage (URL)
// - role (enum: USER, ADMIN)
// - isVerified (boolean)
// - createdAt and updatedAt timestamps
// Please create this model with appropriate field types and modifiers.
```

## 3. MembershipTier Model

```
// I need to create the MembershipTier model to define different subscription tiers.
// Each tier should have:
// - id (UUID primary key)
// - name (unique, required)
// - description
// - price (decimal, required)
// - currency (string, default "USD")
// - billingPeriod (enum: MONTHLY, YEARLY)
// - entryAllocation (integer, how many entries per giveaway)
// - features (JSON array of included features)
// - isActive (boolean)
// - displayOrder (integer for sorting)
// - createdAt and updatedAt timestamps
// Please create this model with appropriate field types and modifiers.
```

## 4. Membership Model

```
// I need to create the Membership model to track user subscriptions.
// This model should have:
// - id (UUID primary key)
// - userId (reference to User)
// - tierId (reference to MembershipTier)
// - startDate (datetime)
// - endDate (datetime)
// - status (enum: ACTIVE, CANCELLED, EXPIRED)
// - autoRenew (boolean)
// - membershipHistory (JSON array to track membership changes)
// - paymentMethodId (optional, for recurring payments)
// - createdAt and updatedAt timestamps
// Please create this model with appropriate field types, modifiers, and relations.
```

## 5. Giveaway Model

```
// I need to create the Giveaway model for prize campaigns.
// This model should have:
// - id (UUID primary key)
// - title (required)
// - description
// - startDate (datetime)
// - endDate (datetime)
// - status (enum: DRAFT, ACTIVE, COMPLETED, CANCELLED)
// - featuredImage (URL)
// - maxEntries (optional integer limit)
// - category (string)
// - tags (string array)
// - termsAndConditions (text)
// - createdAt and updatedAt timestamps
// Please create this model with appropriate field types and modifiers.
```

## 6. Prize Model

```
// I need to create the Prize model for items that can be won in giveaways.
// This model should have:
// - id (UUID primary key)
// - giveawayId (reference to Giveaway)
// - name (required)
// - description
// - value (decimal)
// - currency (string, default "USD")
// - quantity (integer, default 1)
// - images (string array of URLs)
// - specifications (JSON for additional details)
// - createdAt and updatedAt timestamps
// Please create this model with appropriate field types, modifiers, and relations.
```

## 7. Entry Model

```
// I need to create the Entry model to track user entries in giveaways.
// This model should have:
// - id (UUID primary key)
// - giveawayId (reference to Giveaway)
// - userId (reference to User)
// - membershipId (reference to Membership)
// - entryDate (datetime)
// - entryMethod (enum: MEMBERSHIP, REFERRAL, SOCIAL_SHARE, etc.)
// - referenceId (optional, for tracking referrals or social shares)
// - createdAt and updatedAt timestamps
// Please create this model with appropriate field types, modifiers, and relations.
```

## 8. Winner Model

```
// I need to create the Winner model to track giveaway winners.
// This model should have:
// - id (UUID primary key)
// - giveawayId (reference to Giveaway)
// - prizeId (reference to Prize)
// - userId (reference to User)
// - entryId (reference to Entry)
// - selectionDate (datetime)
// - status (enum: SELECTED, NOTIFIED, CLAIMED, FORFEITED)
// - claimDate (optional datetime)
// - shippingDetails (optional JSON)
// - createdAt and updatedAt timestamps
// Please create this model with appropriate field types, modifiers, and relations.
```

## 9. Transaction Model

```
// I need to create the Transaction model to track payment transactions.
// This model should have:
// - id (UUID primary key)
// - userId (reference to User)
// - membershipId (optional reference to Membership)
// - amount (decimal, required)
// - currency (string, default "USD")
// - status (enum: PENDING, COMPLETED, FAILED, REFUNDED)
// - paymentMethod (string)
// - paymentIntentId (for payment processor reference)
// - description
// - metadata (JSON for additional details)
// - createdAt and updatedAt timestamps
// Please create this model with appropriate field types, modifiers, and relations.
```

## 10. Content Model

```
// I need to create the Content model for managing pages and articles.
// This model should have:
// - id (UUID primary key)
// - title (required)
// - slug (unique, required)
// - content (text)
// - excerpt (optional summary)
// - featuredImage (URL)
// - author (reference to User)
// - status (enum: DRAFT, PUBLISHED, ARCHIVED)
// - publishDate (datetime)
// - category (string)
// - tags (string array)
// - accessLevel (enum: PUBLIC, MEMBERS_ONLY, specific tier requirements)
// - createdAt and updatedAt timestamps
// Please create this model with appropriate field types, modifiers, and relations.
```

## 11. Notification Model

```
// I need to create the Notification model for user notifications.
// This model should have:
// - id (UUID primary key)
// - userId (reference to User)
// - title (required)
// - message (text)
// - type (enum: SYSTEM, GIVEAWAY, MEMBERSHIP, PAYMENT, etc.)
// - status (enum: UNREAD, READ)
// - link (optional URL)
// - metadata (JSON for additional details)
// - createdAt and updatedAt timestamps
// Please create this model with appropriate field types, modifiers, and relations.
```

## 12. Setting Model

```
// I need to create the Setting model for system-wide configuration.
// This model should have:
// - id (UUID primary key)
// - key (unique, required)
// - value (JSON)
// - description
// - isPublic (boolean, whether setting is visible to users)
// - createdAt and updatedAt timestamps
// Please create this model with appropriate field types and modifiers.
```

## 13. Define Model Relationships

```
// Now I need to define the relationships between my models.
// Please update the Prisma schema to include:
// - One-to-many relationship between User and Membership
// - One-to-many relationship between MembershipTier and Membership
// - One-to-many relationship between Giveaway and Prize
// - One-to-many relationship between Giveaway and Entry
// - One-to-many relationship between User and Entry
// - One-to-many relationship between Membership and Entry
// - One-to-many relationship between Giveaway and Winner
// - One-to-many relationship between User and Winner
// - One-to-many relationship between Prize and Winner
// - One-to-many relationship between Entry and Winner
// - One-to-many relationship between User and Transaction
// - One-to-many relationship between Membership and Transaction
// - One-to-many relationship between User and Content (author)
// - One-to-many relationship between User and Notification
```

## 14. Add Indexes for Performance

```
// I need to add indexes to my schema for better query performance.
// Please add appropriate indexes for:
// - User email for login queries
// - Membership userId and status for active membership lookups
// - Giveaway status and dates for active giveaway queries
// - Entry giveawayId and userId for entry verification
// - Winner giveawayId and status for winner selection
// - Transaction userId and status for payment history
// - Content slug for page lookups
// - Notification userId and status for unread notifications
```

## 15. Generate Initial Migration

```
// I need to create the initial migration for my database schema.
// Please provide the Prisma CLI command to generate and apply the migration.
```
