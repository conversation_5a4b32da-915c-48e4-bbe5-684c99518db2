# User Signup Workflow

This document outlines the typical user signup workflow for a membership-based giveaway platform, based on common patterns observed in competitor websites.

## Flow Diagram (Conceptual)

```mermaid
graph TD
    A[User visits Website] --> B{Sees CTA: "Join Now" / "Sign Up" / "Select Plan"};
    B --> C[User clicks CTA];
    C --> D{Membership Tier Selection Page};
    D --> E[User selects a Membership Tier (e.g., Bronze, Silver, Gold)];
    E --> F[Registration Form Presented];
    F --> G[User fills: Email, Password, Confirm Password, Name];
    G --> H{Accepts Terms & Conditions & Privacy Policy?};
    H -- Yes --> I[User clicks "Create Account" / "Register"];
    I --> J{Backend: Validate Input Data};
    J -- Valid --> K[Backend: Check if Email already exists];
    K -- Email Unique --> L[Backend: Create User Record (status: pending verification)];
    L --> M[Backend: Generate & Send Email Verification Link/Code];
    M --> N[Frontend: Display "Verification Email Sent" message];
    N --> O[User checks email, clicks verification link/enters code];
    O --> P{Backend: Verify Token/Code};
    P -- Valid --> Q[Backend: Update User Record (status: active/verified)];
    Q --> R[Backend: Log User In (create session)];
    R --> S{User has active membership?};
    S -- No --> T[Redirect to Membership Purchase Flow / Tier Selection Page];
    S -- Yes (e.g., from previous session) --> U[Redirect to User Dashboard];
    K -- Email Exists --> V[Frontend: Display "Email already registered" error];
    J -- Invalid --> W[Frontend: Display validation errors on form];
    H -- No --> X[Frontend: Display "Must accept terms" error];
```

## Detailed Steps

1.  **Initiation**:
    *   The user lands on the website (e.g., homepage, landing page, specific giveaway page).
    *   The user is presented with clear Calls to Action (CTAs) such as "Join Now," "Sign Up," "Get Started," or directly interacts with a membership tier selection element.

2.  **Membership Tier Interaction (Optional at this stage, can be combined with signup)**:
    *   If the CTA leads to a membership comparison page, the user reviews available tiers (e.g., Bronze, Silver, Gold) and their respective benefits, prices, and entry allocations.
    *   The user clicks a "Select Plan" or "Join" button associated with their desired tier. This action might lead directly to a combined registration and payment form or to a registration-first flow.

3.  **Registration Form Presentation**:
    *   The user is presented with a registration form.
    *   **Required Fields Typically Include**:
        *   Full Name (or First Name, Last Name)
        *   Email Address (used for login and communication)
        *   Password (with confirmation field)
    *   **Optional Fields (may be deferred to profile completion)**:
        *   Phone Number
        *   Date of Birth (for age verification if required for giveaways)
        *   Address (less common at initial signup unless directly tied to first payment)

4.  **Input & Submission**:
    *   The user fills in the required information.
    *   The user must typically agree to the platform's Terms & Conditions and Privacy Policy via a checkbox.
    *   The user clicks a "Create Account," "Register," or "Sign Up" button.

5.  **Backend Validation & Processing**:
    *   **Input Validation**: The backend validates all submitted data (e.g., email format, password strength, required fields).
        *   If invalid: Return error messages to the frontend to display next to the respective fields.
    *   **Email Uniqueness Check**: The backend checks if the provided email address already exists in the `User` table.
        *   If email exists: Return an error message (e.g., "An account with this email already exists. Please login or use a different email.").
    *   **Terms Acceptance Check**: Verify that terms were accepted.
        *   If not accepted: Return an error.

6.  **User Record Creation**:
    *   If all validations pass, the backend creates a new record in the `User` table.
    *   Initial status might be `PENDING_VERIFICATION` or `INACTIVE`.
    *   Password should be securely hashed before storing.

7.  **Email Verification (Common Practice)**:
    *   The system generates a unique verification token/link or a one-time code.
    *   An email is sent to the user's provided email address containing this verification link/code.
    *   The frontend displays a message like, "Registration successful! Please check your email to verify your account."

8.  **User Verifies Email**:
    *   The user opens their email and clicks the verification link or returns to the site to enter the code.
    *   Clicking the link directs them to a specific verification endpoint.

9.  **Backend Verification Confirmation**:
    *   The backend validates the token/code (e.g., checks for existence, expiry, and matches it to the user).
    *   If valid: The user's account status in the `User` table is updated to `ACTIVE` or `VERIFIED`.
    *   If invalid (e.g., expired token, incorrect code): An appropriate error message is displayed.

10. **Post-Verification & Login**:
    *   Upon successful email verification, the user is typically automatically logged in (a session is created).
    *   **Membership Check**: The system checks if the newly verified user has an active membership (unlikely for a brand new signup unless membership purchase was part of the registration form itself).
        *   **No Active Membership**: Redirect the user to the membership selection/purchase page (e.g., `/membership-plans`) to encourage them to subscribe.
        *   **Active Membership (Rare for new signup, but for completeness)**: Redirect to the user dashboard (e.g., `/dashboard`).

## Database Tables Touched (based on provided Prisma schema context)

*   **`User`**: A new record is created. Fields like `email`, `password` (hashed), `name`, `role` (default to `CLIENT`), `emailVerified` (initially null/false, then true), `verificationToken` (stored temporarily), `status` (e.g., `PENDING`, `ACTIVE`) are populated/updated.

## Key Validations

*   **Frontend Validations (Client-Side)**:
    *   Required fields are not empty.
    *   Email has a valid format.
    *   Password meets complexity requirements (length, character types).
    *   Password and Confirm Password fields match.
    *   Terms & Conditions checkbox is checked.
*   **Backend Validations (Server-Side - Crucial)**:
    *   All frontend validations are re-verified on the server.
    *   Email uniqueness.
    *   Verification token validity (if applicable).
    *   Password strength (if not fully enforced by frontend).
    *   Data type and length constraints for all fields.

This workflow provides a foundation. The next step would be the "User Buys Membership" workflow, which often follows immediately after this signup process if no plan was selected initially.
