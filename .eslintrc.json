{"env": {"es2022": true, "node": true, "jest": true}, "extends": ["airbnb-base", "airbnb-typescript/base", "plugin:@typescript-eslint/recommended", "plugin:@typescript-eslint/recommended-requiring-type-checking", "plugin:prettier/recommended"], "parser": "@typescript-eslint/parser", "parserOptions": {"ecmaVersion": "latest", "sourceType": "module", "project": "./tsconfig.json"}, "plugins": ["@typescript-eslint", "prettier", "import"], "rules": {"prettier/prettier": "error", "import/extensions": ["error", "ignorePackages", {"js": "never", "ts": "never"}], "import/prefer-default-export": "off", "class-methods-use-this": "off", "no-underscore-dangle": "off", "no-console": "warn", "@typescript-eslint/no-explicit-any": "error", "@typescript-eslint/explicit-function-return-type": ["warn", {"allowExpressions": true, "allowTypedFunctionExpressions": true}], "@typescript-eslint/explicit-module-boundary-types": "warn"}, "settings": {"import/resolver": {"node": {"extensions": [".js", ".ts"]}, "typescript": {"alwaysTryTypes": true, "project": "./tsconfig.json"}}}}