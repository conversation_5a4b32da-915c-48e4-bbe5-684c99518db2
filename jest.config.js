export default {
  // Specify the test environment
  testEnvironment: 'node',
  
  // Use ts-jest for TypeScript files
  preset: 'ts-jest',
  
  // Define the file extensions Jest should look for
  moduleFileExtensions: ['ts', 'js', 'json'],
  
  // Transform TypeScript files with ts-jest
  transform: {
    '^.+\\.ts$': ['ts-jest', {
      useESM: true,
    }]
  },
  
  // Handle module paths
  moduleNameMapper: {
    '^@/(.*)$': '<rootDir>/src/$1'
  },
  
  // Indicate this is an ESM project 
  extensionsToTreatAsEsm: ['.ts'],
  
  // Tell Jest to respect "type": "module" in package.json
  testMatch: ['**/*.test.ts'],
  
  // Collect test coverage information
  collectCoverage: true,
  coverageDirectory: 'coverage',
  collectCoverageFrom: [
    'src/**/*.ts',
    '!src/**/*.d.ts',
    '!src/server.ts',
    '!**/node_modules/**',
    '!**/dist/**',
    '!**/coverage/**'
  ],
  
  // Configure coverage reporting thresholds
  coverageThreshold: {
    global: {
      branches: 70,
      functions: 70,
      lines: 70,
      statements: 70
    }
  },
  
  // Global setup and teardown files for environment settings
  globalSetup: '<rootDir>/tests/setup.ts',
  globalTeardown: '<rootDir>/tests/teardown.ts',
  
  // Additional test configuration
  verbose: true,
  testTimeout: 10000,
  
  // Environment variables for tests
  testEnvironmentOptions: {
    NODE_ENV: 'test'
  }
};
