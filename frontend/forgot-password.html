<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Forgot Password - Winners Society</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .forgot-container {
            background: white;
            border-radius: 12px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            padding: 40px;
            width: 100%;
            max-width: 450px;
        }

        .logo {
            text-align: center;
            margin-bottom: 30px;
        }

        .logo h1 {
            color: #4f46e5;
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 8px;
        }

        .logo p {
            color: #6b7280;
            font-size: 14px;
        }

        .form-title {
            text-align: center;
            margin-bottom: 30px;
        }

        .form-title h2 {
            color: #1f2937;
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 8px;
        }

        .form-title p {
            color: #6b7280;
            font-size: 14px;
            line-height: 1.5;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            color: #374151;
            font-weight: 500;
            margin-bottom: 8px;
            font-size: 14px;
        }

        .form-group input {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s ease;
        }

        .form-group input:focus {
            outline: none;
            border-color: #4f46e5;
            box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
        }

        .submit-btn {
            width: 100%;
            background: #4f46e5;
            color: white;
            border: none;
            padding: 14px 20px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: background-color 0.3s ease;
            margin-bottom: 20px;
        }

        .submit-btn:hover {
            background: #4338ca;
        }

        .submit-btn:disabled {
            background: #9ca3af;
            cursor: not-allowed;
        }

        .alert {
            padding: 12px 16px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-size: 14px;
        }

        .alert-error {
            background: #fef2f2;
            border: 1px solid #fecaca;
            color: #dc2626;
        }

        .alert-success {
            background: #f0fdf4;
            border: 1px solid #bbf7d0;
            color: #16a34a;
        }

        .alert-info {
            background: #eff6ff;
            border: 1px solid #bfdbfe;
            color: #2563eb;
        }

        .back-link {
            text-align: center;
            margin-top: 20px;
        }

        .back-link a {
            color: #4f46e5;
            text-decoration: none;
            font-size: 14px;
            font-weight: 500;
        }

        .back-link a:hover {
            text-decoration: underline;
        }

        .loading {
            display: none;
            text-align: center;
            margin-top: 20px;
        }

        .spinner {
            border: 3px solid #f3f3f3;
            border-top: 3px solid #4f46e5;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            margin: 0 auto;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .success-state {
            display: none;
            text-align: center;
        }

        .success-state .icon {
            font-size: 48px;
            color: #16a34a;
            margin-bottom: 20px;
        }

        .success-state h3 {
            color: #1f2937;
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 12px;
        }

        .success-state p {
            color: #6b7280;
            font-size: 14px;
            line-height: 1.5;
            margin-bottom: 20px;
        }

        .resend-link {
            color: #4f46e5;
            text-decoration: none;
            font-size: 14px;
            font-weight: 500;
        }

        .resend-link:hover {
            text-decoration: underline;
        }

        @media (max-width: 480px) {
            .forgot-container {
                padding: 30px 20px;
            }
        }
    </style>
</head>
<body>
    <div class="forgot-container">
        <div class="logo">
            <h1>Winners Society</h1>
            <p>Password Recovery</p>
        </div>

        <div id="form-state">
            <div class="form-title">
                <h2>Forgot Password?</h2>
                <p>Enter your email address and we'll send you a link to reset your password.</p>
            </div>

            <div id="alert-container"></div>

            <form id="forgotForm">
                <div class="form-group">
                    <label for="email">Email Address</label>
                    <input type="email" id="email" name="email" required placeholder="Enter your email address">
                </div>

                <button type="submit" class="submit-btn" id="submitBtn">
                    Send Reset Link
                </button>
            </form>

            <div class="loading" id="loading">
                <div class="spinner"></div>
                <p>Sending reset link...</p>
            </div>
        </div>

        <div id="success-state" class="success-state">
            <div class="icon">✉️</div>
            <h3>Check Your Email</h3>
            <p>We've sent a password reset link to <strong id="sent-email"></strong>. Please check your inbox and follow the instructions to reset your password.</p>
            <p>Didn't receive the email? Check your spam folder or <a href="#" class="resend-link" id="resend-link">try again</a>.</p>
        </div>

        <div class="back-link">
            <a href="/login">Back to Login</a>
        </div>
    </div>

    <script>
        let currentEmail = '';

        // Form submission handler
        document.getElementById('forgotForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const email = document.getElementById('email').value.trim();
            
            if (!email) {
                showAlert('Please enter your email address.', 'error');
                return;
            }
            
            if (!isValidEmail(email)) {
                showAlert('Please enter a valid email address.', 'error');
                return;
            }
            
            currentEmail = email;
            
            // Show loading state
            setLoading(true);
            
            try {
                const response = await fetch('/api/auth/forgot-password', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        email: email
                    })
                });
                
                const data = await response.json();
                
                if (response.ok && data.success) {
                    // Show success state
                    showSuccessState(email);
                } else {
                    showAlert(data.message || 'Failed to send reset link. Please try again.', 'error');
                }
            } catch (error) {
                console.error('Error sending reset link:', error);
                showAlert('An error occurred. Please try again later.', 'error');
            } finally {
                setLoading(false);
            }
        });

        // Resend link handler
        document.getElementById('resend-link').addEventListener('click', function(e) {
            e.preventDefault();
            
            // Reset to form state
            document.getElementById('form-state').style.display = 'block';
            document.getElementById('success-state').style.display = 'none';
            
            // Pre-fill email
            document.getElementById('email').value = currentEmail;
            
            showAlert('You can send another reset link if needed.', 'info');
        });

        function showAlert(message, type) {
            const alertContainer = document.getElementById('alert-container');
            alertContainer.innerHTML = `
                <div class="alert alert-${type}">
                    ${message}
                </div>
            `;
        }

        function setLoading(loading) {
            const form = document.getElementById('forgotForm');
            const loadingDiv = document.getElementById('loading');
            const submitBtn = document.getElementById('submitBtn');
            
            if (loading) {
                form.style.display = 'none';
                loadingDiv.style.display = 'block';
                submitBtn.disabled = true;
            } else {
                form.style.display = 'block';
                loadingDiv.style.display = 'none';
                submitBtn.disabled = false;
            }
        }

        function showSuccessState(email) {
            document.getElementById('form-state').style.display = 'none';
            document.getElementById('success-state').style.display = 'block';
            document.getElementById('sent-email').textContent = email;
        }

        function isValidEmail(email) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return emailRegex.test(email);
        }
    </script>
</body>
</html>
