# Route Reorganization Plan

## Route Naming Conventions

1. **Public Routes**:
   - No special prefix
   - Examples: `/`, `/about`, `/membership`, `/giveaways`

2. **Authenticated Member Routes**:
   - Prefix: `/member`
   - Examples: `/member/dashboard`, `/member/membership`, `/member/profile`

3. **Administrative Routes**:
   - Prefix: `/admin`
   - Examples: `/admin/users`, `/admin/giveaways`, `/admin/memberships`

## Route Structure

### Public Routes
- `/` - Home page
- `/auth/login` - Login page
- `/auth/signup` - Signup page
- `/auth/forgot-password` - Password recovery
- `/membership` - Public membership plans
- `/giveaways` - Public giveaway listings
- `/winners` - Public winners showcase
- `/about` - About page
- `/contact` - Contact page
- `/faq` - FAQ page
- `/terms` - Terms and conditions
- `/privacy` - Privacy policy

### Authenticated Member Routes
- `/member/dashboard` - Member dashboard
- `/member/membership` - Member's membership details
  - `/member/membership/plans` - Available membership plans
  - `/member/membership/upgrade` - Upgrade membership
  - `/member/membership/payment-success` - Payment success page
- `/member/profile` - Member's profile
  - `/member/profile/edit` - Edit profile
  - `/member/profile/settings` - Account settings
- `/member/entries` - Member's entries
- `/member/giveaways` - Member's giveaway access
  - `/member/giveaways/:id` - Specific giveaway details
  - `/member/giveaways/:id/enter` - Enter a giveaway

### Administrative Routes
- `/admin/dashboard` - Admin dashboard
- `/admin/users` - User management
  - `/admin/users/:id` - Specific user details
- `/admin/giveaways` - Giveaway management
  - `/admin/giveaways/create` - Create new giveaway
  - `/admin/giveaways/:id/edit` - Edit giveaway
- `/admin/memberships` - Membership management
  - `/admin/memberships/tiers` - Manage membership tiers
  - `/admin/memberships/transactions` - View membership transactions

## Security Measures

1. **Route Guards**:
   - `AuthGuard` - Protects all `/member/*` routes
   - `AdminGuard` - Protects all `/admin/*` routes
   - `NoAuthGuard` - Redirects authenticated users from auth pages

2. **Route Data Properties**:
   - `requiresAuth: true` - Marks routes requiring authentication
   - `roles: ['admin', 'moderator']` - Specifies required roles for access
   - `membershipTier: ['premium', 'vip']` - Specifies required membership tiers

3. **Lazy Loading**:
   - All feature modules should be lazy-loaded for better performance
   - Each section (public, member, admin) should have its own module

## Implementation Steps

1. Create new route files for each section
2. Update component paths and imports
3. Implement route guards and data properties
4. Update navigation links throughout the application
5. Test all routes for proper access control
6. Document the new route structure for future developers
