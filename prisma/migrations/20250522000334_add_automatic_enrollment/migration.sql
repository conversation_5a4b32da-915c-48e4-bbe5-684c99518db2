/*
  Warnings:

  - Added the required column `lastUpdatedDate` to the `Entry` table without a default value. This is not possible if the table is not empty.

*/
-- CreateEnum
CREATE TYPE "EntrySource" AS ENUM ('MEMBERSHIP', 'MEMBERSHIP_UPGRADED', 'MEMBERSHIP_UPDATED', '<PERSON>NU<PERSON>', 'REFERRA<PERSON>', 'SOCIAL_SHARE');

-- <PERSON><PERSON>Enum
CREATE TYPE "FailedEnrollmentStatus" AS ENUM ('PENDING', 'RETRIED', 'MANUALLY_RESOLVED', 'SKIPPED');

-- CreateEnum
CREATE TYPE "EnrollmentAction" AS ENUM ('AUTO_ENROLLMENT', 'MA<PERSON><PERSON>_ENROLLMENT', 'RETRY_ENROLLMENT', 'MEMBERSHIP_UPGRADE');

-- CreateEnum
CREATE TYPE "EnrollmentStatus" AS ENUM ('SUCCESS', 'PARTIAL_SUCCESS', 'FAILURE');

-- AlterTable
ALTER TABLE "Entry" ADD COLUMN     "createdByUserId" TEXT,
ADD COLUMN     "lastUpdatedDate" TIMESTAMP(3) NOT NULL,
ADD COLUMN     "membershipTierId" TEXT,
ADD COLUMN     "notes" TEXT,
ADD COLUMN     "source" "EntrySource" NOT NULL DEFAULT 'MEMBERSHIP';

-- AlterTable
ALTER TABLE "Giveaway" ADD COLUMN     "autoEnrollment" BOOLEAN NOT NULL DEFAULT true,
ADD COLUMN     "eligibleMembershipTierIds" TEXT[],
ADD COLUMN     "lastEnrollmentJobId" TEXT;

-- CreateTable
CREATE TABLE "FailedEnrollment" (
    "id" TEXT NOT NULL,
    "giveawayId" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "membershipTierId" TEXT,
    "jobId" TEXT,
    "errorMessage" TEXT NOT NULL,
    "errorCode" TEXT NOT NULL,
    "attemptDate" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "status" "FailedEnrollmentStatus" NOT NULL DEFAULT 'PENDING',
    "resolvedDate" TIMESTAMP(3),
    "resolvedByUserId" TEXT,
    "resolutionMethod" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "FailedEnrollment_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "EnrollmentAuditLog" (
    "id" TEXT NOT NULL,
    "giveawayId" TEXT NOT NULL,
    "userId" TEXT,
    "adminId" TEXT,
    "action" "EnrollmentAction" NOT NULL,
    "details" JSONB,
    "timestamp" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "affectedUserCount" INTEGER NOT NULL DEFAULT 1,
    "status" "EnrollmentStatus" NOT NULL DEFAULT 'SUCCESS',
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "EnrollmentAuditLog_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "FailedEnrollment_giveawayId_status_idx" ON "FailedEnrollment"("giveawayId", "status");

-- CreateIndex
CREATE INDEX "FailedEnrollment_userId_status_idx" ON "FailedEnrollment"("userId", "status");

-- CreateIndex
CREATE INDEX "EnrollmentAuditLog_giveawayId_action_idx" ON "EnrollmentAuditLog"("giveawayId", "action");

-- CreateIndex
CREATE INDEX "EnrollmentAuditLog_userId_idx" ON "EnrollmentAuditLog"("userId");

-- CreateIndex
CREATE INDEX "EnrollmentAuditLog_timestamp_idx" ON "EnrollmentAuditLog"("timestamp");

-- CreateIndex
CREATE INDEX "Entry_source_idx" ON "Entry"("source");

-- CreateIndex
CREATE INDEX "Entry_quantity_idx" ON "Entry"("quantity");

-- CreateIndex
CREATE INDEX "Entry_giveawayId_quantity_idx" ON "Entry"("giveawayId", "quantity");

-- CreateIndex
CREATE INDEX "Giveaway_autoEnrollment_status_idx" ON "Giveaway"("autoEnrollment", "status");

-- CreateIndex
CREATE INDEX "User_isVerified_isActive_idx" ON "User"("isVerified", "isActive");

-- AddForeignKey
ALTER TABLE "FailedEnrollment" ADD CONSTRAINT "FailedEnrollment_giveawayId_fkey" FOREIGN KEY ("giveawayId") REFERENCES "Giveaway"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "FailedEnrollment" ADD CONSTRAINT "FailedEnrollment_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "EnrollmentAuditLog" ADD CONSTRAINT "EnrollmentAuditLog_giveawayId_fkey" FOREIGN KEY ("giveawayId") REFERENCES "Giveaway"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "EnrollmentAuditLog" ADD CONSTRAINT "EnrollmentAuditLog_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE SET NULL ON UPDATE CASCADE;
