/*
  Warnings:

  - You are about to drop the column `cardCVC` on the `PaymentMethod` table. All the data in the column will be lost.
  - You are about to drop the column `cardExpiry` on the `PaymentMethod` table. All the data in the column will be lost.
  - You are about to drop the column `cardName` on the `PaymentMethod` table. All the data in the column will be lost.
  - You are about to drop the column `cardNumber` on the `PaymentMethod` table. All the data in the column will be lost.
  - Added the required column `stripePaymentMethodId` to the `PaymentMethod` table without a default value. This is not possible if the table is not empty.

*/
-- AlterTable
ALTER TABLE "PaymentMethod" DROP COLUMN "cardCVC",
DROP COLUMN "cardExpiry",
DROP COLUMN "cardName",
DROP COLUMN "cardNumber",
ADD COLUMN     "brand" TEXT,
ADD COLUMN     "expiryMonth" INTEGER,
ADD COLUMN     "expiryYear" INTEGER,
ADD COLUMN     "holderName" TEXT,
ADD COLUMN     "last4" TEXT,
ADD COLUMN     "stripePaymentMethodId" TEXT NOT NULL;
