-- CreateMembershipTiers
-- This migration seeds the database with initial membership tiers

-- First, let's make sure we don't have duplicate tiers
DELETE FROM "MembershipTier" WHERE name IN ('Basic', 'Premium', 'VIP');

-- Now insert the basic tier
INSERT INTO "MembershipTier" (
  "id", 
  "name", 
  "description", 
  "price", 
  "billingPeriod", 
  "features", 
  "entryAllocation", 
  "duration",
  "isActive", 
  "createdAt", 
  "updatedAt"
) VALUES (
  gen_random_uuid(), 
  'Basic', 
  'Entry level membership with basic features', 
  9.99, 
  'MONTHLY', 
  '["Access to basic giveaways", "Monthly newsletter"]', 
  5, 
  30,
  true, 
  CURRENT_TIMESTAMP, 
  CURRENT_TIMESTAMP
);

-- Insert the premium tier
INSERT INTO "MembershipTier" (
  "id", 
  "name", 
  "description", 
  "price", 
  "billingPeriod", 
  "features", 
  "entryAllocation", 
  "duration",
  "isActive", 
  "createdAt", 
  "updatedAt"
) VALUES (
  gen_random_uuid(), 
  'Premium', 
  'Enhanced membership with more entries and features', 
  19.99, 
  'MONTHLY', 
  '["Access to all giveaways", "Priority customer support", "Exclusive content"]', 
  15, 
  30,
  true, 
  CURRENT_TIMESTAMP, 
  CURRENT_TIMESTAMP
);

-- Insert the VIP tier
INSERT INTO "MembershipTier" (
  "id", 
  "name", 
  "description", 
  "price", 
  "billingPeriod", 
  "features", 
  "entryAllocation", 
  "duration",
  "isActive", 
  "createdAt", 
  "updatedAt"
) VALUES (
  gen_random_uuid(), 
  'VIP', 
  'Ultimate membership with maximum benefits', 
  39.99, 
  'MONTHLY', 
  '["Unlimited entries", "VIP-only giveaways", "Early access to new giveaways", "24/7 premium support"]', 
  50, 
  30,
  true, 
  CURRENT_TIMESTAMP, 
  CURRENT_TIMESTAMP
); 