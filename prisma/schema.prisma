generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id                String               @id @default(uuid())
  email             String               @unique
  password          String
  firstName         String
  lastName          String
  profileImage      String?
  role              Role                 @default(USER)
  isVerified        Boolean              @default(false)
  isActive          Boolean              @default(true)
  // Stripe customer reference
  stripeCustomerId  String?              @unique
  createdAt         DateTime             @default(now())
  updatedAt         DateTime             @updatedAt
  authoredContent   Content[]
  enrollmentLogs    EnrollmentAuditLog[]
  entries           Entry[]
  failedEnrollments FailedEnrollment[]
  memberships       Membership[]
  notifications     Notification[]
  paymentMethods    PaymentMethod[]
  profile           Profile?
  transactions      Transaction[]
  verificationTokens VerificationToken[]
  winners           Winner[]

  @@index([email])
  @@index([isVerified, isActive])
}

model Profile {
  id        String    @id @default(uuid())
  bio       String?
  avatar    String?
  phone     String?
  address   String?
  city      String?
  state     String?
  zipCode   String?
  country   String?
  birthDate DateTime?
  userId    String    @unique
  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt
  user      User      @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model MembershipTier {
  id              String        @id @default(uuid())
  name            String        @unique
  description     String
  price           Decimal       @db.Decimal(10, 2)
  currency        String        @default("USD")
  billingPeriod   BillingPeriod @default(MONTHLY)
  entryAllocation Int
  duration        Int
  features        Json?
  benefits        String[]
  isActive        Boolean       @default(true)
  displayOrder    Int           @default(0)
  // Stripe product and price references
  stripeProductId String?       @unique
  stripePriceId   String?       @unique
  createdAt       DateTime      @default(now())
  updatedAt       DateTime      @updatedAt
  memberships     Membership[]
}

model Membership {
  id                String           @id @default(uuid())
  userId            String
  membershipTierId  String
  startDate         DateTime
  endDate           DateTime
  status            MembershipStatus @default(ACTIVE)
  autoRenew         Boolean          @default(false)
  membershipHistory Json?
  paymentMethodId   String?
  // Stripe subscription reference - Stripe is source of truth
  stripeSubscriptionId String?       @unique
  stripeCustomerId     String?
  // Cache frequently accessed data from Stripe for performance
  currentPeriodEnd     DateTime?
  cancelAtPeriodEnd    Boolean       @default(false)
  createdAt         DateTime         @default(now())
  updatedAt         DateTime         @updatedAt
  entries           Entry[]
  membershipTier    MembershipTier   @relation(fields: [membershipTierId], references: [id])
  paymentMethod     PaymentMethod?   @relation(fields: [paymentMethodId], references: [id])
  user              User             @relation(fields: [userId], references: [id])
  transactions      Transaction[]

  @@index([userId, status])
}

model Giveaway {
  id                        String               @id @default(uuid())
  title                     String
  description               String
  startDate                 DateTime
  endDate                   DateTime
  status                    GiveawayStatus       @default(ACTIVE)
  featuredImage             String?
  prizeValue                Decimal              @db.Decimal(10, 2)
  prizeDetails              String
  imageUrl                  String?
  category                  String?
  tags                      String[]
  rules                     String
  termsAndConditions        String?
  minTier                   String?
  maxEntries                Int
  isActive                  Boolean              @default(true)
  winnerId                  String?
  createdAt                 DateTime             @default(now())
  updatedAt                 DateTime             @updatedAt
  drawDate                  DateTime?
  autoEnrollment            Boolean              @default(true)
  eligibleMembershipTierIds String[]
  lastEnrollmentJobId       String?
  enrollmentAuditLogs       EnrollmentAuditLog[]
  entries                   Entry[]
  failedEnrollments         FailedEnrollment[]
  prizes                    Prize[]
  winners                   Winner[]

  @@index([status, startDate, endDate])
  @@index([autoEnrollment, status])
}

model Entry {
  id               String      @id @default(uuid())
  userId           String
  giveawayId       String
  membershipId     String?
  entryDate        DateTime    @default(now())
  entryMethod      EntryMethod @default(MEMBERSHIP)
  referenceId      String?
  isWinner         Boolean     @default(false)
  createdAt        DateTime    @default(now())
  updatedAt        DateTime    @updatedAt
  quantity         Int         @default(1)
  createdByUserId  String?
  lastUpdatedDate  DateTime    @updatedAt
  membershipTierId String?
  notes            String?
  source           EntrySource @default(MEMBERSHIP)
  giveaway         Giveaway    @relation(fields: [giveawayId], references: [id])
  membership       Membership? @relation(fields: [membershipId], references: [id])
  user             User        @relation(fields: [userId], references: [id])
  winner           Winner?

  @@unique([userId, giveawayId])
  @@index([giveawayId, userId])
  @@index([source])
  @@index([quantity])
  @@index([giveawayId, quantity])
}

model PaymentMethod {
  id                    String       @id @default(uuid())
  userId                String
  type                  PaymentType
  isDefault             Boolean      @default(false)
  createdAt             DateTime     @default(now())
  updatedAt             DateTime     @updatedAt
  brand                 String?
  expiryMonth           Int?
  expiryYear            Int?
  holderName            String?
  last4                 String?
  stripePaymentMethodId String
  // Billing address for payment method
  billingAddress        Json?
  // Backup payment method functionality
  isBackup              Boolean      @default(false)
  memberships           Membership[]
  user                  User         @relation(fields: [userId], references: [id])
}

model Prize {
  id             String   @id @default(uuid())
  giveawayId     String
  name           String
  description    String?
  value          Decimal  @db.Decimal(10, 2)
  currency       String   @default("USD")
  quantity       Int      @default(1)
  images         String[]
  specifications Json?
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt
  giveaway       Giveaway @relation(fields: [giveawayId], references: [id])
  winners        Winner[]
}

model Winner {
  id              String       @id @default(uuid())
  giveawayId      String
  prizeId         String
  userId          String
  entryId         String       @unique
  selectionDate   DateTime     @default(now())
  status          WinnerStatus @default(SELECTED)
  claimDate       DateTime?
  shippingDetails Json?
  createdAt       DateTime     @default(now())
  updatedAt       DateTime     @updatedAt
  entry           Entry        @relation(fields: [entryId], references: [id])
  giveaway        Giveaway     @relation(fields: [giveawayId], references: [id])
  prize           Prize        @relation(fields: [prizeId], references: [id])
  user            User         @relation(fields: [userId], references: [id])

  @@index([giveawayId, status])
}

model Transaction {
  id              String        @id @default(uuid())
  userId          String
  membershipId    String?
  amount          Decimal       @db.Decimal(10, 2)
  currency        String        @default("USD")
  status          PaymentStatus
  paymentMethod   String
  paymentIntentId String?
  description     String
  metadata        Json?
  createdAt       DateTime      @default(now())
  updatedAt       DateTime      @updatedAt
  membership      Membership?   @relation(fields: [membershipId], references: [id])
  user            User          @relation(fields: [userId], references: [id])

  @@index([userId, status])
}

model Content {
  id            String        @id @default(uuid())
  title         String
  slug          String        @unique
  content       String
  excerpt       String?
  featuredImage String?
  authorId      String
  status        ContentStatus @default(DRAFT)
  publishDate   DateTime?
  category      String?
  tags          String[]
  accessLevel   AccessLevel   @default(PUBLIC)
  createdAt     DateTime      @default(now())
  updatedAt     DateTime      @updatedAt
  author        User          @relation(fields: [authorId], references: [id])

  @@index([slug])
}

model Notification {
  id        String             @id @default(uuid())
  userId    String
  title     String
  message   String
  type      NotificationType
  status    NotificationStatus @default(UNREAD)
  link      String?
  metadata  Json?
  createdAt DateTime           @default(now())
  updatedAt DateTime           @updatedAt
  user      User               @relation(fields: [userId], references: [id])

  @@index([userId, status])
}

model Setting {
  id          String   @id @default(uuid())
  key         String   @unique
  value       Json
  description String?
  isPublic    Boolean  @default(false)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
}

model FailedEnrollment {
  id               String                 @id @default(uuid())
  giveawayId       String
  userId           String
  membershipTierId String?
  jobId            String?
  errorMessage     String
  errorCode        String
  attemptDate      DateTime               @default(now())
  status           FailedEnrollmentStatus @default(PENDING)
  resolvedDate     DateTime?
  resolvedByUserId String?
  resolutionMethod String?
  createdAt        DateTime               @default(now())
  updatedAt        DateTime               @updatedAt
  giveaway         Giveaway               @relation(fields: [giveawayId], references: [id])
  user             User                   @relation(fields: [userId], references: [id])

  @@index([giveawayId, status])
  @@index([userId, status])
}

model EnrollmentAuditLog {
  id                String           @id @default(uuid())
  giveawayId        String
  userId            String?
  adminId           String?
  action            EnrollmentAction
  details           Json?
  timestamp         DateTime         @default(now())
  affectedUserCount Int              @default(1)
  status            EnrollmentStatus @default(SUCCESS)
  createdAt         DateTime         @default(now())
  updatedAt         DateTime         @updatedAt
  giveaway          Giveaway         @relation(fields: [giveawayId], references: [id])
  user              User?            @relation(fields: [userId], references: [id])

  @@index([giveawayId, action])
  @@index([userId])
  @@index([timestamp])
}

model VerificationToken {
  id        String              @id @default(uuid())
  userId    String
  token     String              @unique
  type      VerificationTokenType
  expiresAt DateTime
  isUsed    Boolean             @default(false)
  createdAt DateTime            @default(now())
  updatedAt DateTime            @updatedAt
  user      User                @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([token])
  @@index([userId, type])
  @@index([expiresAt])
}

enum Role {
  USER
  ADMIN
  MODERATOR
}

enum PaymentType {
  CREDIT_CARD
  PAYPAL
  BANK_TRANSFER
}

enum PaymentStatus {
  PENDING
  COMPLETED
  FAILED
  REFUNDED
}

enum BillingPeriod {
  MONTHLY
  YEARLY
}

enum MembershipStatus {
  ACTIVE
  CANCELLED
  EXPIRED
}

enum GiveawayStatus {
  DRAFT
  ACTIVE
  COMPLETED
  CANCELLED
}

enum WinnerStatus {
  SELECTED
  NOTIFIED
  CLAIMED
  FORFEITED
}

enum ContentStatus {
  DRAFT
  PUBLISHED
  ARCHIVED
}

enum AccessLevel {
  PUBLIC
  MEMBERS_ONLY
  PREMIUM_MEMBERS
}

enum NotificationType {
  SYSTEM
  GIVEAWAY
  MEMBERSHIP
  PAYMENT
}

enum NotificationStatus {
  UNREAD
  READ
}

enum EntryMethod {
  MEMBERSHIP
  REFERRAL
  SOCIAL_SHARE
  MANUAL
}

enum EntrySource {
  MEMBERSHIP
  MEMBERSHIP_UPGRADED
  MEMBERSHIP_UPDATED
  MANUAL
  REFERRAL
  SOCIAL_SHARE
}

enum FailedEnrollmentStatus {
  PENDING
  RETRIED
  MANUALLY_RESOLVED
  SKIPPED
}

enum EnrollmentAction {
  AUTO_ENROLLMENT
  MANUAL_ENROLLMENT
  RETRY_ENROLLMENT
  MEMBERSHIP_UPGRADE
}

enum EnrollmentStatus {
  SUCCESS
  PARTIAL_SUCCESS
  FAILURE
}

enum VerificationTokenType {
  EMAIL_VERIFICATION
  PASSWORD_RESET
}
