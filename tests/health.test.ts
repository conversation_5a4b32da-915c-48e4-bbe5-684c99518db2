import request from 'supertest';
import app from '../src/app.js';

describe('Health Check API', () => {
  it('should return 200 OK with status information', async () => {
    const response = await request(app).get('/health');
    
    expect(response.status).toBe(200);
    expect(response.body).toHaveProperty('status', 'ok');
    expect(response.body).toHaveProperty('timestamp');
    expect(response.body).toHaveProperty('service');
    expect(response.body).toHaveProperty('environment');
  });
});
