/**
 * Global setup for Jest tests
 * This file runs once before all tests
 */

// Import any necessary configurations and modules
import { config } from 'dotenv';
import { PrismaClient } from '@prisma/client';

// Load environment variables from .env.test if it exists, or fallback to .env
config({ path: '.env.test' });

const prisma = new PrismaClient({
  datasources: {
    db: {
      url: process.env['DATABASE_URL'] || 'postgresql://postgres@localhost:5432/winnerssociety?connect_timeout=10&sslmode=prefer&schema=public'
    }
  }
});

export default async function setup() {
  // Set NODE_ENV for testing
  process.env.NODE_ENV = 'test';

  // Connect to test database
  global.__PRISMA__ = prisma;

  // Log test setup
  console.log('Test setup complete');
}
