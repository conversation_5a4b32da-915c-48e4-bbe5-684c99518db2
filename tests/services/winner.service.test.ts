import { WinnerStatus } from '@prisma/client';
import { 
  createMockGiveaways, 
  createMockPrizes, 
  createMockUsers, 
  createMockWinners,
  setupServiceTest,
  testComprehensiveErrorHandling,
  isValidEnum,
  TestContext
} from '../../src/services/testing/service-test-utils.js';

import { WinnerServiceImpl } from '../../src/services/winner/winner.service.js';
import { WinnerCreateDto, PrizeClaimDto, ShippingDetailsDto } from '../../src/dtos/winner.dto.js';
import { NotFoundError, ValidationError, AuthorizationError } from '../../src/utils/errors.js';

describe('WinnerService', () => {
  let context: TestContext;
  let winnerService: WinnerServiceImpl;
  
  beforeEach(() => {
    // Setup test context with mock data
    context = setupServiceTest({
      users: createMockUsers(3),
      giveaways: createMockGiveaways(2),
      prizes: createMockPrizes(4, { 
        giveawayId: createMockGiveaways(1)[0].id 
      }),
      winners: createMockWinners(2, {
        userId: createMockUsers(1)[0].id,
        giveawayId: createMockGiveaways(1)[0].id,
        prizeId: createMockPrizes(1)[0].id
      })
    });
    
    // Create service instance with mock repositories
    winnerService = new WinnerServiceImpl(
      context.repositories.winner,
      context.repositories.user,
      context.repositories.prize,
      context.repositories.giveaway,
      context.repositories.notification
    );
  });
  
  afterEach(() => {
    // Clean up mocks
    context.cleanup();
    jest.clearAllMocks();
  });
  
  describe('get', () => {
    it('should return winner by id', async () => {
      // Arrange
      const expectedWinner = context.mockData.winners[0];
      
      // Act
      const result = await winnerService.get(expectedWinner.id);
      
      // Assert
      expect(result).not.toBeNull();
      expect(result?.id).toBe(expectedWinner.id);
    });
    
    it('should return null for non-existent winner', async () => {
      // Act
      const result = await winnerService.get('non-existent-id');
      
      // Assert
      expect(result).toBeNull();
    });
    
    // Test error handling
    testComprehensiveErrorHandling(
      () => winnerService.get('test-id'),
      jest.spyOn(context.repositories.winner, 'findById'),
      ['test-id']
    );
  });
  
  describe('list', () => {
    it('should return all winners', async () => {
      // Act
      const results = await winnerService.list();
      
      // Assert
      expect(results).toHaveLength(context.mockData.winners.length);
      expect(results[0].id).toBe(context.mockData.winners[0].id);
    });
    
    it('should filter winners by criteria', async () => {
      // Arrange
      const userId = context.mockData.winners[0].userId;
      
      // Act
      const results = await winnerService.list({ userId });
      
      // Assert
      expect(results.length).toBeGreaterThan(0);
      expect(results.every(w => w.userId === userId)).toBe(true);
    });
  });
  
  describe('create', () => {
    it('should create a new winner', async () => {
      // Arrange
      const createDto: WinnerCreateDto = {
        userId: context.mockData.users[1].id,
        giveawayId: context.mockData.giveaways[0].id,
        prizeId: context.mockData.prizes[0].id,
        entryId: 'entry-123'
      };
      
      // Spy on repository methods
      const createSpy = jest.spyOn(context.repositories.winner, 'create');
      const findUserSpy = jest.spyOn(context.repositories.user, 'findById');
      const findPrizeSpy = jest.spyOn(context.repositories.prize, 'findById');
      const findGiveawaySpy = jest.spyOn(context.repositories.giveaway, 'findById');
      
      // Act
      const result = await winnerService.create(createDto);
      
      // Assert
      expect(result).toBeDefined();
      expect(result.userId).toBe(createDto.userId);
      expect(result.prizeId).toBe(createDto.prizeId);
      expect(result.giveawayId).toBe(createDto.giveawayId);
      expect(result.status).toBe(WinnerStatus.SELECTED);
      
      // Verify repository calls
      expect(findUserSpy).toHaveBeenCalledWith(createDto.userId);
      expect(findPrizeSpy).toHaveBeenCalledWith(createDto.prizeId);
      expect(findGiveawaySpy).toHaveBeenCalledWith(createDto.giveawayId);
      expect(createSpy).toHaveBeenCalled();
    });
    
    it('should throw validation error if required fields are missing', async () => {
      // Arrange
      const invalidDto = {} as WinnerCreateDto;
      
      // Act & Assert
      await expect(winnerService.create(invalidDto)).rejects.toThrow(ValidationError);
    });
  });
  
  describe('claimPrize', () => {
    it('should claim a prize successfully', async () => {
      // Arrange
      const winner = context.mockData.winners[0];
      const userId = winner.userId;
      const shippingDetails: ShippingDetailsDto = {
        fullName: 'Test User',
        addressLine1: '123 Test St',
        city: 'Test City',
        state: 'Test State',
        postalCode: '12345',
        country: 'Test Country',
        phoneNumber: '************'
      };
      const claimData: PrizeClaimDto = {
        shippingDetails
      };
      
      // Act
      const result = await winnerService.claimPrize(winner.id, userId, claimData);
      
      // Assert
      expect(result).toBeDefined();
      expect(result.id).toBe(winner.id);
      expect(result.status).toBe(WinnerStatus.CLAIMED);
      expect(result.shippingDetails).toEqual(expect.objectContaining(shippingDetails));
    });
    
    it('should throw error if user is not the winner', async () => {
      // Arrange
      const winner = context.mockData.winners[0];
      const wrongUserId = 'wrong-user-id';
      const shippingDetails: ShippingDetailsDto = {
        fullName: 'Test User',
        addressLine1: '123 Test St',
        city: 'Test City',
        state: 'Test State',
        postalCode: '12345',
        country: 'Test Country',
        phoneNumber: '************'
      };
      const claimData: PrizeClaimDto = {
        shippingDetails
      };
      
      // Act & Assert
      await expect(
        winnerService.claimPrize(winner.id, wrongUserId, claimData)
      ).rejects.toThrow(AuthorizationError);
    });
    
    it('should throw error if winner not found', async () => {
      // Arrange
      const nonExistentId = 'non-existent-id';
      const userId = context.mockData.users[0].id;
      const shippingDetails: ShippingDetailsDto = {
        fullName: 'Test User',
        addressLine1: '123 Test St',
        city: 'Test City',
        state: 'Test State',
        postalCode: '12345',
        country: 'Test Country',
        phoneNumber: '************'
      };
      const claimData: PrizeClaimDto = {
        shippingDetails
      };
      
      // Act & Assert
      await expect(
        winnerService.claimPrize(nonExistentId, userId, claimData)
      ).rejects.toThrow(NotFoundError);
    });
  });
  
  describe('enum validation', () => {
    it('should validate WinnerStatus enum values', () => {
      // Test valid enum values
      expect(isValidEnum(WinnerStatus, WinnerStatus.SELECTED)).toBe(true);
      expect(isValidEnum(WinnerStatus, WinnerStatus.NOTIFIED)).toBe(true);
      expect(isValidEnum(WinnerStatus, WinnerStatus.CLAIMED)).toBe(true);
      expect(isValidEnum(WinnerStatus, WinnerStatus.FORFEITED)).toBe(true);
      
      // Test invalid enum values
      expect(isValidEnum(WinnerStatus, 'INVALID_STATUS')).toBe(false);
      expect(isValidEnum(WinnerStatus, 123)).toBe(false);
      expect(isValidEnum(WinnerStatus, null)).toBe(false);
    });
  });
  
  describe('updateWinnerStatus', () => {
    it('should update winner status', async () => {
      // Arrange
      const winner = context.mockData.winners[0];
      const newStatus = WinnerStatus.CLAIMED;
      
      // Act
      const result = await winnerService.updateWinnerStatus(winner.id, newStatus);
      
      // Assert
      expect(result).toBeDefined();
      expect(result.id).toBe(winner.id);
      expect(result.status).toBe(newStatus);
    });
    
    it('should throw not found error for non-existent winner', async () => {
      // Act & Assert
      await expect(
        winnerService.updateWinnerStatus('non-existent-id', WinnerStatus.CLAIMED)
      ).rejects.toThrow(NotFoundError);
    });
  });
  
  describe('getWinnersByGiveaway', () => {
    it('should return winners for a giveaway', async () => {
      // Arrange
      const giveawayId = context.mockData.giveaways[0].id;
      
      // Make sure we have winners for this giveaway
      context.mockData.winners.push(
        ...createMockWinners(3, { 
          giveawayId,
          userId: context.mockData.users[0].id,
          prizeId: context.mockData.prizes[0].id
        })
      );
      
      // Act
      const results = await winnerService.getWinnersByGiveaway(giveawayId);
      
      // Assert
      expect(results.length).toBeGreaterThan(0);
      expect(results.every(w => w.giveawayId === giveawayId)).toBe(true);
    });
  });
}); 