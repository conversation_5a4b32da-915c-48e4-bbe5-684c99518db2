# Email Verification System Usage Examples

## Basic Usage

### 1. User Registration with Email Verification

```typescript
// Registration endpoint automatically sends verification email
const response = await fetch('/api/auth/register', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({
    email: '<EMAIL>',
    password: 'securepassword123',
    firstName: 'John',
    lastName: 'Doe',
    addressLine1: '123 Main St',
    city: 'Anytown',
    state: 'CA',
    postalCode: '12345',
    country: 'USA'
  })
});

const result = await response.json();
// User is created with isVerified: false
// Verification email is sent automatically
```

### 2. Checking Verification Status

```typescript
// After login, check if user is verified
const loginResponse = await fetch('/api/auth/login', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({
    email: '<EMAIL>',
    password: 'securepassword123'
  })
});

const loginResult = await loginResponse.json();
if (!loginResult.data.user.isVerified) {
  // Show verification required message
  console.log('Please verify your email address');
}
```

### 3. Resending Verification Email

```typescript
// User can request a new verification email
const resendResponse = await fetch('/api/auth/resend-verification', {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${accessToken}`,
    'Content-Type': 'application/json',
  }
});

if (resendResponse.ok) {
  console.log('Verification email sent successfully');
}
```

## Frontend Integration

### React Component Example

```jsx
import React, { useState, useEffect } from 'react';

function EmailVerificationBanner({ user, onResend }) {
  const [isResending, setIsResending] = useState(false);
  const [resendMessage, setResendMessage] = useState('');

  if (user.isVerified) {
    return null; // Don't show banner for verified users
  }

  const handleResend = async () => {
    setIsResending(true);
    setResendMessage('');

    try {
      const response = await fetch('/api/auth/resend-verification', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('accessToken')}`,
          'Content-Type': 'application/json',
        }
      });

      if (response.ok) {
        setResendMessage('Verification email sent! Please check your inbox.');
      } else {
        setResendMessage('Failed to send verification email. Please try again.');
      }
    } catch (error) {
      setResendMessage('An error occurred. Please try again.');
    } finally {
      setIsResending(false);
    }
  };

  return (
    <div className="bg-yellow-50 border border-yellow-200 rounded-md p-4 mb-4">
      <div className="flex">
        <div className="flex-shrink-0">
          <svg className="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
          </svg>
        </div>
        <div className="ml-3">
          <h3 className="text-sm font-medium text-yellow-800">
            Email Verification Required
          </h3>
          <div className="mt-2 text-sm text-yellow-700">
            <p>
              Please verify your email address ({user.email}) to access all features.
              Check your inbox for a verification email.
            </p>
          </div>
          <div className="mt-4">
            <div className="-mx-2 -my-1.5 flex">
              <button
                type="button"
                onClick={handleResend}
                disabled={isResending}
                className="bg-yellow-50 px-2 py-1.5 rounded-md text-sm font-medium text-yellow-800 hover:bg-yellow-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-yellow-50 focus:ring-yellow-600 disabled:opacity-50"
              >
                {isResending ? 'Sending...' : 'Resend Email'}
              </button>
            </div>
          </div>
          {resendMessage && (
            <div className="mt-2 text-sm text-yellow-700">
              {resendMessage}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

export default EmailVerificationBanner;
```

### Vue.js Component Example

```vue
<template>
  <div v-if="!user.isVerified" class="verification-banner">
    <div class="banner-content">
      <h3>Email Verification Required</h3>
      <p>
        Please verify your email address ({{ user.email }}) to access all features.
        Check your inbox for a verification email.
      </p>
      <button 
        @click="resendEmail" 
        :disabled="isResending"
        class="resend-button"
      >
        {{ isResending ? 'Sending...' : 'Resend Email' }}
      </button>
      <p v-if="resendMessage" class="resend-message">{{ resendMessage }}</p>
    </div>
  </div>
</template>

<script>
export default {
  name: 'EmailVerificationBanner',
  props: {
    user: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      isResending: false,
      resendMessage: ''
    };
  },
  methods: {
    async resendEmail() {
      this.isResending = true;
      this.resendMessage = '';

      try {
        const response = await fetch('/api/auth/resend-verification', {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('accessToken')}`,
            'Content-Type': 'application/json',
          }
        });

        if (response.ok) {
          this.resendMessage = 'Verification email sent! Please check your inbox.';
        } else {
          this.resendMessage = 'Failed to send verification email. Please try again.';
        }
      } catch (error) {
        this.resendMessage = 'An error occurred. Please try again.';
      } finally {
        this.isResending = false;
      }
    }
  }
};
</script>
```

## Backend Route Protection

### Protecting Routes with Email Verification

```typescript
import { Router } from 'express';
import { authenticateUser } from '../middleware/auth.middleware.js';
import { requireEmailVerification } from '../middleware/email-verification.middleware.js';

const router = Router();

// Route that requires email verification
router.get('/premium-content', 
  authenticateUser,           // First authenticate the user
  requireEmailVerification,   // Then check email verification
  (req, res) => {
    res.json({
      success: true,
      data: {
        message: 'Welcome to premium content!',
        content: 'This content is only available to verified users.'
      }
    });
  }
);

// Route with optional verification check
router.get('/basic-content', 
  authenticateUser,
  checkEmailVerification,     // Adds warning headers if not verified
  (req, res) => {
    res.json({
      success: true,
      data: {
        message: 'Basic content available to all authenticated users',
        content: 'This content is available but verification is recommended.'
      }
    });
  }
);
```

### Custom Verification Logic

```typescript
// Custom middleware for specific verification requirements
export function requireVerificationForAction(action: string) {
  return (req: Request, res: Response, next: NextFunction) => {
    const user = (req as AuthenticatedRequest).user;
    
    if (!user?.isVerified) {
      return res.status(403).json({
        success: false,
        message: `Email verification is required to ${action}`,
        code: 'EMAIL_VERIFICATION_REQUIRED',
        data: {
          action,
          email: user?.email,
          resendEndpoint: '/api/auth/resend-verification'
        }
      });
    }
    
    next();
  };
}

// Usage
router.post('/enter-giveaway/:id', 
  authenticateUser,
  requireVerificationForAction('enter giveaways'),
  enterGiveawayHandler
);
```

## Error Handling

### Frontend Error Handling

```typescript
async function handleApiCall(url: string, options: RequestInit) {
  try {
    const response = await fetch(url, options);
    const data = await response.json();
    
    if (!response.ok) {
      // Check for email verification error
      if (data.code === 'EMAIL_NOT_VERIFIED') {
        // Show verification required modal/banner
        showEmailVerificationRequired(data.data);
        return null;
      }
      
      throw new Error(data.message || 'Request failed');
    }
    
    return data;
  } catch (error) {
    console.error('API call failed:', error);
    throw error;
  }
}

function showEmailVerificationRequired(data: any) {
  // Show modal or banner with verification instructions
  const modal = document.createElement('div');
  modal.innerHTML = `
    <div class="verification-modal">
      <h3>Email Verification Required</h3>
      <p>Please verify your email address (${data.email}) to continue.</p>
      <button onclick="resendVerificationEmail()">Resend Email</button>
      <button onclick="closeModal()">Close</button>
    </div>
  `;
  document.body.appendChild(modal);
}
```

## Testing Examples

### Unit Test for Email Service

```typescript
import { SendGridEmailService } from '../src/services/email/sendgrid-email.service';

describe('SendGridEmailService', () => {
  let emailService: SendGridEmailService;

  beforeEach(() => {
    // Mock SendGrid API key
    process.env.SENDGRID_API_KEY = 'test-api-key';
    emailService = new SendGridEmailService();
  });

  test('should send verification email', async () => {
    const mockSend = jest.fn().mockResolvedValue(true);
    jest.mock('@sendgrid/mail', () => ({
      setApiKey: jest.fn(),
      send: mockSend
    }));

    await emailService.sendVerificationEmail(
      '<EMAIL>',
      'John',
      'http://localhost:3000/verify/token123'
    );

    expect(mockSend).toHaveBeenCalledWith(
      expect.objectContaining({
        to: '<EMAIL>',
        subject: 'Verify Your Email Address - Winners Society'
      })
    );
  });
});
```

### Integration Test

```typescript
import request from 'supertest';
import app from '../src/app';

describe('Email Verification Flow', () => {
  test('should require email verification for protected routes', async () => {
    // Register user
    const registerResponse = await request(app)
      .post('/api/auth/register')
      .send({
        email: '<EMAIL>',
        password: 'password123',
        firstName: 'Test',
        lastName: 'User',
        // ... other required fields
      });

    const { accessToken } = registerResponse.body.data;

    // Try to access protected route
    const protectedResponse = await request(app)
      .get('/api/premium-content')
      .set('Authorization', `Bearer ${accessToken}`);

    expect(protectedResponse.status).toBe(403);
    expect(protectedResponse.body.code).toBe('EMAIL_NOT_VERIFIED');
  });
});
```
