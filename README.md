# Winners Society - Membership-Based Giveaway Platform

A modern platform for membership-based giveaways built with Node.js, Express, TypeScript, Prisma, and PostgreSQL. This application allows users to subscribe to membership tiers and participate in exclusive giveaways.

## Features

- **User Authentication**: Secure signup, login, and JWT-based authentication
- **Membership Management**: Different tiers with varying benefits and pricing
- **Giveaway System**: Create, manage, and enter giveaways with prize distribution
- **Payment Processing**: Secure payment methods for membership subscriptions
- **User Profiles**: Customizable user profiles with membership status
- **Admin Dashboard**: Comprehensive admin controls for platform management

## Tech Stack

- **Backend**: Node.js with Express
- **Language**: TypeScript
- **Database**: PostgreSQL
- **ORM**: Prisma
- **Authentication**: JWT
- **Logging**: Winston
- **Testing**: Jest
- **Code Quality**: ESLint, Prettier

## Prerequisites

- Node.js (v18 or later)
- PostgreSQL (v14 or later)
- npm or yarn

## Setup Instructions

### 1. Clone the Repository

```bash
git clone https://github.com/yourusername/winners-society.git
cd winners-society
```

### 2. Install Dependencies

```bash
npm install
```

### 3. Environment Setup

Copy the example environment file and update it with your settings:

```bash
cp .env.example .env
```

Update the `.env` file with your PostgreSQL credentials and other environment-specific values.

### 4. Database Setup

Make sure your PostgreSQL server is running, then run:

```bash
npm run prisma:migrate
npm run prisma:generate
```

### 5. Start the Development Server

```bash
npm run dev
```

The server will start on http://localhost:3000 (or the port specified in your .env file).

## Available Scripts

- `npm start` - Start the production server
- `npm run dev` - Start the development server with hot reload
- `npm run build` - Build the TypeScript project
- `npm test` - Run tests
- `npm run lint` - Lint the codebase
- `npm run format` - Format the codebase
- `npm run prisma:generate` - Generate Prisma client
- `npm run prisma:migrate` - Run database migrations
- `npm run prisma:studio` - Open Prisma Studio for database management

## Environment Variables

| Variable       | Description                               | Default                                              |
|----------------|-------------------------------------------|----------------------------------------------------- |
| `PORT`         | Port number for the server                | 3000                                                 |
| `NODE_ENV`     | Environment (development/production/test) | development                                          |
| `DATABASE_URL` | PostgreSQL connection URL                 | postgresql://postgres@localhost:5432/winnerssociety  |
| `JWT_SECRET`   | Secret key for JWT token generation       | (required)                                           |
| `JWT_EXPIRES_IN` | JWT token expiration                    | 1d                                                   |
| `APP_NAME`     | Application name                          | Winners Society                                      |
| `APP_URL`      | Application URL                           | http://localhost:3000                                |
| `LOG_LEVEL`    | Logging level                             | debug                                                |

## Project Structure

```
winners-society/
├── prisma/                 # Prisma schema and migrations
│   └── schema.prisma       # Database schema
├── src/                    # Source code
│   ├── config/             # Configuration files
│   ├── controllers/        # API controllers
│   ├── middleware/         # Express middleware
│   ├── models/             # Data models
│   ├── repositories/       # Data access layer
│   ├── routes/             # API routes
│   ├── services/           # Business logic
│   ├── utils/              # Utilities and helpers
│   │   ├── errors.ts       # Error handling utilities
│   │   └── logger.ts       # Logging configuration
│   ├── app.ts              # Express application setup
│   └── server.ts           # Server entry point
├── tests/                  # Test files
├── logs/                   # Log files (generated)
├── .env                    # Environment variables
├── .env.example            # Example environment variables
├── .gitignore              # Git ignore file
├── package.json            # Project dependencies and scripts
└── tsconfig.json           # TypeScript configuration
```

## API Documentation

The API documentation is available at `/api` when the server is running.

## Health Check

A health check endpoint is available at `/health` to verify the server status.

## License

[MIT](LICENSE)

## Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request


cd /Users/<USER>/Documents/Workspace/winners-society && NODE_OPTIONS='--no-warnings' npm run build && npm start