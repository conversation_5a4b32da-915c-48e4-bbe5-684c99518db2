#!/bin/bash

# Create main project directories
mkdir -p src/config src/controllers src/services src/repositories src/middleware src/routes src/utils src/models
mkdir -p prisma tests

# Create placeholder files to ensure git tracks empty directories
touch src/config/.gitkeep
touch src/controllers/.gitkeep
touch src/services/.gitkeep
touch src/repositories/.gitkeep
touch src/middleware/.gitkeep
touch src/routes/.gitkeep
touch src/utils/.gitkeep
touch src/models/.gitkeep
touch prisma/.gitkeep
touch tests/.gitkeep

echo "Project directory structure created successfully!"
