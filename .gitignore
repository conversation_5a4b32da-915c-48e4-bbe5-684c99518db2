# Winners Society - Membership-Based Giveaway Platform

# Dependencies
node_modules/
.pnp/
.pnp.js

# Build
dist/
build/
out/
lib/

# Environment variables
.env
.env.*
!.env.example

# Logs
logs/*
!logs/.gitkeep
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Testing
coverage/
.nyc_output/
jest-report/

# IDE
.idea/
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json

# OS
.DS_Store
Thumbs.db
*.code-workspace

# Prisma
prisma/*.db
prisma/migrations/dev/
prisma/migrations/.temp/
.env.prisma

# Temporary files
*.swp
*.swo
.tmp/
temp/
tmp/

# Package managers
.npm/
.yarn/
yarn.lock
package-lock.json

# TypeScript
*.tsbuildinfo
tsconfig.tsbuildinfo

# Production
/public/uploads
/uploads
/stripe-webhook-key

# Stripe CLI
stripe-cli/

# Misc
.DS_Store
.env.backup
*.backup
*.bak
