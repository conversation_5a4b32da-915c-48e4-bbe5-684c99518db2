/**
 * Custom Error Classes
 * 
 * This file contains custom error classes used throughout the application.
 * It provides a standardized error handling system with HTTP response mapping
 * and type-safe error parameters for client-side error handling.
 */

/**
 * HTTP Status Codes for error responses
 */
export enum HttpStatus {
  BAD_REQUEST = 400,
  UNAUTHORIZED = 401,
  FORBIDDEN = 403,
  NOT_FOUND = 404,
  CONFLICT = 409,
  UNPROCESSABLE_ENTITY = 422,
  INTERNAL_SERVER_ERROR = 500,
  SERVICE_UNAVAILABLE = 503
}

/**
 * Error codes for client-side error handling
 */
export enum ErrorCode {
  // Validation errors (400)
  INVALID_INPUT = 'INVALID_INPUT',
  MISSING_REQUIRED_FIELD = 'MISSING_REQUIRED_FIELD',
  INVALID_FORMAT = 'INVALID_FORMAT',
  
  // Authentication errors (401)
  INVALID_CREDENTIALS = 'INVALID_CREDENTIALS',
  EXPIRED_TOKEN = 'EXPIRED_TOKEN',
  INVALID_TOKEN = 'INVALID_TOKEN',
  
  // Authorization errors (403)
  INSUFFICIENT_PERMISSIONS = 'INSUFFICIENT_PERMISSIONS',
  ACCOUNT_DISABLED = 'ACCOUNT_DISABLED',
  
  // Not found errors (404)
  RESOURCE_NOT_FOUND = 'RESOURCE_NOT_FOUND',
  ENDPOINT_NOT_FOUND = 'ENDPOINT_NOT_FOUND',
  
  // Conflict errors (409)
  DUPLICATE_RESOURCE = 'DUPLICATE_RESOURCE',
  RESOURCE_ALREADY_EXISTS = 'RESOURCE_ALREADY_EXISTS',
  CONCURRENT_MODIFICATION = 'CONCURRENT_MODIFICATION',
  
  // Business logic errors (422)
  BUSINESS_RULE_VIOLATION = 'BUSINESS_RULE_VIOLATION',
  INVALID_STATE_TRANSITION = 'INVALID_STATE_TRANSITION',
  OPERATION_NOT_ALLOWED = 'OPERATION_NOT_ALLOWED',
  
  // Server errors (500)
  INTERNAL_ERROR = 'INTERNAL_ERROR',
  DATABASE_ERROR = 'DATABASE_ERROR',
  UNEXPECTED_ERROR = 'UNEXPECTED_ERROR',
  
  // External service errors (503)
  EXTERNAL_SERVICE_UNAVAILABLE = 'EXTERNAL_SERVICE_UNAVAILABLE',
  EXTERNAL_SERVICE_ERROR = 'EXTERNAL_SERVICE_ERROR',
  RATE_LIMIT_EXCEEDED = 'RATE_LIMIT_EXCEEDED'
}

/**
 * Base application error with enhanced type safety
 */
export class AppError extends Error {
  /**
   * HTTP status code for this error
   */
  statusCode: HttpStatus;
  
  /**
   * Error code for client-side handling
   */
  code: ErrorCode;
  
  /**
   * Additional error details
   */
  details?: Record<string, any> | undefined;
  
  /**
   * Original error that caused this error
   */
  override cause?: unknown;

  /**
   * Create a new AppError
   * 
   * @param message - Human-readable error message
   * @param statusCode - HTTP status code
   * @param code - Error code for client-side handling
   * @param details - Additional error details
   * @param cause - Original error that caused this error
   */
  constructor(
    message: string, 
    statusCode: HttpStatus = HttpStatus.INTERNAL_SERVER_ERROR,
    code: ErrorCode = ErrorCode.UNEXPECTED_ERROR,
    details?: Record<string, any>,
    cause?: unknown
  ) {
    super(message);
    this.name = this.constructor.name;
    this.statusCode = statusCode;
    this.code = code;
    this.details = details;
    this.cause = cause;
    Error.captureStackTrace(this, this.constructor);
  }

  /**
   * Convert error to JSON response object
   */
  toJSON(): Record<string, any> {
    return {
      error: {
        message: this.message,
        code: this.code,
        details: this.details || {},
      }
    };
  }

  /**
   * Convert error to HTTP response object
   */
  toResponse(): { status: number; body: Record<string, any> } {
    return {
      status: this.statusCode,
      body: this.toJSON()
    };
  }
}

/**
 * Validation error for input validation failures
 */
export class ValidationError extends AppError {
  /**
   * Create a new ValidationError
   * 
   * @param message - Error message
   * @param code - Specific validation error code
   * @param details - Validation error details
   * @param cause - Original error that caused this error
   */
  constructor(
    message: string,
    code: ErrorCode = ErrorCode.INVALID_INPUT,
    details?: Record<string, any>,
    cause?: unknown
  ) {
    super(
      message,
      HttpStatus.BAD_REQUEST,
      code,
      details,
      cause
    );
  }
}

/**
 * Authentication error for authentication failures
 */
export class AuthenticationError extends AppError {
  /**
   * Create a new AuthenticationError
   * 
   * @param message - Error message
   * @param code - Specific authentication error code
   * @param details - Authentication error details
   * @param cause - Original error that caused this error
   */
  constructor(
    message: string,
    code: ErrorCode = ErrorCode.INVALID_CREDENTIALS,
    details?: Record<string, any>,
    cause?: unknown
  ) {
    super(
      message,
      HttpStatus.UNAUTHORIZED,
      code,
      details,
      cause
    );
  }
}

/**
 * Authorization error for permission failures
 */
export class AuthorizationError extends AppError {
  /**
   * Create a new AuthorizationError
   * 
   * @param message - Error message
   * @param code - Specific authorization error code
   * @param details - Authorization error details
   * @param cause - Original error that caused this error
   */
  constructor(
    message: string,
    code: ErrorCode = ErrorCode.INSUFFICIENT_PERMISSIONS,
    details?: Record<string, any>,
    cause?: unknown
  ) {
    super(
      message,
      HttpStatus.FORBIDDEN,
      code,
      details,
      cause
    );
  }
}

/**
 * Not found error for resource not found situations
 */
export class NotFoundError extends AppError {
  /**
   * Create a new NotFoundError
   * 
   * @param message - Error message
   * @param code - Specific not found error code
   * @param details - Not found error details
   * @param cause - Original error that caused this error
   */
  constructor(
    message: string,
    code: ErrorCode = ErrorCode.RESOURCE_NOT_FOUND,
    details?: Record<string, any>,
    cause?: unknown
  ) {
    super(
      message,
      HttpStatus.NOT_FOUND,
      code,
      details,
      cause
    );
  }
}

/**
 * Conflict error for resource conflicts
 */
export class ConflictError extends AppError {
  /**
   * Create a new ConflictError
   * 
   * @param message - Error message
   * @param code - Specific conflict error code
   * @param details - Conflict error details
   * @param cause - Original error that caused this error
   */
  constructor(
    message: string,
    code: ErrorCode = ErrorCode.DUPLICATE_RESOURCE,
    details?: Record<string, any>,
    cause?: unknown
  ) {
    super(
      message,
      HttpStatus.CONFLICT,
      code,
      details,
      cause
    );
  }
}

/**
 * Business logic error for business rule violations
 */
export class BusinessLogicError extends AppError {
  /**
   * Create a new BusinessLogicError
   * 
   * @param message - Error message
   * @param code - Specific business logic error code
   * @param details - Business logic error details
   * @param cause - Original error that caused this error
   */
  constructor(
    message: string,
    code: ErrorCode = ErrorCode.BUSINESS_RULE_VIOLATION,
    details?: Record<string, any>,
    cause?: unknown
  ) {
    super(
      message,
      HttpStatus.UNPROCESSABLE_ENTITY,
      code,
      details,
      cause
    );
  }
}

/**
 * External service error for third-party service failures
 */
export class ExternalServiceError extends AppError {
  /**
   * Create a new ExternalServiceError
   * 
   * @param message - Error message
   * @param code - Specific external service error code
   * @param details - External service error details
   * @param cause - Original error that caused this error
   */
  constructor(
    message: string,
    code: ErrorCode = ErrorCode.EXTERNAL_SERVICE_ERROR,
    details?: Record<string, any>,
    cause?: unknown
  ) {
    super(
      message,
      HttpStatus.SERVICE_UNAVAILABLE,
      code,
      details,
      cause
    );
  }
}

/**
 * Repository error for database operation failures
 */
export class RepositoryError extends AppError {
  /**
   * Create a new RepositoryError
   * 
   * @param message - Error message
   * @param code - Specific repository error code
   * @param details - Repository error details
   * @param cause - Original error that caused this error
   */
  constructor(
    message: string,
    code: ErrorCode = ErrorCode.DATABASE_ERROR,
    details?: Record<string, any>,
    cause?: unknown
  ) {
    super(
      message,
      HttpStatus.INTERNAL_SERVER_ERROR,
      code,
      details,
      cause
    );
  }
}

/**
 * Map unknown error to AppError
 * 
 * @param error - Unknown error
 * @param defaultMessage - Default error message
 * @returns AppError instance
 */
export function mapUnknownError(error: unknown, defaultMessage = 'An unexpected error occurred'): AppError {
  // If it's already an AppError, return it
  if (error instanceof AppError) {
    return error;
  }

  // If it's a standard Error, convert it
  if (error instanceof Error) {
    return new AppError(
      error.message || defaultMessage,
      HttpStatus.INTERNAL_SERVER_ERROR,
      ErrorCode.UNEXPECTED_ERROR,
      undefined,
      error
    );
  }

  // For other types, create a generic error
  return new AppError(
    typeof error === 'string' ? error : defaultMessage,
    HttpStatus.INTERNAL_SERVER_ERROR,
    ErrorCode.UNEXPECTED_ERROR,
    typeof error === 'object' && error !== null ? { originalError: error } : undefined,
    error
  );
}

/**
 * Create a user-friendly error message
 * 
 * @param error - Error to process
 * @param showDetails - Whether to include technical details (defaults to false in production)
 * @returns User-friendly error message
 */
export function createUserFriendlyMessage(error: unknown, showDetails = process.env['NODE_ENV'] !== 'production'): string {
  const appError = error instanceof AppError ? error : mapUnknownError(error);
  
  if (!showDetails) {
    // Generic messages based on status code
    switch (appError.statusCode) {
      case HttpStatus.BAD_REQUEST:
        return 'The request contains invalid data. Please check your input and try again.';
      case HttpStatus.UNAUTHORIZED:
        return 'Authentication is required to access this resource.';
      case HttpStatus.FORBIDDEN:
        return 'You don\'t have permission to access this resource.';
      case HttpStatus.NOT_FOUND:
        return 'The requested resource was not found.';
      case HttpStatus.CONFLICT:
        return 'The request conflicts with the current state of the resource.';
      case HttpStatus.UNPROCESSABLE_ENTITY:
        return 'The request couldn\'t be processed due to business rule violations.';
      case HttpStatus.SERVICE_UNAVAILABLE:
        return 'The service is temporarily unavailable. Please try again later.';
      default:
        return 'An unexpected error occurred. Please try again later.';
    }
  }
  
  // Return the actual error message with details in development/testing
  return appError.message;
}

/**
 * Convert any error to HTTP response
 * 
 * @param error - Error to convert
 * @param includeStack - Whether to include stack trace (defaults to false in production)
 * @returns HTTP response object
 */
export function errorToHttpResponse(
  error: unknown, 
  includeStack = process.env['NODE_ENV'] !== 'production'
): { status: number; body: Record<string, any> } {
  const appError = error instanceof AppError ? error : mapUnknownError(error);
  
  const response = appError.toResponse();
  
  // Add stack trace in non-production environments if requested
  if (includeStack && appError.stack) {
    response.body['error']['stack'] = appError.stack;
  }
  
  return response;
}
