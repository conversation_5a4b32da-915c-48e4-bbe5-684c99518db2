/**
 * Password Utility Functions
 * 
 * Shared utilities for password handling, validation, and security operations.
 */

import bcrypt from 'bcrypt';
import { ValidationError } from './errors.js';

/**
 * Hash a password using bcrypt
 * 
 * @param password - Plain text password to hash
 * @returns Promise with the hashed password
 */
export async function hashPassword(password: string): Promise<string> {
  const saltRounds = parseInt(process.env['BCRYPT_SALT_ROUNDS'] || '10', 10);
  return bcrypt.hash(password, saltRounds);
}

/**
 * Validate a password against its hashed version
 * 
 * @param plainPassword - Plain text password
 * @param hashedPassword - Hashed password to compare against
 * @returns Promise with boolean indicating if passwords match
 */
export async function validatePasswordMatch(
  plainPassword: string,
  hashedPassword: string
): Promise<boolean> {
  return bcrypt.compare(plainPassword, hashedPassword);
}

/**
 * Validate password complexity
 * 
 * @param password - Password to validate
 * @throws ValidationError if password does not meet requirements
 */
export function validatePasswordComplexity(password: string): void {
  // Check password length
  if (password.length < 8) {
    throw new ValidationError('Password must be at least 8 characters long');
  }
  
  // Check for at least one uppercase letter
  if (!/[A-Z]/.test(password)) {
    throw new ValidationError('Password must contain at least one uppercase letter');
  }
  
  // Check for at least one lowercase letter
  if (!/[a-z]/.test(password)) {
    throw new ValidationError('Password must contain at least one lowercase letter');
  }
  
  // Check for at least one number
  if (!/\d/.test(password)) {
    throw new ValidationError('Password must contain at least one number');
  }
  
  // Check for at least one special character
  if (!/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)) {
    throw new ValidationError('Password must contain at least one special character');
  }
} 