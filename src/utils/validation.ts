/**
 * @file validation.ts
 * @description Robust type definitions and validation utilities for service inputs
 * Contains Zod schemas, type guards, and helper functions for data validation
 */

import { z } from 'zod';
import {
  PrismaClient,
  Role,
  MembershipStatus,
  GiveawayStatus,
  PaymentStatus,
} from '@prisma/client';

/**
 * Common validation patterns
 */
export const Patterns = {
  EMAIL: /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,
  PASSWORD: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/,
  USERNAME: /^[a-zA-Z0-9_-]{3,20}$/,
  SLUG: /^[a-z0-9-]+$/,
  UUID: /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i,
  PHONE: /^\+?[1-9]\d{1,14}$/,
};

/**
 * Base schema for pagination and filtering
 */
export const paginationSchema = z.object({
  page: z.number().int().positive().optional().default(1),
  limit: z.number().int().positive().optional().default(10),
  sortBy: z.string().optional(),
  sortOrder: z.enum(['asc', 'desc']).optional().default('asc'),
});

/**
 * Common field schemas
 */
export const Fields = {
  id: z.string().uuid(),
  email: z.string().email().regex(Patterns.EMAIL),
  password: z.string().min(8).regex(Patterns.PASSWORD),
  name: z.string().min(2).max(100),
  username: z.string().regex(Patterns.USERNAME),
  phone: z.string().regex(Patterns.PHONE).optional(),
  slug: z.string().regex(Patterns.SLUG),
  date: z.coerce.date(),
  role: z.nativeEnum(Role),
  membershipStatus: z.nativeEnum(MembershipStatus),
  giveawayStatus: z.nativeEnum(GiveawayStatus),
  paymentStatus: z.nativeEnum(PaymentStatus),
};

/**
 * User validation schemas
 */
export const userSchemas = {
  create: z.object({
    email: Fields.email,
    password: Fields.password,
    name: Fields.name,
    username: Fields.username,
    phone: Fields.phone,
    addressLine1: z.string().min(1, { message: 'Address Line 1 is required' }),
    city: z.string().min(1, { message: 'City is required' }),
    state: z.string().min(1, { message: 'State is required' }),
    postalCode: z.string().min(1, { message: 'Postal code is required' }),
    country: z.string().min(1, { message: 'Country is required' }),
    role: Fields.role.optional().default(Role.USER),
    profileImage: z.string().url().nullable().optional(),
    preferences: z.record(z.unknown()).optional(),
  }),
  update: z.object({
    name: Fields.name.optional(),
    username: Fields.username.optional(),
    phone: Fields.phone,
    profileImage: z.string().url().nullable().optional(),
    preferences: z.record(z.unknown()).optional(),
  }),
  changePassword: z
    .object({
      currentPassword: z.string(),
      newPassword: Fields.password,
      confirmPassword: z.string(),
    })
    .refine(
      (data: { newPassword: string; confirmPassword: string }): boolean => {
        return data.newPassword === data.confirmPassword;
      },
      {
        message: "Passwords don't match",
        path: ['confirmPassword'],
      }
    ),
};

/**
 * Membership validation schemas
 */
export const membershipSchemas = {
  create: z.object({
    userId: Fields.id,
    tierId: Fields.id,
    status: Fields.membershipStatus.optional().default(MembershipStatus.ACTIVE),
    autoRenew: z.boolean().optional().default(true),
    startDate: Fields.date,
    endDate: Fields.date,
  }),
  update: z.object({
    status: Fields.membershipStatus.optional(),
    autoRenew: z.boolean().optional(),
    tierId: Fields.id.optional(),
    endDate: Fields.date.optional(),
  }),
};

/**
 * Giveaway validation schemas
 */
export const giveawaySchemas = {
  create: z
    .object({
      title: z.string().min(3).max(100),
      description: z.string().min(10),
      startDate: Fields.date,
      endDate: Fields.date,
      drawDate: Fields.date,
      status: Fields.giveawayStatus.optional().default(GiveawayStatus.DRAFT),
      minTier: Fields.id,
      entriesPerUser: z.number().int().positive(),
      slug: Fields.slug,
      featured: z.boolean().optional().default(false),
      imageUrl: z.string().url().optional(),
    })
    .refine(
      (data: { startDate: Date; endDate: Date }): boolean => {
        return data.startDate < data.endDate;
      },
      {
        message: 'End date must be after start date',
        path: ['endDate'],
      }
    )
    .refine(
      (data: { endDate: Date; drawDate: Date }): boolean => {
        return data.endDate <= data.drawDate;
      },
      {
        message: 'Draw date must be after or equal to end date',
        path: ['drawDate'],
      }
    ),
  update: z.object({
    title: z.string().min(3).max(100).optional(),
    description: z.string().min(10).optional(),
    startDate: Fields.date.optional(),
    endDate: Fields.date.optional(),
    drawDate: Fields.date.optional(),
    status: Fields.giveawayStatus.optional(),
    minTier: Fields.id.optional(),
    entriesPerUser: z.number().int().positive().optional(),
    slug: Fields.slug.optional(),
    featured: z.boolean().optional(),
    imageUrl: z.string().url().optional(),
  }),
};

/**
 * Payment validation schemas
 */
export const paymentSchemas = {
  /**
   * Schema for creating a payment intent
   * Only planId is required from the client; amount and currency are determined server-side
   */
  createIntent: z.object({
    planId: Fields.id,
    description: z.string().optional(),
    metadata: z.record(z.string()).optional(),
  }),

  /**
   * Schema for processing a payment
   * Both paymentIntentId and paymentMethodId are required
   */
  process: z.object({
    paymentIntentId: z.string().min(3, { message: "Payment intent ID is required" }),
    paymentMethodId: z.string().min(3, { message: "Payment method ID is required" }),
  }),
};

/**
 * Type guards for runtime type checking
 */

/**
 * Type guard to check if a value is a valid UUID
 */
export function isUuid(value: unknown): value is string {
  return typeof value === 'string' && Patterns.UUID.test(value);
}

/**
 * Type guard to check if a value is a valid email
 */
export function isEmail(value: unknown): value is string {
  return typeof value === 'string' && Patterns.EMAIL.test(value);
}

/**
 * Type guard to check if a value is a valid role
 */
export function isRole(value: unknown): value is Role {
  return typeof value === 'string' && Object.values(Role).includes(value as Role);
}

/**
 * Type guard to check if a value is a valid membership status
 */
export function isMembershipStatus(value: unknown): value is MembershipStatus {
  return (
    typeof value === 'string' && Object.values(MembershipStatus).includes(value as MembershipStatus)
  );
}

/**
 * Type guard to check if a value is a valid giveaway status
 */
export function isGiveawayStatus(value: unknown): value is GiveawayStatus {
  return (
    typeof value === 'string' && Object.values(GiveawayStatus).includes(value as GiveawayStatus)
  );
}

/**
 * Utility functions for handling nullable fields
 */

/**
 * Safely handle nullable fields
 * @param value The value to check
 * @param defaultValue The default value to return if the value is null/undefined
 */
export function nullable<T>(value: T | null | undefined, defaultValue: T): T {
  return value === null || value === undefined ? defaultValue : value;
}

/**
 * Convert a value to null if it's undefined or empty string
 */
export function nullableString(value: string | null | undefined): string | null {
  if (value === undefined || value === '') return null;
  return value;
}

/**
 * Helper to safely parse JSON with a fallback
 */
export function safeParseJSON<T>(json: string | null | undefined, fallback: T): T {
  if (!json) return fallback;
  try {
    return JSON.parse(json) as T;
  } catch (e) {
    return fallback;
  }
}

/**
 * Mapping functions between DTOs and entity types
 */

/**
 * Generic mapper function to convert from DTO to entity
 * @param dto Data Transfer Object
 * @param mapperFn Optional custom mapper function
 */
export function mapDtoToEntity<DTO, Entity>(
  dto: DTO,
  mapperFn?: (dto: DTO) => Partial<Entity>
): Partial<Entity> {
  if (mapperFn) {
    return mapperFn(dto);
  }
  return dto as unknown as Partial<Entity>;
}

/**
 * Generic mapper function to convert from entity to DTO
 * @param entity Entity object
 * @param mapperFn Optional custom mapper function
 */
export function mapEntityToDto<Entity, DTO>(
  entity: Entity,
  mapperFn?: (entity: Entity) => DTO
): DTO {
  if (mapperFn) {
    return mapperFn(entity);
  }
  return entity as unknown as DTO;
}

/**
 * Type-safe enum validation helpers
 */

/**
 * Validate that a value is a valid enum value
 * @param enumObj The enum object
 * @param value The value to check
 */
export function validateEnum<T extends Record<string, string | number>>(
  enumObj: T,
  value: unknown
): value is T[keyof T] {
  return Object.values(enumObj).includes(value as T[keyof T]);
}

/**
 * Get a valid enum value or throw an error
 * @param enumObj The enum object
 * @param value The value to check
 * @param defaultValue Optional default value if the value is invalid
 */
export function getEnumValue<T extends Record<string, string | number>>(
  enumObj: T,
  value: unknown,
  defaultValue?: T[keyof T]
): T[keyof T] {
  if (validateEnum(enumObj, value)) return value;
  if (defaultValue !== undefined) return defaultValue;
  throw new Error(`Invalid enum value: ${String(value)}`);
}

/**
 * Validate input data using Zod schemas
 * @param schema Zod schema
 * @param data Data to validate
 */
export function validateWithZod<T>(schema: z.ZodType<T>, data: unknown): T {
  return schema.parse(data);
}

/**
 * Validate input data with Zod and return the result or null on error
 * @param schema Zod schema
 * @param data Data to validate
 */
export function safeValidate<T>(
  schema: z.ZodType<T>,
  data: unknown
): { success: true; data: T } | { success: false; error: z.ZodError } {
  const result = schema.safeParse(data);
  return result;
}

/**
 * Async validation with database checks
 */

/**
 * Check if a field value is unique in a table
 * @param prisma PrismaClient instance
 * @param table Table name
 * @param field Field name
 * @param value Value to check
 * @param excludeId Optional ID to exclude from the check (for updates)
 */
export async function isUnique(
  prisma: PrismaClient,
  table: string,
  field: string,
  value: string,
  excludeId?: string
): Promise<boolean> {
  const whereClause: Record<string, unknown> = { [field]: value };

  if (excludeId) {
    whereClause['id'] = { not: excludeId };
  }

  // Define a type-safe interface for the model operations
  interface PrismaModel {
    count: (args: { where: Record<string, unknown> }) => Promise<number>;
  }

  // Use proper type casting to avoid 'any'
  const model = prisma[table as keyof PrismaClient] as unknown as PrismaModel;
  const count = await model.count({
    where: whereClause,
  });

  return count === 0;
}

/**
 * Create a Zod schema refinement for checking uniqueness
 * @param prisma PrismaClient instance
 * @param table Table name
 * @param field Field name
 * @param excludeIdField Field name for the ID to exclude
 */
export function uniqueFieldRefinement<T extends Record<string, unknown>>(
  prisma: PrismaClient,
  table: string,
  field: string,
  excludeIdField?: keyof T
): (data: T) => Promise<boolean> {
  return async (data: T): Promise<boolean> => {
    const excludeId = excludeIdField ? (data[excludeIdField] as string) : undefined;
    return isUnique(prisma, table, field, data[field] as string, excludeId);
  };
}
