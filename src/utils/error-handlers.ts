/**
 * Error Handler Utilities
 * 
 * This file contains utilities for handling errors in a consistent way throughout
 * the application, with a focus on database errors and providing user-friendly messages.
 */

import { Prisma } from '@prisma/client';
import {
  ValidationError,
  NotFoundError,
  ConflictError,
  BusinessLogicError,
  RepositoryError,
  ExternalServiceError,
  ErrorCode,
  createUserFriendlyMessage
} from './errors.js';
import logger from './logger.js';

/**
 * Prisma error codes for common error scenarios
 */
enum PrismaErrorCode {
  // Unique constraint violations (e.g., duplicate email)
  UNIQUE_CONSTRAINT_VIOLATION = 'P2002',
  
  // Foreign key constraint violations
  FOREIGN_KEY_CONSTRAINT_VIOLATION = 'P2003',
  
  // Record not found
  RECORD_NOT_FOUND = 'P2025',
  
  // Required field missing
  REQUIRED_FIELD_MISSING = 'P2012',
  
  // Value out of range
  VALUE_OUT_OF_RANGE = 'P2007',
  
  // Database query interpretation errors
  QUERY_INTERPRETATION = 'P2010'
}

/**
 * Entity type definition
 */
export interface Entity {
  id: string | number;
  [key: string]: any;
}

/**
 * Database error handler utility
 * Maps Prisma database errors to domain-specific error classes
 * 
 * @param error - The error to handle
 * @param entityName - The name of the entity being operated on
 * @returns Appropriate domain error
 */
export function handleDatabaseError(error: unknown, entityName: string): Error {
  // Log the original error for debugging
  logger.debug(`Database error for ${entityName}: `, error);
  
  // Handle Prisma-specific errors
  if (error instanceof Prisma.PrismaClientKnownRequestError) {
    const meta = error.meta || {};
    const target = Array.isArray(meta['target']) 
      ? meta['target'].join(', ') 
      : (meta['target'] as string) || 'unknown field';
    
    switch (error.code) {
      case PrismaErrorCode.UNIQUE_CONSTRAINT_VIOLATION:
        return new ConflictError(
          `${entityName} with this ${target} already exists`, 
          ErrorCode.DUPLICATE_RESOURCE,
          { field: target },
          error
        );
        
      case PrismaErrorCode.FOREIGN_KEY_CONSTRAINT_VIOLATION:
        return new ValidationError(
          `Related ${meta['field_name'] || 'record'} does not exist`,
          ErrorCode.INVALID_INPUT,
          { field: meta['field_name'] },
          error
        );
        
      case PrismaErrorCode.RECORD_NOT_FOUND:
        return new NotFoundError(
          `${entityName} not found`,
          ErrorCode.RESOURCE_NOT_FOUND,
          { entity: entityName },
          error
        );
        
      case PrismaErrorCode.REQUIRED_FIELD_MISSING:
        return new ValidationError(
          `Required field ${meta['field_name'] || ''} is missing`,
          ErrorCode.MISSING_REQUIRED_FIELD,
          { field: meta['field_name'] },
          error
        );
        
      case PrismaErrorCode.VALUE_OUT_OF_RANGE:
        return new ValidationError(
          `Value out of range for ${meta['field_name'] || 'field'}`,
          ErrorCode.INVALID_INPUT,
          { field: meta['field_name'] },
          error
        );
        
      default:
        return new RepositoryError(
          `Database error: ${error.message}`,
          ErrorCode.DATABASE_ERROR,
          { code: error.code },
          error
        );
    }
  } 
  
  // Handle Prisma validation errors
  if (error instanceof Prisma.PrismaClientValidationError) {
    return new ValidationError(
      `Validation error for ${entityName}: ${error.message}`,
      ErrorCode.INVALID_INPUT,
      { entity: entityName },
      error
    );
  }
  
  // Handle Prisma unknown errors
  if (error instanceof Prisma.PrismaClientUnknownRequestError) {
    return new RepositoryError(
      `Unknown database error for ${entityName}`,
      ErrorCode.DATABASE_ERROR,
      { entity: entityName },
      error
    );
  }
  
  // Handle standard errors
  if (error instanceof Error) {
    return new RepositoryError(
      `Repository error for ${entityName}: ${error.message}`,
      ErrorCode.DATABASE_ERROR,
      { entity: entityName },
      error
    );
  }
  
  // Handle any other type of error
  return new RepositoryError(
    `Unknown repository error for ${entityName}`,
    ErrorCode.DATABASE_ERROR,
    { entity: entityName },
    error
  );
}

/**
 * Type for database operation
 */
type DatabaseOperation<T> = () => Promise<T>;

/**
 * Execute a database operation with standardized error handling
 * 
 * @param operation - The database operation to execute
 * @param entityName - The name of the entity being operated on
 * @param errorMessage - Optional custom error message
 * @returns Promise with the operation result
 */
export async function executeWithErrorHandling<T>(
  operation: DatabaseOperation<T>,
  entityName: string,
  errorMessage?: string
): Promise<T> {
  try {
    return await operation();
  } catch (error) {
    const mappedError = handleDatabaseError(error, entityName);
    logger.error(`Error in ${entityName} operation: ${errorMessage || mappedError.message}`, { 
      error: mappedError, 
      entity: entityName 
    });
    throw mappedError;
  }
}

/**
 * Create a function to handle "not found" scenarios
 * 
 * @param entityName - The name of the entity
 * @param identifier - The identifier used in the query (e.g., ID, email)
 * @param identifierValue - The value of the identifier
 * @returns Function that throws NotFoundError if entity is null/undefined
 */
export function ensureExists<T>(
  entityName: string, 
  identifier: string = 'ID', 
  identifierValue?: string | number
): (entity: T | null | undefined) => T {
  return (entity: T | null | undefined): T => {
    if (entity === null || entity === undefined) {
      const idValue = identifierValue !== undefined ? identifierValue : 'specified';
      throw new NotFoundError(
        `${entityName} with ${identifier} ${idValue} not found`,
        ErrorCode.RESOURCE_NOT_FOUND,
        { 
          entity: entityName, 
          identifier, 
          value: identifierValue 
        }
      );
    }
    return entity;
  };
}

/**
 * Create a function to validate entities against a business rule
 * 
 * @param rule - The validation function
 * @param errorMessage - The error message if validation fails
 * @returns Function that throws BusinessLogicError if validation fails
 */
export function validateBusinessRule<T>(
  rule: (entity: T) => boolean,
  errorMessage: string
): (entity: T) => T {
  return (entity: T): T => {
    if (!rule(entity)) {
      throw new BusinessLogicError(
        errorMessage,
        ErrorCode.BUSINESS_RULE_VIOLATION
      );
    }
    return entity;
  };
}

/**
 * Higher-order function to handle specific error types in a custom way
 * 
 * @param operation - The operation to execute
 * @param errorHandlers - Map of error types to handler functions
 * @param defaultHandler - Optional default error handler
 * @returns Promise with operation result
 */
export async function handleSpecificErrors<T>(
  operation: () => Promise<T>,
  errorHandlers: Map<Function, (error: Error) => Error | Promise<Error>>,
  defaultHandler?: (error: unknown) => Error | Promise<Error>
): Promise<T> {
  try {
    return await operation();
  } catch (error) {
    // Find a handler for this error type
    for (const [ErrorType, handler] of errorHandlers.entries()) {
      if (error instanceof ErrorType) {
        const handledError = await handler(error as Error);
        throw handledError;
      }
    }
    
    // Use default handler if provided, otherwise rethrow
    if (defaultHandler) {
      throw await defaultHandler(error);
    }
    
    throw error;
  }
}

/**
 * Create a logging wrapper for database operations
 * 
 * @param operation - The operation to execute
 * @param operationName - Name of the operation for logging
 * @param entityName - Name of the entity being operated on
 * @returns Promise with operation result
 */
export async function withLogging<T>(
  operation: () => Promise<T>,
  operationName: string,
  entityName: string
): Promise<T> {
  logger.debug(`Starting ${operationName} on ${entityName}`);
  
  try {
    const startTime = Date.now();
    const result = await operation();
    const duration = Date.now() - startTime;
    
    logger.debug(`Completed ${operationName} on ${entityName} in ${duration}ms`);
    return result;
  } catch (error) {
    logger.error(`Failed ${operationName} on ${entityName}`, { error });
    throw error;
  }
}

/**
 * Create a retry wrapper for operations that might fail temporarily
 * 
 * @param operation - The operation to execute
 * @param maxRetries - Maximum number of retry attempts
 * @param delayMs - Delay between retries in milliseconds
 * @param retryableErrors - Array of error types that should trigger retry
 * @returns Promise with operation result
 */
export async function withRetry<T>(
  operation: () => Promise<T>,
  maxRetries: number = 3,
  delayMs: number = 500,
  retryableErrors: Array<Function> = [ExternalServiceError]
): Promise<T> {
  let lastError: unknown;
  
  for (let attempt = 1; attempt <= maxRetries + 1; attempt++) {
    try {
      return await operation();
    } catch (error) {
      lastError = error;
      
      // Check if this error type is retryable
      const isRetryable = retryableErrors.some(ErrorType => error instanceof ErrorType);
      
      if (!isRetryable || attempt > maxRetries) {
        throw error;
      }
      
      // Log retry attempt
      logger.warn(`Operation failed, retrying (${attempt}/${maxRetries})`, { error });
      
      // Wait before retrying
      await new Promise(resolve => setTimeout(resolve, delayMs * attempt));
    }
  }
  
  // This should never be reached due to the throw in the catch block
  throw lastError;
}

/**
 * Extract user-friendly validation error messages from a validation error
 * 
 * @param error - The validation error
 * @returns Object with field names and error messages
 */
export function extractValidationErrors(error: unknown): Record<string, string> {
  if (error instanceof ValidationError && error.details) {
    return error.details as Record<string, string>;
  }
  
  return {
    general: createUserFriendlyMessage(error)
  };
}

/**
 * Create a transaction context for operations that need to be atomic
 * 
 * @param operation - The operation to execute within a transaction
 * @param prismaClient - The Prisma client instance
 * @returns Promise with operation result
 */
export async function withTransaction<T>(
  operation: (tx: any) => Promise<T>,
  prismaClient: any
): Promise<T> {
  return prismaClient.$transaction(async (tx: any) => {
    return await operation(tx);
  });
} 