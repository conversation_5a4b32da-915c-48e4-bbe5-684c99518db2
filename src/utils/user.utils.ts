/**
 * User Utility Functions
 * 
 * Shared utilities for user data transformation and mapping operations.
 */

import { User } from '@prisma/client';
import { UserResponseDto } from '../dtos/user.dto.js';

/**
 * Map a User entity to a UserResponseDto
 * 
 * @param user - User entity from the database
 * @returns UserResponseDto with selected user properties
 */
export function mapUserToDto(user: User): UserResponseDto {
  // Create the response DTO with required fields
  const responseDto: UserResponseDto = {
    id: user.id,
    email: user.email,
    firstName: user.firstName,
    lastName: user.lastName,
    role: user.role,
    isVerified: user.isVerified,
    createdAt: user.createdAt,
    updatedAt: user.updatedAt
  };
  
  // Add optional fields if they exist
  if (user.profileImage) {
    responseDto.profileImage = user.profileImage;
  }
  
  // Add extended fields if they exist
  if ((user as any).preferences) {
    responseDto.preferences = (user as any).preferences;
  }
  
  if ((user as any).lastLoginAt) {
    responseDto.lastLoginAt = (user as any).lastLoginAt;
  }
  
  return responseDto;
} 