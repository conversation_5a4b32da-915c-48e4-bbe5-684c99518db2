/* Global styles for Winners Society app */

/* Import Angular Material theming */
@use '@angular/material' as mat;

/* Import design system */
@use './assets/scss/_index' as *;

/* General styling */
html, body {
  height: 100%;
  width: 100%;
  overflow-x: hidden;
}

body {
  margin: 0;
  font-family: var(--font-primary);
  background-color: var(--bg-light);
  color: var(--text-primary);
  line-height: var(--leading-normal);
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  margin-top: 0;
  margin-bottom: var(--space-4);
  font-weight: var(--font-semibold);
  line-height: var(--leading-tight);
  color: var(--text-primary);
}

h1 { font-size: var(--text-4xl); }
h2 { font-size: var(--text-3xl); }
h3 { font-size: var(--text-2xl); }
h4 { font-size: var(--text-xl); }
h5 { font-size: var(--text-lg); }
h6 { font-size: var(--text-md); }

/* Links */
a {
  color: var(--primary);
  text-decoration: none;
  transition: color var(--duration-200) var(--ease-in-out);

  &:hover {
    color: var(--primary-dark);
    text-decoration: underline;
  }

  &:focus {
    outline: none;
    box-shadow: var(--shadow-outline);
  }
}

/* Additional utility classes */
.cursor-pointer {
  cursor: pointer;
}

.transition-all {
  transition: all var(--duration-300) var(--ease-in-out);
}

.shadow-hover {
  transition: box-shadow var(--duration-300) var(--ease-out),
              transform var(--duration-300) var(--ease-out);

  &:hover {
    box-shadow: var(--shadow-lg);
    transform: translateY(-4px);
  }
}

.text-truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.focus-ring {
  &:focus {
    outline: none;
    box-shadow: var(--shadow-outline);
  }
}

/* Additional animations */
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.slide-in-up {
  animation: slideInUp var(--duration-300) var(--ease-out);
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .slide-in-up,
  .fade-in,
  .shadow-hover:hover {
    animation: none;
    transition: none;
    transform: none;
  }
}

/* Material component customizations */

/* Badge customizations */
.badge {
  padding: 0.35em 0.65em;
  font-weight: var(--font-semibold);
  border-radius: var(--radius-full);
}

/* Material Button customizations */
.mat-mdc-button,
.mat-mdc-raised-button,
.mat-mdc-outlined-button,
.mat-mdc-unelevated-button {
  border-radius: var(--radius-lg) !important;
  font-weight: var(--font-medium) !important;
  letter-spacing: var(--tracking-wide) !important;
  transition: all var(--duration-200) var(--ease-in-out) !important;

  &:hover {
    transform: translateY(-2px);
  }

  &:active {
    transform: translateY(0);
  }

  &:focus {
    box-shadow: var(--shadow-outline);
  }
}

/* Material Card customizations */
.mat-mdc-card {
  border-radius: var(--radius-2xl) !important;
  box-shadow: var(--shadow-md) !important;
  overflow: hidden;
  transition: transform var(--duration-300) var(--ease-out),
              box-shadow var(--duration-300) var(--ease-out) !important;

  &:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg) !important;
  }

  .mat-mdc-card-header {
    padding: var(--space-4) var(--space-6) !important;
  }

  .mat-mdc-card-content {
    padding: var(--space-6) !important;
  }

  .mat-mdc-card-actions {
    padding: var(--space-4) var(--space-6) !important;
  }
}
/* Form customizations */
.mat-mdc-form-field {
  width: 100%;
  margin-bottom: var(--space-4);

  .mat-mdc-text-field-wrapper {
    border-radius: var(--radius-lg) !important;
  }

  .mat-mdc-form-field-focus-overlay {
    background-color: rgba(var(--primary-rgb), 0.05);
  }

  &.mat-focused {
    .mat-mdc-form-field-focus-overlay {
      opacity: 1;
    }
  }
}

/* Container and layout utilities */
.container {
  max-width: 1280px;
  margin: 0 auto;
  padding: 0 var(--space-4);

  @media (min-width: 768px) {
    padding: 0 var(--space-6);
  }
}

.spacer {
  flex: 1 1 auto;
}

/* Accessibility utilities */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}

/* High contrast mode adjustments */
@media (forced-colors: active) {
  .btn,
  .card,
  .mat-mdc-card,
  .mat-mdc-button {
    border: 1px solid ButtonText;
  }
}
