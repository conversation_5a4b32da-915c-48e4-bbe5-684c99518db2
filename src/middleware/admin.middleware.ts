import { Request, Response, NextFunction } from 'express';
import { Role } from '@prisma/client';

/**
 * Admin middleware
 * 
 * This middleware checks if the authenticated user has admin privileges.
 * It expects the user object to be attached to the request by the auth middleware.
 */
export function createAdminMiddleware() {
  return async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      // Check if user exists on the request (should be set by auth middleware)
      const user = (req as any).user;
      
      console.log('Admin middleware - User object:', JSON.stringify(user));
      
      if (!user) {
        res.status(401).json({
          success: false,
          message: 'Authentication required'
        });
        return;
      }
      
      // Check if user has admin role
      console.log(`User role: ${user.role}, type: ${typeof user.role}`);
      console.log(`Comparison: role === 'ADMIN': ${user.role === 'ADMIN'}, role === Role.ADMIN: ${user.role === Role.ADMIN}`);
      
      if (user.role !== 'ADMIN' && user.role !== Role.ADMIN) {
        console.log('User is not admin, returning 403');
        res.status(403).json({
          success: false,
          message: 'Admin access required'
        });
        return;
      }
      
      // User is admin, proceed to next middleware/controller
      console.log('User is admin, proceeding to next middleware');
      next();
    } catch (error) {
      console.error('Error in admin middleware:', error);
      next(error);
    }
  };
} 