import { Request, Response, NextFunction } from 'express';
import { AuthService } from '../services/auth/auth-service.interface.js';
import { getPrismaClient } from '../repositories/index.js';

// Extended request interface with authenticated user
export interface AuthenticatedRequest extends Request {
  user: {
    id: string;
    email: string;
    role: string;
    isVerified?: boolean;
  };
}

/**
 * Authentication middleware factory
 *
 * @param authService - Authentication service for token validation
 * @returns Middleware function
 */
export function createAuthMiddleware(authService: AuthService) {
  /**
   * Authenticate user middleware
   *
   * @param req - Express request
   * @param res - Express response
   * @param next - Next function
   */
  return async function authenticateUser(
    req: Request,
    res: Response,
    next: NextFunction
  ): Promise<void> {
    try {
      // Get authorization header
      const authHeader = req.headers.authorization;

      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        res.status(401).json({
          success: false,
          message: 'Authentication required'
        });
        return;
      }

      // Extract token
      const token = authHeader.split(' ')[1];

      // Validate token
      const payload = await authService.validateToken(token || '');
      console.log('Auth middleware - Token payload:', JSON.stringify(payload));

      if (!payload) {
        res.status(401).json({
          success: false,
          message: 'Invalid or expired token'
        });
        return;
      }

      // Fetch user's current verification status from database
      const prisma = getPrismaClient();
      const user = await prisma.user.findUnique({
        where: { id: payload.userId },
        select: { isVerified: true }
      });

      // Attach user data to request
      (req as AuthenticatedRequest).user = {
        id: payload.userId,
        email: payload.email,
        role: payload.role,
        isVerified: user?.isVerified || false
      };

      console.log('Auth middleware - Attached user object:', JSON.stringify((req as AuthenticatedRequest).user));

      next();
    } catch (error) {
      console.error('Error in auth middleware:', error);
      res.status(401).json({
        success: false,
        message: 'Authentication failed'
      });
    }
  };
}

// Export a placeholder for the middleware to be replaced with the actual implementation when the app is initialized
export const authenticateUser = (_req: Request, res: Response, _next: NextFunction): void => {
  res.status(401).json({
    success: false,
    message: 'Authentication middleware not initialized'
  });
};

/**
 * Authorization middleware
 * Checks if the authenticated user has the required roles
 *
 * @param allowedRoles - Array of roles allowed to access the route
 * @returns Middleware function
 */
export const authorizeRoles = (allowedRoles: string[]) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    try {
      const authenticatedReq = req as AuthenticatedRequest;

      // Check if user exists on request (should be added by authenticateUser middleware)
      if (!authenticatedReq.user) {
        res.status(401).json({
          success: false,
          message: 'Authentication required'
        });
        return;
      }

      // Check if user has required role
      if (!allowedRoles.includes(authenticatedReq.user.role)) {
        res.status(403).json({
          success: false,
          message: 'Insufficient permissions'
        });
        return;
      }

      next();
    } catch (error) {
      console.error('Error in authorization middleware:', error);
      res.status(500).json({
        success: false,
        message: 'Authorization failed'
      });
    }
  };
};