/**
 * Email Verification Middleware
 * 
 * This middleware checks if the authenticated user has verified their email address.
 * It should be used on routes that require email verification.
 */

import { Request, Response, NextFunction } from 'express';

// Request interface with user property for authenticated routes
interface AuthenticatedRequest extends Request {
  user?: {
    id: string;
    email: string;
    role: string;
    isVerified?: boolean;
  };
}

/**
 * Middleware to check if user has verified their email
 * This middleware should be used after the authentication middleware
 * 
 * @param req - Express request
 * @param res - Express response
 * @param next - Next function
 */
export function requireEmailVerification(
  req: Request,
  res: Response,
  next: NextFunction
): void {
  try {
    const authenticatedReq = req as AuthenticatedRequest;

    // Check if user exists on request (should be added by authenticateUser middleware)
    if (!authenticatedReq.user) {
      res.status(401).json({
        success: false,
        message: 'Authentication required'
      });
      return;
    }

    // Check if user has verified their email
    if (!authenticatedReq.user.isVerified) {
      res.status(403).json({
        success: false,
        message: 'Email verification required. Please check your email and click the verification link.',
        code: 'EMAIL_NOT_VERIFIED',
        data: {
          email: authenticatedReq.user.email,
          resendEndpoint: '/api/auth/resend-verification'
        }
      });
      return;
    }

    // User is verified, proceed to next middleware
    next();
  } catch (error) {
    console.error('Error in email verification middleware:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
}

/**
 * Optional email verification middleware
 * This middleware checks email verification but doesn't block access
 * It adds a warning to the response if email is not verified
 * 
 * @param req - Express request
 * @param res - Express response
 * @param next - Next function
 */
export function checkEmailVerification(
  req: Request,
  res: Response,
  next: NextFunction
): void {
  try {
    const authenticatedReq = req as AuthenticatedRequest;

    // Check if user exists and is not verified
    if (authenticatedReq.user && !authenticatedReq.user.isVerified) {
      // Add verification warning to response headers
      res.setHeader('X-Email-Verification-Required', 'true');
      res.setHeader('X-Email-Verification-Email', authenticatedReq.user.email);
    }

    // Always proceed to next middleware
    next();
  } catch (error) {
    console.error('Error in email verification check middleware:', error);
    // Don't block the request, just log the error
    next();
  }
}
