/**
 * Enrollment Job Processor
 *
 * This file contains the processors for enrollment queue jobs.
 * It handles the actual business logic for automatic enrollment.
 */

import { Job } from 'bull';
import { PrismaClient } from '@prisma/client';
import logger from '../../utils/logger.js';
import { EnrollmentJobType, EnrollmentJobData } from '../config.js';

// Initialize Prisma client with connection from environment variable
const prisma = new PrismaClient({
  datasources: {
    db: {
      url: process.env['DATABASE_URL'] || 'postgresql://postgres@localhost:5432/winnerssociety?connect_timeout=10&sslmode=prefer&schema=public'
    }
  }
});

/**
 * Process a giveaway activation job
 * This enrolls all eligible users in a newly activated giveaway
 *
 * @param job - The Bull job containing giveaway activation data
 */
export const processGiveawayActivation = async (job: Job<EnrollmentJobData>): Promise<void> => {
  const { giveawayId } = job.data;

  if (!giveawayId) {
    throw new Error('Giveaway ID is required');
  }

  logger.info(`Processing giveaway activation job ${job.id} for giveaway ${giveawayId}`);

  try {
    // Get the giveaway
    const giveaway = await prisma.giveaway.findUnique({
      where: { id: giveawayId }
    });

    if (!giveaway) {
      throw new Error(`Giveaway with ID ${giveawayId} not found`);
    }

    if (!giveaway.autoEnrollment) {
      logger.info(`Skipping automatic enrollment for giveaway ${giveawayId} as it's disabled`);
      return;
    }

    // Update the giveaway with the job ID
    await prisma.giveaway.update({
      where: { id: giveawayId },
      data: { lastEnrollmentJobId: job.id.toString() }
    });

    // Get eligible membership tiers
    const eligibleTierIds = giveaway.eligibleMembershipTierIds as string[];

    if (!eligibleTierIds.length) {
      logger.info(`No eligible membership tiers defined for giveaway ${giveawayId}`);
      return;
    }

    // Find all eligible users with active memberships in the eligible tiers
    const eligibleUsers = await prisma.user.findMany({
      where: {
        isVerified: true,
        isActive: true,
        memberships: {
          some: {
            status: 'ACTIVE',
            membershipTierId: {
              in: eligibleTierIds
            }
          }
        }
      },
      include: {
        memberships: {
          where: {
            status: 'ACTIVE',
            membershipTierId: {
              in: eligibleTierIds
            }
          },
          include: {
            membershipTier: true
          }
        }
      }
    });

    logger.info(`Found ${eligibleUsers.length} eligible users for giveaway ${giveawayId}`);

    // Process users in batches to avoid memory issues
    const batchSize = 100;
    let successCount = 0;
    let failureCount = 0;

    // Create an audit log entry for this batch enrollment
    const auditLog = await prisma.enrollmentAuditLog.create({
      data: {
        giveawayId,
        action: 'AUTO_ENROLLMENT',
        affectedUserCount: eligibleUsers.length,
        status: 'SUCCESS',
        details: {
          jobId: job.id.toString(),
          eligibleTierIds
        }
      }
    });

    // Process users in batches
    for (let i = 0; i < eligibleUsers.length; i += batchSize) {
      const batch = eligibleUsers.slice(i, i + batchSize);

      // Use a transaction for each batch
      await prisma.$transaction(async (tx) => {
        for (const user of batch) {
          try {
            // Skip users without active memberships
            if (!user.memberships.length) continue;

            // Get the user's highest tier membership
            const membership = user.memberships.reduce((highest, current) => {
              const highestEntryAllocation = highest?.membershipTier.entryAllocation || 0;
              const currentEntryAllocation = current.membershipTier.entryAllocation;
              return currentEntryAllocation > highestEntryAllocation ? current : highest;
            }, user.memberships[0]);

            // Check if user already has an entry for this giveaway
            const existingEntry = await tx.entry.findUnique({
              where: {
                userId_giveawayId: {
                  userId: user.id,
                  giveawayId
                }
              }
            });

            if (existingEntry) {
              // If entry exists, update it if the new tier has more entries
              if (membership && membership.membershipTier.entryAllocation > existingEntry.quantity) {
                await tx.entry.update({
                  where: { id: existingEntry.id },
                  data: {
                    quantity: membership.membershipTier.entryAllocation,
                    source: 'MEMBERSHIP_UPDATED',
                    membershipId: membership.id,
                    membershipTierId: membership.membershipTierId,
                    lastUpdatedDate: new Date()
                  }
                });

                logger.debug(`Updated entry for user ${user.id} in giveaway ${giveawayId}`);
              }
            } else {
              // Create a new entry
              if (membership) {
                await tx.entry.create({
                  data: {
                    userId: user.id,
                    giveawayId,
                    membershipId: membership.id,
                    membershipTierId: membership.membershipTierId,
                    quantity: membership.membershipTier.entryAllocation,
                    source: 'MEMBERSHIP',
                    entryMethod: 'MEMBERSHIP'
                  }
                });
              }

              logger.debug(`Created entry for user ${user.id} in giveaway ${giveawayId}`);
            }

            successCount++;
          } catch (error) {
            failureCount++;

            // Log the error
            logger.error(`Failed to enroll user ${user.id} in giveaway ${giveawayId}:`, error);

            // Create a failed enrollment record
            await tx.failedEnrollment.create({
              data: {
                giveawayId,
                userId: user.id,
                membershipTierId: user.memberships[0]?.membershipTierId || null,
                jobId: job.id.toString(),
                errorMessage: error instanceof Error ? error.message : 'Unknown error',
                errorCode: error instanceof Error ? error.name : 'UNKNOWN',
                status: 'PENDING'
              }
            });
          }
        }
      });

      // Update progress
      await job.progress((i + batch.length) / eligibleUsers.length * 100);
    }

    // Update the audit log with final counts
    await prisma.enrollmentAuditLog.update({
      where: { id: auditLog.id },
      data: {
        status: failureCount > 0 ? 'PARTIAL_SUCCESS' : 'SUCCESS',
        details: {
          jobId: job.id.toString(),
          successCount,
          failureCount,
          totalProcessed: successCount + failureCount
        }
      }
    });

    logger.info(`Completed giveaway activation job ${job.id} for giveaway ${giveawayId}`);
    logger.info(`Successfully enrolled ${successCount} users, failed to enroll ${failureCount} users`);
  } catch (error) {
    logger.error(`Error processing giveaway activation job ${job.id}:`, error);

    // Create an audit log entry for the failed job
    await prisma.enrollmentAuditLog.create({
      data: {
        giveawayId,
        action: 'AUTO_ENROLLMENT',
        status: 'FAILURE',
        details: {
          jobId: job.id.toString(),
          error: error instanceof Error ? error.message : 'Unknown error'
        }
      }
    });

    throw error;
  }
};

/**
 * Process a new membership job
 * This enrolls a new member in all eligible active giveaways
 *
 * @param job - The Bull job containing new membership data
 */
export const processNewMembership = async (job: Job<EnrollmentJobData>): Promise<void> => {
  const { userId, membershipId, membershipTierId } = job.data;

  if (!userId || !membershipId || !membershipTierId) {
    throw new Error('User ID, membership ID, and membership tier ID are required');
  }

  logger.info(`Processing new membership job ${job.id} for user ${userId}`);

  try {
    // Get the user
    const user = await prisma.user.findUnique({
      where: { id: userId },
      include: {
        memberships: {
          where: {
            id: membershipId,
            status: 'ACTIVE'
          }
        }
      }
    });

    if (!user) {
      throw new Error(`User with ID ${userId} not found`);
    }

    if (!user.isVerified) {
      logger.info(`Skipping enrollment for user ${userId} as email is not verified`);
      return;
    }

    if (!user.memberships.length) {
      throw new Error(`Active membership with ID ${membershipId} not found for user ${userId}`);
    }

    // Get the membership tier
    const membershipTier = await prisma.membershipTier.findUnique({
      where: { id: membershipTierId }
    });

    if (!membershipTier) {
      throw new Error(`Membership tier with ID ${membershipTierId} not found`);
    }

    // Find all active giveaways with auto-enrollment enabled
    const activeGiveaways = await prisma.giveaway.findMany({
      where: {
        status: 'ACTIVE',
        autoEnrollment: true,
        startDate: { lte: new Date() },
        endDate: { gte: new Date() },
        eligibleMembershipTierIds: {
          has: membershipTierId
        }
      }
    });

    logger.info(`Found ${activeGiveaways.length} eligible giveaways for user ${userId}`);

    // Create an audit log entry
    const auditLog = await prisma.enrollmentAuditLog.create({
      data: {
        userId,
        giveawayId: activeGiveaways[0]?.id || '00000000-0000-0000-0000-000000000000', // Use first giveaway or placeholder
        action: 'AUTO_ENROLLMENT',
        affectedUserCount: 1,
        status: 'SUCCESS',
        details: {
          jobId: job.id.toString(),
          membershipId,
          membershipTierId
        }
      }
    });

    let successCount = 0;
    let failureCount = 0;

    // Process each giveaway
    for (const giveaway of activeGiveaways) {
      try {
        // Check if user already has an entry for this giveaway
        const existingEntry = await prisma.entry.findUnique({
          where: {
            userId_giveawayId: {
              userId,
              giveawayId: giveaway.id
            }
          }
        });

        if (existingEntry) {
          // If entry exists, update it if the new tier has more entries
          if (membershipTier.entryAllocation > existingEntry.quantity) {
            await prisma.entry.update({
              where: { id: existingEntry.id },
              data: {
                quantity: membershipTier.entryAllocation,
                source: 'MEMBERSHIP_UPDATED',
                membershipId,
                membershipTierId,
                lastUpdatedDate: new Date()
              }
            });

            logger.debug(`Updated entry for user ${userId} in giveaway ${giveaway.id}`);
          }
        } else {
          // Create a new entry
          await prisma.entry.create({
            data: {
              userId,
              giveawayId: giveaway.id,
              membershipId,
              membershipTierId,
              quantity: membershipTier.entryAllocation,
              source: 'MEMBERSHIP',
              entryMethod: 'MEMBERSHIP'
            }
          });

          logger.debug(`Created entry for user ${userId} in giveaway ${giveaway.id}`);
        }

        successCount++;
      } catch (error) {
        failureCount++;

        // Log the error
        logger.error(`Failed to enroll user ${userId} in giveaway ${giveaway.id}:`, error);

        // Create a failed enrollment record
        await prisma.failedEnrollment.create({
          data: {
            giveawayId: giveaway.id,
            userId,
            membershipTierId,
            jobId: job.id.toString(),
            errorMessage: error instanceof Error ? error.message : 'Unknown error',
            errorCode: error instanceof Error ? error.name : 'UNKNOWN',
            status: 'PENDING'
          }
        });
      }

      // Update progress
      await job.progress((successCount + failureCount) / activeGiveaways.length * 100);
    }

    // Update the audit log with final counts
    await prisma.enrollmentAuditLog.update({
      where: { id: auditLog.id },
      data: {
        status: failureCount > 0 ? 'PARTIAL_SUCCESS' : 'SUCCESS',
        details: {
          jobId: job.id.toString(),
          successCount,
          failureCount,
          totalProcessed: successCount + failureCount
        }
      }
    });

    logger.info(`Completed new membership job ${job.id} for user ${userId}`);
    logger.info(`Successfully enrolled in ${successCount} giveaways, failed to enroll in ${failureCount} giveaways`);
  } catch (error) {
    logger.error(`Error processing new membership job ${job.id}:`, error);

    // Create an audit log entry for the failed job
    await prisma.enrollmentAuditLog.create({
      data: {
        userId,
        giveawayId: '00000000-0000-0000-0000-000000000000', // Use placeholder for error case
        action: 'AUTO_ENROLLMENT',
        status: 'FAILURE',
        details: {
          jobId: job.id.toString(),
          error: error instanceof Error ? error.message : 'Unknown error'
        }
      }
    });

    throw error;
  }
};

/**
 * Process a membership upgrade job
 * This updates entries for a user who upgraded their membership
 *
 * @param job - The Bull job containing membership upgrade data
 */
export const processMembershipUpgrade = async (job: Job<EnrollmentJobData>): Promise<void> => {
  const { userId, membershipId, membershipTierId, metadata } = job.data;

  if (!userId || !membershipId || !membershipTierId) {
    throw new Error('User ID, membership ID, and membership tier ID are required');
  }

  const previousTierId = metadata ? metadata['previousTierId'] : undefined;
  if (!previousTierId) {
    throw new Error('Previous tier ID is required in metadata');
  }

  logger.info(`Processing membership upgrade job ${job.id} for user ${userId}`);

  try {
    // Get the user
    const user = await prisma.user.findUnique({
      where: { id: userId }
    });

    if (!user) {
      throw new Error(`User with ID ${userId} not found`);
    }

    // Get the membership tiers
    const [previousTier, newTier] = await Promise.all([
      prisma.membershipTier.findUnique({ where: { id: previousTierId } }),
      prisma.membershipTier.findUnique({ where: { id: membershipTierId } })
    ]);

    if (!previousTier) {
      throw new Error(`Previous membership tier with ID ${previousTierId} not found`);
    }

    if (!newTier) {
      throw new Error(`New membership tier with ID ${membershipTierId} not found`);
    }

    // Only proceed if the new tier has more entries than the previous tier
    if (newTier.entryAllocation <= previousTier.entryAllocation) {
      logger.info(`Skipping upgrade for user ${userId} as new tier doesn't provide more entries`);
      return;
    }

    // Find all active giveaways where the user has entries
    const userEntries = await prisma.entry.findMany({
      where: {
        userId,
        giveaway: {
          status: 'ACTIVE',
          endDate: { gte: new Date() }
        }
      },
      include: {
        giveaway: true
      }
    });

    logger.info(`Found ${userEntries.length} active entries for user ${userId}`);

    // Create an audit log entry
    const auditLog = await prisma.enrollmentAuditLog.create({
      data: {
        userId,
        giveawayId: '00000000-0000-0000-0000-000000000000', // Use placeholder for membership upgrade
        action: 'MEMBERSHIP_UPGRADE',
        affectedUserCount: 1,
        status: 'SUCCESS',
        details: {
          jobId: job.id.toString(),
          membershipId,
          membershipTierId,
          previousTierId,
          previousEntryAllocation: previousTier.entryAllocation,
          newEntryAllocation: newTier.entryAllocation
        }
      }
    });

    let successCount = 0;
    let failureCount = 0;

    // Process each entry
    for (const entry of userEntries) {
      try {
        // Check if the giveaway allows this tier
        const giveaway = entry.giveaway;
        const eligibleTierIds = giveaway.eligibleMembershipTierIds as string[];

        if (!eligibleTierIds.includes(membershipTierId)) {
          logger.debug(`Skipping giveaway ${giveaway.id} as new tier is not eligible`);
          continue;
        }

        // Update the entry with the new tier's allocation
        await prisma.entry.update({
          where: { id: entry.id },
          data: {
            quantity: newTier.entryAllocation,
            source: 'MEMBERSHIP_UPGRADED',
            membershipId,
            membershipTierId,
            lastUpdatedDate: new Date()
          }
        });

        logger.debug(`Updated entry for user ${userId} in giveaway ${giveaway.id}`);
        successCount++;
      } catch (error) {
        failureCount++;

        // Log the error
        logger.error(`Failed to update entry for user ${userId} in giveaway ${entry.giveawayId}:`, error);

        // Create a failed enrollment record
        await prisma.failedEnrollment.create({
          data: {
            giveawayId: entry.giveawayId,
            userId,
            membershipTierId,
            jobId: job.id.toString(),
            errorMessage: error instanceof Error ? error.message : 'Unknown error',
            errorCode: error instanceof Error ? error.name : 'UNKNOWN',
            status: 'PENDING'
          }
        });
      }

      // Update progress
      await job.progress((successCount + failureCount) / userEntries.length * 100);
    }

    // Update the audit log with final counts
    await prisma.enrollmentAuditLog.update({
      where: { id: auditLog.id },
      data: {
        status: failureCount > 0 ? 'PARTIAL_SUCCESS' : 'SUCCESS',
        details: {
          jobId: job.id.toString(),
          successCount,
          failureCount,
          totalProcessed: successCount + failureCount
        }
      }
    });

    logger.info(`Completed membership upgrade job ${job.id} for user ${userId}`);
    logger.info(`Successfully updated ${successCount} entries, failed to update ${failureCount} entries`);
  } catch (error) {
    logger.error(`Error processing membership upgrade job ${job.id}:`, error);

    // Create an audit log entry for the failed job
    await prisma.enrollmentAuditLog.create({
      data: {
        userId,
        giveawayId: '00000000-0000-0000-0000-000000000000', // Use placeholder for error case
        action: 'MEMBERSHIP_UPGRADE',
        status: 'FAILURE',
        details: {
          jobId: job.id.toString(),
          error: error instanceof Error ? error.message : 'Unknown error'
        }
      }
    });

    throw error;
  }
};

/**
 * Process a retry failed enrollments job
 * This retries previously failed enrollment attempts
 *
 * @param job - The Bull job containing retry data
 */
export const processRetryFailedEnrollments = async (job: Job<EnrollmentJobData>): Promise<void> => {
  const { giveawayId, failedEnrollmentIds } = job.data;

  if (!giveawayId) {
    throw new Error('Giveaway ID is required');
  }

  if (!failedEnrollmentIds || !failedEnrollmentIds.length) {
    throw new Error('Failed enrollment IDs are required');
  }

  logger.info(`Processing retry failed enrollments job ${job.id} for giveaway ${giveawayId}`);

  try {
    // Get the giveaway
    const giveaway = await prisma.giveaway.findUnique({
      where: { id: giveawayId }
    });

    if (!giveaway) {
      throw new Error(`Giveaway with ID ${giveawayId} not found`);
    }

    // Get the failed enrollments
    const failedEnrollments = await prisma.failedEnrollment.findMany({
      where: {
        id: { in: failedEnrollmentIds },
        giveawayId,
        status: 'PENDING'
      }
    });

    logger.info(`Found ${failedEnrollments.length} failed enrollments to retry`);

    // Create an audit log entry
    const auditLog = await prisma.enrollmentAuditLog.create({
      data: {
        giveawayId,
        action: 'RETRY_ENROLLMENT',
        affectedUserCount: failedEnrollments.length,
        status: 'SUCCESS',
        details: {
          jobId: job.id.toString(),
          failedEnrollmentIds
        }
      }
    });

    let successCount = 0;
    let failureCount = 0;

    // Process each failed enrollment
    for (let i = 0; i < failedEnrollments.length; i++) {
      const failedEnrollment = failedEnrollments[i];

      // Skip if failed enrollment is undefined
      if (!failedEnrollment) {
        logger.warn(`Failed enrollment at index ${i} is undefined`);
        continue;
      }

      try {
        // Get the user and membership tier
        const [user, membershipTier] = await Promise.all([
          prisma.user.findUnique({ where: { id: failedEnrollment.userId } }),
          failedEnrollment.membershipTierId
            ? prisma.membershipTier.findUnique({ where: { id: failedEnrollment.membershipTierId } })
            : null
        ]);

        if (!user) {
          throw new Error(`User with ID ${failedEnrollment.userId} not found`);
        }

        if (!membershipTier && failedEnrollment.membershipTierId) {
          throw new Error(`Membership tier with ID ${failedEnrollment.membershipTierId} not found`);
        }

        // Check if user already has an entry for this giveaway
        const existingEntry = await prisma.entry.findUnique({
          where: {
            userId_giveawayId: {
              userId: failedEnrollment.userId,
              giveawayId
            }
          }
        });

        if (existingEntry) {
          // If entry exists and we have a membership tier, update it if the tier has more entries
          if (membershipTier && membershipTier.entryAllocation > existingEntry.quantity) {
            await prisma.entry.update({
              where: { id: existingEntry.id },
              data: {
                quantity: membershipTier.entryAllocation,
                source: 'MEMBERSHIP_UPDATED',
                membershipTierId: failedEnrollment.membershipTierId,
                lastUpdatedDate: new Date()
              }
            });

            logger.debug(`Updated entry for user ${failedEnrollment.userId} in giveaway ${giveawayId}`);
          } else {
            logger.debug(`Entry already exists for user ${failedEnrollment.userId} in giveaway ${giveawayId}`);
          }
        } else {
          // Create a new entry
          await prisma.entry.create({
            data: {
              userId: failedEnrollment.userId,
              giveawayId,
              membershipTierId: failedEnrollment.membershipTierId,
              quantity: membershipTier ? membershipTier.entryAllocation : 1,
              source: 'MEMBERSHIP',
              entryMethod: 'MEMBERSHIP'
            }
          });

          logger.debug(`Created entry for user ${failedEnrollment.userId} in giveaway ${giveawayId}`);
        }

        // Update the failed enrollment status
        await prisma.failedEnrollment.update({
          where: { id: failedEnrollment.id },
          data: {
            status: 'RETRIED',
            resolvedDate: new Date(),
            resolutionMethod: 'RETRY_ENROLLMENT'
          }
        });

        successCount++;
      } catch (error) {
        failureCount++;

        // Log the error
        logger.error(`Failed to retry enrollment for user ${failedEnrollment.userId} in giveaway ${giveawayId}:`, error);

        // Update the failed enrollment with the new error
        await prisma.failedEnrollment.update({
          where: { id: failedEnrollment.id },
          data: {
            errorMessage: error instanceof Error ? error.message : 'Unknown error',
            errorCode: error instanceof Error ? error.name : 'UNKNOWN',
            attemptDate: new Date() // Update the attempt date
          }
        });
      }

      // Update progress
      await job.progress((i + 1) / failedEnrollments.length * 100);
    }

    // Update the audit log with final counts
    await prisma.enrollmentAuditLog.update({
      where: { id: auditLog.id },
      data: {
        status: failureCount > 0 ? 'PARTIAL_SUCCESS' : 'SUCCESS',
        details: {
          jobId: job.id.toString(),
          successCount,
          failureCount,
          totalProcessed: successCount + failureCount
        }
      }
    });

    logger.info(`Completed retry failed enrollments job ${job.id} for giveaway ${giveawayId}`);
    logger.info(`Successfully retried ${successCount} enrollments, failed to retry ${failureCount} enrollments`);
  } catch (error) {
    logger.error(`Error processing retry failed enrollments job ${job.id}:`, error);

    // Create an audit log entry for the failed job
    await prisma.enrollmentAuditLog.create({
      data: {
        giveawayId,
        action: 'RETRY_ENROLLMENT',
        status: 'FAILURE',
        details: {
          jobId: job.id.toString(),
          error: error instanceof Error ? error.message : 'Unknown error'
        }
      }
    });

    throw error;
  }
};

/**
 * Register all enrollment job processors with the queue
 * @param queue - The Bull queue to register processors with
 */
export const registerEnrollmentProcessors = (queue: any): void => {
  queue.process(EnrollmentJobType.PROCESS_GIVEAWAY_ACTIVATION, processGiveawayActivation);
  queue.process(EnrollmentJobType.PROCESS_NEW_MEMBERSHIP, processNewMembership);
  queue.process(EnrollmentJobType.PROCESS_MEMBERSHIP_UPGRADE, processMembershipUpgrade);
  queue.process(EnrollmentJobType.RETRY_FAILED_ENROLLMENTS, processRetryFailedEnrollments);

  logger.info('Enrollment job processors registered');
};