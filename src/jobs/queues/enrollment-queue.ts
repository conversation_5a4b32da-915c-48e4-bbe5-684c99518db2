/**
 * Enrollment Queue
 *
 * This file provides functions for adding jobs to the enrollment queue.
 * It handles job creation for different enrollment scenarios.
 *
 * Note: This implementation uses PostgreSQL directly instead of Redis/Bull.
 */

import logger from '../../utils/logger.js';
import * as pgJobProcessor from '../../services/pg-job-processor.js';
import { EnrollmentJobType } from '../config.js';

/**
 * Add a job to process giveaway activation
 * @param giveawayId - ID of the giveaway being activated
 * @returns The created job
 */
export const addGiveawayActivationJob = async (
  giveawayId: string
): Promise<{ id: string }> => {
  try {
    logger.info(`Processing giveaway activation for giveaway ${giveawayId}`);

    // Process the job immediately using PostgreSQL
    await pgJobProcessor.processJob(
      EnrollmentJobType.PROCESS_GIVEAWAY_ACTIVATION,
      { giveawayId }
    );

    const mockJobId = `pg-${Date.now()}-${Math.floor(Math.random() * 1000)}`;
    logger.info(`Completed giveaway activation job ${mockJobId} for giveaway ${giveawayId}`);

    return { id: mockJobId };
  } catch (error) {
    logger.error(`Failed to process giveaway activation for giveaway ${giveawayId}:`, error);
    throw error;
  }
};

/**
 * Add a job to process new membership
 * @param userId - ID of the user with new membership
 * @param membershipId - ID of the new membership
 * @param membershipTierId - ID of the membership tier
 * @returns The created job
 */
export const addNewMembershipJob = async (
  userId: string,
  membershipId: string,
  membershipTierId: string
): Promise<{ id: string }> => {
  try {
    logger.info(`Processing new membership for user ${userId}`);

    // Process the job immediately using PostgreSQL
    await pgJobProcessor.processJob(
      EnrollmentJobType.PROCESS_NEW_MEMBERSHIP,
      { userId, membershipId, membershipTierId }
    );

    const mockJobId = `pg-${Date.now()}-${Math.floor(Math.random() * 1000)}`;
    logger.info(`Completed new membership job ${mockJobId} for user ${userId}`);

    return { id: mockJobId };
  } catch (error) {
    logger.error(`Failed to process new membership for user ${userId}:`, error);
    throw error;
  }
};

/**
 * Add a job to process membership upgrade
 * @param userId - ID of the user upgrading membership
 * @param membershipId - ID of the upgraded membership
 * @param membershipTierId - ID of the new membership tier
 * @param previousTierId - ID of the previous membership tier
 * @returns The created job
 */
export const addMembershipUpgradeJob = async (
  userId: string,
  membershipId: string,
  membershipTierId: string,
  previousTierId: string
): Promise<{ id: string }> => {
  try {
    logger.info(`Processing membership upgrade for user ${userId}`);

    // Process the job immediately using PostgreSQL
    await pgJobProcessor.processJob(
      EnrollmentJobType.PROCESS_MEMBERSHIP_UPGRADE,
      {
        userId,
        membershipId,
        membershipTierId,
        metadata: { previousTierId }
      }
    );

    const mockJobId = `pg-${Date.now()}-${Math.floor(Math.random() * 1000)}`;
    logger.info(`Completed membership upgrade job ${mockJobId} for user ${userId}`);

    return { id: mockJobId };
  } catch (error) {
    logger.error(`Failed to process membership upgrade for user ${userId}:`, error);
    throw error;
  }
};

/**
 * Add a job to retry failed enrollments
 * @param giveawayId - ID of the giveaway
 * @param failedEnrollmentIds - Array of failed enrollment IDs to retry
 * @returns The created job
 */
export const addRetryFailedEnrollmentsJob = async (
  giveawayId: string,
  failedEnrollmentIds: string[]
): Promise<{ id: string }> => {
  try {
    logger.info(`Processing retry failed enrollments for giveaway ${giveawayId}`);

    // Process the job immediately using PostgreSQL
    await pgJobProcessor.processJob(
      EnrollmentJobType.RETRY_FAILED_ENROLLMENTS,
      { giveawayId, failedEnrollmentIds }
    );

    const mockJobId = `pg-${Date.now()}-${Math.floor(Math.random() * 1000)}`;
    logger.info(`Completed retry failed enrollments job ${mockJobId} for giveaway ${giveawayId}`);

    return { id: mockJobId };
  } catch (error) {
    logger.error(`Failed to process retry failed enrollments for giveaway ${giveawayId}:`, error);
    throw error;
  }
};
