/**
 * Job Queue Configuration
 *
 * This file contains configuration for the PostgreSQL-based job system.
 * It defines queue names and job types.
 */

/**
 * Queue names for different job types
 */
export enum QueueName {
  ENROLLMENT = 'enrollment',
  NOTIFICATION = 'notification',
  MAINTENANCE = 'maintenance'
}

/**
 * Job types for the enrollment queue
 */
export enum EnrollmentJobType {
  PROCESS_GIVEAWAY_ACTIVATION = 'process-giveaway-activation',
  PROCESS_NEW_MEMBERSHIP = 'process-new-membership',
  PROCESS_MEMBERSHIP_UPGRADE = 'process-membership-upgrade',
  RETRY_FAILED_ENROLLMENTS = 'retry-failed-enrollments'
}

/**
 * Job data interface for enrollment jobs
 */
export interface EnrollmentJobData {
  giveawayId?: string;
  userId?: string;
  membershipId?: string;
  membershipTierId?: string;
  batchSize?: number;
  failedEnrollmentIds?: string[];
  metadata?: Record<string, any>;
}
