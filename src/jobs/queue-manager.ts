/**
 * Queue Manager
 *
 * This file provides a singleton manager for PostgreSQL-based job processing.
 * It handles job processing and provides a mock queue interface for compatibility.
 */

import logger from '../utils/logger.js';
import { QueueName } from './config.js';

// Define a Queue interface for compatibility with the old Bull interface
type Queue = {
  name: string;
  add: (jobName: string, data: any) => Promise<{ id: string }>;
  process: () => any;
  on: (event: string, callback: any) => any;
  close: () => Promise<void>;
}

/**
 * Queue Manager class for handling PostgreSQL-based job processing
 */
class QueueManager {
  private static instance: QueueManager;
  private queues: Map<QueueName, Queue>;
  private initialized: boolean = false;

  /**
   * Private constructor to enforce singleton pattern
   */
  private constructor() {
    this.queues = new Map();
  }

  /**
   * Get the singleton instance of QueueManager
   * @returns The QueueManager instance
   */
  public static getInstance(): QueueManager {
    if (!QueueManager.instance) {
      QueueManager.instance = new QueueManager();
    }
    return QueueManager.instance;
  }

  /**
   * Initialize all queues
   */
  public initialize(): void {
    if (this.initialized) {
      return;
    }

    try {
      // Using PostgreSQL-based job processing
      logger.info('Queue Manager using PostgreSQL-based job processing');
      this.initialized = true;
    } catch (error) {
      logger.error('Failed to initialize Queue Manager:', error);
      throw error;
    }
  }

  // No queue creation needed for PostgreSQL-based job processor

  /**
   * Get a queue by name
   * @param name - Queue name
   * @returns The requested queue
   */
  public getQueue(name: QueueName): Queue {
    if (!this.initialized) {
      this.initialize();
    }

    // Always return a mock queue
    logger.debug(`Returning mock queue for ${name} (using PostgreSQL-based job processing)`);
    return this.getMockQueue(name);
  }

  /**
   * Get a mock queue that doesn't use Redis
   * @param name - Queue name
   * @returns A mock queue
   */
  private getMockQueue(name: QueueName): Queue {
    // Create a mock queue object that implements the Queue interface
    // but doesn't actually use Redis
    const mockQueue = {
      name,
      add: async (jobName: string, data: any) => {
        logger.debug(`Mock queue ${name} add job ${jobName}`, { data });

        // For enrollment jobs, use the PostgreSQL job processor directly
        if (name === QueueName.ENROLLMENT) {
          const pgJobProcessor = await import('../services/pg-job-processor.js');

          try {
            // Process the job immediately
            await pgJobProcessor.processJob(jobName, data);
            return { id: 'mock-job-id' } as any;
          } catch (error) {
            logger.error(`Error processing job ${jobName} with PostgreSQL processor:`, error);
            throw error;
          }
        }

        return { id: 'mock-job-id' } as any;
      },
      process: () => {
        logger.debug(`Mock queue ${name} process called`);
        return {} as any;
      },
      on: (event: string, _callback: any) => {
        // Don't actually register any event handlers
        logger.debug(`Mock queue ${name} on ${event} called`);
        return mockQueue;
      },
      close: async () => {
        logger.debug(`Mock queue ${name} closed`);
        return Promise.resolve();
      }
    } as unknown as Queue;

    return mockQueue;
  }

  /**
   * Shut down all queues
   */
  public async shutdown(): Promise<void> {
    logger.info('Shutting down Queue Manager...');

    const closePromises = Array.from(this.queues.values()).map(queue =>
      queue.close()
    );

    await Promise.all(closePromises);
    this.initialized = false;
    logger.info('Queue Manager shut down successfully');
  }
}

export default QueueManager.getInstance();
