/**
 * Job System Initialization
 *
 * This file initializes the job queue system and registers all job processors.
 * It provides a central point for starting and stopping the job system.
 */

import logger from '../utils/logger.js';

/**
 * Initialize the job system
 * This sets up all queues and registers job processors
 */
export const initializeJobSystem = (): void => {
  try {
    // Use PostgreSQL-based job processor exclusively
    logger.info('Initializing job system with PostgreSQL...');
    // No initialization needed for PostgreSQL-based job processor

    logger.info('Job system initialized successfully');
  } catch (error) {
    logger.error('Failed to initialize job system:', error);
  }
};

/**
 * Shut down the job system
 * This gracefully shuts down any background processes
 */
export const shutdownJobSystem = async (): Promise<void> => {
  try {
    // No shutdown needed for PostgreSQL-based job processor
    logger.info('Job system shut down successfully');
  } catch (error) {
    logger.error('Failed to shut down job system:', error);
    throw error;
  }
};

// Re-export queue functions for convenience
export * from './queues/enrollment-queue.js';

// Export queue manager for direct access if needed
export { default as queueManager } from './queue-manager.js';
