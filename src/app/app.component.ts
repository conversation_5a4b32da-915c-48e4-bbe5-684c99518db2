import { Component, inject, OnInit } from '@angular/core';
import { NavigationEnd, Router, RouterOutlet } from '@angular/router';
import { filter, map } from 'rxjs/operators';
import { CommonModule } from '@angular/common';
import { ToolbarComponent } from './layout/toolbar/toolbar.component';
import { FooterComponent } from './layout/footer/footer.component';

@Component({
  selector: 'app-root',
  standalone: true,
  imports: [RouterOutlet, CommonModule, ToolbarComponent, FooterComponent],
  templateUrl: './app.component.html',
  styleUrls: ['./app.component.scss']
})
export class AppComponent implements OnInit {
  title = 'Winners Society';
  isAdminRoute = false;
  isMemberRoute = false;
  isAuthRoute = false;
  isPublicRoute = true;
  isFullWidthRoute = false;

  private router = inject(Router);

  // Routes that should use the admin layout
  private adminPaths = ['/admin'];

  // Routes that should use the member layout
  private memberPaths = ['/dashboard', '/profile', '/membership/dashboard', '/membership/plans', '/membership/upgrade', '/membership/payment-success'];

  // Routes that should use auth layout (full screen, no header/footer)
  private authPaths = ['/auth/login', '/auth/signup', '/auth/register', '/auth/forgot-password', '/auth/reset-password', '/auth/verify-email', '/auth/email-verified'];

  // Routes that should use full width (no container padding)
  private fullWidthPaths = ['/', '/home', '/landing', '/membership', '/about', '/contact'];

  ngOnInit(): void {
    // Monitor route changes to conditionally show appropriate layout
    this.router.events.pipe(
      filter(event => event instanceof NavigationEnd),
      map(() => this.router.url)
    ).subscribe(url => {
      // Check route types
      this.isAdminRoute = this.checkIfAdminRoute(url);
      this.isMemberRoute = this.checkIfMemberRoute(url);
      this.isAuthRoute = this.checkIfAuthRoute(url);
      this.isPublicRoute = !this.isAdminRoute && !this.isMemberRoute && !this.isAuthRoute;
      this.isFullWidthRoute = this.checkIfFullWidthRoute(url);

      // Debug logging
      console.log('Current URL:', url);
      console.log('Is Admin Route:', this.isAdminRoute);
      console.log('Is Member Route:', this.isMemberRoute);
      console.log('Is Auth Route:', this.isAuthRoute);
      console.log('Is Public Route:', this.isPublicRoute);
    });
  }

  /**
   * Check if the current URL is for an admin route
   */
  private checkIfAdminRoute(url: string): boolean {
    return this.adminPaths.some(path => url.startsWith(path));
  }

  /**
   * Check if the current URL is for a member route
   */
  private checkIfMemberRoute(url: string): boolean {
    return this.memberPaths.some(path => url.startsWith(path));
  }

  /**
   * Check if the current URL is for an auth route
   */
  private checkIfAuthRoute(url: string): boolean {
    return this.authPaths.some(path => url === path);
  }

  /**
   * Check if the current URL should use full width layout
   */
  private checkIfFullWidthRoute(url: string): boolean {
    return this.fullWidthPaths.some(path => {
      // Exact match for root path
      if (path === '/' && url === '/') return true;
      // Exact match for other paths
      if (path !== '/' && url === path) return true;
      // Partial match for paths like /membership (but not /membership/dashboard)
      if (path !== '/' && url.startsWith(path + '/') && !url.includes('/dashboard')) return true;
      return false;
    });
  }
}
