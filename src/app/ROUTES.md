# Winners Society Routing Structure

This document outlines the routing structure and security practices for the Winners Society application.

## Route Naming Conventions

The application follows a clear naming convention for routes to distinguish between different access levels:

1. **Public Routes**:
   - No special prefix
   - Examples: `/`, `/about`, `/membership`, `/giveaways`

2. **Authenticated Member Routes**:
   - Prefix: `/member`
   - Examples: `/member/dashboard`, `/member/membership`, `/member/profile`

3. **Administrative Routes**:
   - Prefix: `/admin`
   - Examples: `/admin/users`, `/admin/giveaways`, `/admin/memberships`

## Security Measures

### Route Guards

The application uses several route guards to protect routes:

1. **AuthGuard**: Protects all authenticated routes (`/member/*` and `/admin/*`)
   - Redirects unauthenticated users to the login page
   - Stores the attempted URL for redirection after login

2. **AdminGuard**: Additional protection for admin routes (`/admin/*`)
   - Checks if the user has admin privileges
   - Redirects unauthorized users to the member dashboard

3. **RoleGuard**: Fine-grained access control based on user roles
   - Used with route data property `roles: ['admin', 'moderator']`
   - Redirects users without the required role

4. **MembershipTierGuard**: Access control based on membership tier
   - Used with route data property `requiredTiers: ['premium', 'vip']`
   - Redirects users to upgrade page if they don't have the required tier

5. **NoAuthGuard**: Prevents authenticated users from accessing auth pages
   - Redirects authenticated users to the dashboard
   - Used for login, signup, and password recovery pages

### Route Data Properties

Routes use data properties to specify security requirements:

```typescript
{
  path: 'vip-content',
  component: VipContentComponent,
  canActivate: [AuthGuard, membershipTierGuard],
  data: { 
    requiresAuth: true,
    requiredTiers: ['premium', 'vip'],
    title: 'VIP Content - Winners Society'
  }
}
```

## Route Structure

### Public Routes
- `/` - Home page
- `/auth/login` - Login page
- `/auth/signup` - Signup page
- `/auth/forgot-password` - Password recovery
- `/membership` - Public membership plans
- `/giveaways` - Public giveaway listings
- `/winners` - Public winners showcase
- `/about` - About page
- `/contact` - Contact page
- `/faq` - FAQ page
- `/terms` - Terms and conditions
- `/privacy` - Privacy policy

### Authenticated Member Routes
- `/member/dashboard` - Member dashboard
- `/member/membership` - Member's membership details
  - `/member/membership/plans` - Available membership plans
  - `/member/membership/upgrade` - Upgrade membership
  - `/member/membership/payment-success` - Payment success page
- `/member/profile` - Member's profile
  - `/member/profile/edit` - Edit profile
  - `/member/profile/settings` - Account settings
- `/member/entries` - Member's entries
- `/member/giveaways` - Member's giveaway access
  - `/member/giveaways/:id` - Specific giveaway details
  - `/member/giveaways/:id/enter` - Enter a giveaway

### Administrative Routes
- `/admin/dashboard` - Admin dashboard
- `/admin/users` - User management
  - `/admin/users/:id` - Specific user details
- `/admin/giveaways` - Giveaway management
  - `/admin/giveaways/create` - Create new giveaway
  - `/admin/giveaways/:id/edit` - Edit giveaway
- `/admin/memberships` - Membership management
  - `/admin/memberships/tiers` - Manage membership tiers
  - `/admin/memberships/transactions` - View membership transactions

## Legacy Routes

For backward compatibility, the application includes redirects from legacy routes to the new structure:

```typescript
{
  path: 'dashboard',
  redirectTo: '/member/dashboard',
  pathMatch: 'full'
},
{
  path: 'profile',
  redirectTo: '/member/profile',
  pathMatch: 'full'
},
{
  path: 'membership/dashboard',
  redirectTo: '/member/membership',
  pathMatch: 'full'
}
```

## Best Practices

1. **Always use route guards** for protected routes
2. **Include route data properties** to specify security requirements
3. **Use lazy loading** for feature modules to improve performance
4. **Maintain consistent naming conventions** for routes
5. **Document any changes** to the routing structure in this file
