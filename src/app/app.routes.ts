import { Component } from '@angular/core';
import { Routes } from '@angular/router';
import { AdminLayoutComponent } from './layout/admin-layout/admin-layout.component';
import { MemberLayoutComponent } from './layout/member-layout/member-layout.component';
import { AuthGuard } from './core/guards/auth.guard';
import { AdminGuard } from './core/guards/admin.guard';
import { roleGuard } from './core/guards/role.guard';

import { DashboardComponent } from './features/admin/dashboard/dashboard.component';

// Simple empty component for placeholder routes
@Component({
  template: `
    <div class="container mt-5">
      <div class="card">
        <div class="card-body">
          <h2 class="card-title">Placeholder Component</h2>
          <p class="card-text">This is a placeholder component. Replace this with your actual component when ready.</p>
        </div>
      </div>
    </div>
  `,
  standalone: true
})
export class EmptyComponent {}

/**
 * Main application routes
 * Using lazy-loading for feature modules to improve initial load performance
 *
 * Route structure follows these conventions:
 * - Public routes: No prefix (/, /membership, /giveaways)
 * - Authentication routes: /auth prefix (/auth/login, /auth/signup)
 * - Member routes: /member prefix (/member/dashboard, /member/membership)
 * - Admin routes: /admin prefix (/admin/dashboard, /admin/users)
 *
 * Security measures:
 * - Public routes: No guards
 * - Auth routes: Protected by noAuthGuard (prevents authenticated users from accessing)
 * - Member routes: Protected by AuthGuard
 * - Admin routes: Protected by AuthGuard and AdminGuard
 * - Some routes may have additional protection via roleGuard or membershipTierGuard
 */
export const routes: Routes = [
  // Public routes
  {
    path: '',
    loadChildren: () => import('./features/content/content.routes').then(m => m.CONTENT_ROUTES),
  },
  {
    path: 'auth',
    loadChildren: () => import('./features/auth/auth.routes').then(m => m.AUTH_ROUTES),
  },
  {
    path: 'membership',
    loadChildren: () => import('./features/membership/membership.routes').then(m => m.MEMBERSHIP_ROUTES),
    data: { layout: 'public' }
  },

  // Member routes with MemberLayoutComponent
  {
    path: 'member',
    component: MemberLayoutComponent,
    canActivate: [AuthGuard],
    loadChildren: () => import('./features/member/member.routes').then(m => m.MEMBER_ROUTES),
    data: {
      layout: 'member',
      requiresAuth: true
    }
  },

  // Legacy member routes - redirect to new structure
  {
    path: 'dashboard',
    redirectTo: '/member/dashboard',
    pathMatch: 'full'
  },
  {
    path: 'profile',
    redirectTo: '/member/profile',
    pathMatch: 'full'
  },
  {
    path: 'membership/dashboard',
    redirectTo: '/member/membership/dashboard',
    pathMatch: 'full'
  },
  {
    path: 'membership/plans',
    redirectTo: '/member/membership/plans',
    pathMatch: 'full'
  },
  {
    path: 'giveaways',
    redirectTo: '/member/giveaways',
    pathMatch: 'prefix'
  },

  // Admin routes with AdminLayoutComponent
  {
    path: 'admin',
    component: AdminLayoutComponent,
    canActivate: [AuthGuard, AdminGuard, roleGuard],
    data: {
      roles: ['admin'],
      requiresAuth: true
    },
    children: [
      {
        path: '',
        redirectTo: 'dashboard',
        pathMatch: 'full'
      },
      {
        path: 'dashboard',
        component: DashboardComponent,
        title: 'Admin Dashboard - Winners Society'
      },
      {
        path: 'users',
        loadChildren: () => import('./features/admin/users/users.routes').then(m => m.USERS_ROUTES),
        title: 'User Management - Winners Society'
      },
      {
        path: 'membership',
        component: EmptyComponent,
        title: 'Membership Management - Winners Society'
      },
      {
        path: 'giveaways',
        loadChildren: () => import('./features/admin/giveaways/admin-giveaways.routes').then(m => m.ADMIN_GIVEAWAYS_ROUTES),
        title: 'Giveaway Management - Winners Society'
      },
      {
        path: 'content',
        component: EmptyComponent,
        title: 'Content Management - Winners Society'
      },
      {
        path: 'transactions',
        component: EmptyComponent,
        title: 'Transaction Management - Winners Society'
      },
      {
        path: 'reports',
        component: EmptyComponent,
        title: 'Reports - Winners Society'
      },
      {
        path: 'settings',
        component: EmptyComponent,
        title: 'Admin Settings - Winners Society'
      },
      // Temporarily commented out until components are created
      /*
      {
        path: 'giveaways',
        loadChildren: () => import('./features/admin/giveaways/admin-giveaways.routes').then(m => m.ADMIN_GIVEAWAYS_ROUTES),
      },
      {
        path: 'memberships',
        loadChildren: () => import('./features/admin/memberships/admin-memberships.routes').then(m => m.ADMIN_MEMBERSHIPS_ROUTES),
      },
      {
        path: 'content',
        loadChildren: () => import('./features/admin/content/admin-content.routes').then(m => m.ADMIN_CONTENT_ROUTES),
      },
      {
        path: 'transactions',
        loadChildren: () => import('./features/admin/transactions/admin-transactions.routes').then(m => m.ADMIN_TRANSACTIONS_ROUTES),
      },
      {
        path: 'settings',
        loadComponent: () => import('./features/admin/settings/admin-settings.component').then(c => c.AdminSettingsComponent),
      }
      */
    ]
  },

  {
    path: '**',
    redirectTo: '/not-found',
    pathMatch: 'full'
  }
];
