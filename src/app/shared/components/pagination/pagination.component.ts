import { Component, EventEmitter, Input, OnChanges, Output, SimpleChanges } from '@angular/core';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-pagination',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './pagination.component.html',
  styleUrls: ['./pagination.component.scss']
})
export class PaginationComponent implements OnChanges {
  @Input() currentPage: number = 1;
  @Input() totalPages: number = 1;
  @Output() pageChanged = new EventEmitter<number>();
  
  pages: Array<number | string> = [];

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['currentPage'] || changes['totalPages']) {
      this.generatePageNumbers();
    }
  }

  generatePageNumbers(): void {
    this.pages = [];
    
    if (this.totalPages <= 7) {
      // If we have 7 pages or less, show all page numbers
      for (let i = 1; i <= this.totalPages; i++) {
        this.pages.push(i);
      }
    } else {
      // Always include first page
      this.pages.push(1);
      
      // Calculate start and end of pagination links
      let startPage = Math.max(2, this.currentPage - 1);
      let endPage = Math.min(this.totalPages - 1, this.currentPage + 1);
      
      // Add dots if needed before startPage
      if (startPage > 2) {
        this.pages.push('...');
      }
      
      // Add mid pages
      for (let i = startPage; i <= endPage; i++) {
        this.pages.push(i);
      }
      
      // Add dots if needed after endPage
      if (endPage < this.totalPages - 1) {
        this.pages.push('...');
      }
      
      // Always include last page
      this.pages.push(this.totalPages);
    }
  }

  // Helper method to handle both string and number page values
  changePage(page: number | string): void {
    if (typeof page === 'number') {
      this.onPageChange(page);
    }
  }

  onPageChange(page: number): void {
    // Ensure page change is within bounds
    if (page < 1 || page > this.totalPages || page === this.currentPage) {
      return;
    }

    this.pageChanged.emit(page);
  }
} 