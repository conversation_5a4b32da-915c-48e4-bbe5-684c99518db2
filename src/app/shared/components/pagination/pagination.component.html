<nav aria-label="Page navigation" *ngIf="pages.length > 1">
  <ul class="pagination justify-content-center">
    <li class="page-item" [class.disabled]="currentPage === 1">
      <a class="page-link" href="javascript:void(0)" (click)="onPageChange(currentPage - 1)" aria-label="Previous">
        <span aria-hidden="true">&laquo;</span>
      </a>
    </li>
    
    <ng-container *ngFor="let page of pages">
      <li class="page-item" *ngIf="page !== '...'" [class.active]="page === currentPage">
        <a class="page-link" href="javascript:void(0)" (click)="changePage(page)">{{ page }}</a>
      </li>
      <li class="page-item disabled" *ngIf="page === '...'">
        <span class="page-link">{{ page }}</span>
      </li>
    </ng-container>
    
    <li class="page-item" [class.disabled]="currentPage === totalPages">
      <a class="page-link" href="javascript:void(0)" (click)="onPageChange(currentPage + 1)" aria-label="Next">
        <span aria-hidden="true">&raquo;</span>
      </a>
    </li>
  </ul>
</nav> 