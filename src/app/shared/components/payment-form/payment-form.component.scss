.payment-form-container {
  padding: 0;

  &.compact {
    font-size: 0.875rem;
  }
}

.card-element {
  padding: 1rem;
  border-radius: 4px;
  border: 1px solid #ced4da;
  background-color: white;
  min-height: 40px;

  &.compact {
    padding: 0.75rem;
    min-height: 36px;
  }
}

.card-errors {
  color: #dc3545;
  font-size: 0.875rem;
  margin-top: 0.5rem;
  min-height: 20px;

  &.small {
    font-size: 0.75rem;
    margin-top: 0.25rem;
    min-height: 16px;
  }
}

.payment-method-toggle {
  .btn-group {
    border-radius: 4px;
    overflow: hidden;
  }
}

.saved-methods {
  .list-group-item {
    cursor: pointer;

    &.active {
      background-color: #f8f9fa;
      border-color: #dee2e6;
      color: #212529;

      .form-check-input:checked {
        background-color: #0d6efd;
        border-color: #0d6efd;
      }
    }

    .form-check {
      display: flex;
      align-items: center;

      .form-check-input {
        margin-top: 0;
        margin-right: 1rem;
      }

      .form-check-label {
        cursor: pointer;
      }
    }
  }
}

.form-group {
  margin-bottom: 1.25rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: #495057;
}

.checkbox-container {
  display: flex;
  align-items: center;
  cursor: pointer;
  font-weight: normal;
}

.checkbox-container input {
  margin-right: 10px;
}

input[type="text"] {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 1rem;
}

.btn-primary {
  padding: 0.75rem 2rem;
  background-color: var(--bs-primary);
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
  font-size: 1rem;
  width: 100%;
}

.btn-primary:hover {
  background-color: var(--bs-primary);
  filter: brightness(90%);
}
