import { Component, OnInit, OnDestroy, Input, Output, EventEmitter, ViewChild, ElementRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { StripeService } from '../../../core/services/stripe.service';
import { PaymentMethod } from '../../../core/models/payment.model';

@Component({
  selector: 'app-payment-form',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule],
  templateUrl: './payment-form.component.html',
  styleUrls: ['./payment-form.component.scss']
})
export class PaymentFormComponent implements OnInit, OnDestroy {
  @Input() amount: number = 0;
  @Input() currency: string = 'USD';
  @Input() savedPaymentMethods: PaymentMethod[] = [];
  @Input() showSaveOption: boolean = true;
  @Input() processingPayment: boolean = false;

  @Output() paymentMethodCreated = new EventEmitter<string>();
  @Output() paymentMethodSelected = new EventEmitter<string>();
  @Output() paymentFormError = new EventEmitter<string>();

  @ViewChild('cardElement') cardElement!: ElementRef;

  paymentForm: FormGroup;
  cardErrors: string = '';
  cardComplete: boolean = false;
  useSavedMethod: boolean = false;
  stripeLoaded: boolean = false;
  stripeCardElement: any;
  cardElementId: string = `card-element-${Math.random().toString(36).substring(2, 9)}`;

  constructor(
    private fb: FormBuilder,
    private stripeService: StripeService
  ) {
    this.paymentForm = this.fb.group({
      nameOnCard: ['', Validators.required],
      saveCard: [false],
      selectedPaymentMethod: ['']
    });
  }

  ngOnInit(): void {
    this.initializeStripe();

    // If there are saved payment methods, default to using them
    if (this.savedPaymentMethods.length > 0) {
      this.useSavedMethod = true;
      this.paymentForm.get('selectedPaymentMethod')?.setValue(this.savedPaymentMethods[0].id);
    }
  }

  ngOnDestroy(): void {
    // Always attempt to destroy the card element when component is destroyed
    this.stripeService.destroyCardElement();
    this.stripeCardElement = null;
  }

  /**
   * Initialize Stripe and create card element
   */
  private initializeStripe(): void {
    this.stripeService.loadStripe().subscribe({
      next: () => {
        this.stripeLoaded = true;
        // Wait for view to initialize
        setTimeout(() => {
          this.setupCardElement();
        }, 0);
      },
      error: (error) => {
        console.error('Failed to load Stripe', error);
        this.paymentFormError.emit('Failed to load payment processor. Please try again later.');
      }
    });
  }

  /**
   * Set up the Stripe card element
   */
  private setupCardElement(): void {
    if (!this.cardElement) {
      return;
    }

    // Make sure any previous card element is destroyed first
    this.stripeService.destroyCardElement();

    // Create a new card element with the unique ID
    this.stripeCardElement = this.stripeService.createCardElement(
      {
        hidePostalCode: true,
        style: {
          base: {
            color: '#32325d',
            fontFamily: '"Helvetica Neue", Helvetica, sans-serif',
            fontSmoothing: 'antialiased',
            fontSize: '16px',
            '::placeholder': {
              color: '#aab7c4'
            }
          },
          invalid: {
            color: '#fa755a',
            iconColor: '#fa755a'
          }
        }
      },
      `#${this.cardElementId}`
    );

    // Listen for changes in the card element
    this.stripeCardElement.on('change', (event: any) => {
      this.cardErrors = event.error ? event.error.message : '';
      this.cardComplete = event.complete;
    });
  }

  /**
   * Toggle between saved payment methods and new card
   */
  togglePaymentMethod(useSaved: boolean): void {
    this.useSavedMethod = useSaved;

    if (useSaved && this.savedPaymentMethods.length > 0) {
      this.paymentForm.get('selectedPaymentMethod')?.setValue(this.savedPaymentMethods[0].id);
    } else {
      this.paymentForm.get('selectedPaymentMethod')?.setValue('');
      // Re-mount card element if needed
      if (!this.stripeCardElement && this.stripeLoaded) {
        setTimeout(() => {
          this.setupCardElement();
        }, 0);
      }
    }
  }

  /**
   * Create a payment method from the card details
   */
  createPaymentMethod(): void {
    if (this.useSavedMethod) {
      const selectedMethodId = this.paymentForm.get('selectedPaymentMethod')?.value;
      if (selectedMethodId) {
        this.paymentMethodSelected.emit(selectedMethodId);
      } else {
        this.paymentFormError.emit('Please select a payment method');
      }
      return;
    }

    if (!this.cardComplete) {
      this.paymentFormError.emit('Please complete the card details');
      return;
    }

    const nameOnCard = this.paymentForm.get('nameOnCard')?.value;
    if (!nameOnCard) {
      this.paymentFormError.emit('Please enter the name on card');
      return;
    }

    const billingDetails = {
      name: nameOnCard
    };

    this.stripeService.createPaymentMethod(billingDetails).subscribe({
      next: (paymentMethod) => {
        this.paymentMethodCreated.emit(paymentMethod.id);
      },
      error: (error) => {
        console.error('Payment method creation failed', error);
        this.paymentFormError.emit(error.message || 'Failed to process payment method');
      }
    });
  }

  /**
   * Get the selected payment method
   */
  getSelectedPaymentMethod(): PaymentMethod | undefined {
    const selectedId = this.paymentForm.get('selectedPaymentMethod')?.value;
    return this.savedPaymentMethods.find(method => method.id === selectedId);
  }

  /**
   * Format currency for display
   * Assumes amount is already in cents for Stripe
   */
  formatCurrency(amount: number, currency: string = 'USD'): string {
    // Stripe amounts are in cents, so we need to divide by 100
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency
    }).format(amount / 100);
  }
}
