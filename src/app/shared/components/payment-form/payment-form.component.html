<div class="payment-form-container compact">
  <!-- Payment Method Selection -->
  <div *ngIf="savedPaymentMethods.length > 0" class="payment-method-toggle mb-2">
    <div class="btn-group w-100" role="group">
      <button type="button" class="btn btn-sm"
              [ngClass]="{'btn-primary': useSavedMethod, 'btn-outline-primary': !useSavedMethod}"
              (click)="togglePaymentMethod(true)">
        Use Saved Card
      </button>
      <button type="button" class="btn btn-sm"
              [ngClass]="{'btn-primary': !useSavedMethod, 'btn-outline-primary': useSavedMethod}"
              (click)="togglePaymentMethod(false)">
        Use New Card
      </button>
    </div>
  </div>

  <form [formGroup]="paymentForm" (ngSubmit)="createPaymentMethod()">
    <!-- Saved Payment Methods -->
    <div *ngIf="useSavedMethod && savedPaymentMethods.length > 0" class="saved-methods mb-2">
      <label class="form-label small mb-1">Select Payment Method</label>
      <div class="list-group">
        <div *ngFor="let method of savedPaymentMethods" class="list-group-item list-group-item-action py-2"
             [ngClass]="{'active': paymentForm.get('selectedPaymentMethod')?.value === method.id}">
          <div class="form-check">
            <input class="form-check-input" type="radio" [value]="method.id"
                   formControlName="selectedPaymentMethod"
                   [id]="'payment-method-' + method.id">
            <label class="form-check-label w-100" [for]="'payment-method-' + method.id">
              <div class="d-flex justify-content-between align-items-center small">
                <div>
                  <span class="fw-bold">{{ method.brand | titlecase }}</span> ending in {{ method.last4 }}
                </div>
                <div class="text-muted">
                  Expires {{ method.expiryMonth }}/{{ method.expiryYear }}
                </div>
              </div>
            </label>
          </div>
        </div>
      </div>
    </div>

    <!-- New Card Form -->
    <div *ngIf="!useSavedMethod" class="new-card-form">
      <div class="form-group mb-2">
        <label for="nameOnCard" class="small mb-1">Name on Card</label>
        <input type="text" id="nameOnCard" class="py-1"
               formControlName="nameOnCard" placeholder="John Doe">
      </div>

      <div class="form-group mb-2">
        <label [attr.for]="cardElementId" class="small mb-1">Card Information</label>
        <div [id]="cardElementId" class="card-element compact" #cardElement></div>
        <div *ngIf="cardErrors" class="card-errors small">
          {{ cardErrors }}
        </div>
      </div>

      <!-- Save Card Option -->
      <div *ngIf="showSaveOption" class="form-group mb-2">
        <label class="checkbox-container small">
          <input type="checkbox" id="saveCard" formControlName="saveCard">
          <span class="checkmark"></span>
          Save this card for future payments
        </label>
      </div>
    </div>

    <!-- Submit Button -->
    <div class="d-grid gap-2 mt-3">
      <button type="submit" class="btn-primary py-2" [disabled]="processingPayment">
        <span *ngIf="processingPayment" class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
        {{ processingPayment ? 'Processing...' : 'Pay Now' }}
      </button>
    </div>
  </form>
</div>
