// Main container
.membership-container {
  display: flex;
  flex-direction: column;
  width: 100%;
  max-width: 1100px;
  margin: 0 auto;
  overflow: visible; // Allow badges and other elements to extend beyond container
}

// Navigation bar
.navigation-bar {
  padding: 1.5rem;
  border-bottom: 1px solid #f0f0f0;
}

// Step indicators
.step-indicators {
  display: flex;
  justify-content: center;
  align-items: center;
}

// Step styling
.step {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  z-index: 2;
  transition: transform 0.3s ease;
  cursor: pointer;

  &:hover {
    transform: translateY(-2px);
  }

  .reduced-motion & {
    transition: none;

    &:hover {
      transform: none;
    }
  }
}

.step-number {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: #e0e0e0;
  color: #757575;
  display: flex;
  justify-content: center;
  align-items: center;
  font-weight: bold;
  margin-bottom: 8px;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

  .reduced-motion & {
    transition: none;
  }
}

.step.active .step-number {
  background-color: var(--bs-primary);
  color: white;
  transform: scale(1.1);
  box-shadow: 0 0 0 4px rgba(13, 110, 253, 0.2), 0 4px 6px rgba(0, 0, 0, 0.1);

  .reduced-motion & {
    transform: none;
  }
}

.step.completed .step-number {
  background-color: #28a745;
  color: white;
  box-shadow: 0 0 0 4px rgba(40, 167, 69, 0.2), 0 2px 4px rgba(0, 0, 0, 0.1);
}

.step-label {
  font-size: 14px;
  color: #757575;
  transition: all 0.3s ease;
  font-weight: 500;

  .reduced-motion & {
    transition: none;
  }
}

.step.active .step-label {
  color: var(--bs-primary);
  font-weight: 600;
}

.step.completed .step-label {
  color: #28a745;
  font-weight: 500;
}

.step-connector {
  height: 3px;
  width: 80px;
  background-color: #e0e0e0;
  margin: 0 15px;
  transition: all 0.3s ease;
  position: relative;
  top: -20px;

  .reduced-motion & {
    transition: none;
  }
}

.step-connector.active {
  background-color: var(--bs-primary);
  box-shadow: 0 1px 3px rgba(13, 110, 253, 0.3);
}

// Content area
.content-area {
  padding: 1.5rem 0;

  @media (min-width: 768px) {
    padding: 2rem 0;
  }
}

// Responsive adjustments
@media (max-width: 767px) {
  .step-connector {
    width: 40px;
  }

  .step-number {
    width: 36px;
    height: 36px;
    font-size: 14px;
  }

  .step-label {
    font-size: 12px;
  }
}
