import { Component, Input, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';

@Component({
  selector: 'app-membership-container',
  standalone: true,
  imports: [CommonModule, RouterModule],
  templateUrl: './membership-container.component.html',
  styleUrls: ['./membership-container.component.scss']
})
export class MembershipContainerComponent implements OnInit {
  @Input() currentStep: 'select' | 'payment' | 'confirmation' = 'select';
  @Input() reducedMotion = false;

  constructor() { }

  ngOnInit(): void {
    // Check user's motion preference
    this.checkReducedMotionPreference();
  }

  private checkReducedMotionPreference(): void {
    // Check if the user prefers reduced motion
    const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;
    if (prefersReducedMotion) {
      this.reducedMotion = true;
    }
  }
}
