<div class="membership-container" [class.reduced-motion]="reducedMotion">
  <!-- Navigation Bar with Steps -->
  <div class="navigation-bar">
    <div class="step-indicators">
      <div class="step" [class.active]="currentStep === 'select'" [class.completed]="currentStep === 'payment' || currentStep === 'confirmation'">
        <div class="step-number">1</div>
        <div class="step-label">Select Plan</div>
      </div>
      <div class="step-connector" [class.active]="currentStep === 'payment' || currentStep === 'confirmation'"></div>
      <div class="step" [class.active]="currentStep === 'payment'" [class.completed]="currentStep === 'confirmation'">
        <div class="step-number">2</div>
        <div class="step-label">Payment</div>
      </div>
      <div class="step-connector" [class.active]="currentStep === 'confirmation'"></div>
      <div class="step" [class.active]="currentStep === 'confirmation'">
        <div class="step-number">3</div>
        <div class="step-label">Confirmation</div>
      </div>
    </div>
  </div>

  <!-- Content Area -->
  <div class="content-area">
    <ng-content></ng-content>
  </div>
</div>
