<!-- Winners Society App Main Layout -->
<div class="app-container">
  <!-- Admin layout -->
  <div *ngIf="isAdminRoute" class="admin-layout">
    <router-outlet></router-outlet>
  </div>

  <!-- Member layout - just the router outlet for the member layout component -->
  <div *ngIf="isMemberRoute" class="member-layout">
    <router-outlet></router-outlet>
  </div>

  <!-- Auth layout - full screen with header, no footer -->
  <div *ngIf="isAuthRoute" class="auth-layout">
    <!-- Header -->
    <header class="app-header">
      <app-toolbar></app-toolbar>
    </header>

    <!-- Auth content area -->
    <main class="auth-main">
      <router-outlet></router-outlet>
    </main>
  </div>

  <!-- Public layout for public routes -->
  <div *ngIf="isPublicRoute" class="main-layout">
    <!-- Header -->
    <header class="app-header">
      <app-toolbar></app-toolbar>
    </header>

    <!-- Main content -->
    <main class="app-main">
      <div class="content-container" [ngClass]="{'full-width': isFullWidthRoute}">
        <router-outlet></router-outlet>
      </div>
    </main>

    <!-- Footer -->
    <footer class="app-footer">
      <app-footer></app-footer>
    </footer>
  </div>
</div>
