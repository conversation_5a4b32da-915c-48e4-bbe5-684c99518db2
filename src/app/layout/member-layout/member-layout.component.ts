import { Component, OnInit } from '@angular/core';
import { RouterOutlet, RouterLink, RouterLinkActive, Router, NavigationEnd } from '@angular/router';
import { CommonModule } from '@angular/common';
import { filter } from 'rxjs/operators';
import { AuthService } from '../../core/services/auth.service';
import { UserContextService } from '../../core/services/user-context.service';
import { AuthUser } from '../../core/models/user.model';
import { Subscription as MembershipSubscription } from '../../core/models/membership.model';

interface Notification {
  id: string;
  message: string;
  time: string;
  read: boolean;
  icon: string;
}

@Component({
  selector: 'app-member-layout',
  standalone: true,
  imports: [
    CommonModule, 
    RouterOutlet, 
    RouterLink,
    RouterLinkActive
  ],
  templateUrl: './member-layout.component.html',
  styleUrls: ['./member-layout.component.scss']
})
export class MemberLayoutComponent implements OnInit {
  isSidebarOpen = true;
  userName = 'Member';
  membershipTier = '';
  hasActiveMembership = false;
  nextBillingDate: Date | null = null;
  pageTitle = 'Dashboard';
  currentYear = new Date().getFullYear();
  activeSubmenus: string[] = [];
  notificationCount = 3;

  notifications: Notification[] = [
    {
      id: '1',
      message: 'New giveaway available: MacBook Pro',
      time: '5 minutes ago',
      read: false,
      icon: 'fa-gift'
    },
    {
      id: '2',
      message: 'Your membership will expire in 7 days',
      time: '1 hour ago',
      read: false,
      icon: 'fa-bell'
    },
    {
      id: '3',
      message: 'Congratulations! You won the iPhone giveaway',
      time: '2 days ago',
      read: true,
      icon: 'fa-trophy'
    }
  ];

  constructor(
    private router: Router,
    private authService: AuthService,
    private userContext: UserContextService
  ) {}

  ngOnInit() {
    // Subscribe to current user to update profile info
    this.authService.currentUser$.subscribe(user => {
      if (user) {
        // Verify user is not an admin (regular user roles: 'USER', 'member', or undefined)
        if (user.role === 'ADMIN' || user.role === 'admin') {
          this.router.navigate(['/auth/admin/login']);
          return;
        }
        // Update profile info
        this.userName = `${user.firstName} ${user.lastName}`.trim() || user.email?.split('@')[0] || 'Member';
      } else {
        this.userName = 'Member';
      }
    });
    this.userContext.membership$.subscribe(membership => {
      if (membership && membership.status === 'active') {
        this.hasActiveMembership = true;
        this.membershipTier = membership.tierName;
        this.nextBillingDate = membership.nextBillingDate ? new Date(membership.nextBillingDate) : null;
      } else {
        this.hasActiveMembership = false;
        this.membershipTier = '';
        this.nextBillingDate = null;
      }
    });

    // Update page title based on current route
    this.updatePageTitle();

    // Update the page title when the route changes
    this.router.events.pipe(
      filter(event => event instanceof NavigationEnd)
    ).subscribe(() => {
      this.updatePageTitle();
    });
  }

  toggleSidebar() {
    this.isSidebarOpen = !this.isSidebarOpen;
  }

  toggleSubmenu(menuName: string) {
    if (this.activeSubmenus.includes(menuName)) {
      this.activeSubmenus = this.activeSubmenus.filter(name => name !== menuName);
    } else {
      this.activeSubmenus.push(menuName);
    }
  }
  
  isSubmenuExpanded(menuName: string): boolean {
    return this.activeSubmenus.includes(menuName);
  }
  
  isSubmenuActive(path: string): boolean {
    return this.router.url.includes(path);
  }
  
  logout() {
    this.authService.logout();
    this.router.navigate(['/auth/login']);
  }
  
  /**
   * Update page title based on current route
   */
  private updatePageTitle(): void {
    const url = this.router.url;
    
    if (url.includes('/dashboard')) {
      this.pageTitle = 'Dashboard';
    } else if (url.includes('/giveaways')) {
      this.pageTitle = 'Giveaways';
    } else if (url.includes('/membership')) {
      this.pageTitle = 'Membership';
    } else if (url.includes('/winners')) {
      this.pageTitle = 'Winners';
    } else if (url.includes('/profile')) {
      if (url.includes('/entries')) {
        this.pageTitle = 'Entry History';
      } else if (url.includes('/wins')) {
        this.pageTitle = 'Win History';
      } else if (url.includes('/transactions')) {
        this.pageTitle = 'Transaction History';
      } else {
        this.pageTitle = 'My Profile';
      }
    } else if (url.includes('/help')) {
      this.pageTitle = 'Help & FAQ';
    } else {
      this.pageTitle = 'Member Area';
    }
  }
}