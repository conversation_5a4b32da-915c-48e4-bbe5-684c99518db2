<!-- Professional Member Dashboard Layout -->
<div class="member-dashboard d-flex" id="member-wrapper">
  <!-- Backdrop overlay for mobile - only visible when sidebar is open -->
  <div class="sidebar-backdrop" *ngIf="isSidebarOpen" (click)="toggleSidebar()"></div>
  <!-- Modern Sidebar with light design -->
  <div class="sidebar" [ngClass]="{'collapsed': !isSidebarOpen}">
    <!-- Sidebar expand button - visible only when sidebar is collapsed -->
    <button *ngIf="!isSidebarOpen" class="sidebar-expand-button" (click)="toggleSidebar()" title="Expand sidebar">
      <i class="fas fa-chevron-right"></i>
    </button>
    <!-- Sidebar Header/Logo -->
    <div class="sidebar-header">
      <div class="logo-container">
        <a routerLink="/dashboard">
          <div class="logo-wrapper">
            <div class="logo-icon">
              <i class="fas fa-trophy"></i>
            </div>
            <div class="logo-text" *ngIf="isSidebarOpen">
              <span>WINNERS SOCIETY</span>
            </div>
          </div>
        </a>
      </div>
      <button class="sidebar-toggle" (click)="toggleSidebar()" title="Toggle sidebar">
        <i class="fas" [ngClass]="isSidebarOpen ? 'fa-chevron-left' : 'fa-chevron-right'"></i>
      </button>
    </div>

    <!-- User Profile Summary -->
    <div class="sidebar-user" *ngIf="isSidebarOpen">
      <div class="user-avatar">
        <i class="fa fa-user-circle"></i>
        <span class="status-dot" [ngClass]="{'active': hasActiveMembership}"></span>
      </div>
      <div class="user-info">
        <div class="user-name">{{userName}}</div>
        <div class="user-status">
          <ng-container *ngIf="hasActiveMembership; else noPlan">
            <span class="user-badge primary">{{membershipTier}}</span>
            <span class="user-badge success">Active</span>
          </ng-container>
          <ng-template #noPlan>
            <span class="user-badge secondary">No Plan</span>
          </ng-template>
        </div>
      </div>
    </div>

    <!-- Navigation Menu -->
    <nav class="sidebar-nav">
      <div class="nav-section">
        <div class="nav-section-header" *ngIf="isSidebarOpen">
          <span>MAIN MENU</span>
        </div>
        <ul class="nav-list">
          <li class="nav-item">
            <a class="nav-link" routerLink="/member/dashboard" routerLinkActive="active">
              <div class="nav-icon">
                <i class="fas fa-home"></i>
              </div>
              <span class="nav-label" *ngIf="isSidebarOpen">Dashboard</span>
            </a>
          </li>

          <li class="nav-item">
            <a class="nav-link" routerLink="/member/giveaways" routerLinkActive="active">
              <div class="nav-icon">
                <i class="fas fa-gift"></i>
              </div>
              <span class="nav-label" *ngIf="isSidebarOpen">Giveaways</span>
            </a>
          </li>

          <li class="nav-item">
            <a class="nav-link" routerLink="/member/membership/dashboard" routerLinkActive="active">
              <div class="nav-icon">
                <i class="fas fa-id-card"></i>
              </div>
              <span class="nav-label" *ngIf="isSidebarOpen">Membership</span>
            </a>
          </li>

          <li class="nav-item">
            <a class="nav-link" routerLink="/member/winners" routerLinkActive="active">
              <div class="nav-icon">
                <i class="fas fa-trophy"></i>
              </div>
              <span class="nav-label" *ngIf="isSidebarOpen">Winners</span>
            </a>
          </li>

          <li class="nav-item">
            <a class="nav-link submenu-toggle"
               (click)="toggleSubmenu('profile')"
               [class.active]="isSubmenuActive('/profile')">
              <div class="nav-icon">
                <i class="fas fa-user"></i>
              </div>
              <span class="nav-label" *ngIf="isSidebarOpen">My Profile</span>
              <i class="submenu-arrow fas fa-chevron-right"
                 *ngIf="isSidebarOpen"
                 [ngClass]="{'expanded': isSubmenuExpanded('profile')}"></i>
            </a>
            <ul class="submenu"
                [ngClass]="{'expanded': isSubmenuExpanded('profile') && isSidebarOpen}"
                *ngIf="isSidebarOpen">
              <li class="submenu-item">
                <a class="submenu-link" routerLink="/member/profile" routerLinkActive="active">
                  <i class="submenu-icon fas fa-circle"></i>
                  <span>Profile Settings</span>
                </a>
              </li>
              <li class="submenu-item">
                <a class="submenu-link" routerLink="/member/entries" routerLinkActive="active">
                  <i class="submenu-icon fas fa-circle"></i>
                  <span>Entry History</span>
                </a>
              </li>
              <li class="submenu-item">
                <a class="submenu-link" routerLink="/member/profile/wins" routerLinkActive="active">
                  <i class="submenu-icon fas fa-circle"></i>
                  <span>Win History</span>
                </a>
              </li>
              <li class="submenu-item">
                <a class="submenu-link" routerLink="/member/profile/transactions" routerLinkActive="active">
                  <i class="submenu-icon fas fa-circle"></i>
                  <span>Transactions</span>
                </a>
              </li>
            </ul>
          </li>

          <li class="nav-item">
            <a class="nav-link" routerLink="/member/help" routerLinkActive="active">
              <div class="nav-icon">
                <i class="fas fa-question-circle"></i>
              </div>
              <span class="nav-label" *ngIf="isSidebarOpen">Help & FAQ</span>
            </a>
          </li>
        </ul>
      </div>

      <div class="nav-section logout-section">
        <ul class="nav-list">
          <li class="nav-item">
            <a class="nav-link logout" (click)="logout()">
              <div class="nav-icon">
                <i class="fas fa-sign-out-alt"></i>
              </div>
              <span class="nav-label" *ngIf="isSidebarOpen">Logout</span>
            </a>
          </li>
        </ul>
      </div>
    </nav>
  </div>

  <!-- Main Content -->
  <div class="main-content flex-grow-1 d-flex flex-column">
    <!-- Top Header/Navbar - Modern & Compact Design -->
    <header class="header bg-white sticky-top">
      <nav class="navbar navbar-expand navbar-light py-2">
        <div class="container-fluid px-3 px-lg-4">
          <!-- Mobile Sidebar Toggle - Enhanced for better visibility -->
          <button class="btn btn-icon btn-sm d-flex d-md-none align-items-center justify-content-center me-2 mobile-sidebar-toggle"
                  (click)="toggleSidebar()">
            <i class="fas" [ngClass]="isSidebarOpen ? 'fa-times' : 'fa-bars'"></i>
          </button>

          <!-- Page Title with Breadcrumb -->
          <div class="d-flex align-items-center">
            <h5 class="mb-0 fw-semibold">{{pageTitle}}</h5>
            <!-- Quick Breadcrumb for context - visible on larger screens -->
            <div class="breadcrumb-compact ms-3 d-none d-lg-flex align-items-center">
              <span class="text-muted small">
                <i class="fas fa-home fa-xs me-1"></i>
                <span class="mx-1">/</span>
                <span class="text-body-secondary">{{pageTitle}}</span>
              </span>
            </div>
          </div>

          <!-- Right Menu Items -->
          <ul class="navbar-nav ms-auto d-flex align-items-center">
            <!-- Search Button (toggles search overlay) -->
            <li class="nav-item me-2 d-none d-md-block">
              <button class="btn btn-icon btn-sm" title="Search">
                <i class="fas fa-search"></i>
              </button>
            </li>

            <!-- Notifications Dropdown - Redesigned -->
            <li class="nav-item dropdown me-2">
              <button class="btn btn-icon btn-sm position-relative"
                      id="notificationsDropdown"
                      data-bs-toggle="dropdown"
                      aria-expanded="false"
                      title="Notifications">
                <i class="fas fa-bell"></i>
                <span class="notification-indicator" *ngIf="notificationCount > 0">
                  <span class="notification-count">{{notificationCount}}</span>
                </span>
              </button>
              <div class="dropdown-menu dropdown-menu-end notifications-dropdown shadow-sm"
                   aria-labelledby="notificationsDropdown">
                <div class="dropdown-header d-flex justify-content-between align-items-center">
                  <span>Notifications</span>
                  <button class="btn btn-sm btn-link p-0 text-decoration-none" title="Mark all as read">
                    <i class="fas fa-check-double fa-sm me-1"></i>
                    <span class="small">Mark all read</span>
                  </button>
                </div>
                <div class="notification-list">
                  <a href="#" class="notification-item" *ngFor="let notification of notifications">
                    <div class="notification-icon" [ngClass]="{'unread': !notification.read}">
                      <i class="fas" [ngClass]="notification.icon"></i>
                    </div>
                    <div class="notification-content">
                      <p class="notification-text" [ngClass]="{'fw-medium': !notification.read}">
                        {{notification.message}}
                      </p>
                      <p class="notification-time">{{notification.time}}</p>
                    </div>
                  </a>

                  <!-- Empty state -->
                  <div class="empty-notifications" *ngIf="notifications.length === 0">
                    <i class="fas fa-bell-slash"></i>
                    <p>No notifications</p>
                  </div>
                </div>
                <div class="dropdown-footer">
                  <a href="#" class="view-all">View all notifications</a>
                </div>
              </div>
            </li>

            <!-- User Profile Dropdown - Redesigned -->
            <li class="nav-item dropdown">
              <button class="user-profile-toggle"
                      id="userDropdown"
                      data-bs-toggle="dropdown"
                      aria-expanded="false">
                <div class="user-avatar">
                  <i class="fa fa-user-circle"></i>
                  <span class="status-indicator" [ngClass]="{'active': hasActiveMembership}"></span>
                </div>
                <div class="user-info d-none d-md-block">
                  <span class="user-name">{{userName}}</span>
                  <i class="fas fa-chevron-down"></i>
                </div>
              </button>
              <div class="dropdown-menu dropdown-menu-end user-dropdown shadow-sm"
                   aria-labelledby="userDropdown">
                <div class="user-dropdown-header">
                  <div class="user-avatar-large">
                    <i class="fa fa-user-circle"></i>
                  </div>
                  <div class="user-details">
                    <h6 class="mb-0">{{userName}}</h6>
                    <div class="user-badges">
                      <ng-container *ngIf="hasActiveMembership; else noPlanBadge">
                        <span class="badge bg-primary">{{membershipTier}}</span>
                        <span class="badge bg-success">Active</span>
                      </ng-container>
                      <ng-template #noPlanBadge>
                        <span class="badge bg-secondary">No Plan</span>
                      </ng-template>
                    </div>
                  </div>
                </div>
                <div class="user-dropdown-info">
                  <div class="info-item">
                    <span class="info-label">Status</span>
                    <span class="info-value" [ngClass]="hasActiveMembership ? 'text-success' : 'text-secondary'">
                      {{ hasActiveMembership ? 'Active' : 'No Plan' }}
                    </span>
                  </div>
                  <div class="info-item">
                    <span class="info-label">Next Billing</span>
                    <span class="info-value" *ngIf="hasActiveMembership && nextBillingDate; else noBilling">
                      {{ nextBillingDate | date:'MMM d, yyyy' }}
                    </span>
                    <ng-template #noBilling>
                      <span class="info-value text-secondary">-</span>
                    </ng-template>
                  </div>
                </div>
                <div class="user-dropdown-menu">
                  <a class="dropdown-item" routerLink="/member/profile">
                    <i class="fas fa-user"></i> My Profile
                  </a>
                  <a class="dropdown-item" routerLink="/member/membership/dashboard">
                    <i class="fas fa-id-card"></i> My Membership
                  </a>
                  <a class="dropdown-item" routerLink="/member/profile/settings">
                    <i class="fas fa-cog"></i> Settings
                  </a>
                  <div class="dropdown-divider"></div>
                  <a class="dropdown-item text-danger" (click)="logout()">
                    <i class="fas fa-sign-out-alt"></i> Logout
                  </a>
                </div>
              </div>
            </li>
          </ul>
        </div>
      </nav>
    </header>

    <!-- Main Content Area - Redesigned -->
    <div class="content-wrapper">
      <!-- Breadcrumb Navigation -->
      <div class="breadcrumb-wrapper">
        <nav aria-label="breadcrumb">
          <ol class="breadcrumb">
            <li class="breadcrumb-item">
              <a routerLink="/member/dashboard">
                <i class="fas fa-home fa-sm"></i>
              </a>
            </li>
            <li class="breadcrumb-item active" aria-current="page">{{pageTitle}}</li>
          </ol>
        </nav>
      </div>

      <!-- Content Container - Single white container with rounded borders -->
      <div class="content-container">
        <!-- Router Outlet for Page Content -->
        <router-outlet></router-outlet>
      </div>
    </div>

    <!-- Footer - Redesigned -->
    <footer class="footer">
      <div class="footer-content">
        <div class="footer-copyright">
          <span>© {{currentYear}} Winners Society</span>
        </div>
        <div class="footer-links">
          <a href="/terms">Terms</a>
          <a href="/privacy">Privacy</a>
          <a href="/help">Help</a>
        </div>
      </div>
    </footer>
  </div>
</div>