// Host component styles
:host {
  display: block;
  height: 100vh;
  width: 100vw;
  overflow: hidden;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1050;
}

// Member Dashboard Layout
.member-dashboard {
  height: 100vh;
  width: 100vw;
  overflow: hidden;
  background-color: #f8f9fa;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;

  // Backdrop overlay for mobile
  .sidebar-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1040;
    display: none;
    animation: fadeIn 0.2s ease-out;

    @media (max-width: 991.98px) {
      display: block;
    }
  }
}

// Modern Sidebar with Light Design
.sidebar {
  width: 260px;
  height: 100vh;
  overflow-y: auto;
  overflow-x: hidden;
  transition: all 0.3s ease;
  z-index: 1030;
  background-color: #ffffff;
  color: #495057;
  border-right: 1px solid rgba(0, 0, 0, 0.05);
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.03);
  display: flex;
  flex-direction: column;

  // Sidebar expand button - visible when sidebar is collapsed
  .sidebar-expand-button {
    position: fixed;
    top: 50%;
    left: 64px; // Aligns with the width of collapsed sidebar
    transform: translateY(-50%);
    width: 24px;
    height: 48px;
    background-color: var(--bs-primary);
    border: none;
    border-radius: 0 6px 6px 0;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    z-index: 1040;
    box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);
    transition: all 0.2s ease;

    &:hover {
      background-color: var(--bs-primary-dark);
      width: 28px;
    }

    i {
      font-size: 12px;
    }
  }

  // Collapsed state
  &.collapsed {
    width: 64px;

    .sidebar-header {
      padding: 0;

      .logo-container {
        width: 100%;
        justify-content: center;
      }

      .logo-wrapper {
        justify-content: center;
      }

      .sidebar-toggle {
        display: none;
      }
    }

    .nav-icon {
      margin-right: 0;
    }

    .nav-link {
      justify-content: center;
      padding: 12px 0;
    }

    .logout-section {
      margin-top: auto;
    }
  }

  // Scrollbar styling
  &::-webkit-scrollbar {
    width: 4px;
  }

  &::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.02);
  }

  &::-webkit-scrollbar-thumb {
    background-color: rgba(0, 0, 0, 0.1);
    border-radius: 4px;
  }

  // Sidebar Header
  .sidebar-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    height: 64px;

    .logo-container {
      a {
        text-decoration: none;
      }

      .logo-wrapper {
        display: flex;
        align-items: center;

        .logo-icon {
          width: 32px;
          height: 32px;
          border-radius: 8px;
          background-color: var(--bs-primary);
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 12px;

          i {
            color: white;
            font-size: 16px;
          }
        }

        .logo-text {
          span {
            font-weight: 700;
            font-size: 14px;
            color: #212529;
            letter-spacing: 0.5px;
          }
        }
      }
    }

    .sidebar-toggle {
      width: 24px;
      height: 24px;
      border-radius: 4px;
      background-color: #f8f9fa;
      border: 1px solid #e9ecef;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: all 0.2s ease;

      &:hover {
        background-color: #e9ecef;
      }

      i {
        font-size: 10px;
        color: #6c757d;
      }
    }
  }

  // User Profile in Sidebar
  .sidebar-user {
    display: flex;
    align-items: center;
    padding: 16px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);

    .user-avatar {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      background-color: #f8f9fa;
      border: 1px solid #e9ecef;
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;
      margin-right: 12px;

      i {
        font-size: 28px;
        color: #adb5bd;
      }

      .status-dot {
        position: absolute;
        bottom: 0;
        right: 0;
        width: 10px;
        height: 10px;
        border-radius: 50%;
        background-color: #adb5bd;
        border: 2px solid #fff;

        &.active {
          background-color: var(--bs-success);
        }
      }
    }

    .user-info {
      flex: 1;

      .user-name {
        font-weight: 600;
        font-size: 14px;
        color: #212529;
        margin-bottom: 4px;
      }

      .user-status {
        display: flex;
        gap: 4px;

        .user-badge {
          font-size: 10px;
          font-weight: 500;
          padding: 2px 6px;
          border-radius: 4px;

          &.primary {
            background-color: rgba(var(--bs-primary-rgb), 0.1);
            color: var(--bs-primary);
          }

          &.success {
            background-color: rgba(var(--bs-success-rgb), 0.1);
            color: var(--bs-success);
          }

          &.secondary {
            background-color: rgba(var(--bs-secondary-rgb), 0.1);
            color: var(--bs-secondary);
          }
        }
      }
    }
  }

  // Navigation
  .sidebar-nav {
    flex: 1;
    display: flex;
    flex-direction: column;
    padding: 16px 0;
    overflow-y: auto;

    .nav-section {
      margin-bottom: 24px;

      &:last-child {
        margin-bottom: 0;
      }

      .nav-section-header {
        padding: 0 16px;
        margin-bottom: 8px;

        span {
          font-size: 11px;
          font-weight: 600;
          color: #6c757d;
          letter-spacing: 0.5px;
        }
      }

      .nav-list {
        list-style: none;
        padding: 0;
        margin: 0;

        .nav-item {
          margin-bottom: 4px;

          &:last-child {
            margin-bottom: 0;
          }

          .nav-link {
            display: flex;
            align-items: center;
            padding: 10px 16px;
            color: #495057;
            text-decoration: none;
            border-radius: 8px;
            margin: 0 8px;
            transition: all 0.2s ease;
            position: relative;

            &:hover {
              background-color: #f8f9fa;
              color: var(--bs-primary);
            }

            &.active {
              background-color: rgba(var(--bs-primary-rgb), 0.08);
              color: var(--bs-primary);
              font-weight: 500;

              &::before {
                content: '';
                position: absolute;
                left: 0;
                top: 50%;
                transform: translateY(-50%);
                height: 60%;
                width: 3px;
                background-color: var(--bs-primary);
                border-radius: 0 4px 4px 0;
              }

              .nav-icon {
                background-color: rgba(var(--bs-primary-rgb), 0.1);

                i {
                  color: var(--bs-primary);
                }
              }
            }

            .nav-icon {
              width: 32px;
              height: 32px;
              border-radius: 8px;
              background-color: #f8f9fa;
              display: flex;
              align-items: center;
              justify-content: center;
              margin-right: 12px;
              transition: all 0.2s ease;

              i {
                font-size: 14px;
                color: #6c757d;
                transition: all 0.2s ease;
              }
            }

            .nav-label {
              flex: 1;
              font-size: 14px;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
            }

            &.submenu-toggle {
              justify-content: space-between;

              .submenu-arrow {
                font-size: 10px;
                color: #adb5bd;
                transition: transform 0.3s ease;

                &.expanded {
                  transform: rotate(90deg);
                }
              }
            }

            &.logout {
              color: #dc3545;

              &:hover {
                background-color: rgba(var(--bs-danger-rgb), 0.08);
              }

              .nav-icon {
                background-color: rgba(var(--bs-danger-rgb), 0.1);

                i {
                  color: #dc3545;
                }
              }
            }
          }

          .submenu {
            list-style: none;
            padding: 0;
            margin: 4px 0 4px 28px;
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease;

            &.expanded {
              max-height: 200px;
            }

            .submenu-item {
              margin-bottom: 2px;

              &:last-child {
                margin-bottom: 0;
              }

              .submenu-link {
                display: flex;
                align-items: center;
                padding: 8px 16px;
                color: #6c757d;
                text-decoration: none;
                border-radius: 6px;
                transition: all 0.2s ease;
                font-size: 13px;

                &:hover {
                  background-color: #f8f9fa;
                  color: var(--bs-primary);
                }

                &.active {
                  background-color: rgba(var(--bs-primary-rgb), 0.05);
                  color: var(--bs-primary);
                  font-weight: 500;
                }

                .submenu-icon {
                  font-size: 4px;
                  margin-right: 8px;
                  color: #adb5bd;
                }
              }
            }
          }
        }
      }
    }

    .logout-section {
      margin-top: auto;
      padding-top: 16px;
      border-top: 1px solid rgba(0, 0, 0, 0.05);
    }
  }

  // Most Popular Badge (preserved from original)
  .most-popular-badge {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    margin: 0 auto;
    background: #3498db;
    color: #fff;
    font-size: 0.95rem;
    font-weight: 700;
    padding: 4px 18px;
    border-radius: 16px 16px 0 0;
    box-shadow: 0 2px 8px rgba(52,152,219,0.12);
    z-index: 10;
    letter-spacing: 0.04em;
    white-space: nowrap;
    display: block;
    text-align: center;
    pointer-events: none;
  }
}

// Main Content
.main-content {
  width: calc(100% - 260px);
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  height: 100vh;
  overflow: hidden;
  flex: 1;

  .sidebar.collapsed + & {
    width: calc(100% - 64px);
  }

  // Header - Modern & Compact Design
  .header {
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.04);
    z-index: 1020;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);

    .navbar {
      height: 56px; // More compact height
    }

    // Compact breadcrumb in header
    .breadcrumb-compact {
      color: #6c757d;
      font-size: 0.8rem;
    }

    // Button styling
    .btn-icon {
      width: 36px;
      height: 36px;
      border-radius: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: #f8f9fa;
      border: 1px solid #f0f0f0;
      color: #495057;
      transition: all 0.2s ease;

      &:hover {
        background-color: #e9ecef;
        color: var(--bs-primary);
      }

      i {
        font-size: 16px;
      }

      &.btn-sm {
        width: 32px;
        height: 32px;

        i {
          font-size: 14px;
        }
      }
    }

    // Notification indicator
    .notification-indicator {
      position: absolute;
      top: -2px;
      right: -2px;
      width: 18px;
      height: 18px;
      border-radius: 50%;
      background-color: var(--bs-danger);
      display: flex;
      align-items: center;
      justify-content: center;
      border: 2px solid #fff;

      .notification-count {
        color: white;
        font-size: 10px;
        font-weight: 600;
      }
    }

    // Notifications dropdown
    .notifications-dropdown {
      width: 320px;
      padding: 0;
      border: none;
      border-radius: 12px;
      overflow: hidden;

      .dropdown-header {
        padding: 12px 16px;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        font-weight: 600;
        display: flex;
        justify-content: space-between;
        align-items: center;
      }

      .notification-list {
        max-height: 320px;
        overflow-y: auto;

        .notification-item {
          display: flex;
          padding: 12px 16px;
          border-bottom: 1px solid rgba(0, 0, 0, 0.03);
          text-decoration: none;
          color: inherit;
          transition: background-color 0.2s ease;

          &:hover {
            background-color: rgba(0, 0, 0, 0.02);
          }

          .notification-icon {
            width: 36px;
            height: 36px;
            border-radius: 8px;
            background-color: #f0f3f6;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 12px;
            color: #6c757d;

            &.unread {
              background-color: rgba(var(--bs-primary-rgb), 0.1);
              color: var(--bs-primary);
            }

            i {
              font-size: 14px;
            }
          }

          .notification-content {
            flex: 1;

            .notification-text {
              margin-bottom: 4px;
              font-size: 0.875rem;
              color: #212529;
            }

            .notification-time {
              margin-bottom: 0;
              font-size: 0.75rem;
              color: #6c757d;
            }
          }
        }

        .empty-notifications {
          padding: 24px;
          text-align: center;
          color: #6c757d;

          i {
            font-size: 24px;
            margin-bottom: 8px;
            opacity: 0.5;
          }

          p {
            margin-bottom: 0;
            font-size: 0.875rem;
          }
        }
      }

      .dropdown-footer {
        padding: 12px 16px;
        text-align: center;
        border-top: 1px solid rgba(0, 0, 0, 0.05);

        .view-all {
          color: var(--bs-primary);
          font-size: 0.875rem;
          text-decoration: none;

          &:hover {
            text-decoration: underline;
          }
        }
      }
    }

    // User profile toggle
    .user-profile-toggle {
      display: flex;
      align-items: center;
      background: none;
      border: none;
      padding: 0;
      cursor: pointer;

      .user-avatar {
        width: 36px;
        height: 36px;
        border-radius: 50%;
        background-color: #f8f9fa;
        border: 1px solid #e9ecef;
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
        margin-right: 8px;

        i {
          font-size: 24px;
          color: #adb5bd;
        }

        .status-indicator {
          position: absolute;
          bottom: 0;
          right: 0;
          width: 10px;
          height: 10px;
          border-radius: 50%;
          background-color: #adb5bd;
          border: 2px solid #fff;

          &.active {
            background-color: var(--bs-success);
          }
        }
      }

      .user-info {
        display: flex;
        align-items: center;

        .user-name {
          font-size: 0.875rem;
          font-weight: 500;
          margin-right: 4px;
          color: #495057;
        }

        i {
          font-size: 10px;
          color: #adb5bd;
        }
      }
    }

    // User dropdown
    .user-dropdown {
      width: 280px;
      padding: 0;
      border: none;
      border-radius: 12px;
      overflow: hidden;

      .user-dropdown-header {
        padding: 16px;
        background-color: #f8f9fa;
        display: flex;
        align-items: center;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);

        .user-avatar-large {
          width: 48px;
          height: 48px;
          border-radius: 50%;
          background-color: #fff;
          border: 1px solid #e9ecef;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 12px;

          i {
            font-size: 32px;
            color: #adb5bd;
          }
        }

        .user-details {
          flex: 1;

          h6 {
            font-weight: 600;
          }

          .user-badges {
            display: flex;
            gap: 4px;
            margin-top: 4px;

            .badge {
              font-size: 0.7rem;
              font-weight: 500;
              padding: 0.25em 0.6em;
              border-radius: 4px;
            }
          }
        }
      }

      .user-dropdown-info {
        padding: 12px 16px;
        background-color: #fff;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);

        .info-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 8px;

          &:last-child {
            margin-bottom: 0;
          }

          .info-label {
            font-size: 0.75rem;
            color: #6c757d;
          }

          .info-value {
            font-size: 0.75rem;
            font-weight: 500;
          }
        }
      }

      .user-dropdown-menu {
        padding: 8px 0;

        .dropdown-item {
          padding: 8px 16px;
          font-size: 0.875rem;

          i {
            width: 20px;
            margin-right: 8px;
            text-align: center;
            color: #6c757d;
          }

          &:hover {
            background-color: #f8f9fa;
          }

          &.text-danger i {
            color: var(--bs-danger);
          }
        }
      }
    }
  }

  // Content Wrapper - Redesigned
  .content-wrapper {
    flex: 1;
    background-color: #f8f9fa;
    overflow-y: auto;
    padding: 16px;
    display: flex;
    flex-direction: column;

    @media (min-width: 768px) {
      padding: 24px;
    }

    // Breadcrumb Navigation
    .breadcrumb-wrapper {
      margin-bottom: 16px;

      .breadcrumb {
        background-color: transparent;
        padding: 0;
        margin: 0;
        display: flex;
        align-items: center;

        .breadcrumb-item {
          font-size: 0.875rem;
          display: flex;
          align-items: center;

          a {
            color: #6c757d;
            text-decoration: none;
            display: flex;
            align-items: center;

            &:hover {
              color: var(--bs-primary);
            }

            i {
              font-size: 14px;
            }
          }

          &.active {
            color: #343a40;
            font-weight: 500;
          }

          + .breadcrumb-item {
            padding-left: 8px;

            &::before {
              content: "/";
              color: #adb5bd;
              padding-right: 8px;
              float: none;
            }
          }
        }
      }
    }

    // Content Container - Single white container with rounded borders
    .content-container {
      background-color: #ffffff;
      border-radius: 16px;
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
      padding: 24px;
      flex: 1;
      overflow-y: auto;

      @media (min-width: 768px) {
        padding: 32px;
      }

      @media (max-width: 767.98px) {
        padding: 20px;
      }
    }
  }

  // Footer - Redesigned
  .footer {
    background-color: #ffffff;
    border-top: 1px solid rgba(0, 0, 0, 0.05);
    padding: 12px 24px;

    .footer-content {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .footer-copyright {
        font-size: 12px;
        color: #6c757d;
      }

      .footer-links {
        display: flex;
        gap: 16px;

        a {
          font-size: 12px;
          color: #6c757d;
          text-decoration: none;
          transition: color 0.2s ease;

          &:hover {
            color: var(--bs-primary);
          }
        }
      }
    }

    @media (max-width: 576px) {
      padding: 12px 16px;

      .footer-content {
        flex-direction: column;
        gap: 8px;

        .footer-copyright {
          order: 2;
        }

        .footer-links {
          order: 1;
        }
      }
    }
  }
}

// Dashboard Cards and Elements
.card {
  border: none;
  border-radius: 0.75rem;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.05);
  transition: all 0.2s ease-in-out;

  &:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.08);
  }

  .card-header {
    background-color: transparent;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    font-weight: 600;
    padding: 1rem 1.25rem;
  }

  .card-footer {
    background-color: transparent;
    border-top: 1px solid rgba(0, 0, 0, 0.05);
  }
}

// Notifications
.notification-list {
  .dropdown-item {
    &:hover {
      background-color: rgba(var(--bs-primary-rgb), 0.05);
    }
  }

  .smaller {
    font-size: 0.75rem;
  }

  .icon-circle {
    i {
      font-size: 14px;
    }
  }
}

// Badge
.badge {
  font-weight: 500;
  padding: 0.35em 0.65em;
  font-size: 0.75em;
  line-height: 1;
}

// Animation for Notifications Badge
@keyframes pulseNotification {
  0% { transform: translate(-50%, -50%) scale(1); }
  50% { transform: translate(-50%, -50%) scale(1.1); }
  100% { transform: translate(-50%, -50%) scale(1); }
}

.badge.bg-danger {
  animation: pulseNotification 2s infinite;
}

// Responsive Styles
@media (max-width: 991.98px) {
  .member-dashboard {
    overflow-x: hidden;
  }

  .sidebar {
    position: fixed;
    left: -270px;
    z-index: 1050;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);

    &:not(.collapsed) {
      left: 0;
    }

    &.collapsed {
      left: -64px;

      .sidebar-expand-button {
        left: 0;
        border-radius: 0 6px 6px 0;
      }
    }

    .sidebar-expand-button {
      display: flex !important; // Ensure it's always visible on mobile
    }
  }

  .main-content {
    width: 100% !important;
    margin-left: 0;
  }

  // Adjust content padding for smaller screens
  .content-wrapper {
    padding: 12px !important;

    .content-container {
      padding: 16px !important;
    }
  }

  // Make header more compact on mobile
  .header .navbar {
    height: 52px !important;
    padding-left: 12px !important;
    padding-right: 12px !important;

    // Enhanced mobile sidebar toggle
    .mobile-sidebar-toggle {
      background-color: var(--bs-primary);
      color: white;
      width: 36px;
      height: 36px;
      border-radius: 6px;
      transition: all 0.2s ease;

      &:hover {
        background-color: var(--bs-primary-dark);
      }

      i {
        font-size: 14px;
      }
    }
  }
}

// Utility Classes
.bg-primary-subtle {
  background-color: rgba(var(--bs-primary-rgb), 0.1);
}

.text-primary {
  color: var(--bs-primary) !important;
}

// Subtle Animations
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideInUp {
  from {
    transform: translateY(10px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

// Apply animations to elements
.content-container {
  animation: fadeIn 0.3s ease-out;
}

.nav-link {
  transition: all 0.2s ease-out;

  &:hover {
    .nav-icon {
      animation: pulse 0.4s ease-out;
    }
  }
}

.dropdown-menu {
  animation: slideInUp 0.25s ease-out;
}

// Reduced motion preferences
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}