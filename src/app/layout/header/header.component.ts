import { Component, EventEmitter, Output, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterLink } from '@angular/router';
import { UserContextService } from '../../core/services/user-context.service';
import { AuthService } from '../../core/services/auth.service';
import { AuthUser } from '../../core/models/user.model';
import { Subscription } from '../../core/models/membership.model';

@Component({
  selector: 'app-header',
  standalone: true,
  imports: [CommonModule, RouterLink],
  templateUrl: './header.component.html',
  styleUrls: ['./header.component.scss']
})
export class HeaderComponent implements OnInit {
  @Output() toggleSidebarEvent = new EventEmitter<void>();

  user: AuthUser | null = null;
  membership: Subscription | null = null;
  hasActiveMembership = false;

  constructor(
    private userContext: UserContextService,
    private authService: AuthService
  ) {}

  ngOnInit() {
    this.authService.currentUser$.subscribe(user => {
      this.user = user;
    });
    this.userContext.membership$.subscribe(membership => {
      this.membership = membership;
      this.hasActiveMembership = !!(membership && membership.status === 'active');
    });
  }

  toggleSidebar() {
    this.toggleSidebarEvent.emit();
  }

  logout() {
    this.authService.logout();
  }
}