.page-header {
  background-color: #ffffff;
  border-bottom: 1px solid #eeeeee;
  position: sticky;
  top: 0;
  z-index: 1000;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.05);
}

.header-wrapper {
  padding: 0 30px;
  height: 80px;
  align-items: center;
  
  .form-inline {
    display: none;
    
    &.search-full {
      position: absolute;
      left: 0;
      right: 0;
      top: 0;
      height: 100%;
      background-color: #fff;
      z-index: 1;
      display: none;
      
      &.open {
        display: block;
      }
      
      .form-group {
        height: 100%;
        
        .u-posRelative {
          height: 100%;
          display: flex;
          align-items: center;
          
          input {
            height: 100%;
            padding: 0 20px;
          }
          
          .close-search {
            position: absolute;
            right: 20px;
            cursor: pointer;
          }
        }
      }
    }
  }
  
  .header-logo-wrapper {
    display: flex;
    align-items: center;
    
    .logo-wrapper {
      margin-right: 15px;
      
      img {
        max-height: 40px;
      }
    }
    
    .toggle-sidebar {
      border-radius: 4px;
      padding: 6px 12px;
      cursor: pointer;
      
      &:hover {
        background-color: #f5f5f5;
      }
      
      i {
        font-size: 18px;
      }
    }
  }
  
  .nav-right {
    justify-content: flex-end;
    
    .nav-menus {
      margin: 0;
      padding: 0;
      list-style: none;
      display: flex;
      align-items: center;
      
      > li {
        margin: 0 10px;
        position: relative;
        
        &:last-child {
          margin-right: 0;
        }
        
        .notification-box, .header-search, .profile-nav {
          cursor: pointer;
          font-size: 18px;
          display: flex;
          align-items: center;
          
          .badge {
            position: absolute;
            top: -5px;
            right: -5px;
            font-size: 10px;
            padding: 3px 5px;
          }
          
          img {
            width: 35px;
            height: 35px;
          }
        }
        
        &.onhover-dropdown {
          position: relative;
          
          .onhover-show-div {
            position: absolute;
            top: 100%;
            right: 0;
            background-color: #fff;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
            border-radius: 4px;
            min-width: 200px;
            display: none;
            overflow: hidden;
            margin-top: 15px;
            
            .dropdown-title {
              padding: 15px;
              border-bottom: 1px solid #eeeeee;
              margin-bottom: 0;
            }
            
            ul {
              margin: 0;
              padding: 0;
              list-style: none;
              
              li {
                padding: 10px 15px;
                border-bottom: 1px solid #f5f5f5;
                
                &:last-child {
                  border-bottom: none;
                }
                
                a {
                  display: flex;
                  align-items: center;
                  color: #333;
                  text-decoration: none;
                  
                  i {
                    width: 20px;
                    margin-right: 10px;
                  }
                }
                
                &.b-l-primary {
                  border-left: 3px solid #7366ff;
                }
                
                &.b-l-success {
                  border-left: 3px solid #51bb25;
                }
              }
            }
          }
          
          &:hover {
            .onhover-show-div {
              display: block;
            }
          }
        }
      }
    }
  }
} 