<div class="header-wrapper row m-0">
  <!-- Search Form -->
  <form class="form-inline search-full col" action="#" method="get">
    <div class="form-group w-100">
      <div class="Typeahead Typeahead--twitterUsers">
        <div class="u-posRelative">
          <input class="demo-input Typeahead-input form-control-plaintext w-100" type="text" placeholder="Search Anything Here..." name="q" title="">
          <div class="spinner-border Typeahead-spinner" role="status"><span class="sr-only">Loading...</span></div>
          <span class="close-search" (click)="$event.stopPropagation()"><i class="fa fa-times"></i></span>
        </div>
      </div>
    </div>
  </form>

  <!-- Logo and Sidebar Toggle -->
  <div class="header-logo-wrapper col-auto p-0">
    <div class="logo-wrapper">
      <a routerLink="/admin/dashboard">
        <img class="img-fluid for-light" src="assets/images/logo/logo.png" alt="Winners Society Logo">
        <img class="img-fluid for-dark" src="assets/images/logo/logo_dark.png" alt="Winners Society Logo">
      </a>
    </div>
    <div class="toggle-sidebar" (click)="toggleSidebar()">
      <i class="status_toggle middle sidebar-toggle" data-feather="align-center"></i>
    </div>
  </div>

  <!-- Left Header Notification Slider -->
  <div class="left-header col-xxl-5 col-xl-6 col-lg-5 col-md-4 col-sm-3 p-0">
    <div class="notification-slider">
      <div class="d-flex h-100">
        <img src="assets/images/giftools.gif" alt="gif">
        <h6 class="mb-0 f-w-400">
          <span class="font-primary">Don't Miss Out! </span>
          <span class="f-light">Our new update has been released.</span>
        </h6>
        <i class="icon-arrow-top-right f-light"></i>
      </div>
    </div>
  </div>

  <!-- Right Header Menu -->
  <div class="nav-right col-xxl-7 col-xl-6 col-md-7 col-8 pull-right right-header p-0 ms-auto">
    <ul class="nav-menus d-flex align-items-center" style="gap: 12px;">
      <!-- Language Selector -->
      <li class="language-nav">
        <div class="translate_wrapper">
          <div class="current_lang">
            <div class="lang">
              <i class="flag-icon flag-icon-us"></i>
              <span class="lang-txt">EN</span>
            </div>
          </div>
          <div class="more_lang">
            <div class="lang selected" data-value="en">
              <i class="flag-icon flag-icon-us"></i>
              <span class="lang-txt">English<span> (US)</span></span>
            </div>
            <div class="lang" data-value="de">
              <i class="flag-icon flag-icon-de"></i>
              <span class="lang-txt">Deutsch</span>
            </div>
            <div class="lang" data-value="es">
              <i class="flag-icon flag-icon-es"></i>
              <span class="lang-txt">Español</span>
            </div>
            <div class="lang" data-value="fr">
              <i class="flag-icon flag-icon-fr"></i>
              <span class="lang-txt">Français</span>
            </div>
          </div>
        </div>
      </li>

      <!-- Full Screen Toggle -->
      <li class="fullscreen-body">
        <span>
          <svg id="maximize-screen">
            <use href="assets/svg/icon-sprite.svg#full-screen"></use>
          </svg>
        </span>
      </li>

      <!-- Search Button -->
      <li>
        <span class="header-search">
          <svg>
            <use href="assets/svg/icon-sprite.svg#search"></use>
          </svg>
        </span>
      </li>

      <!-- Bookmark Button -->
      <li class="onhover-dropdown">
        <svg>
          <use href="assets/svg/icon-sprite.svg#star"></use>
        </svg>
        <div class="onhover-show-div bookmark-flip">
          <div class="flip-card">
            <div class="flip-card-inner">
              <div class="front">
                <h6 class="f-18 mb-0 dropdown-title">Bookmark</h6>
                <ul class="bookmark-dropdown">
                  <li>
                    <div class="row">
                      <div class="col-4 text-center">
                        <div class="bookmark-content">
                          <div class="bookmark-icon"><i data-feather="file-text"></i></div>
                          <span>Forms</span>
                        </div>
                      </div>
                      <div class="col-4 text-center">
                        <div class="bookmark-content">
                          <div class="bookmark-icon"><i data-feather="user"></i></div>
                          <span>Profile</span>
                        </div>
                      </div>
                      <div class="col-4 text-center">
                        <div class="bookmark-content">
                          <div class="bookmark-icon"><i data-feather="server"></i></div>
                          <span>Tables</span>
                        </div>
                      </div>
                    </div>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </li>

      <!-- Theme Mode Toggle -->
      <li>
        <div class="mode">
          <svg>
            <use href="assets/svg/icon-sprite.svg#moon"></use>
          </svg>
        </div>
      </li>

      <!-- Notifications -->
      <li class="onhover-dropdown d-flex align-items-center position-relative" style="height: 48px;">
        <div class="notification-box position-relative d-flex align-items-center" style="height: 48px;">
          <svg style="display: block;">
            <use href="assets/svg/icon-sprite.svg#notification"></use>
          </svg>
          <span class="badge rounded-pill badge-success position-absolute d-flex align-items-center justify-content-center" style="top: 6px; right: 2px; font-size: 11px; min-width: 18px; height: 18px; padding: 0;">4</span>
        </div>
        <div class="onhover-show-div notification-dropdown">
          <h6 class="f-18 mb-0 dropdown-title">Notifications</h6>
          <ul>
            <li class="b-l-primary border-4 toast">
              <div class="d-flex justify-content-between">
                <div class="toast-body">
                  <p>New giveaway launched</p>
                </div>
              </div>
            </li>
            <li class="b-l-success border-4 toast">
              <div class="d-flex justify-content-between">
                <div class="toast-body">
                  <p>Entry confirmed</p>
                </div>
              </div>
            </li>
          </ul>
        </div>
      </li>

      <!-- User Profile -->
      <li class="profile-nav onhover-dropdown pe-0 py-0 d-flex align-items-center" style="height: 48px;">
        <div class="media profile-media d-flex align-items-center" style="height: 48px;">
          <i class="fa fa-user-circle fa-2x b-r-10" style="width:40px;height:40px;line-height:40px;text-align:center;color:#adb5bd;background:transparent;box-shadow:none;"></i>
          <div class="media-body d-flex flex-column justify-content-center ps-2" style="height: 40px;">
            <span style="line-height:1;">{{ user?.firstName || user?.email || 'Member' }}</span>
            <p class="mb-0 font-roboto" style="line-height:1;">
              <ng-container *ngIf="hasActiveMembership; else noPlanHeader">
                <span class="badge bg-primary text-white me-1" style="font-size: 10px;">{{ membership?.tierName }}</span>
                <span class="badge bg-success text-white" style="font-size: 10px;">Active</span>
              </ng-container>
              <ng-template #noPlanHeader>
                <span class="badge bg-secondary text-white" style="font-size: 10px;">No Plan</span>
              </ng-template>
              <i class="middle fa fa-chevron-down" style="font-size:18px;vertical-align:middle;line-height:1;display:inline-block;margin-left:5px;"></i>
            </p>
          </div>
        </div>
        <ul class="profile-dropdown onhover-show-div">
          <li><a routerLink="/profile"><i data-feather="user"></i><span>Account</span></a></li>
          <li><a routerLink="/profile/entries"><i data-feather="mail"></i><span>Entries</span></a></li>
          <li><a routerLink="/profile/settings"><i data-feather="settings"></i><span>Settings</span></a></li>
          <li><a href="javascript:void(0)" (click)="logout()"><i data-feather="log-out"></i><span>Log out</span></a></li>
        </ul>
      </li>
    </ul>
  </div>
</div>