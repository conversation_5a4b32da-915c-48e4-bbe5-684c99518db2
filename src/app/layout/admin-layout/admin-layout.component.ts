import { Component, AfterViewInit, OnInit, Renderer2, <PERSON><PERSON><PERSON><PERSON>, HostListener } from '@angular/core';
import { Router, RouterOutlet, RouterLink, RouterLinkActive, NavigationEnd } from '@angular/router';
import { CommonModule } from '@angular/common';
import { filter, Subscription } from 'rxjs';

import { AuthService } from '../../core/services/auth.service';
import { NotificationService } from '../../core/services/notification.service';

interface MenuItem {
  title: string;
  icon: string;
  path?: string;
  children?: MenuItem[];
  expanded?: boolean;
}

interface Notification {
  id: number;
  message: string;
  time: string;
  icon: string;
  read: boolean;
}

@Component({
  selector: 'app-admin-layout',
  standalone: true,
  imports: [
    CommonModule,
    RouterOutlet,
    RouterLink,
    RouterLinkActive
  ],
  templateUrl: './admin-layout.component.html',
  styleUrls: ['./admin-layout.component.scss']
})
export class AdminLayoutComponent implements OnInit, AfterView<PERSON>nit, On<PERSON><PERSON>roy {
  // Layout state
  isSidebarOpen = true;
  currentYear = new Date().getFullYear();
  pageTitle = 'Dashboard';
  isSmallScreen = false;

  // User info
  adminName = 'Admin User';

  // Notifications
  notificationCount = 3;
  notifications: Notification[] = [
    {
      id: 1,
      message: 'New user registered',
      time: '5 minutes ago',
      icon: 'fa-user-plus',
      read: false
    },
    {
      id: 2,
      message: 'New giveaway created',
      time: '2 hours ago',
      icon: 'fa-gift',
      read: false
    },
    {
      id: 3,
      message: 'Payment processing completed',
      time: 'Yesterday',
      icon: 'fa-dollar-sign',
      read: true
    }
  ];

  // Submenu state
  activeSubmenus: { [key: string]: boolean } = {
    'users': false,
    'membership': false,
    'giveaways': false,
    'content': false
  };

  private routerSubscription: Subscription | undefined;

  constructor(
    private renderer: Renderer2,
    private router: Router,
    private authService: AuthService,
    private notificationService: NotificationService
  ) {}

  ngOnInit() {
    // Subscribe to the auth state to get admin info
    this.authService.currentUser$.subscribe(user => {
      if (user) {
        // Verify user is an admin (support both 'admin' and 'ADMIN' values)
        if (user.role !== 'admin' && user.role !== 'ADMIN') {
          this.notificationService.error('You do not have admin privileges');
          this.authService.logout();
          this.router.navigate(['/auth/login']);
          return;
        }

        // Use available user info for the admin name
        this.adminName = `${user.firstName} ${user.lastName}`.trim() || user.email?.split('@')[0] || 'Admin';
      }
    });

    // Update page title based on route
    this.routerSubscription = this.router.events.pipe(
      filter(event => event instanceof NavigationEnd)
    ).subscribe(() => {
      const urlParts = this.router.url.split('/');
      if (urlParts.length > 2) {
        // Convert URL segment to title case (e.g., "user-management" -> "User Management")
        this.pageTitle = urlParts[2]
          .split('-')
          .map(word => word.charAt(0).toUpperCase() + word.slice(1))
          .join(' ');
      } else {
        this.pageTitle = 'Dashboard';
      }

      // Check URL and expand relevant submenus
      this.checkAndExpandSubmenu();
    });
  }

  toggleSidebar() {
    this.isSidebarOpen = !this.isSidebarOpen;
  }

  toggleSubmenu(key: string) {
    this.activeSubmenus[key] = !this.activeSubmenus[key];
  }

  isSubmenuExpanded(key: string): boolean {
    return this.activeSubmenus[key];
  }

  isSubmenuActive(path: string): boolean {
    return this.router.url.startsWith(path);
  }

  checkAndExpandSubmenu() {
    // Expand submenu based on current URL
    const url = this.router.url;

    if (url.includes('/admin/users')) {
      this.activeSubmenus['users'] = true;
    } else if (url.includes('/admin/membership')) {
      this.activeSubmenus['membership'] = true;
    } else if (url.includes('/admin/giveaways')) {
      this.activeSubmenus['giveaways'] = true;
    } else if (url.includes('/admin/content')) {
      this.activeSubmenus['content'] = true;
    }
  }

  logout() {
    // Call auth service to handle logout process
    this.authService.logout();
    this.router.navigate(['/auth/admin/login']);
  }

  ngAfterViewInit() {
    // Initialize any UI-related code that requires DOM elements to be ready
    this.setupResponsiveLayout();
    // Bootstrap components are initialized automatically via the bootstrap.bundle.min.js
  }

  ngOnDestroy() {
    // Clean up subscriptions to prevent memory leaks
    if (this.routerSubscription) {
      this.routerSubscription.unsubscribe();
    }
  }

  @HostListener('window:resize', ['$event'])
  onResize(event: any) {
    this.checkScreenSize();
  }

  private setupResponsiveLayout() {
    // Handle responsive layout changes
    this.checkScreenSize();
  }

  private checkScreenSize() {
    this.isSmallScreen = window.innerWidth < 992;

    // Auto-collapse sidebar on small screens
    if (this.isSmallScreen) {
      this.isSidebarOpen = false;
    }
  }

  initSidebarToggle() {
    // Get all sidebar title elements with submenu
    const sidebarTitles = document.querySelectorAll('.sidebar-title');

    // Add click event listener to each title with submenu
    sidebarTitles.forEach(title => {
      const accordingMenu = title.querySelector('.according-menu');
      if (accordingMenu) {
        title.addEventListener('click', (e) => {
          e.preventDefault();

          // Find the parent list item
          const listItem = (title as HTMLElement).closest('.sidebar-list');

          // Toggle active class on the list item
          if (listItem) {
            listItem.classList.toggle('active');

            // Rotate submenu indicator
            accordingMenu.classList.toggle('active');
            const icon = accordingMenu.querySelector('i');
            if (icon) {
              this.renderer.setStyle(icon, 'transform',
                listItem.classList.contains('active') ? 'rotate(90deg)' : 'none');
            }
          }
        });
      }
    });
  }

  initDropdownMenus() {
    // Get all hover dropdown elements
    const dropdowns = document.querySelectorAll('.onhover-dropdown');

    // Add hover event listeners
    dropdowns.forEach(dropdown => {
      dropdown.addEventListener('mouseenter', () => {
        const dropdownMenu = dropdown.querySelector('.onhover-show-div');
        if (dropdownMenu) {
          dropdownMenu.classList.add('active');
        }
      });

      dropdown.addEventListener('mouseleave', () => {
        const dropdownMenu = dropdown.querySelector('.onhover-show-div');
        if (dropdownMenu) {
          dropdownMenu.classList.remove('active');
        }
      });
    });
  }

  loadFeatherIcons() {
    // Load Feather icons if available
    if (typeof (window as any).feather !== 'undefined') {
      (window as any).feather.replace();
    }
  }
}