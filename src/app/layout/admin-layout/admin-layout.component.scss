:host {
  display: block;
  height: 100vh;
  width: 100vw;
  overflow: hidden;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1050;
}

// Admin Dashboard Layout
.admin-dashboard {
  height: 100vh;
  width: 100vw;
  overflow: hidden;
  background-color: #f0f2f8; // Slightly blueish background
  display: flex;
  flex-direction: row;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;

  // Admin color scheme variables
  --admin-primary: #3a36db; // Deep indigo
  --admin-secondary: #6c63ff; // Bright indigo
  --admin-dark: #2a2356; // Dark purple
  --admin-light: #f0f2f8; // Light blue-gray
  --admin-accent: #ff6b6b; // Coral red for accents
  --admin-success: #00c896; // Teal green
  --admin-warning: #ffb400; // Amber
  --admin-danger: #ff5252; // Red
  --admin-info: #4fc3f7; // Light blue
  --admin-text: #2d3748; // Dark slate
  --admin-text-light: #718096; // Medium slate
  --admin-border: #e2e8f0; // Light gray
  --admin-hover: rgba(108, 99, 255, 0.1); // Light indigo for hover states

  // Animation variables
  --transition-speed-fast: 0.2s;
  --transition-speed-normal: 0.3s;
  --transition-speed-slow: 0.5s;
  --transition-timing: cubic-bezier(0.4, 0, 0.2, 1);
}

// Sidebar
.sidebar {
  width: 280px;
  height: 100vh;
  overflow-y: auto;
  overflow-x: hidden;
  transition: width var(--transition-speed-normal) var(--transition-timing);
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.15);
  z-index: 1030;
  background: linear-gradient(135deg, var(--admin-dark) 0%, var(--admin-primary) 100%);
  border-right: 1px solid rgba(255, 255, 255, 0.1);

  &.collapsed {
    width: 80px;

    .sidebar-header {
      padding: 1rem 0.5rem;
      justify-content: center;
    }

    .logo-text,
    .admin-profile,
    .menu-label,
    .nav-item span {
      display: none;
    }

    .submenu {
      display: none !important;
    }

    .nav-link {
      padding: 0.75rem;
      justify-content: center !important;

      i.fa-fw {
        margin-right: 0 !important;
        font-size: 1.2rem;
      }
    }
  }

  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.05);
  }

  &::-webkit-scrollbar-thumb {
    background-color: rgba(255, 255, 255, 0.15);
    border-radius: 10px;

    &:hover {
      background-color: rgba(255, 255, 255, 0.25);
    }
  }

  .sidebar-header {
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    padding: 1.25rem 1.5rem;

    .logo-container {
      .logo-icon {
        background: rgba(255, 255, 255, 0.1);
        width: 40px;
        height: 40px;
        border-radius: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15);

        i {
          font-size: 1.25rem;
          color: var(--admin-warning);
          filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
        }
      }

      .logo-text {
        span {
          font-weight: 700;
          letter-spacing: 0.5px;
        }
      }
    }

    button {
      background: rgba(255, 255, 255, 0.1);
      width: 30px;
      height: 30px;
      border-radius: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all var(--transition-speed-fast) var(--transition-timing);

      &:hover {
        background: rgba(255, 255, 255, 0.2);
        transform: scale(1.05);
      }

      i {
        font-size: 0.8rem;
      }
    }
  }

  .sidebar-nav {
    display: flex;
    flex-direction: column;
    height: calc(100vh - 200px);
    overflow-y: auto;
    padding: 1rem 0.75rem;
  }

  .nav-section {
    margin-bottom: 1.5rem;

    .menu-label {
      letter-spacing: 0.05rem;
      padding: 0.5rem 0.75rem;
      font-size: 0.7rem;
      font-weight: 600;
      color: rgba(255, 255, 255, 0.5);
    }
  }

  .nav-link {
    color: rgba(255, 255, 255, 0.85);
    border-radius: 10px;
    transition: all var(--transition-speed-fast) var(--transition-timing);
    margin-bottom: 0.25rem;
    padding: 0.75rem 1rem;
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      left: 0;
      top: 0;
      height: 100%;
      width: 0;
      background: var(--admin-secondary);
      opacity: 0.2;
      transition: width var(--transition-speed-fast) var(--transition-timing);
    }

    &:hover {
      color: #ffffff;
      background-color: rgba(255, 255, 255, 0.1);
      transform: translateX(3px);

      &::before {
        width: 5px;
      }

      i {
        transform: scale(1.1);
      }
    }

    &.active {
      color: #ffffff;
      background-color: rgba(255, 255, 255, 0.15);
      box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);

      &::before {
        width: 5px;
        background: var(--admin-warning);
        opacity: 1;
      }

      i {
        color: var(--admin-warning);
        filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
      }
    }

    i.fa-fw {
      width: 20px;
      text-align: center;
      transition: transform var(--transition-speed-fast) var(--transition-timing);
      margin-right: 0.75rem;
    }

    i.fa-angle-down, i.fa-angle-right {
      transition: transform var(--transition-speed-fast) var(--transition-timing);
    }
  }

  .submenu {
    padding-left: 0;
    margin-top: 0.25rem;
    margin-bottom: 0.5rem;

    .nav-link {
      padding: 0.5rem 0.75rem 0.5rem 2.75rem;
      font-size: 0.85rem;
      margin-bottom: 0.125rem;
      border-radius: 8px;

      &::before {
        display: none;
      }

      i.fa-circle {
        font-size: 0.4rem;
        opacity: 0.7;
      }

      &:hover {
        transform: translateX(2px);
      }

      &.active {
        background-color: rgba(255, 255, 255, 0.1);
        box-shadow: none;

        i.fa-circle {
          color: var(--admin-warning);
          opacity: 1;
        }
      }
    }
  }

  // Admin Profile Section
  .admin-profile {
    padding: 1.25rem 1.5rem;
    background: rgba(0, 0, 0, 0.15);
    margin-bottom: 1rem;
    border-radius: 0;
    border-top: 1px solid rgba(255, 255, 255, 0.05);
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);

    .avatar-container {
      position: relative;

      img {
        border: 2px solid rgba(255, 255, 255, 0.2);
        box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
        transition: all var(--transition-speed-fast) var(--transition-timing);

        &:hover {
          transform: scale(1.05);
          border-color: var(--admin-warning);
        }
      }

      &::after {
        content: '';
        position: absolute;
        bottom: 2px;
        right: 2px;
        width: 12px;
        height: 12px;
        background-color: var(--admin-success);
        border-radius: 50%;
        border: 2px solid var(--admin-dark);
      }
    }

    .admin-info {
      .admin-name {
        font-weight: 600;
        letter-spacing: 0.5px;
      }

      .admin-role {
        opacity: 0.7;
        font-size: 0.8rem;
        background: rgba(255, 255, 255, 0.1);
        padding: 0.2rem 0.5rem;
        border-radius: 4px;
        display: inline-block;
        margin-top: 0.25rem;
      }
    }
  }
}

// Main Content
.main-content {
  width: calc(100% - 280px);
  transition: width var(--transition-speed-normal) var(--transition-timing);
  max-width: 100%;
  background-color: var(--admin-light);

  .sidebar.collapsed + & {
    width: calc(100% - 80px);
  }

  // Header
  .header {
    height: 70px;
    background-color: #ffffff;
    border-bottom: 1px solid var(--admin-border);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
    position: relative;
    z-index: 10;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 3px;
      background: linear-gradient(90deg, var(--admin-primary) 0%, var(--admin-secondary) 100%);
    }

    .navbar {
      height: 100%;
      padding: 0 1.5rem;

      .btn-link {
        color: var(--admin-text);
        transition: all var(--transition-speed-fast) var(--transition-timing);

        &:hover {
          color: var(--admin-primary);
          transform: scale(1.05);
        }
      }

      h5 {
        color: var(--admin-text);
        font-weight: 600;
        letter-spacing: 0.5px;
        position: relative;
        padding-left: 15px;

        &::before {
          content: '';
          position: absolute;
          left: 0;
          top: 50%;
          transform: translateY(-50%);
          width: 5px;
          height: 20px;
          background: var(--admin-primary);
          border-radius: 3px;
        }
      }
    }

    .dropdown-menu {
      border-radius: 12px;
      border: none;
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
      animation: fadeInDown var(--transition-speed-fast) var(--transition-timing);
      overflow: hidden;

      .dropdown-header {
        font-size: 0.875rem;
        font-weight: 600;
        background-color: #f8f9fa;
        border-bottom: 1px solid var(--admin-border);
        padding: 1rem 1.25rem;
        color: var(--admin-text);

        a {
          color: var(--admin-primary);
          font-weight: 500;
          transition: all var(--transition-speed-fast) var(--transition-timing);

          &:hover {
            color: var(--admin-secondary);
            text-decoration: underline !important;
          }
        }
      }
    }

    .notification-list {
      max-height: 350px;
      overflow-y: auto;

      &::-webkit-scrollbar {
        width: 5px;
      }

      &::-webkit-scrollbar-track {
        background: #f1f1f1;
      }

      &::-webkit-scrollbar-thumb {
        background-color: #ddd;
        border-radius: 10px;

        &:hover {
          background-color: #ccc;
        }
      }

      .dropdown-item {
        padding: 1rem 1.25rem;
        transition: all var(--transition-speed-fast) var(--transition-timing);
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);

        &:last-child {
          border-bottom: none;
        }

        &:hover {
          background-color: var(--admin-hover);
          transform: translateY(-2px);
        }

        .icon-circle {
          border-radius: 12px;
          width: 42px;
          height: 42px;
          display: flex;
          align-items: center;
          justify-content: center;
          box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
          background: var(--admin-primary);

          i {
            font-size: 1rem;
          }
        }

        p {
          line-height: 1.4;

          &.text-muted {
            color: var(--admin-text-light) !important;
          }
        }
      }
    }

    // User dropdown
    .nav-item.dropdown {
      .nav-link {
        border-radius: 50px;
        padding: 0.25rem;
        transition: all var(--transition-speed-fast) var(--transition-timing);

        &:hover {
          background-color: var(--admin-hover);

          img {
            transform: scale(1.05);
          }
        }

        img {
          border: 2px solid var(--admin-border);
          transition: all var(--transition-speed-fast) var(--transition-timing);
        }

        span {
          color: var(--admin-text);
          font-weight: 500;
        }
      }

      .dropdown-menu {
        min-width: 220px;

        .dropdown-item {
          padding: 0.75rem 1.25rem;
          transition: all var(--transition-speed-fast) var(--transition-timing);
          color: var(--admin-text);

          &:hover {
            background-color: var(--admin-hover);
            color: var(--admin-primary);

            i {
              color: var(--admin-primary) !important;
              transform: translateX(3px);
            }
          }

          i {
            transition: all var(--transition-speed-fast) var(--transition-timing);
            width: 20px;
          }
        }
      }
    }

    // Search button
    .btn-light {
      transition: all var(--transition-speed-fast) var(--transition-timing);
      border-color: var(--admin-border);
      padding: 0.5rem 1rem;

      &:hover {
        background-color: var(--admin-hover);
        border-color: var(--admin-primary);
        color: var(--admin-primary);
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
      }

      i {
        color: var(--admin-text-light);
      }
    }
  }

  // Content Wrapper
  .content-wrapper {
    background-color: var(--admin-light);
    width: 100%;
    max-width: 100%;
    padding: 1.5rem;
    position: relative;

    // Breadcrumb
    .breadcrumb {
      background-color: transparent;
      padding: 0;
      margin-bottom: 1.5rem;
      display: inline-flex;
      align-items: center;
      background: #ffffff;
      padding: 0.75rem 1.25rem;
      border-radius: 50px;
      box-shadow: 0 4px 10px rgba(0, 0, 0, 0.03);
      border: 1px solid var(--admin-border);

      .breadcrumb-item {
        font-size: 0.875rem;
        font-weight: 500;

        a {
          color: var(--admin-text-light);
          text-decoration: none;
          transition: all var(--transition-speed-fast) var(--transition-timing);

          &:hover {
            color: var(--admin-primary);
          }
        }

        &.active {
          color: var(--admin-text);
          font-weight: 600;
        }

        + .breadcrumb-item {
          padding-left: 1rem;

          &::before {
            content: "›";
            font-size: 1.2rem;
            line-height: 1;
            vertical-align: middle;
            color: var(--admin-text-light);
            padding-right: 1rem;
            font-weight: 300;
          }
        }
      }
    }

    // Cards for dashboard widgets
    .card {
      border: none;
      border-radius: 16px;
      box-shadow: 0 5px 20px rgba(0, 0, 0, 0.05);
      transition: all var(--transition-speed-normal) var(--transition-timing);
      margin-bottom: 1.5rem;
      overflow: hidden;
      background-color: #ffffff;

      &:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
      }

      .card-header {
        background-color: transparent;
        border-bottom: 1px solid var(--admin-border);
        font-weight: 600;
        padding: 1.25rem 1.5rem;
        display: flex;
        align-items: center;
        justify-content: space-between;

        .card-title {
          margin-bottom: 0;
          font-size: 1.1rem;
          color: var(--admin-text);
          position: relative;
          padding-left: 15px;

          &::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 4px;
            height: 18px;
            background: var(--admin-primary);
            border-radius: 2px;
          }
        }

        .card-actions {
          display: flex;
          gap: 0.5rem;

          .btn {
            width: 32px;
            height: 32px;
            padding: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 8px;
            transition: all var(--transition-speed-fast) var(--transition-timing);
            background-color: var(--admin-light);
            color: var(--admin-text-light);
            border: none;

            &:hover {
              background-color: var(--admin-hover);
              color: var(--admin-primary);
              transform: translateY(-2px);
            }
          }
        }
      }

      .card-body {
        padding: 1.5rem;
      }

      .card-footer {
        background-color: transparent;
        border-top: 1px solid var(--admin-border);
        padding: 1.25rem 1.5rem;
      }
    }

    // Admin-specific tables
    .table {
      border-collapse: separate;
      border-spacing: 0;
      width: 100%;

      thead {
        th {
          background-color: rgba(var(--admin-primary-rgb), 0.05);
          color: var(--admin-text);
          font-weight: 600;
          padding: 1rem 1.25rem;
          border-bottom: 2px solid var(--admin-border);
          white-space: nowrap;

          &:first-child {
            border-top-left-radius: 10px;
          }

          &:last-child {
            border-top-right-radius: 10px;
          }
        }
      }

      tbody {
        tr {
          transition: all var(--transition-speed-fast) var(--transition-timing);

          &:hover {
            background-color: var(--admin-hover);
          }

          td {
            padding: 1rem 1.25rem;
            vertical-align: middle;
            border-bottom: 1px solid var(--admin-border);
            color: var(--admin-text);

            .badge {
              padding: 0.4rem 0.75rem;
              font-weight: 500;
              border-radius: 50px;
              font-size: 0.75rem;

              &.bg-success {
                background-color: rgba(var(--admin-success-rgb), 0.1) !important;
                color: var(--admin-success);
              }

              &.bg-warning {
                background-color: rgba(var(--admin-warning-rgb), 0.1) !important;
                color: var(--admin-warning);
              }

              &.bg-danger {
                background-color: rgba(var(--admin-danger-rgb), 0.1) !important;
                color: var(--admin-danger);
              }

              &.bg-info {
                background-color: rgba(var(--admin-info-rgb), 0.1) !important;
                color: var(--admin-info);
              }
            }

            .btn-icon {
              width: 32px;
              height: 32px;
              padding: 0;
              display: flex;
              align-items: center;
              justify-content: center;
              border-radius: 8px;
              transition: all var(--transition-speed-fast) var(--transition-timing);
              background-color: var(--admin-light);
              color: var(--admin-text-light);
              border: none;

              &:hover {
                background-color: var(--admin-hover);
                color: var(--admin-primary);
                transform: translateY(-2px);
              }

              &.btn-edit:hover {
                color: var(--admin-info);
              }

              &.btn-delete:hover {
                color: var(--admin-danger);
              }
            }
          }

          &:last-child {
            td {
              &:first-child {
                border-bottom-left-radius: 10px;
              }

              &:last-child {
                border-bottom-right-radius: 10px;
              }
            }
          }
        }
      }
    }
  }

  // Footer
  .footer {
    height: 60px;
    background-color: #ffffff;
    border-top: 1px solid var(--admin-border);

    .container-fluid {
      height: 100%;
    }

    span {
      color: var(--admin-text-light);
      font-size: 0.875rem;
    }

    .text-danger {
      color: var(--admin-accent) !important;
      animation: heartbeat 1.5s infinite;
    }
  }

  // Admin Quick Access Toolbar
  .admin-quick-toolbar {
    position: fixed;
    bottom: 2rem;
    right: 2rem;
    z-index: 1040;
    display: flex;
    flex-direction: column;
    gap: 0.75rem;

    .quick-action {
      width: 50px;
      height: 50px;
      border-radius: 50%;
      background: linear-gradient(135deg, var(--admin-primary) 0%, var(--admin-secondary) 100%);
      color: #ffffff;
      display: flex;
      align-items: center;
      justify-content: center;
      box-shadow: 0 5px 15px rgba(var(--admin-primary-rgb), 0.3);
      transition: all var(--transition-speed-fast) var(--transition-timing);
      position: relative;
      overflow: hidden;

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(255, 255, 255, 0.1);
        transform: translateY(100%);
        transition: transform var(--transition-speed-fast) var(--transition-timing);
      }

      &:hover {
        transform: translateY(-5px) scale(1.05);
        box-shadow: 0 10px 20px rgba(var(--admin-primary-rgb), 0.4);

        &::before {
          transform: translateY(0);
        }

        i {
          transform: scale(1.1);
        }
      }

      i {
        font-size: 1.25rem;
        transition: transform var(--transition-speed-fast) var(--transition-timing);
      }

      &.main-action {
        width: 60px;
        height: 60px;
        background: linear-gradient(135deg, var(--admin-accent) 0%, var(--admin-danger) 100%);

        i {
          font-size: 1.5rem;
        }
      }

      .tooltip {
        position: absolute;
        right: 100%;
        top: 50%;
        transform: translateY(-50%);
        background-color: var(--admin-dark);
        color: #ffffff;
        padding: 0.5rem 0.75rem;
        border-radius: 5px;
        font-size: 0.8rem;
        white-space: nowrap;
        margin-right: 10px;
        opacity: 0;
        visibility: hidden;
        transition: all var(--transition-speed-fast) var(--transition-timing);

        &::after {
          content: '';
          position: absolute;
          right: -5px;
          top: 50%;
          transform: translateY(-50%);
          border-width: 5px 0 5px 5px;
          border-style: solid;
          border-color: transparent transparent transparent var(--admin-dark);
        }
      }

      &:hover .tooltip {
        opacity: 1;
        visibility: visible;
        margin-right: 15px;
      }
    }
  }

  // Security indicator
  .security-indicator {
    position: fixed;
    top: 80px;
    right: 20px;
    background-color: rgba(var(--admin-dark-rgb), 0.9);
    color: #ffffff;
    padding: 0.5rem 0.75rem;
    border-radius: 50px;
    font-size: 0.75rem;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    z-index: 1030;
    animation: fadeInDown var(--transition-speed-normal) var(--transition-timing);

    i {
      color: var(--admin-warning);
    }
  }
}

// Dashboard Specific Cards and Widgets
.stat-card {
  .stat-icon {
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    font-size: 1.5rem;
  }

  .stat-content {
    .stat-title {
      font-size: 0.875rem;
      color: #6c757d;
      margin-bottom: 0.5rem;
    }

    .stat-value {
      font-size: 1.5rem;
      font-weight: 600;
      margin-bottom: 0.25rem;
    }

    .stat-desc {
      font-size: 0.75rem;
      color: #6c757d;
    }

    .stat-change {
      &.positive {
        color: #28a745;
      }

      &.negative {
        color: #dc3545;
      }
    }
  }
}

// Responsive styles
@media (max-width: 991.98px) {
  .admin-dashboard {
    overflow-x: hidden;
  }

  .sidebar {
    position: fixed;
    left: -280px;
    z-index: 1050;

    &:not(.collapsed) {
      left: 0;
      box-shadow: 0 0 30px rgba(0, 0, 0, 0.3);
    }
  }

  .main-content {
    width: 100% !important;
    margin-left: 0;

    .header {
      .navbar {
        padding: 0 1rem;
      }
    }

    .content-wrapper {
      padding: 1rem;
    }

    .breadcrumb {
      width: 100%;
      overflow-x: auto;
      white-space: nowrap;
      -webkit-overflow-scrolling: touch;

      &::-webkit-scrollbar {
        display: none;
      }
    }

    .admin-quick-toolbar {
      bottom: 1rem;
      right: 1rem;

      .quick-action {
        width: 45px;
        height: 45px;

        &.main-action {
          width: 55px;
          height: 55px;
        }

        .tooltip {
          display: none;
        }
      }
    }

    .security-indicator {
      top: auto;
      bottom: 1rem;
      left: 1rem;
      right: auto;
    }
  }
}

// Small screens
@media (max-width: 575.98px) {
  .main-content {
    .header {
      height: auto;

      .navbar {
        flex-wrap: wrap;
        padding: 0.75rem;

        h5 {
          font-size: 1rem;
          width: 100%;
          margin-bottom: 0.5rem;
          margin-top: 0.5rem;
        }

        .navbar-nav {
          margin-left: 0 !important;
          width: 100%;
          justify-content: flex-end;
        }
      }
    }

    .content-wrapper {
      padding: 0.75rem;

      .card {
        .card-header {
          padding: 1rem;
          flex-direction: column;
          align-items: flex-start;

          .card-actions {
            margin-top: 0.5rem;
            align-self: flex-end;
          }
        }

        .card-body {
          padding: 1rem;
        }

        .card-footer {
          padding: 1rem;
        }
      }

      .table {
        thead th, tbody td {
          padding: 0.75rem;
        }
      }
    }

    .footer {
      height: auto;
      padding: 1rem 0;

      .row {
        flex-direction: column;
        text-align: center;

        .col-md-6 {
          margin-bottom: 0.5rem;

          &:last-child {
            margin-bottom: 0;
            text-align: center !important;
          }
        }
      }
    }
  }
}

// Custom Animations
@keyframes fadeIn {
  0% { opacity: 0; }
  100% { opacity: 1; }
}

@keyframes fadeInDown {
  0% {
    opacity: 0;
    transform: translateY(-10px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInUp {
  0% {
    opacity: 0;
    transform: translateY(10px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  0% {
    opacity: 0;
    transform: translateX(-20px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  0% {
    opacity: 0;
    transform: translateX(20px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes heartbeat {
  0% { transform: scale(1); }
  14% { transform: scale(1.3); }
  28% { transform: scale(1); }
  42% { transform: scale(1.3); }
  70% { transform: scale(1); }
}

@keyframes pulseNotification {
  0% { transform: scale(1); }
  50% { transform: scale(1.1); }
  100% { transform: scale(1); }
}

.badge-notification {
  animation: pulseNotification 2s infinite;
}

// Animation classes
.fade-in {
  animation: fadeIn var(--transition-speed-normal) var(--transition-timing);
}

.fade-in-down {
  animation: fadeInDown var(--transition-speed-normal) var(--transition-timing);
}

.fade-in-up {
  animation: fadeInUp var(--transition-speed-normal) var(--transition-timing);
}

.slide-in-left {
  animation: slideInLeft var(--transition-speed-normal) var(--transition-timing);
}

.slide-in-right {
  animation: slideInRight var(--transition-speed-normal) var(--transition-timing);
}

// Add RGB versions of the colors for rgba usage
.admin-dashboard {
  --admin-primary-rgb: 58, 54, 219;
  --admin-secondary-rgb: 108, 99, 255;
  --admin-dark-rgb: 42, 35, 86;
  --admin-accent-rgb: 255, 107, 107;
  --admin-success-rgb: 0, 200, 150;
  --admin-warning-rgb: 255, 180, 0;
  --admin-danger-rgb: 255, 82, 82;
  --admin-info-rgb: 79, 195, 247;
}