// Admin Layout Component Styles
:host {
  display: block;
  height: 100vh;
  width: 100%;
  overflow: hidden;
}

// Admin Dashboard Layout
.admin-dashboard {
  height: 100vh;
  width: 100%;
  overflow: hidden;
  background-color: #f8f9fa;
}

// Sidebar
.sidebar {
  width: 260px;
  height: 100vh;
  overflow-y: auto;
  overflow-x: hidden;
  transition: all 0.3s ease;
  box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);
  z-index: 1030;
  
  &.collapsed {
    width: 70px;
    
    .sidebar-header {
      padding: 0.75rem 0.5rem;
      justify-content: center;
    }
    
    .logo-text, 
    .admin-profile,
    .menu-label,
    .nav-item span {
      display: none;
    }
    
    .submenu {
      display: none !important;
    }
    
    .nav-link {
      padding: 0.5rem;
      justify-content: center !important;
      
      i.fa-fw {
        margin-right: 0 !important;
      }
    }
  }
  
  &::-webkit-scrollbar {
    width: 5px;
  }
  
  &::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
  }
  
  &::-webkit-scrollbar-thumb {
    background-color: rgba(255, 255, 255, 0.2);
    border-radius: 10px;
  }

  .sidebar-header {
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  }
  
  .sidebar-nav {
    display: flex;
    flex-direction: column;
    height: calc(100vh - 180px);
    overflow-y: auto;
  }
  
  .nav-link {
    color: rgba(255, 255, 255, 0.75);
    border-radius: 0.25rem;
    transition: all 0.2s ease;
    
    &:hover {
      color: #ffffff;
      background-color: rgba(255, 255, 255, 0.1);
    }
    
    &.active {
      color: #ffffff;
      background-color: rgba(255, 255, 255, 0.2);
      
      i {
        color: #ffc107; // Bootstrap warning color for emphasis
      }
    }
    
    i.fa-fw {
      width: 20px;
      text-align: center;
    }
  }
  
  .submenu {
    padding-left: 0;
    
    .nav-link {
      padding-left: 2.5rem;
      font-size: 0.9rem;
    }
  }
  
  .menu-label {
    letter-spacing: 0.05rem;
  }
}

// Main Content
.main-content {
  width: calc(100% - 260px);
  transition: all 0.3s ease;
  
  .sidebar.collapsed + & {
    width: calc(100% - 70px);
  }
  
  // Header
  .header {
    height: 60px;
    
    .navbar {
      height: 100%;
    }
    
    .dropdown-menu {
      border-radius: 0.5rem;
    }
  }
  
  // Content Wrapper
  .content-wrapper {
    background-color: #f8f9fa;
    
    // Breadcrumb
    .breadcrumb {
      background-color: transparent;
      padding: 0;
      
      .breadcrumb-item {
        font-size: 0.875rem;
        
        a {
          color: #6c757d;
          text-decoration: none;
          
          &:hover {
            color: #007bff;
          }
        }
        
        &.active {
          color: #495057;
        }
        
        + .breadcrumb-item::before {
          content: "›";
          font-size: 1rem;
          line-height: 1;
          vertical-align: middle;
          color: #6c757d;
        }
      }
    }
  }
  
  // Footer
  .footer {
    height: 60px;
  }
}

// Cards and Dashboard Elements
.card {
  border: none;
  border-radius: 0.5rem;
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
  transition: all 0.2s ease-in-out;
  
  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
  }
  
  .card-header {
    background-color: #fff;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    font-weight: 600;
  }
}

// Responsive styles
@media (max-width: 767.98px) {
  .admin-dashboard {
    overflow-x: hidden;
  }
  
  .sidebar {
    position: fixed;
    left: -280px;
    z-index: 1050;
    
    &:not(.collapsed) {
      left: 0;
    }
  }
  
  .main-content {
    width: 100%;
    margin-left: 0;
  }
}

// CSS Animation for Notifications
@keyframes pulseNotification {
  0% { transform: scale(1); }
  50% { transform: scale(1.1); }
  100% { transform: scale(1); }
}

.badge {
  animation: pulseNotification 2s infinite;
}

// Custom scrollbar for the entire application
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #aaa;
  border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
  background: #888;
}
        
                    display: block;
                    color: #fff;
                  }
                }
                
                .according-menu {
                  position: absolute;
                  right: 20px;
                  top: 15px;
                  
                  i {
                    vertical-align: middle;
                    font-size: 16px;
                    transition: transform 0.3s ease;
                  }
                }
              }
            }
            
            .sidebar-submenu {
              list-style: none;
              margin: 0;
              padding: 0;
              display: none;
              
              li a {
                padding: 9px 15px 9px 54px;
                color: #77778e;
                font-weight: 400;
                display: block;
                font-size: 13px;
                position: relative;
                transition: all 0.3s ease;
                
                &:hover, &.active {
                  color: #7366ff;
                }
              }
            }
            
            &.active {
              > a .according-menu i {
                transform: rotate(90deg);
              }
              
              > .sidebar-submenu {
                display: block;
              }
            }
          }
        }
      }
    }
  }
  
  // Page Body
  .page-body {
    margin-left: 280px;
    padding: 20px;
    min-height: calc(100vh - 80px);
    background-color: #f6f7fb;
    flex: 1;
    transition: all 0.3s ease;
    
    .page-title {
      padding: 30px 0 20px;
      margin-bottom: 30px;
      border-bottom: 1px solid #f1f1f1;
      
      h3 {
        font-size: 22px;
        font-weight: 600;
        margin-bottom: 0;
        text-transform: capitalize;
      }
      
      .breadcrumb {
        float: right;
        background-color: transparent;
        margin-bottom: 0;
        padding: 0;
        
        .breadcrumb-item {
          font-size: 14px;
          
          a {
            color: #7366ff;
            
            i {
              font-size: 16px;
            }
          }
          
          &.active {
            color: #59667a;
          }
          
          + .breadcrumb-item::before {
            content: "/";
            color: #77778e;
          }
        }
      }
    }
  }
  
  // Footer
  .footer {
    margin-left: 280px;
    background-color: #fff;
    border-top: 1px solid #f1f1f1;
    padding: 15px 25px;
    color: #77778e;
    position: relative;
    transition: all 0.3s ease;
    width: calc(100% - 280px);
    
    p {
      margin-bottom: 0;
      font-size: 14px;
      text-align: center;
    }
  }
}

// Page Header
.page-header {
  background-color: #fff;
  padding: 0;
  box-shadow: 0 0 20px rgba(89, 102, 122, 0.1);
  position: fixed;
  top: 0;
  width: 100%;
  height: 80px;
  display: flex;
  align-items: center;
  z-index: 10;
  transition: all 0.3s ease;
  
  &.close_icon {
    margin-left: 0;
  }
}

// Responsive styles
@media (max-width: 991px) {
  .page-body-wrapper .sidebar-wrapper ~ .page-body,
  .page-body-wrapper .sidebar-wrapper ~ .footer {
    margin-left: 0;
    width: 100%;
  }
} 