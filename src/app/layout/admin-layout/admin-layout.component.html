<!-- Admin Dashboard Layout -->
<div class="admin-dashboard d-flex w-100 vw-100" id="admin-wrapper">
  <!-- Sidebar -->
  <div class="sidebar" [ngClass]="{'collapsed': !isSidebarOpen}">
    <!-- Sidebar Header/Logo -->
    <div class="sidebar-header d-flex justify-content-between align-items-center">
      <div class="logo-container">
        <a routerLink="/admin/dashboard" class="text-decoration-none">
          <div class="d-flex align-items-center">
            <div class="logo-icon me-3">
              <i class="fas fa-trophy"></i>
            </div>
            <div class="logo-text" *ngIf="isSidebarOpen">
              <span class="h5 mb-0 text-white">Admin Portal</span>
            </div>
          </div>
        </a>
      </div>
      <button class="btn btn-link text-white p-0 d-none d-md-block" (click)="toggleSidebar()" title="Toggle Sidebar">
        <i class="fas" [ngClass]="isSidebarOpen ? 'fa-chevron-left' : 'fa-chevron-right'"></i>
      </button>
    </div>

    <!-- Security Badge -->
    <div class="admin-security-badge px-3 py-2 mb-2" *ngIf="isSidebarOpen">
      <div class="d-flex align-items-center">
        <i class="fas fa-shield-alt me-2 text-warning"></i>
        <span class="small">Admin Access</span>
      </div>
    </div>

    <!-- Admin Profile Summary -->
    <div class="admin-profile" *ngIf="isSidebarOpen">
      <div class="d-flex align-items-center">
        <div class="avatar-container me-3">
          <img src="assets/images/avatar-placeholder.png" alt="Admin Avatar" class="rounded-circle" width="45" height="45">
        </div>
        <div class="admin-info">
          <div class="admin-name">{{adminName}}</div>
          <div class="admin-role">Administrator</div>
        </div>
      </div>
    </div>

    <!-- Navigation Menu -->
    <nav class="sidebar-nav">
      <div class="nav-section">
        <div class="menu-label" *ngIf="isSidebarOpen">Dashboard</div>
        <ul class="nav flex-column">
          <li class="nav-item">
            <a class="nav-link d-flex align-items-center px-3 py-2" routerLink="/admin/dashboard" routerLinkActive="active">
              <i class="fas fa-tachometer-alt fa-fw me-2"></i>
              <span *ngIf="isSidebarOpen">Dashboard</span>
            </a>
          </li>

          <li class="nav-item">
            <a class="nav-link d-flex align-items-center justify-content-between px-3 py-2"
               (click)="toggleSubmenu('users')"
               [class.active]="isSubmenuActive('/admin/users')">
              <div>
                <i class="fas fa-users fa-fw me-2"></i>
                <span *ngIf="isSidebarOpen">Users</span>
              </div>
              <i *ngIf="isSidebarOpen" class="fas" [ngClass]="isSubmenuExpanded('users') ? 'fa-angle-down' : 'fa-angle-right'"></i>
            </a>
            <ul class="submenu list-unstyled ms-3" *ngIf="isSubmenuExpanded('users') && isSidebarOpen">
              <li>
                <a class="nav-link d-flex align-items-center px-3 py-2" routerLink="/admin/users" routerLinkActive="active">
                  <i class="fas fa-circle fa-xs me-2"></i>
                  <span>All Users</span>
                </a>
              </li>
              <li>
                <a class="nav-link d-flex align-items-center px-3 py-2" routerLink="/admin/users/create" routerLinkActive="active">
                  <i class="fas fa-circle fa-xs me-2"></i>
                  <span>Add User</span>
                </a>
              </li>
            </ul>
          </li>

          <li class="nav-item">
            <a class="nav-link d-flex align-items-center justify-content-between px-3 py-2"
               (click)="toggleSubmenu('membership')"
               [class.active]="isSubmenuActive('/admin/membership')">
              <div>
                <i class="fas fa-id-card fa-fw me-2"></i>
                <span *ngIf="isSidebarOpen">Membership</span>
              </div>
              <i *ngIf="isSidebarOpen" class="fas" [ngClass]="isSubmenuExpanded('membership') ? 'fa-angle-down' : 'fa-angle-right'"></i>
            </a>
            <ul class="submenu list-unstyled ms-3" *ngIf="isSubmenuExpanded('membership') && isSidebarOpen">
              <li>
                <a class="nav-link d-flex align-items-center px-3 py-2" routerLink="/admin/membership/tiers" routerLinkActive="active">
                  <i class="fas fa-circle fa-xs me-2"></i>
                  <span>Membership Tiers</span>
                </a>
              </li>
              <li>
                <a class="nav-link d-flex align-items-center px-3 py-2" routerLink="/admin/membership/subscribers" routerLinkActive="active">
                  <i class="fas fa-circle fa-xs me-2"></i>
                  <span>Subscribers</span>
                </a>
              </li>
            </ul>
          </li>

          <li class="nav-item">
            <a class="nav-link d-flex align-items-center justify-content-between px-3 py-2"
               (click)="toggleSubmenu('giveaways')"
               [class.active]="isSubmenuActive('/admin/giveaways')">
              <div>
                <i class="fas fa-gift fa-fw me-2"></i>
                <span *ngIf="isSidebarOpen">Giveaways</span>
              </div>
              <i *ngIf="isSidebarOpen" class="fas" [ngClass]="isSubmenuExpanded('giveaways') ? 'fa-angle-down' : 'fa-angle-right'"></i>
            </a>
            <ul class="submenu list-unstyled ms-3" *ngIf="isSubmenuExpanded('giveaways') && isSidebarOpen">
              <li>
                <a class="nav-link d-flex align-items-center px-3 py-2" routerLink="/admin/giveaways" routerLinkActive="active">
                  <i class="fas fa-circle fa-xs me-2"></i>
                  <span>All Giveaways</span>
                </a>
              </li>
              <li>
                <a class="nav-link d-flex align-items-center px-3 py-2" routerLink="/admin/giveaways/create" routerLinkActive="active">
                  <i class="fas fa-circle fa-xs me-2"></i>
                  <span>Create Giveaway</span>
                </a>
              </li>
              <li>
                <a class="nav-link d-flex align-items-center px-3 py-2" routerLink="/admin/giveaways/winners" routerLinkActive="active">
                  <i class="fas fa-circle fa-xs me-2"></i>              <span>Winners</span>
            </a>
          </li>
            </ul>
          </li>

          <li class="nav-item">
            <a class="nav-link d-flex align-items-center justify-content-between px-3 py-2"
               (click)="toggleSubmenu('content')"
               [class.active]="isSubmenuActive('/admin/content')">
              <div>
                <i class="fas fa-file-alt fa-fw me-2"></i>
                <span *ngIf="isSidebarOpen">Content</span>
              </div>
              <i *ngIf="isSidebarOpen" class="fas" [ngClass]="isSubmenuExpanded('content') ? 'fa-angle-down' : 'fa-angle-right'"></i>
            </a>
            <ul class="submenu list-unstyled ms-3" *ngIf="isSubmenuExpanded('content') && isSidebarOpen">
              <li>
                <a class="nav-link d-flex align-items-center px-3 py-2" routerLink="/admin/content/pages" routerLinkActive="active">
                  <i class="fas fa-circle fa-xs me-2"></i>
                  <span>Pages</span>
                </a>
              </li>
              <li>
                <a class="nav-link d-flex align-items-center px-3 py-2" routerLink="/admin/content/create" routerLinkActive="active">
                  <i class="fas fa-circle fa-xs me-2"></i>
                  <span>Create Content</span>
                </a>
              </li>
            </ul>
          </li>

          <li class="nav-item">
            <a class="nav-link d-flex align-items-center px-3 py-2" routerLink="/admin/transactions" routerLinkActive="active">
              <i class="fas fa-money-bill-wave fa-fw me-2"></i>
              <span *ngIf="isSidebarOpen">Transactions</span>
            </a>
          </li>

          <li class="nav-item">
            <a class="nav-link d-flex align-items-center px-3 py-2" routerLink="/admin/reports" routerLinkActive="active">
              <i class="fas fa-chart-bar fa-fw me-2"></i>
              <span *ngIf="isSidebarOpen">Reports</span>
            </a>
          </li>

          <li class="nav-item">
            <a class="nav-link d-flex align-items-center px-3 py-2" routerLink="/admin/settings" routerLinkActive="active">
              <i class="fas fa-cog fa-fw me-2"></i>
              <span *ngIf="isSidebarOpen">Settings</span>
            </a>
          </li>
        </ul>
      </div>

      <div class="nav-section">
        <div class="menu-label" *ngIf="isSidebarOpen">System</div>
        <ul class="nav flex-column">
          <li class="nav-item">
            <a class="nav-link d-flex align-items-center px-3 py-2" routerLink="/admin/settings" routerLinkActive="active">
              <i class="fas fa-cog fa-fw me-2"></i>
              <span *ngIf="isSidebarOpen">Settings</span>
            </a>
          </li>
          <li class="nav-item">
            <a class="nav-link d-flex align-items-center px-3 py-2" routerLink="/admin/logs" routerLinkActive="active">
              <i class="fas fa-clipboard-list fa-fw me-2"></i>
              <span *ngIf="isSidebarOpen">System Logs</span>
            </a>
          </li>
          <li class="nav-item">
            <a class="nav-link d-flex align-items-center px-3 py-2" routerLink="/admin/backups" routerLinkActive="active">
              <i class="fas fa-database fa-fw me-2"></i>
              <span *ngIf="isSidebarOpen">Backups</span>
            </a>
          </li>
        </ul>
      </div>

      <div class="nav-section mt-auto">
        <ul class="nav flex-column">
          <li class="nav-item">
            <a class="nav-link d-flex align-items-center px-3 py-2 text-danger" (click)="logout()">
              <i class="fas fa-sign-out-alt fa-fw me-2"></i>
              <span *ngIf="isSidebarOpen">Logout</span>
            </a>
          </li>
        </ul>
      </div>
    </nav>
  </div>

  <!-- Main Content -->
  <div class="main-content flex-grow-1 d-flex flex-column w-100">
    <!-- Top Header/Navbar -->
    <header class="header w-100">
      <nav class="navbar navbar-expand navbar-light py-2 px-3 w-100">
        <div class="container-fluid w-100">
          <!-- Mobile Sidebar Toggle -->
          <button class="btn btn-link text-dark d-block d-md-none me-3" (click)="toggleSidebar()">
            <i class="fas fa-bars"></i>
          </button>

          <!-- Page Title -->
          <h5 class="mb-0">{{pageTitle}}</h5>

          <!-- Security Indicator -->
          <div class="security-indicator d-none d-md-flex">
            <i class="fas fa-shield-alt"></i>
            <span>Admin Mode</span>
          </div>

          <!-- Right Menu Items -->
          <ul class="navbar-nav ms-auto">
            <!-- Search Button -->
            <li class="nav-item me-2">
              <button class="btn btn-light border border-secondary-subtle rounded-pill">
                <i class="fas fa-search"></i>
              </button>
            </li>

            <!-- Notifications Dropdown -->
            <li class="nav-item dropdown me-2">
              <a class="nav-link position-relative" href="#" id="notificationsDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                <i class="fas fa-bell"></i>
                <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger badge-notification">
                  {{notificationCount}}
                </span>
              </a>
              <div class="dropdown-menu dropdown-menu-end shadow-sm border-0" aria-labelledby="notificationsDropdown" style="min-width: 300px;">
                <div class="dropdown-header d-flex justify-content-between align-items-center">
                  <span>Notifications</span>
                  <a href="#" class="text-decoration-none small">Mark all as read</a>
                </div>
                <div class="dropdown-divider"></div>
                <div class="notification-list" style="max-height: 300px; overflow-y: auto;">
                  <a href="#" class="dropdown-item" *ngFor="let notification of notifications">
                    <div class="d-flex">
                      <div class="icon-circle me-3">
                        <i class="fas" [ngClass]="notification.icon"></i>
                      </div>
                      <div>
                        <p class="mb-1">{{notification.message}}</p>
                        <p class="text-muted mb-0 small">{{notification.time}}</p>
                      </div>
                    </div>
                  </a>
                </div>
                <div class="dropdown-divider"></div>
                <a href="#" class="dropdown-item text-center small">Show all notifications</a>
              </div>
            </li>

            <!-- User Profile Dropdown -->
            <li class="nav-item dropdown">
              <a class="nav-link d-flex align-items-center" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                <img src="assets/images/avatar-placeholder.png" alt="Admin Avatar" class="rounded-circle me-2" width="32" height="32">
                <span class="d-none d-md-inline-block">{{adminName}} <i class="fas fa-chevron-down ms-1 small"></i></span>
              </a>
              <div class="dropdown-menu dropdown-menu-end shadow-sm border-0" aria-labelledby="userDropdown">
                <a class="dropdown-item" routerLink="/admin/profile">
                  <i class="fas fa-user fa-fw me-2 text-muted"></i> Profile
                </a>
                <a class="dropdown-item" routerLink="/admin/settings">
                  <i class="fas fa-cog fa-fw me-2 text-muted"></i> Settings
                </a>
                <div class="dropdown-divider"></div>
                <a class="dropdown-item" (click)="logout()">
                  <i class="fas fa-sign-out-alt fa-fw me-2 text-muted"></i> Logout
                </a>
              </div>
            </li>
          </ul>
        </div>
      </nav>
    </header>

    <!-- Main Content Area -->
    <div class="content-wrapper flex-grow-1 p-3 p-md-4 overflow-auto w-100">
      <!-- Breadcrumb -->
      <nav aria-label="breadcrumb" class="mb-4">
        <ol class="breadcrumb">
          <li class="breadcrumb-item"><a routerLink="/admin/dashboard">Dashboard</a></li>
          <li class="breadcrumb-item active" aria-current="page">{{pageTitle}}</li>
        </ol>
      </nav>

      <!-- Router Outlet for Page Content -->
      <router-outlet></router-outlet>
    </div>

    <!-- Footer -->
    <footer class="footer bg-white border-top p-3 text-center text-md-start w-100">
      <div class="container-fluid w-100">
        <div class="row align-items-center w-100">
          <div class="col-md-6 small">
            <span>Copyright © {{currentYear}} Winners Society. All rights reserved.</span>
          </div>
          <div class="col-md-6 text-md-end small">
            <span>Created with <i class="fas fa-heart text-danger"></i> by Winners Society Team</span>
          </div>
        </div>
      </div>
    </footer>

    <!-- Quick Access Toolbar -->
    <div class="admin-quick-toolbar">
      <a href="#" class="quick-action" title="Add User">
        <i class="fas fa-user-plus"></i>
        <div class="tooltip">Add User</div>
      </a>
      <a href="#" class="quick-action" title="New Giveaway">
        <i class="fas fa-gift"></i>
        <div class="tooltip">New Giveaway</div>
      </a>
      <a href="#" class="quick-action main-action" title="Quick Actions">
        <i class="fas fa-plus"></i>
        <div class="tooltip">Quick Actions</div>
      </a>
    </div>
  </div>
</div>
