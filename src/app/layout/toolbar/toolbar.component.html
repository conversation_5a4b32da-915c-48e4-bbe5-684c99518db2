<div class="modern-navbar">
  <div class="navbar-container">
    <!-- Logo and Brand -->
    <div class="brand-section">
      <a routerLink="/" class="brand-link">
        <div class="logo-icon">
          <svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
            <circle cx="16" cy="16" r="16" fill="url(#gradient)"/>
            <path d="M16 8L20 12L16 16L12 12L16 8Z" fill="white"/>
            <path d="M16 16L20 20L16 24L12 20L16 16Z" fill="white" opacity="0.8"/>
            <defs>
              <linearGradient id="gradient" x1="0" y1="0" x2="32" y2="32" gradientUnits="userSpaceOnUse">
                <stop stop-color="#667eea"/>
                <stop offset="1" stop-color="#764ba2"/>
              </linearGradient>
            </defs>
          </svg>
        </div>
        <span class="brand-text">Winners Society</span>
      </a>
    </div>

    <!-- Desktop Navigation -->
    <nav class="desktop-nav">
      <a routerLink="/" class="nav-item" routerLinkActive="active" [routerLinkActiveOptions]="{exact: true}">
        Home
      </a>
      <a routerLink="/membership" class="nav-item" routerLinkActive="active">
        Plans
      </a>
      <a routerLink="/about" class="nav-item" routerLinkActive="active">
        About
      </a>
      <a routerLink="/contact" class="nav-item" routerLinkActive="active">
        Contact
      </a>
    </nav>

    <!-- Auth Section -->
    <div class="auth-section">
      <ng-container *ngIf="!(authService.isAuthenticated$ | async)">
        <a routerLink="/auth/login" class="login-btn">
          Sign In
        </a>
        <a routerLink="/auth/signup" class="signup-btn">
          Get Started
        </a>
      </ng-container>

      <!-- Authenticated User Menu -->
      <ng-container *ngIf="authService.isAuthenticated$ | async">
        <div class="user-menu" [matMenuTriggerFor]="userMenu">
          <div class="user-avatar">
            <mat-icon>account_circle</mat-icon>
          </div>
          <span class="user-greeting">Dashboard</span>
          <mat-icon class="dropdown-icon">keyboard_arrow_down</mat-icon>
        </div>
      </ng-container>

      <!-- Mobile Menu Toggle -->
      <button class="mobile-toggle" (click)="toggleMobileMenu()" [class.active]="isMobileMenuOpen">
        <span class="hamburger-line"></span>
        <span class="hamburger-line"></span>
        <span class="hamburger-line"></span>
      </button>
    </div>
  </div>

  <!-- Mobile Menu -->
  <div class="mobile-menu" [class.open]="isMobileMenuOpen">
    <div class="mobile-menu-content">
      <div class="mobile-nav-section">
        <a routerLink="/" class="mobile-link" (click)="closeMobileMenu()">Home</a>
        <a routerLink="/membership" class="mobile-link" (click)="closeMobileMenu()">Plans</a>
        <a routerLink="/about" class="mobile-link" (click)="closeMobileMenu()">About</a>
        <a routerLink="/contact" class="mobile-link" (click)="closeMobileMenu()">Contact</a>
      </div>

      <div class="mobile-auth-section">
        <ng-container *ngIf="!(authService.isAuthenticated$ | async)">
          <a routerLink="/auth/login" class="mobile-link" (click)="closeMobileMenu()">Sign In</a>
          <a routerLink="/auth/signup" class="mobile-link highlight" (click)="closeMobileMenu()">Get Started</a>
        </ng-container>

        <ng-container *ngIf="authService.isAuthenticated$ | async">
          <a routerLink="/member/dashboard" class="mobile-link" (click)="closeMobileMenu()">Dashboard</a>
          <a routerLink="/member/profile" class="mobile-link" (click)="closeMobileMenu()">My Profile</a>
          <a routerLink="/member/membership/dashboard" class="mobile-link" (click)="closeMobileMenu()">My Membership</a>
          <a routerLink="/member/entries" class="mobile-link" (click)="closeMobileMenu()">My Entries</a>
          <a (click)="logout(); closeMobileMenu()" class="mobile-link">Logout</a>
        </ng-container>
      </div>
    </div>
  </div>
</div>

<!-- User dropdown menu -->
<mat-menu #userMenu="matMenu" class="user-menu">
  <a mat-menu-item routerLink="/member/profile">
    <mat-icon>person</mat-icon>
    <span>My Profile</span>
  </a>
  <a mat-menu-item routerLink="/member/membership/dashboard">
    <mat-icon>card_membership</mat-icon>
    <span>My Membership</span>
  </a>
  <a mat-menu-item routerLink="/member/entries">
    <mat-icon>list_alt</mat-icon>
    <span>My Entries</span>
  </a>
  <button mat-menu-item (click)="logout()">
    <mat-icon>exit_to_app</mat-icon>
    <span>Logout</span>
  </button>
</mat-menu>
