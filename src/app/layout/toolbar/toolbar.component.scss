// Modern Navbar Styles
.modern-navbar {
  position: relative;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  z-index: 1000;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.navbar-container {
  width: 100%;
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 80px;

  @media (max-width: 768px) {
    padding: 0 1.5rem;
    height: 70px;
  }
}

// Brand Section
.brand-section {
  display: flex;
  align-items: center;
  z-index: 10;
}

.brand-link {
  display: flex;
  align-items: center;
  text-decoration: none;
  color: inherit;
  transition: transform 0.3s ease;

  &:hover {
    transform: scale(1.02);
    text-decoration: none;
  }
}

.logo-icon {
  margin-right: 0.75rem;
  display: flex;
  align-items: center;

  svg {
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
  }
}

.brand-text {
  font-size: 1.5rem;
  font-weight: 700;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;

  @media (max-width: 768px) {
    font-size: 1.25rem;
  }
}

// Desktop Navigation
.desktop-nav {
  display: flex;
  align-items: center;
  gap: 2rem;

  @media (max-width: 992px) {
    display: none;
  }
}

.nav-item {
  position: relative;
  font-weight: 500;
  color: #374151;
  text-decoration: none;
  padding: 0.75rem 0;
  transition: all 0.3s ease;

  &::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 2px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    transition: width 0.3s ease;
  }

  &:hover {
    color: #667eea;
    text-decoration: none;

    &::after {
      width: 100%;
    }
  }

  &.active {
    color: #667eea;

    &::after {
      width: 100%;
    }
  }
}

// Auth Section
.auth-section {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.login-btn {
  font-weight: 500;
  color: #374151;
  text-decoration: none;
  padding: 0.75rem 1.5rem;
  border-radius: 12px;
  transition: all 0.3s ease;

  &:hover {
    color: #667eea;
    background-color: rgba(102, 126, 234, 0.05);
    text-decoration: none;
  }

  @media (max-width: 992px) {
    display: none;
  }
}

.signup-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  font-weight: 600;
  padding: 0.875rem 2rem;
  border-radius: 12px;
  text-decoration: none;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
  }

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(102, 126, 234, 0.4);
    text-decoration: none;

    &::before {
      left: 100%;
    }
  }

  @media (max-width: 992px) {
    display: none;
  }
}

// User Menu (Authenticated)
.user-menu {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    background-color: rgba(102, 126, 234, 0.05);
  }

  @media (max-width: 992px) {
    display: none;
  }
}

.user-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;

  mat-icon {
    color: white;
    font-size: 20px;
    width: 20px;
    height: 20px;
  }
}

.user-greeting {
  font-weight: 500;
  color: #374151;
}

.dropdown-icon {
  color: #9ca3af;
  font-size: 18px;
  width: 18px;
  height: 18px;
}

// Mobile Toggle
.mobile-toggle {
  display: none;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 40px;
  height: 40px;
  background: none;
  border: none;
  cursor: pointer;
  padding: 0;

  @media (max-width: 992px) {
    display: flex;
  }
}

.hamburger-line {
  width: 24px;
  height: 2px;
  background-color: #374151;
  margin: 3px 0;
  transition: all 0.3s ease;
  border-radius: 2px;
}

.mobile-toggle.active {
  .hamburger-line:nth-child(1) {
    transform: rotate(45deg) translate(6px, 6px);
  }

  .hamburger-line:nth-child(2) {
    opacity: 0;
  }

  .hamburger-line:nth-child(3) {
    transform: rotate(-45deg) translate(6px, -6px);
  }
}

// Mobile Menu
.mobile-menu {
  position: fixed;
  top: 80px;
  left: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.98);
  backdrop-filter: blur(20px);
  border-top: 1px solid rgba(0, 0, 0, 0.05);
  transform: translateY(-100%);
  opacity: 0;
  visibility: hidden;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 999;

  @media (max-width: 768px) {
    top: 70px;
  }

  &.open {
    transform: translateY(0);
    opacity: 1;
    visibility: visible;
  }
}

.mobile-menu-content {
  padding: 2rem 1.5rem;
  max-height: calc(100vh - 80px);
  overflow-y: auto;

  @media (max-width: 768px) {
    max-height: calc(100vh - 70px);
  }
}

.mobile-nav-section {
  margin-bottom: 2rem;
  padding-bottom: 2rem;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.mobile-auth-section {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.mobile-link {
  display: block;
  padding: 1rem 1.5rem;
  color: #374151;
  text-decoration: none;
  font-weight: 500;
  border-radius: 12px;
  transition: all 0.3s ease;

  &:hover {
    color: #667eea;
    background-color: rgba(102, 126, 234, 0.05);
    text-decoration: none;
    transform: translateX(4px);
  }

  &.highlight {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    text-align: center;
    font-weight: 600;

    &:hover {
      background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
      color: white;
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
    }
  }
}

// User Menu Dropdown
::ng-deep .user-menu {
  .mat-mdc-menu-panel {
    background-color: white;
    border-radius: 16px;
    box-shadow: 0 12px 32px rgba(0, 0, 0, 0.12);
    border: 1px solid rgba(0, 0, 0, 0.05);
    margin-top: 0.5rem;
    min-width: 200px;
  }

  .mat-mdc-menu-item {
    padding: 1rem 1.5rem;
    font-size: 0.95rem;
    transition: all 0.3s ease;

    &:hover {
      background-color: rgba(102, 126, 234, 0.05);
      color: #667eea;
    }

    mat-icon {
      margin-right: 0.75rem;
      color: #9ca3af;
      transition: color 0.3s ease;
    }

    &:hover mat-icon {
      color: #667eea;
    }
  }
}

// Animations
@keyframes slideInDown {
  from {
    transform: translateY(-100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

// Reduced Motion
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}
