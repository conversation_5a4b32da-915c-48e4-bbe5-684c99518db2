<!-- page-wrapper Start-->
<div class="page-wrapper compact-wrapper" [ngClass]="{'sidebar-close': !isSidebarOpen}" id="pageWrapper">
  <!-- Page Header Start-->
  <app-header (toggleSidebarEvent)="toggleSidebar()"></app-header>
  <!-- Page Header Ends-->
  
  <!-- Page Body Start-->
  <div class="page-body-wrapper">
    <!-- Page Sidebar Start-->
    <app-sidebar [isOpen]="isSidebarOpen"></app-sidebar>
    <!-- Page Sidebar Ends-->
    
    <!-- Page Body Start-->
    <div class="page-body">
      <div class="container-fluid">
        <div class="page-title">
          <div class="row">
            <div class="col-6">
              <h3>Winners Society</h3>
            </div>
            <div class="col-6">
              <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="index.html"><i class="home-icon"></i></a></li>
                <li class="breadcrumb-item">Home</li>
              </ol>
            </div>
          </div>
        </div>
      </div>
      
      <!-- Page Content -->
      <div class="container-fluid">
        <router-outlet></router-outlet>
      </div>
    </div>
    <!-- Page Body Ends-->
    
    <!-- footer start-->
    <app-footer></app-footer>
    <!-- footer end-->
  </div>
  <!-- Page Body Ends-->
</div>
<!-- page-wrapper Ends--> 