:host {
  display: block;
  height: 100%;
  width: 100%;
}

.page-wrapper {
  height: 100%;
  display: flex;
  flex-direction: column;
  transition: 0.3s;
  
  &.sidebar-close {
    .page-body-wrapper {
      .sidebar {
        width: 0;
        transform: translateX(-280px);
      }
      
      .page-body {
        margin-left: 0;
      }
    }
  }
}

.page-body-wrapper {
  flex: 1;
  display: flex;
  min-height: calc(100vh - 80px); // Adjust based on your header height
}

.page-body {
  flex: 1;
  margin-left: 280px; // Width of sidebar
  transition: margin-left 0.3s ease;
  background-color: #f6f7fb;
  padding: 20px;
}

.page-title {
  margin-bottom: 30px;
  padding: 15px 0;
  border-bottom: 1px solid #dee2e6;
  
  h3 {
    margin-bottom: 0;
    color: #2c323f;
    font-weight: 500;
  }
  
  .breadcrumb {
    padding: 0;
    margin-bottom: 0;
    background-color: transparent;
    justify-content: flex-end;
    
    .breadcrumb-item {
      font-size: 14px;
      
      a {
        color: #7366ff;
      }
      
      &.active {
        color: #2c323f;
      }
    }
  }
} 