<div class="sidebar" [class.open]="isOpen">
  <div class="sidebar-logo-wrapper">
    <div class="logo-wrapper">
      <a routerLink="/">
        <img src="assets/images/logo/logo.png" alt="Winners Society Logo">
      </a>
    </div>
  </div>
  <!-- User Info Section -->
  <div class="sidebar-user-info" style="padding: 1rem; border-bottom: 1px solid #eee;">
    <div style="display: flex; align-items: center; gap: 10px;">
      <i class="fa fa-user-circle fa-2x" style="color: #adb5bd;"></i>
      <div>
    <div style="font-weight: 600;">{{ user?.firstName || user?.email || 'Member' }}</div>
    <div style="font-size: 13px; color: #666;">
      <ng-container *ngIf="hasActiveMembership; else noPlan">
        <span class="badge bg-primary text-white me-1">{{ membership?.tierName }}</span>
        <span class="badge bg-success text-white">Active</span>
      </ng-container>
      <ng-template #noPlan>
        <span class="badge bg-secondary text-white">No Plan</span>
      </ng-template>
    </div>
      </div>
    </div>
  </div>
  <div class="sidebar-menu">
    <ul class="sidebar-links">
      <li *ngFor="let item of menuItems" [class.sidebar-dropdown]="item.children" [class.active]="item.expanded">
        <a [routerLink]="item.path" *ngIf="!item.children" routerLinkActive="active">
          <i [class]="item.icon" *ngIf="item.icon"></i>
          <span>{{ item.title }}</span>
        </a>
        <a href="javascript:void(0)" *ngIf="item.children" (click)="toggleSubmenu(item)">
          <i [class]="item.icon" *ngIf="item.icon"></i>
          <span>{{ item.title }}</span>
          <i class="fa fa-angle-down submenu-indicator"></i>
        </a>
        <ul class="submenu" *ngIf="item.children" [class.submenu-open]="item.expanded">
          <li *ngFor="let child of item.children">
            <a [routerLink]="child.path" routerLinkActive="active">
              <span>{{ child.title }}</span>
            </a>
          </li>
        </ul>
      </li>
    </ul>
  </div>
</div>