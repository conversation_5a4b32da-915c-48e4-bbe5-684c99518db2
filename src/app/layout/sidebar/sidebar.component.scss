.sidebar {
  width: 280px;
  height: 100%;
  background-color: #ffffff;
  border-right: 1px solid #eeeeee;
  position: fixed;
  top: 80px;
  left: 0;
  z-index: 999;
  transition: all 0.3s ease;
  overflow-y: auto;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.05);
  
  &:not(.open) {
    transform: translateX(-280px);
  }
  
  .sidebar-logo-wrapper {
    padding: 20px;
    border-bottom: 1px solid #eeeeee;
    display: flex;
    justify-content: center;
    
    .logo-wrapper {
      img {
        max-height: 40px;
      }
    }
  }
  
  .sidebar-menu {
    padding: 15px 0;
    
    .sidebar-links {
      list-style: none;
      margin: 0;
      padding: 0;
      
      > li {
        position: relative;
        
        > a {
          display: flex;
          align-items: center;
          padding: 12px 20px;
          color: #666666;
          text-decoration: none;
          transition: all 0.3s ease;
          position: relative;
          
          i {
            margin-right: 10px;
            font-size: 16px;
            width: 20px;
            text-align: center;
          }
          
          span {
            font-size: 14px;
          }
          
          .submenu-indicator {
            position: absolute;
            right: 20px;
            top: 50%;
            transform: translateY(-50%);
            transition: transform 0.3s ease;
          }
          
          &:hover {
            color: #7366ff;
            background-color: #f8f8f8;
          }
          
          &.active {
            color: #7366ff;
            background-color: #f8f8f8;
            border-left: 3px solid #7366ff;
          }
        }
        
        &.active {
          > a {
            color: #7366ff;
            
            .submenu-indicator {
              transform: translateY(-50%) rotate(180deg);
            }
          }
        }
        
        .submenu {
          list-style: none;
          margin: 0;
          padding: 0;
          max-height: 0;
          overflow: hidden;
          transition: max-height 0.3s ease;
          background-color: #f8f8f8;
          
          &.submenu-open {
            max-height: 1000px;
          }
          
          li {
            a {
              display: block;
              padding: 10px 20px 10px 50px;
              color: #666666;
              text-decoration: none;
              font-size: 14px;
              transition: all 0.3s ease;
              
              &:hover {
                color: #7366ff;
              }
              
              &.active {
                color: #7366ff;
                background-color: rgba(115, 102, 255, 0.05);
              }
            }
          }
        }
      }
    }
  }
} 