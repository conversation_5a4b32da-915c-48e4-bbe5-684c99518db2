
import { Component, Input, OnInit } from '@angular/core';
import { RouterLink, RouterLinkActive } from '@angular/router';
import { CommonModule } from '@angular/common';
import { UserContextService } from '../../core/services/user-context.service';
import { AuthService } from '../../core/services/auth.service';
import { AuthUser } from '../../core/models/user.model';
import { Subscription } from '../../core/models/membership.model';

interface MenuItem {
  title: string;
  icon?: string;
  path?: string;
  children?: MenuItem[];
  expanded?: boolean;
}

@Component({
  selector: 'app-sidebar',
  standalone: true,
  imports: [CommonModule, RouterLink, RouterLinkActive],
  templateUrl: './sidebar.component.html',
  styleUrls: ['./sidebar.component.scss']
})
export class SidebarComponent implements OnInit {
  @Input() isOpen = true;

  user: AuthUser | null = null;
  membership: Subscription | null = null;
  hasActiveMembership = false;

  menuItems: MenuItem[] = [
    {
      title: 'Dashboard',
      icon: 'fa fa-home',
      path: '/dashboard'
    },
    {
      title: 'Giveaways',
      icon: 'fa fa-gift',
      path: '/giveaways'
    },
    {
      title: 'Membership',
      icon: 'fa fa-id-card',
      path: '/membership'
    },
    {
      title: 'Winners',
      icon: 'fa fa-trophy',
      path: '/winners'
    },
    {
      title: 'My Profile',
      icon: 'fa fa-user',
      children: [
        {
          title: 'Profile',
          path: '/profile'
        },
        {
          title: 'Entry History',
          path: '/profile/entries'
        },
        {
          title: 'Win History',
          path: '/profile/wins'
        },
        {
          title: 'Transaction History',
          path: '/profile/transactions'
        }
      ]
    },
    {
      title: 'Admin',
      icon: 'fa fa-cog',
      children: [
        {
          title: 'Users',
          path: '/admin/users'
        },
        {
          title: 'Memberships',
          path: '/admin/memberships'
        },
        {
          title: 'Giveaways',
          path: '/admin/giveaways'
        },
        {
          title: 'Content',
          path: '/admin/content'
        },
        {
          title: 'Settings',
          path: '/admin/settings'
        }
      ]
    }
  ];
  
  constructor(
    private userContext: UserContextService,
    private authService: AuthService
  ) {}

  ngOnInit() {
    this.authService.currentUser$.subscribe(user => {
      this.user = user;
    });
    this.userContext.membership$.subscribe(membership => {
      this.membership = membership;
      this.hasActiveMembership = !!(membership && membership.status === 'active');
    });
  }

  toggleSubmenu(item: MenuItem) {
    item.expanded = !item.expanded;
  }
}