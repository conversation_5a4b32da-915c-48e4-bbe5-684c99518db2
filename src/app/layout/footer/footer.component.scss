// Modern Footer Styles
.modern-footer {
  background: linear-gradient(135deg, #1a202c 0%, #2d3748 100%);
  color: #e2e8f0;
  padding: 0;
  margin-top: auto;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"><g fill="none" fill-rule="evenodd"><g fill="%23ffffff" fill-opacity="0.02"><circle cx="30" cy="30" r="2"/></g></svg>');
    pointer-events: none;
  }
}

.footer-container {
  position: relative;
  z-index: 2;
  width: 100%;
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;

  @media (max-width: 768px) {
    padding: 0 1.5rem;
  }
}

// Main Footer Content
.footer-main {
  display: grid;
  grid-template-columns: 2fr 2fr 1.5fr;
  gap: 4rem;
  padding: 4rem 0;

  @media (max-width: 992px) {
    grid-template-columns: 1fr 1fr;
    gap: 3rem;
    padding: 3rem 0;
  }

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: 2.5rem;
    padding: 2.5rem 0;
  }
}

// Brand Section
.footer-brand {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.brand-logo {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.logo-icon {
  display: flex;
  align-items: center;

  svg {
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
  }
}

.brand-name {
  font-size: 1.5rem;
  font-weight: 700;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.brand-description {
  color: #a0aec0;
  line-height: 1.6;
  max-width: 300px;
  margin: 0;
}

.social-links {
  display: flex;
  gap: 1rem;
}

.social-link {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 44px;
  height: 44px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  color: #a0aec0;
  text-decoration: none;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  &:hover {
    background: rgba(102, 126, 234, 0.1);
    border-color: rgba(102, 126, 234, 0.3);
    color: #667eea;
    transform: translateY(-2px);
  }

  svg {
    width: 20px;
    height: 20px;
    stroke: currentColor;
  }
}

// Navigation Section
.footer-nav {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 2rem;

  @media (max-width: 992px) {
    grid-column: span 2;
  }

  @media (max-width: 768px) {
    grid-column: span 1;
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
  }

  @media (max-width: 480px) {
    grid-template-columns: 1fr;
  }
}

.nav-column {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.nav-title {
  font-size: 1rem;
  font-weight: 600;
  color: #f7fafc;
  margin: 0 0 0.5rem 0;
}

.nav-links {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.nav-link {
  color: #a0aec0;
  text-decoration: none;
  font-size: 0.95rem;
  transition: all 0.3s ease;
  padding: 0.25rem 0;

  &:hover {
    color: #667eea;
    text-decoration: none;
    transform: translateX(4px);
  }
}

// Newsletter Section
.footer-newsletter {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;

  @media (max-width: 992px) {
    grid-column: span 2;
    max-width: 400px;
  }

  @media (max-width: 768px) {
    grid-column: span 1;
    max-width: none;
  }
}

.newsletter-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #f7fafc;
  margin: 0;
}

.newsletter-description {
  color: #a0aec0;
  line-height: 1.6;
  margin: 0;
  font-size: 0.95rem;
}

.newsletter-form {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.input-group {
  display: flex;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.3s ease;

  &:focus-within {
    border-color: rgba(102, 126, 234, 0.5);
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
  }
}

.newsletter-input {
  flex: 1;
  background: none;
  border: none;
  padding: 0.875rem 1rem;
  color: #f7fafc;
  font-size: 0.95rem;
  outline: none;

  &::placeholder {
    color: #a0aec0;
  }
}

.newsletter-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 0.875rem 1.5rem;
  font-weight: 600;
  font-size: 0.95rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.3s ease;

  &:hover {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
    transform: translateX(-2px);
  }

  svg {
    transition: transform 0.3s ease;
  }

  &:hover svg {
    transform: translateX(2px);
  }
}

.newsletter-disclaimer {
  font-size: 0.8rem;
  color: #718096;
  margin: 0;
  line-height: 1.4;
}

// Footer Bottom
.footer-bottom {
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding: 2rem 0;
}

.footer-bottom-content {
  display: flex;
  justify-content: space-between;
  align-items: center;

  @media (max-width: 768px) {
    flex-direction: column;
    gap: 1.5rem;
    text-align: center;
  }
}

.copyright {
  color: #a0aec0;
  font-size: 0.9rem;
  margin: 0;
}

.footer-badges {
  display: flex;
  gap: 1.5rem;

  @media (max-width: 480px) {
    gap: 1rem;
  }
}

.security-badge {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #a0aec0;
  font-size: 0.85rem;
  font-weight: 500;

  svg {
    width: 16px;
    height: 16px;
    stroke: currentColor;
  }
}

// Reduced Motion
@media (prefers-reduced-motion: reduce) {
  * {
    transition-duration: 0.01ms !important;
    animation-duration: 0.01ms !important;
  }
}