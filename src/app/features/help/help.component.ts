import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { MatCardModule } from '@angular/material/card';
import { MatIconModule } from '@angular/material/icon';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatTabsModule } from '@angular/material/tabs';
import { RouterLink } from '@angular/router';

interface FAQItem {
  question: string;
  answer: string;
  category: 'general' | 'membership' | 'giveaways' | 'account' | 'technical';
}

interface HelpCategory {
  id: string;
  title: string;
  description: string;
  icon: string;
  articles: Array<{
    title: string;
    description: string;
    link: string;
  }>;
}

@Component({
  selector: 'app-help',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    MatCardModule,
    MatIconModule,
    MatExpansionModule,
    MatButtonModule,
    MatFormFieldModule,
    MatInputModule,
    MatTabsModule
  ],
  templateUrl: './help.component.html',
  styleUrls: ['./help.component.scss']
})
export class HelpComponent implements OnInit {
  searchQuery = '';
  selectedCategory: string = 'all';

  faqItems: FAQItem[] = [
    {
      question: 'How do I participate in giveaways?',
      answer: 'To participate in giveaways, you need an active membership plan. Each plan comes with a specific number of entries per month. Simply browse available giveaways and use your entries to participate.',
      category: 'giveaways'
    },
    {
      question: 'What happens if I win a prize?',
      answer: 'When you win, you\'ll receive a notification via email and in your dashboard. You\'ll need to claim your prize within the specified timeframe and provide shipping information if required.',
      category: 'giveaways'
    },
    {
      question: 'How do membership entries work?',
      answer: 'Each membership plan includes a monthly allocation of entries. You can use these entries to participate in any active giveaways. Entries reset at the beginning of each billing cycle.',
      category: 'membership'
    },
    {
      question: 'Can I upgrade or downgrade my membership?',
      answer: 'Yes, you can change your membership plan at any time. Upgrades take effect immediately, while downgrades take effect at the next billing cycle.',
      category: 'membership'
    },
    {
      question: 'How do I update my profile information?',
      answer: 'Go to your profile page and click "Edit Profile". You can update your personal information, address, and profile picture.',
      category: 'account'
    },
    {
      question: 'How do I change my password?',
      answer: 'Navigate to Settings > Security and use the "Change Password" form. You\'ll need to enter your current password and choose a new one.',
      category: 'account'
    },
    {
      question: 'What payment methods do you accept?',
      answer: 'We accept all major credit cards (Visa, MasterCard, American Express) and PayPal. All payments are processed securely through Stripe.',
      category: 'general'
    },
    {
      question: 'Is my personal information secure?',
      answer: 'Yes, we take security seriously. All data is encrypted and we follow industry best practices to protect your information.',
      category: 'general'
    }
  ];

  helpCategories: HelpCategory[] = [
    {
      id: 'getting-started',
      title: 'Getting Started',
      description: 'Learn the basics of Winners Society',
      icon: 'play_circle',
      articles: [
        {
          title: 'Creating Your Account',
          description: 'Step-by-step guide to signing up',
          link: '#'
        },
        {
          title: 'Choosing a Membership Plan',
          description: 'Find the right plan for you',
          link: '#'
        },
        {
          title: 'Your First Giveaway Entry',
          description: 'How to participate in giveaways',
          link: '#'
        }
      ]
    },
    {
      id: 'membership',
      title: 'Membership & Billing',
      description: 'Manage your subscription and payments',
      icon: 'card_membership',
      articles: [
        {
          title: 'Understanding Entry Allocations',
          description: 'How monthly entries work',
          link: '#'
        },
        {
          title: 'Upgrading Your Plan',
          description: 'Get more entries and benefits',
          link: '#'
        },
        {
          title: 'Billing and Payments',
          description: 'Payment methods and billing cycles',
          link: '#'
        }
      ]
    },
    {
      id: 'giveaways',
      title: 'Giveaways & Prizes',
      description: 'Everything about participating and winning',
      icon: 'card_giftcard',
      articles: [
        {
          title: 'How Giveaways Work',
          description: 'Understanding the process',
          link: '#'
        },
        {
          title: 'Claiming Your Prize',
          description: 'What to do when you win',
          link: '#'
        },
        {
          title: 'Prize Shipping',
          description: 'Delivery and tracking information',
          link: '#'
        }
      ]
    },
    {
      id: 'account',
      title: 'Account Management',
      description: 'Manage your profile and settings',
      icon: 'account_circle',
      articles: [
        {
          title: 'Profile Settings',
          description: 'Update your personal information',
          link: '#'
        },
        {
          title: 'Security Settings',
          description: 'Password and account security',
          link: '#'
        },
        {
          title: 'Notification Preferences',
          description: 'Control your email notifications',
          link: '#'
        }
      ]
    }
  ];

  constructor() {}

  ngOnInit(): void {}

  /**
   * Get filtered FAQ items based on search and category
   */
  getFilteredFAQs(): FAQItem[] {
    let filtered = this.faqItems;

    // Filter by category
    if (this.selectedCategory !== 'all') {
      filtered = filtered.filter(item => item.category === this.selectedCategory);
    }

    // Filter by search query
    if (this.searchQuery.trim()) {
      const query = this.searchQuery.toLowerCase();
      filtered = filtered.filter(item =>
        item.question.toLowerCase().includes(query) ||
        item.answer.toLowerCase().includes(query)
      );
    }

    return filtered;
  }

  /**
   * Get unique categories from FAQ items
   */
  getCategories(): Array<{id: string, label: string}> {
    const categories = [
      { id: 'all', label: 'All Categories' },
      { id: 'general', label: 'General' },
      { id: 'membership', label: 'Membership' },
      { id: 'giveaways', label: 'Giveaways' },
      { id: 'account', label: 'Account' },
      { id: 'technical', label: 'Technical' }
    ];
    return categories;
  }

  /**
   * Clear search and filters
   */
  clearSearch(): void {
    this.searchQuery = '';
    this.selectedCategory = 'all';
  }
}
