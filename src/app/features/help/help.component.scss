.page-header {
  margin-bottom: 2rem;

  .page-title {
    font-size: 2rem;
    font-weight: 600;
    color: #333;
    margin: 0 0 0.5rem 0;
  }

  .page-subtitle {
    color: #666;
    margin: 0;
    font-size: 1rem;
  }
}

.quick-actions {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;

  .action-card {
    border-radius: 16px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    border: none;
    transition: transform 0.3s ease, box-shadow 0.3s ease;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
    }

    mat-card-content {
      text-align: center;
      padding: 2rem;

      .action-icon {
        font-size: 3rem;
        width: 3rem;
        height: 3rem;
        color: #667eea;
        margin-bottom: 1rem;
      }

      h3 {
        font-size: 1.25rem;
        font-weight: 600;
        color: #333;
        margin: 0 0 0.5rem 0;
      }

      p {
        color: #666;
        margin: 0 0 1.5rem 0;
        font-size: 0.875rem;
      }

      button {
        border-radius: 12px;
        height: 40px;
        font-weight: 600;
      }
    }
  }
}

.help-tabs {
  ::ng-deep .mat-mdc-tab-group {
    .mat-mdc-tab-header {
      border-bottom: 1px solid #e0e0e0;
    }

    .mat-mdc-tab {
      min-width: 160px;
    }
  }
}

.tab-content {
  padding: 2rem 0;
}

.search-section {
  margin-bottom: 2rem;

  .search-card {
    border-radius: 16px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border: none;

    .search-controls {
      display: flex;
      flex-direction: column;
      gap: 1rem;

      .search-field {
        width: 100%;
      }

      .category-filters {
        display: flex;
        flex-wrap: wrap;
        gap: 0.5rem;

        button {
          border-radius: 20px;
          font-size: 0.875rem;

          &.active {
            background-color: var(--mdc-filled-button-container-color, #1976d2);
            color: white;
          }
        }
      }
    }
  }
}

.faq-section {
  .faq-accordion {
    .faq-panel {
      border-radius: 12px;
      margin-bottom: 1rem;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      border: none;

      .faq-question {
        font-weight: 600;
        color: #333;
      }

      .faq-category {
        color: #666;
        font-size: 0.875rem;
      }

      .faq-answer {
        padding: 1rem 0;

        p {
          color: #555;
          line-height: 1.6;
          margin: 0;
        }
      }
    }
  }

  .no-results {
    display: flex;
    justify-content: center;
    padding: 3rem;

    .no-results-card {
      max-width: 400px;
      border-radius: 16px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      border: none;

      mat-card-content {
        text-align: center;
        padding: 2rem;

        .no-results-icon {
          font-size: 3rem;
          width: 3rem;
          height: 3rem;
          color: #ff9800;
          margin-bottom: 1rem;
        }

        h3 {
          margin: 0 0 1rem 0;
          color: #333;
        }

        p {
          color: #666;
          margin-bottom: 2rem;
        }

        button {
          border-radius: 12px;
          height: 44px;
          font-weight: 600;
        }
      }
    }
  }
}

.categories-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;

  .category-card {
    border-radius: 16px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    border: none;
    transition: transform 0.3s ease, box-shadow 0.3s ease;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
    }

    mat-card-header {
      padding-bottom: 1rem;

      .category-icon {
        font-size: 2rem;
        width: 2rem;
        height: 2rem;
        color: #667eea;
        margin-bottom: 0.5rem;
      }

      mat-card-title {
        font-size: 1.25rem;
        font-weight: 600;
        color: #333;
      }

      mat-card-subtitle {
        color: #666;
        margin-top: 0.25rem;
      }
    }

    .articles-list {
      display: flex;
      flex-direction: column;
      gap: 0.75rem;

      .article-link {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 1rem;
        border-radius: 8px;
        background-color: #f8f9fa;
        text-decoration: none;
        transition: background-color 0.3s ease;

        &:hover {
          background-color: #e9ecef;
        }

        .article-info {
          flex: 1;

          .article-title {
            font-size: 1rem;
            font-weight: 600;
            color: #333;
            margin: 0 0 0.25rem 0;
          }

          .article-description {
            font-size: 0.875rem;
            color: #666;
            margin: 0;
          }
        }

        .article-arrow {
          color: #667eea;
          font-size: 20px;
          width: 20px;
          height: 20px;
        }
      }
    }
  }
}

.contact-section {
  max-width: 800px;
  margin: 0 auto;

  .contact-card {
    border-radius: 16px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    border: none;

    mat-card-header {
      padding-bottom: 1rem;

      mat-card-title {
        font-size: 1.5rem;
        font-weight: 600;
        color: #333;
      }

      mat-card-subtitle {
        color: #666;
        margin-top: 0.5rem;
      }
    }

    .contact-methods {
      display: flex;
      flex-direction: column;
      gap: 2rem;

      .contact-method {
        display: flex;
        align-items: center;
        gap: 1rem;
        padding: 1.5rem;
        border-radius: 12px;
        background-color: #f8f9fa;

        .contact-icon {
          font-size: 2rem;
          width: 2rem;
          height: 2rem;
          color: #667eea;
          flex-shrink: 0;
        }

        .contact-info {
          flex: 1;

          h4 {
            font-size: 1.125rem;
            font-weight: 600;
            color: #333;
            margin: 0 0 0.25rem 0;
          }

          p {
            font-size: 1rem;
            color: #555;
            margin: 0 0 0.25rem 0;
          }

          small {
            color: #666;
            font-size: 0.875rem;
          }
        }

        button {
          border-radius: 12px;
          height: 40px;
          font-weight: 600;
        }
      }
    }
  }
}
