<!-- <PERSON> Header -->
<div class="page-header">
  <h1 class="page-title">Help & Support</h1>
  <p class="page-subtitle">Find answers to your questions and get the help you need</p>
</div>

<!-- Quick Actions -->
<div class="quick-actions">
  <mat-card class="action-card">
    <mat-card-content>
      <mat-icon class="action-icon">support_agent</mat-icon>
      <h3>Contact Support</h3>
      <p>Get personalized help from our support team</p>
      <button mat-raised-button color="primary">Contact Us</button>
    </mat-card-content>
  </mat-card>

  <mat-card class="action-card">
    <mat-card-content>
      <mat-icon class="action-icon">video_library</mat-icon>
      <h3>Video Tutorials</h3>
      <p>Watch step-by-step guides and tutorials</p>
      <button mat-stroked-button color="primary">Watch Videos</button>
    </mat-card-content>
  </mat-card>

  <mat-card class="action-card">
    <mat-card-content>
      <mat-icon class="action-icon">forum</mat-icon>
      <h3>Community Forum</h3>
      <p>Connect with other members and share tips</p>
      <button mat-stroked-button color="primary">Visit Forum</button>
    </mat-card-content>
  </mat-card>
</div>

<!-- Help Content Tabs -->
<mat-tab-group class="help-tabs" animationDuration="300ms">
  <!-- FAQ Tab -->
  <mat-tab label="Frequently Asked Questions">
    <div class="tab-content">
      <!-- Search and Filter -->
      <div class="search-section">
        <mat-card class="search-card">
          <mat-card-content>
            <div class="search-controls">
              <mat-form-field appearance="outline" class="search-field">
                <mat-label>Search FAQs</mat-label>
                <input matInput [(ngModel)]="searchQuery" placeholder="Type your question...">
                <mat-icon matSuffix>search</mat-icon>
              </mat-form-field>

              <div class="category-filters">
                <button 
                  mat-stroked-button 
                  *ngFor="let category of getCategories()" 
                  [color]="selectedCategory === category.id ? 'primary' : ''"
                  [class.active]="selectedCategory === category.id"
                  (click)="selectedCategory = category.id">
                  {{ category.label }}
                </button>
              </div>

              <button mat-button color="warn" (click)="clearSearch()" *ngIf="searchQuery || selectedCategory !== 'all'">
                <mat-icon>clear</mat-icon>
                Clear Filters
              </button>
            </div>
          </mat-card-content>
        </mat-card>
      </div>

      <!-- FAQ List -->
      <div class="faq-section">
        <mat-accordion class="faq-accordion" *ngIf="getFilteredFAQs().length > 0">
          <mat-expansion-panel *ngFor="let faq of getFilteredFAQs()" class="faq-panel">
            <mat-expansion-panel-header>
              <mat-panel-title class="faq-question">
                {{ faq.question }}
              </mat-panel-title>
              <mat-panel-description class="faq-category">
                {{ faq.category | titlecase }}
              </mat-panel-description>
            </mat-expansion-panel-header>
            <div class="faq-answer">
              <p>{{ faq.answer }}</p>
            </div>
          </mat-expansion-panel>
        </mat-accordion>

        <!-- No Results -->
        <div class="no-results" *ngIf="getFilteredFAQs().length === 0">
          <mat-card class="no-results-card">
            <mat-card-content>
              <mat-icon class="no-results-icon">help_outline</mat-icon>
              <h3>No FAQs Found</h3>
              <p>We couldn't find any FAQs matching your search. Try different keywords or browse all categories.</p>
              <button mat-raised-button color="primary" (click)="clearSearch()">
                Show All FAQs
              </button>
            </mat-card-content>
          </mat-card>
        </div>
      </div>
    </div>
  </mat-tab>

  <!-- Help Categories Tab -->
  <mat-tab label="Help Categories">
    <div class="tab-content">
      <div class="categories-grid">
        <mat-card class="category-card" *ngFor="let category of helpCategories">
          <mat-card-header>
            <mat-icon class="category-icon">{{ category.icon }}</mat-icon>
            <mat-card-title>{{ category.title }}</mat-card-title>
            <mat-card-subtitle>{{ category.description }}</mat-card-subtitle>
          </mat-card-header>
          
          <mat-card-content>
            <div class="articles-list">
              <a href="{{ article.link }}" class="article-link" *ngFor="let article of category.articles">
                <div class="article-info">
                  <h4 class="article-title">{{ article.title }}</h4>
                  <p class="article-description">{{ article.description }}</p>
                </div>
                <mat-icon class="article-arrow">arrow_forward</mat-icon>
              </a>
            </div>
          </mat-card-content>
        </mat-card>
      </div>
    </div>
  </mat-tab>
</mat-tab-group>
