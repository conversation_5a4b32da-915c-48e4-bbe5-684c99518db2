import { Routes } from '@angular/router';
import { AuthGuard } from '../../core/guards/auth.guard';
import { roleGuard, membershipTierGuard } from '../../core/guards/role.guard';

/**
 * Routes for authenticated members
 * All routes are protected by AuthGuard
 * Some routes may have additional protection via roleGuard or membershipTierGuard
 *
 * Note: MemberLayoutComponent is already defined in app.routes.ts for the /member path
 */
export const MEMBER_ROUTES: Routes = [
  {
    path: '',
    redirectTo: 'dashboard',
    pathMatch: 'full'
  },
  {
    path: 'dashboard',
    loadChildren: () => import('../dashboard/dashboard.routes').then(m => m.DASHBOARD_ROUTES),
    data: {
      requiresAuth: true,
      title: 'Member Dashboard - Winners Society'
    }
  },
  {
    path: 'membership',
    loadChildren: () => import('./membership/member-membership.routes').then(m => m.MEMBER_MEMBERSHIP_ROUTES),
    data: {
      requiresAuth: true,
      title: 'My Membership - Winners Society'
    }
  },
  {
    path: 'profile',
    loadChildren: () => import('../profile/profile.routes').then(m => m.PROFILE_ROUTES),
    data: {
      requiresAuth: true,
      title: 'My Profile - Winners Society'
    }
  },
  {
    path: 'giveaways',
    loadChildren: () => import('../giveaways/giveaways.routes').then(m => m.GIVEAWAYS_ROUTES),
    data: {
      requiresAuth: true,
      title: 'Giveaways - Winners Society'
    }
  },
  {
    path: 'winners',
    loadComponent: () => import('../winners/winners.component').then(m => m.WinnersComponent),
    data: {
      requiresAuth: true,
      title: 'Winners Gallery - Winners Society'
    }
  },
  {
    path: 'help',
    loadComponent: () => import('../help/help.component').then(m => m.HelpComponent),
    data: {
      requiresAuth: true,
      title: 'Help & Support - Winners Society'
    }
  },
  {
    path: 'entries',
    redirectTo: 'profile/entries',
    pathMatch: 'full'
  }
];
