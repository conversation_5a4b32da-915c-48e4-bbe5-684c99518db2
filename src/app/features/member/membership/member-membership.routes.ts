import { Routes } from '@angular/router';
import { AuthGuard } from '../../../core/guards/auth.guard';

/**
 * Routes for authenticated member's membership section
 * All routes are protected by AuthGuard
 */
export const MEMBER_MEMBERSHIP_ROUTES: Routes = [
  {
    path: '',
    redirectTo: 'dashboard',
    pathMatch: 'full'
  },
  {
    path: 'dashboard',
    loadComponent: () => import('../../membership/membership-overview/membership-overview.component')
      .then(c => c.MembershipOverviewComponent),
    canActivate: [AuthGuard],
    title: 'My Membership - Winners Society'
  },
  {
    path: 'plans',
    loadComponent: () => import('../../membership/membership-plans/membership-plans.component')
      .then(c => c.MembershipPlansComponent),
    canActivate: [AuthGuard],
    title: 'Membership Plans - Winners Society'
  },
  {
    path: 'upgrade',
    loadComponent: () => import('../../membership/membership-upgrade/membership-upgrade.component')
      .then(c => c.MembershipUpgradeComponent),
    canActivate: [AuthGuard],
    title: 'Upgrade Membership - Winners Society'
  },
  {
    path: 'payment-success',
    loadComponent: () => import('../../membership/payment-success/payment-success.component')
      .then(c => c.PaymentSuccessComponent),
    canActivate: [AuthGuard],
    title: 'Payment Successful - Winners Society'
  }
];
