import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ActivatedRoute, Router, RouterModule } from '@angular/router';
import { FormBuilder, FormGroup, Validators, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { UserService } from '../../../../core/services/user.service';

@Component({
  selector: 'app-user-edit',
  standalone: true,
  imports: [CommonModule, RouterModule, FormsModule, ReactiveFormsModule],
  templateUrl: './user-edit.component.html',
  styleUrls: ['./user-edit.component.scss']
})
export class UserEditComponent implements OnInit {
  userId: string | null = null;
  user: any = null;
  userForm!: FormGroup;
  loading = true;
  saving = false;
  error: string | null = null;
  isCreateMode = false;

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private userService: UserService,
    private formBuilder: FormBuilder
  ) { }

  ngOnInit(): void {
    this.userId = this.route.snapshot.paramMap.get('id');
    
    if (this.route.snapshot.routeConfig?.path === 'create') {
      this.isCreateMode = true;
      this.initEmptyForm();
      this.loading = false;
    } else if (this.userId) {
      this.loadUserDetails(this.userId);
    } else {
      this.loading = false;
      this.error = 'User ID not provided';
    }
  }

  initEmptyForm(): void {
    this.userForm = this.formBuilder.group({
      email: ['', [Validators.required, Validators.email]],
      firstName: ['', Validators.required],
      lastName: ['', Validators.required],
      phoneNumber: [''],
      birthDate: [''],
      role: ['USER', Validators.required],
      status: ['ACTIVE', Validators.required],
      emailVerified: [false],
      phoneVerified: [false],
      password: ['', [Validators.required, Validators.minLength(8)]]
    });
  }

  loadUserDetails(id: string): void {
    this.loading = true;
    this.userService.getUserById(id).subscribe({
      next: (response: any) => {
        this.user = response.data;
        this.initForm();
        this.loading = false;
      },
      error: (error: any) => {
        console.error('Error loading user details:', error);
        this.error = 'Failed to load user details';
        this.loading = false;
      }
    });
  }

  initForm(): void {
    // Format date to YYYY-MM-DD if it exists
    let birthDate = null;
    if (this.user.birthDate) {
      const date = new Date(this.user.birthDate);
      birthDate = date.toISOString().split('T')[0];
    }

    this.userForm = this.formBuilder.group({
      email: [{ value: this.user.email, disabled: true }],
      firstName: [this.user.firstName, Validators.required],
      lastName: [this.user.lastName, Validators.required],
      phoneNumber: [this.user.phoneNumber || ''],
      birthDate: [birthDate],
      role: [this.user.role, Validators.required],
      status: [this.user.status, Validators.required],
      emailVerified: [this.user.emailVerified],
      phoneVerified: [this.user.phoneVerified]
    });
  }

  onSubmit(): void {
    if (this.userForm.invalid) {
      return;
    }

    this.saving = true;
    const formData = this.isCreateMode ? this.userForm.value : { ...this.userForm.getRawValue() };
    
    if (!this.isCreateMode) {
      delete formData.email; // Remove the disabled email field for edit mode
    }

    if (this.isCreateMode) {
      this.createUser(formData);
    } else if (this.userId) {
      this.updateUser(this.userId, formData);
    }
  }

  createUser(userData: any): void {
    this.userService.createUser(userData).subscribe({
      next: (response: any) => {
        this.saving = false;
        this.router.navigate(['/admin/users']);
      },
      error: (error: any) => {
        console.error('Error creating user:', error);
        this.error = 'Failed to create user';
        this.saving = false;
      }
    });
  }

  updateUser(id: string, userData: any): void {
    this.userService.updateUser(id, userData).subscribe({
      next: (response: any) => {
        this.saving = false;
        this.router.navigate(['/admin/users', this.userId]);
      },
      error: (error: any) => {
        console.error('Error updating user:', error);
        this.error = 'Failed to update user';
        this.saving = false;
      }
    });
  }
} 