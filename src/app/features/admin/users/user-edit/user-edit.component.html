<div class="mb-4 d-flex justify-content-between align-items-center">
  <h2>{{ isCreateMode ? 'Create New User' : 'Edit User: ' + user?.firstName + ' ' + user?.lastName }}</h2>
  <button class="btn btn-outline-secondary" [routerLink]="['/admin/users']">
    Cancel
  </button>
</div>

<div *ngIf="loading" class="text-center py-5">
  <div class="spinner-border" role="status">
    <span class="visually-hidden">Loading...</span>
  </div>
</div>

<div *ngIf="error" class="alert alert-danger">
  {{ error }}
</div>

<form *ngIf="!loading && userForm" [formGroup]="userForm" (ngSubmit)="onSubmit()">
  <div class="row">
    <div class="col-md-6 mb-4">
      <div class="card">
        <div class="card-header">
          <h5 class="mb-0">Basic Information</h5>
        </div>
        <div class="card-body">
          <div class="mb-3">
            <label for="email" class="form-label">Email</label>
            <input type="email" class="form-control" id="email" formControlName="email" [readonly]="!isCreateMode">
            <div class="form-text text-muted" *ngIf="!isCreateMode">Email cannot be changed</div>
            <div *ngIf="userForm.get('email')?.invalid && userForm.get('email')?.touched" class="text-danger">
              Email is required and must be valid
            </div>
          </div>
          
          <div class="mb-3" *ngIf="isCreateMode">
            <label for="password" class="form-label">Password</label>
            <input type="password" class="form-control" id="password" formControlName="password">
            <div *ngIf="userForm.get('password')?.invalid && userForm.get('password')?.touched" class="text-danger">
              Password is required (minimum 8 characters)
            </div>
          </div>
          
          <div class="row">
            <div class="col-md-6 mb-3">
              <label for="firstName" class="form-label">First Name</label>
              <input type="text" class="form-control" id="firstName" formControlName="firstName">
              <div *ngIf="userForm.get('firstName')?.invalid && userForm.get('firstName')?.touched" class="text-danger">
                First name is required
              </div>
            </div>
            
            <div class="col-md-6 mb-3">
              <label for="lastName" class="form-label">Last Name</label>
              <input type="text" class="form-control" id="lastName" formControlName="lastName">
              <div *ngIf="userForm.get('lastName')?.invalid && userForm.get('lastName')?.touched" class="text-danger">
                Last name is required
              </div>
            </div>
          </div>
          
          <div class="mb-3">
            <label for="phoneNumber" class="form-label">Phone Number</label>
            <input type="tel" class="form-control" id="phoneNumber" formControlName="phoneNumber">
          </div>
          
          <div class="mb-3">
            <label for="birthDate" class="form-label">Birth Date</label>
            <input type="date" class="form-control" id="birthDate" formControlName="birthDate">
          </div>
        </div>
      </div>
    </div>
    
    <div class="col-md-6 mb-4">
      <div class="card">
        <div class="card-header">
          <h5 class="mb-0">Account Settings</h5>
        </div>
        <div class="card-body">
          <div class="mb-3">
            <label for="role" class="form-label">Role</label>
            <select class="form-select" id="role" formControlName="role">
              <option value="USER">User</option>
              <option value="MODERATOR">Moderator</option>
              <option value="ADMIN">Admin</option>
            </select>
          </div>
          
          <div class="mb-3">
            <label for="status" class="form-label">Account Status</label>
            <select class="form-select" id="status" formControlName="status">
              <option value="ACTIVE">Active</option>
              <option value="INACTIVE">Inactive</option>
              <option value="SUSPENDED">Suspended</option>
            </select>
          </div>
          
          <div class="mb-3 form-check">
            <input type="checkbox" class="form-check-input" id="emailVerified" formControlName="emailVerified">
            <label class="form-check-label" for="emailVerified">Email Verified</label>
          </div>
          
          <div class="mb-3 form-check">
            <input type="checkbox" class="form-check-input" id="phoneVerified" formControlName="phoneVerified">
            <label class="form-check-label" for="phoneVerified">Phone Verified</label>
          </div>
        </div>
      </div>
      
      <div class="d-flex justify-content-end mt-3">
        <button type="button" class="btn btn-outline-secondary me-2" [routerLink]="['/admin/users']">
          Cancel
        </button>
        <button type="submit" class="btn btn-primary" [disabled]="userForm.invalid || saving">
          <span *ngIf="saving" class="spinner-border spinner-border-sm me-1" role="status" aria-hidden="true"></span>
          {{ isCreateMode ? 'Create User' : 'Save Changes' }}
        </button>
      </div>
    </div>
  </div>
</form> 