import { Routes } from '@angular/router';
import { UsersComponent } from './users.component';
import { UserListComponent } from './user-list/user-list.component';
import { UserDetailComponent } from './user-detail/user-detail.component';
import { UserEditComponent } from './user-edit/user-edit.component';
import { AuthGuard } from '../../../core/guards/auth.guard';
import { AdminGuard } from '../../../core/guards/admin.guard';

export const USERS_ROUTES: Routes = [
  {
    path: '',
    component: UsersComponent,
    children: [
      {
        path: '',
        component: UserListComponent,
        title: 'User Management - Winners Society',
      },
      {
        path: 'create',
        component: UserEditComponent,
        title: 'Create User - Winners Society',
      },
      {
        path: ':id',
        component: UserDetailComponent,
        title: 'User Details - Winners Society',
      },
      {
        path: ':id/edit',
        component: UserEditComponent,
        title: 'Edit User - Winners Society',
      }
    ],
    canActivate: [AuthGuard, AdminGuard]
  }
]; 