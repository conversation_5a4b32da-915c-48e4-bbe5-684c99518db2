<div class="mb-4 d-flex justify-content-between align-items-center">
  <h2>User Profile: {{ user?.firstName }} {{ user?.lastName }}</h2>
  <div>
    <button class="btn btn-outline-primary me-2" [routerLink]="['/admin/users', userId, 'edit']">Edit User</button>
    <button class="btn btn-outline-danger" (click)="onDeleteUser()">Delete User</button>
  </div>
</div>

<div *ngIf="loading" class="text-center py-5">
  <div class="spinner-border" role="status">
    <span class="visually-hidden">Loading...</span>
  </div>
</div>

<div *ngIf="!loading && user">
  <div class="row">
    <div class="col-md-4 mb-4">
      <div class="card">
        <div class="card-body">
          <h5 class="card-title">Basic Information</h5>
          <div class="mb-3">
            <label class="form-label fw-bold">Email</label>
            <p>{{ user.email }}</p>
          </div>
          <div class="mb-3">
            <label class="form-label fw-bold">Name</label>
            <p>{{ user.firstName }} {{ user.lastName }}</p>
          </div>
          <div class="mb-3">
            <label class="form-label fw-bold">Phone</label>
            <p>{{ user.phoneNumber || 'Not provided' }}</p>
          </div>
          <div class="mb-3">
            <label class="form-label fw-bold">Birth Date</label>
            <p>{{ user.birthDate ? (user.birthDate | date:'mediumDate') : 'Not provided' }}</p>
          </div>
          <div class="mb-3">
            <label class="form-label fw-bold">Created</label>
            <p>{{ user.createdAt | date:'medium' }}</p>
          </div>
        </div>
      </div>
    </div>

    <div class="col-md-4 mb-4">
      <div class="card">
        <div class="card-body">
          <h5 class="card-title">Status & Role</h5>
          
          <div class="mb-3">
            <label class="form-label fw-bold">Account Status</label>
            <div>
              <span class="badge" [ngClass]="{
                'bg-success': user.status === 'ACTIVE',
                'bg-danger': user.status === 'INACTIVE',
                'bg-warning': user.status === 'SUSPENDED'
              }">{{ user.status }}</span>
              <button class="btn btn-sm btn-outline-primary ms-2" 
                (click)="toggleUserStatus()">
                {{ user.status === 'ACTIVE' ? 'Deactivate' : 'Activate' }}
              </button>
            </div>
          </div>
          
          <div class="mb-3">
            <label class="form-label fw-bold">Role</label>
            <div class="d-flex align-items-center">
              <span class="badge me-2" [ngClass]="{
                'bg-primary': user.role === 'USER',
                'bg-danger': user.role === 'ADMIN',
                'bg-warning': user.role === 'MODERATOR'
              }">{{ user.role }}</span>
              <select class="form-select form-select-sm w-auto" 
                (change)="changeUserRole($event)" 
                [value]="user.role">
                <option value="USER">User</option>
                <option value="MODERATOR">Moderator</option>
                <option value="ADMIN">Admin</option>
              </select>
            </div>
          </div>
          
          <div class="mb-3">
            <label class="form-label fw-bold">Email Verification</label>
            <div>
              <span class="badge" [ngClass]="{
                'bg-success': user.emailVerified,
                'bg-danger': !user.emailVerified
              }">{{ user.emailVerified ? 'Verified' : 'Not Verified' }}</span>
              <button class="btn btn-sm btn-outline-primary ms-2" 
                (click)="toggleEmailVerification()">
                {{ user.emailVerified ? 'Mark Unverified' : 'Mark Verified' }}
              </button>
            </div>
          </div>
          
          <div class="mb-3">
            <label class="form-label fw-bold">Phone Verification</label>
            <div>
              <span class="badge" [ngClass]="{
                'bg-success': user.phoneVerified,
                'bg-danger': !user.phoneVerified
              }">{{ user.phoneVerified ? 'Verified' : 'Not Verified' }}</span>
              <button class="btn btn-sm btn-outline-primary ms-2" 
                (click)="togglePhoneVerification()">
                {{ user.phoneVerified ? 'Mark Unverified' : 'Mark Verified' }}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="col-md-4 mb-4">
      <div class="card">
        <div class="card-body">
          <h5 class="card-title">Membership</h5>
          
          <div *ngIf="user.membership; else noMembership">
            <div class="mb-3">
              <label class="form-label fw-bold">Tier</label>
              <p>{{ user.membership.tier.name }}</p>
            </div>
            <div class="mb-3">
              <label class="form-label fw-bold">Status</label>
              <p>
                <span class="badge" [ngClass]="{
                  'bg-success': user.membership.status === 'ACTIVE',
                  'bg-secondary': user.membership.status === 'EXPIRED',
                  'bg-danger': user.membership.status === 'CANCELED'
                }">{{ user.membership.status }}</span>
              </p>
            </div>
            <div class="mb-3">
              <label class="form-label fw-bold">Start Date</label>
              <p>{{ user.membership.startDate | date:'mediumDate' }}</p>
            </div>
            <div class="mb-3">
              <label class="form-label fw-bold">End Date</label>
              <p>{{ user.membership.endDate | date:'mediumDate' }}</p>
            </div>
            <div class="mb-3">
              <label class="form-label fw-bold">Renewal Date</label>
              <p>{{ user.membership.renewalDate | date:'mediumDate' }}</p>
            </div>
          </div>
          <ng-template #noMembership>
            <p class="text-muted">No active membership</p>
          </ng-template>
        </div>
      </div>
    </div>
  </div>

  <div class="row">
    <div class="col-12">
      <ul class="nav nav-tabs" id="userTabs" role="tablist">
        <li class="nav-item" role="presentation">
          <button class="nav-link active" id="stats-tab" data-bs-toggle="tab" data-bs-target="#stats" type="button" role="tab" aria-controls="stats" aria-selected="true">
            Stats
          </button>
        </li>
        <li class="nav-item" role="presentation">
          <button class="nav-link" id="membership-history-tab" data-bs-toggle="tab" data-bs-target="#membership-history" type="button" role="tab" aria-controls="membership-history" aria-selected="false">
            Membership History
          </button>
        </li>
        <li class="nav-item" role="presentation">
          <button class="nav-link" id="transactions-tab" data-bs-toggle="tab" data-bs-target="#transactions" type="button" role="tab" aria-controls="transactions" aria-selected="false">
            Transactions
          </button>
        </li>
        <li class="nav-item" role="presentation">
          <button class="nav-link" id="entries-tab" data-bs-toggle="tab" data-bs-target="#entries" type="button" role="tab" aria-controls="entries" aria-selected="false">
            Giveaway Entries
          </button>
        </li>
        <li class="nav-item" role="presentation">
          <button class="nav-link" id="wins-tab" data-bs-toggle="tab" data-bs-target="#wins" type="button" role="tab" aria-controls="wins" aria-selected="false">
            Wins
          </button>
        </li>
      </ul>
      
      <div class="tab-content p-3 border border-top-0 rounded-bottom" id="userTabsContent">
        <div class="tab-pane fade show active" id="stats" role="tabpanel" aria-labelledby="stats-tab">
          <div class="row">
            <div class="col-md-4">
              <div class="card text-center">
                <div class="card-body">
                  <h5 class="card-title">Total Entries</h5>
                  <h3 class="card-text">{{ user.stats?.totalEntries || 0 }}</h3>
                </div>
              </div>
            </div>
            <div class="col-md-4">
              <div class="card text-center">
                <div class="card-body">
                  <h5 class="card-title">Total Wins</h5>
                  <h3 class="card-text">{{ user.stats?.totalWins || 0 }}</h3>
                </div>
              </div>
            </div>
            <div class="col-md-4">
              <div class="card text-center">
                <div class="card-body">
                  <h5 class="card-title">Total Spent</h5>
                  <h3 class="card-text">${{ user.stats?.totalSpent || 0 }}</h3>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <div class="tab-pane fade" id="membership-history" role="tabpanel" aria-labelledby="membership-history-tab">
          <p class="text-muted" *ngIf="!user.membership?.membershipHistory?.length">No membership history found</p>
          <!-- Membership history table would go here -->
        </div>
        
        <div class="tab-pane fade" id="transactions" role="tabpanel" aria-labelledby="transactions-tab">
          <p class="text-muted">Transaction history will be displayed here</p>
          <!-- Transactions table would go here -->
        </div>
        
        <div class="tab-pane fade" id="entries" role="tabpanel" aria-labelledby="entries-tab">
          <p class="text-muted">Giveaway entries will be displayed here</p>
          <!-- Entries table would go here -->
        </div>
        
        <div class="tab-pane fade" id="wins" role="tabpanel" aria-labelledby="wins-tab">
          <p class="text-muted">Wins history will be displayed here</p>
          <!-- Wins table would go here -->
        </div>
      </div>
    </div>
  </div>
</div>

<div *ngIf="!loading && !user" class="alert alert-danger">
  User not found
</div> 