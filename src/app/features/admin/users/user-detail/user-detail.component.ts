import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ActivatedRoute, Router, RouterModule } from '@angular/router';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { UserService } from '../../../../core/services/user.service';

interface UserDetail {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  phoneNumber?: string;
  birthDate?: string;
  role: string;
  status: string;
  emailVerified: boolean;
  phoneVerified: boolean;
  membership?: {
    id: string;
    tier: {
      id: string;
      name: string;
    };
    status: string;
    startDate: string;
    endDate: string;
    renewalDate: string;
    membershipHistory?: any[];
  };
  stats?: {
    totalEntries: number;
    totalWins: number;
    totalSpent: number;
  };
  createdAt: string;
  updatedAt: string;
}

@Component({
  selector: 'app-user-detail',
  standalone: true,
  imports: [CommonModule, RouterModule, FormsModule, ReactiveFormsModule],
  templateUrl: './user-detail.component.html',
  styleUrls: ['./user-detail.component.scss']
})
export class UserDetailComponent implements OnInit {
  userId: string | null = null;
  user: UserDetail | null = null;
  loading = true;
  error: string | null = null;

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private userService: UserService
  ) { }

  ngOnInit(): void {
    this.userId = this.route.snapshot.paramMap.get('id');
    if (this.userId) {
      this.loadUserDetails(this.userId);
    } else {
      this.loading = false;
      this.error = 'User ID not provided';
    }
  }

  loadUserDetails(id: string): void {
    this.loading = true;
    this.userService.getUserById(id).subscribe({
      next: (response: any) => {
        this.user = response.data;
        this.loading = false;
      },
      error: (error: any) => {
        console.error('Error loading user details:', error);
        this.error = 'Failed to load user details';
        this.loading = false;
      }
    });
  }

  toggleUserStatus(): void {
    if (!this.user) return;
    
    const newStatus = this.user.status === 'ACTIVE' ? 'INACTIVE' : 'ACTIVE';
    
    this.userService.toggleUserStatus(this.user.id, newStatus).subscribe({
      next: (response: any) => {
        if (this.user) {
          this.user.status = newStatus;
        }
      },
      error: (error: any) => {
        console.error('Error toggling user status:', error);
      }
    });
  }

  changeUserRole(event: Event): void {
    if (!this.user) return;
    
    const select = event.target as HTMLSelectElement;
    const newRole = select.value;
    
    this.userService.changeUserRole(this.user.id, newRole).subscribe({
      next: (response: any) => {
        if (this.user) {
          this.user.role = newRole;
        }
      },
      error: (error: any) => {
        console.error('Error changing user role:', error);
        // Reset the select to the current role
        if (this.user) {
          select.value = this.user.role;
        }
      }
    });
  }

  toggleEmailVerification(): void {
    if (!this.user) return;
    
    const newValue = !this.user.emailVerified;
    
    this.userService.updateUser(this.user.id, { emailVerified: newValue }).subscribe({
      next: (response: any) => {
        if (this.user) {
          this.user.emailVerified = newValue;
        }
      },
      error: (error: any) => {
        console.error('Error updating email verification status:', error);
      }
    });
  }

  togglePhoneVerification(): void {
    if (!this.user) return;
    
    const newValue = !this.user.phoneVerified;
    
    this.userService.updateUser(this.user.id, { phoneVerified: newValue }).subscribe({
      next: (response: any) => {
        if (this.user) {
          this.user.phoneVerified = newValue;
        }
      },
      error: (error: any) => {
        console.error('Error updating phone verification status:', error);
      }
    });
  }

  onDeleteUser(): void {
    if (!this.user) return;
    
    if (confirm(`Are you sure you want to delete ${this.user.firstName} ${this.user.lastName}'s account? This action cannot be undone.`)) {
      this.userService.deleteUser(this.user.id).subscribe({
        next: (response: any) => {
          alert('User successfully deleted');
          this.router.navigate(['/admin/users']);
        },
        error: (error: any) => {
          console.error('Error deleting user:', error);
          alert('Failed to delete user');
        }
      });
    }
  }
} 