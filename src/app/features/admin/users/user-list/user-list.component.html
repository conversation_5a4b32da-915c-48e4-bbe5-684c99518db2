<div class="mb-4">
  <form [formGroup]="filterForm" class="row g-3">
    <div class="col-md-3">
      <label for="email" class="form-label">Email</label>
      <input 
        type="text" 
        class="form-control" 
        id="email" 
        formControlName="email"
        placeholder="Search by email">
    </div>
    <div class="col-md-2">
      <label for="role" class="form-label">Role</label>
      <select class="form-select" id="role" formControlName="role">
        <option value="">All Roles</option>
        <option value="USER">User</option>
        <option value="ADMIN">Admin</option>
        <option value="MODERATOR">Moderator</option>
      </select>
    </div>
    <div class="col-md-2">
      <label for="membershipStatus" class="form-label">Membership</label>
      <select class="form-select" id="membershipStatus" formControlName="membershipStatus">
        <option value="">All Statuses</option>
        <option value="ACTIVE">Active</option>
        <option value="EXPIRED">Expired</option>
        <option value="CANCELED">Canceled</option>
      </select>
    </div>
    <div class="col-md-3 d-flex align-items-end">
      <button type="button" class="btn btn-primary me-2" (click)="applyFilters()">
        Apply Filters
      </button>
      <button type="button" class="btn btn-outline-secondary" (click)="resetFilters()">
        Reset
      </button>
    </div>
    <div class="col-md-2 d-flex align-items-end justify-content-end">
      <button type="button" class="btn btn-success" (click)="navigateToRegister()">
        Add New User
      </button>
    </div>
  </form>
</div>

<div class="table-responsive">
  <table class="table table-striped table-hover">
    <thead>
      <tr>
        <th>Name</th>
        <th>Email</th>
        <th>Role</th>
        <th>Membership</th>
        <th>Status</th>
        <th>Created Date</th>
        <th>Actions</th>
      </tr>
    </thead>
    <tbody *ngIf="users.length > 0">
      <tr *ngFor="let user of users">
        <td>{{ user.firstName }} {{ user.lastName }}</td>
        <td>{{ user.email }}</td>
        <td>
          <span class="badge" [ngClass]="{
            'bg-primary': user.role === 'USER',
            'bg-danger': user.role === 'ADMIN',
            'bg-warning': user.role === 'MODERATOR'
          }">{{ user.role }}</span>
        </td>
        <td>
          <span *ngIf="user.membership; else noMembership" class="badge" [ngClass]="{
            'bg-success': user.membership.status === 'ACTIVE',
            'bg-secondary': user.membership.status === 'EXPIRED',
            'bg-danger': user.membership.status === 'CANCELED'
          }">
            {{ user.membership.tier }} ({{ user.membership.status }})
          </span>
          <ng-template #noMembership>
            <span class="badge bg-light text-dark">None</span>
          </ng-template>
        </td>
        <td>
          <span class="badge" [ngClass]="{
            'bg-success': user.status === 'ACTIVE',
            'bg-danger': user.status === 'INACTIVE',
            'bg-warning': user.status === 'SUSPENDED'
          }">{{ user.status }}</span>
        </td>
        <td>{{ user.createdAt | date:'medium' }}</td>
        <td>
          <button class="btn btn-sm btn-outline-primary me-1" [routerLink]="['/admin/users', user.id]">
            View
          </button>
          <button class="btn btn-sm btn-outline-secondary me-1" [routerLink]="['/admin/users', user.id, 'edit']">
            Edit
          </button>
        </td>
      </tr>
    </tbody>
    <tbody *ngIf="users.length === 0">
      <tr>
        <td colspan="7" class="text-center py-4">
          <div *ngIf="loading">Loading users...</div>
          <div *ngIf="!loading">No users found with the current filters</div>
        </td>
      </tr>
    </tbody>
  </table>
</div>

<app-pagination
  *ngIf="pagination && pagination.total > 0"
  [currentPage]="pagination.page"
  [totalPages]="pagination.pages"
  (pageChanged)="onPageChange($event)"
></app-pagination> 