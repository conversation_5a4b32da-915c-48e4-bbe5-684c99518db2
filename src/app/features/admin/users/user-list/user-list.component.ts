import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router, RouterModule } from '@angular/router';
import { FormsModule, ReactiveFormsModule, FormBuilder, FormGroup } from '@angular/forms';
import { UserService } from '../../../../core/services/user.service';
import { PaginationComponent } from '../../../../shared/components/pagination/pagination.component';

interface User {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  role: string;
  status: string;
  membership?: {
    tier: string;
    status: string;
    endDate: string;
  };
  createdAt: string;
}

interface Pagination {
  page: number;
  limit: number;
  total: number;
  pages: number;
}

@Component({
  selector: 'app-user-list',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    FormsModule,
    ReactiveFormsModule,
    PaginationComponent
  ],
  templateUrl: './user-list.component.html',
  styleUrls: ['./user-list.component.scss']
})
export class UserListComponent implements OnInit {
  users: User[] = [];
  loading = true;
  filterForm: FormGroup;
  pagination: Pagination | null = null;

  constructor(
    private userService: UserService,
    private formBuilder: FormBuilder,
    private router: Router
  ) {
    this.filterForm = this.formBuilder.group({
      email: [''],
      role: [''],
      membershipStatus: [''],
      hasActiveMembership: [null],
      isVerified: [null],
      createdAfter: [''],
      createdBefore: ['']
    });
  }

  ngOnInit(): void {
    this.loadUsers();
  }

  loadUsers(page: number = 1): void {
    this.loading = true;
    
    // Prepare filter parameters
    const params: any = {
      page,
      limit: 10,
      ...this.getFilters()
    };

    this.userService.getAdminUsers(params).subscribe({
      next: (response: any) => {
        this.users = response.data;
        this.pagination = response.meta.pagination;
        this.loading = false;
      },
      error: (error: any) => {
        console.error('Error loading users:', error);
        this.loading = false;
      }
    });
  }

  getFilters(): any {
    const filters: any = {};
    const formValues = this.filterForm.value;

    // Only add non-empty filters
    if (formValues.email) filters.email = formValues.email;
    if (formValues.role) filters.role = formValues.role;
    if (formValues.membershipStatus) filters.membershipStatus = formValues.membershipStatus;
    if (formValues.hasActiveMembership !== null) filters.hasActiveMembership = formValues.hasActiveMembership;
    if (formValues.isVerified !== null) filters.isVerified = formValues.isVerified;
    if (formValues.createdAfter) filters.createdAfter = formValues.createdAfter;
    if (formValues.createdBefore) filters.createdBefore = formValues.createdBefore;

    return filters;
  }

  applyFilters(): void {
    this.loadUsers(1); // Reset to first page when applying filters
  }

  resetFilters(): void {
    this.filterForm.reset();
    this.loadUsers(1);
  }

  onPageChange(page: number): void {
    this.loadUsers(page);
  }

  navigateToRegister(): void {
    this.router.navigate(['/auth/register']);
  }
} 