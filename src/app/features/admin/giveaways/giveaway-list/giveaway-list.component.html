<div class="container-fluid p-4">
  <div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="mb-0">Giveaway Management</h1>
    <a [routerLink]="['/admin/giveaways/create']" class="btn btn-primary">
      <i class="fas fa-plus me-2"></i> Create New Giveaway
    </a>
  </div>

  <!-- Filters -->
  <div class="card shadow-sm mb-4">
    <div class="card-body">
      <form [formGroup]="filterForm" (ngSubmit)="applyFilters()">
        <div class="row g-3">
          <div class="col-md-4">
            <label for="status" class="form-label">Status</label>
            <select id="status" class="form-select" formControlName="status">
              <option *ngFor="let option of statusOptions" [value]="option.value">{{ option.label }}</option>
            </select>
          </div>
          <div class="col-md-6">
            <label for="search" class="form-label">Search</label>
            <input type="text" id="search" class="form-control" placeholder="Search by title..." formControlName="search">
          </div>
          <div class="col-md-2 d-flex align-items-end">
            <div class="d-grid gap-2 w-100">
              <button type="submit" class="btn btn-primary">
                <i class="fas fa-search me-2"></i> Filter
              </button>
              <button type="button" class="btn btn-outline-secondary" (click)="resetFilters()">
                <i class="fas fa-undo me-2"></i> Reset
              </button>
            </div>
          </div>
        </div>
      </form>
    </div>
  </div>

  <!-- Error Alert -->
  <div *ngIf="error" class="alert alert-danger alert-dismissible fade show" role="alert">
    {{ error }}
    <button type="button" class="btn-close" (click)="error = ''"></button>
  </div>

  <!-- Giveaways Table -->
  <div class="card shadow-sm">
    <div class="card-body">
      <div class="table-responsive">
        <table class="table table-hover">
          <thead>
            <tr>
              <th>Title</th>
              <th>Status</th>
              <th>Start Date</th>
              <th>End Date</th>
              <th>Prize Value</th>
              <th>Entries</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody *ngIf="!loading && giveaways.length > 0">
            <tr *ngFor="let giveaway of giveaways">
              <td>{{ giveaway.title }}</td>
              <td>
                <span class="badge" [ngClass]="getStatusClass(giveaway.status)">
                  {{ giveaway.status }}
                </span>
              </td>
              <td>{{ formatDate(giveaway.startDate) }}</td>
              <td>{{ formatDate(giveaway.endDate) }}</td>
              <td>{{ formatCurrency(giveaway.prizeValue) }}</td>
              <td>{{ giveaway.entryCount || 0 }}</td>
              <td>
                <div class="btn-group">
                  <a [routerLink]="['/admin/giveaways', giveaway.id]" class="btn btn-sm btn-outline-primary" title="View Details">
                    <i class="fas fa-eye"></i>
                  </a>
                  <a [routerLink]="['/admin/giveaways', giveaway.id, 'edit']" class="btn btn-sm btn-outline-secondary" title="Edit">
                    <i class="fas fa-edit"></i>
                  </a>
                  <button *ngIf="giveaway.status === 'DRAFT'" (click)="publishGiveaway(giveaway.id)" class="btn btn-sm btn-outline-success" title="Publish">
                    <i class="fas fa-check"></i>
                  </button>
                  <a *ngIf="canConductDraw(giveaway)" [routerLink]="['/admin/giveaways', giveaway.id, 'draw']" class="btn btn-sm btn-outline-info" title="Conduct Draw">
                    <i class="fas fa-trophy"></i>
                  </a>
                  <button (click)="deleteGiveaway(giveaway.id)" class="btn btn-sm btn-outline-danger" title="Delete">
                    <i class="fas fa-trash"></i>
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
          <tbody *ngIf="loading">
            <tr>
              <td colspan="7" class="text-center py-4">
                <div class="spinner-border text-primary" role="status">
                  <span class="visually-hidden">Loading...</span>
                </div>
                <p class="mt-2 mb-0">Loading giveaways...</p>
              </td>
            </tr>
          </tbody>
          <tbody *ngIf="!loading && giveaways.length === 0">
            <tr>
              <td colspan="7" class="text-center py-4">
                <p class="mb-0">No giveaways found. Create a new giveaway to get started.</p>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- Pagination -->
      <div *ngIf="totalPages > 1" class="d-flex justify-content-center mt-4">
        <nav aria-label="Giveaway pagination">
          <ul class="pagination">
            <li class="page-item" [class.disabled]="currentPage === 1">
              <a class="page-link" href="javascript:void(0)" (click)="onPageChange(currentPage - 1)" aria-label="Previous">
                <span aria-hidden="true">&laquo;</span>
              </a>
            </li>
            <li class="page-item" *ngFor="let page of [].constructor(totalPages); let i = index" [class.active]="currentPage === i + 1">
              <a class="page-link" href="javascript:void(0)" (click)="onPageChange(i + 1)">{{ i + 1 }}</a>
            </li>
            <li class="page-item" [class.disabled]="currentPage === totalPages">
              <a class="page-link" href="javascript:void(0)" (click)="onPageChange(currentPage + 1)" aria-label="Next">
                <span aria-hidden="true">&raquo;</span>
              </a>
            </li>
          </ul>
        </nav>
      </div>
    </div>
  </div>
</div>
