import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { Giveaway, GiveawayStatus } from '../../../../core/models/giveaway.model';
import { AdminGiveawayService } from '../../services/admin-giveaway.service';
import { FormBuilder, FormGroup } from '@angular/forms';

@Component({
  selector: 'app-giveaway-list',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    FormsModule,
    ReactiveFormsModule
  ],
  templateUrl: './giveaway-list.component.html',
  styleUrls: ['./giveaway-list.component.scss']
})
export class GiveawayListComponent implements OnInit {
  giveaways: Giveaway[] = [];
  loading = true;
  error = '';
  
  // Pagination
  currentPage = 1;
  itemsPerPage = 10;
  totalItems = 0;
  totalPages = 0;
  
  // Filters
  filterForm: FormGroup;
  
  // Status options for filter
  statusOptions = [
    { value: '', label: 'All Statuses' },
    { value: GiveawayStatus.DRAFT, label: 'Draft' },
    { value: GiveawayStatus.ACTIVE, label: 'Active' },
    { value: GiveawayStatus.COMPLETED, label: 'Completed' },
    { value: GiveawayStatus.CANCELLED, label: 'Cancelled' }
  ];
  
  constructor(
    private giveawayService: AdminGiveawayService,
    private fb: FormBuilder
  ) {
    this.filterForm = this.fb.group({
      status: [''],
      search: ['']
    });
  }
  
  ngOnInit(): void {
    this.loadGiveaways();
  }
  
  loadGiveaways(): void {
    this.loading = true;
    
    const filters = {
      page: this.currentPage,
      limit: this.itemsPerPage,
      ...this.filterForm.value
    };
    
    // Remove empty filters
    Object.keys(filters).forEach(key => {
      if (filters[key] === '') {
        delete filters[key];
      }
    });
    
    this.giveawayService.getGiveaways(filters).subscribe({
      next: (response) => {
        this.giveaways = response.data;
        this.totalItems = response.meta.total;
        this.totalPages = response.meta.pages;
        this.loading = false;
      },
      error: (err) => {
        this.error = 'Failed to load giveaways. Please try again.';
        console.error('Error loading giveaways:', err);
        this.loading = false;
      }
    });
  }
  
  onPageChange(page: number): void {
    this.currentPage = page;
    this.loadGiveaways();
  }
  
  applyFilters(): void {
    this.currentPage = 1; // Reset to first page when filtering
    this.loadGiveaways();
  }
  
  resetFilters(): void {
    this.filterForm.reset({
      status: '',
      search: ''
    });
    this.applyFilters();
  }
  
  deleteGiveaway(id: string): void {
    if (confirm('Are you sure you want to delete this giveaway? This action cannot be undone.')) {
      this.giveawayService.deleteGiveaway(id).subscribe({
        next: () => {
          this.loadGiveaways();
        },
        error: (err) => {
          this.error = 'Failed to delete giveaway. Please try again.';
          console.error('Error deleting giveaway:', err);
        }
      });
    }
  }
  
  publishGiveaway(id: string): void {
    if (confirm('Are you sure you want to publish this giveaway? It will be visible to users.')) {
      this.giveawayService.publishGiveaway(id).subscribe({
        next: () => {
          this.loadGiveaways();
        },
        error: (err) => {
          this.error = 'Failed to publish giveaway. Please try again.';
          console.error('Error publishing giveaway:', err);
        }
      });
    }
  }
  
  getStatusClass(status: GiveawayStatus): string {
    switch (status) {
      case GiveawayStatus.DRAFT:
        return 'bg-secondary';
      case GiveawayStatus.ACTIVE:
        return 'bg-success';
      case GiveawayStatus.COMPLETED:
        return 'bg-primary';
      case GiveawayStatus.CANCELLED:
        return 'bg-danger';
      default:
        return 'bg-secondary';
    }
  }
  
  formatDate(date: Date): string {
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  }
  
  formatCurrency(value: number): string {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(value / 100); // Convert cents to dollars
  }
  
  canConductDraw(giveaway: Giveaway): boolean {
    const now = new Date();
    return giveaway.status === GiveawayStatus.ACTIVE && now > giveaway.endDate;
  }
}
