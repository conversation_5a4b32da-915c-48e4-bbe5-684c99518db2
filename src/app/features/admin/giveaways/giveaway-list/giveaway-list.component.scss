// Giveaway list component styles

// Status badge styles
.badge {
  padding: 0.5rem 0.75rem;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  border-radius: 4px;
}

// Table styles
.table {
  th {
    font-weight: 600;
    color: var(--admin-text);
    white-space: nowrap;
  }
  
  td {
    vertical-align: middle;
  }
}

// Action buttons
.btn-group {
  .btn {
    padding: 0.375rem 0.5rem;
    
    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    }
  }
}

// Card styles
.card {
  border-radius: 16px;
  border: none;
  overflow: hidden;
  
  .card-body {
    padding: 1.5rem;
  }
}

// Filter form
.form-label {
  font-weight: 500;
  margin-bottom: 0.5rem;
}

.form-control, .form-select {
  padding: 0.75rem 1rem;
  border-radius: 8px;
  border: 1px solid var(--admin-border);
  
  &:focus {
    border-color: var(--admin-primary);
    box-shadow: 0 0 0 0.25rem rgba(var(--admin-primary-rgb), 0.25);
  }
}

// Pagination
.pagination {
  .page-link {
    color: var(--admin-primary);
    border-color: var(--admin-border);
    padding: 0.5rem 0.75rem;
    
    &:hover {
      background-color: rgba(var(--admin-primary-rgb), 0.1);
    }
  }
  
  .active .page-link {
    background-color: var(--admin-primary);
    border-color: var(--admin-primary);
  }
}
