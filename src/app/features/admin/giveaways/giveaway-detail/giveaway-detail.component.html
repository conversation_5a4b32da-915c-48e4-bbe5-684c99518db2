<div class="container-fluid p-4">
  <div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="mb-0">Giveaway Details</h1>
    <a [routerLink]="['/admin/giveaways']" class="btn btn-outline-secondary">
      <i class="fas fa-arrow-left me-2"></i> Back to Giveaways
    </a>
  </div>

  <!-- Loading Indicator -->
  <div *ngIf="loading.giveaway" class="card shadow-sm mb-4">
    <div class="card-body text-center py-5">
      <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden">Loading...</span>
      </div>
      <p class="mt-3 mb-0">Loading giveaway data...</p>
    </div>
  </div>

  <!-- Error Alert -->
  <div *ngIf="error" class="alert alert-danger alert-dismissible fade show" role="alert">
    {{ error }}
    <button type="button" class="btn-close" (click)="error = ''"></button>
  </div>

  <!-- Giveaway Details -->
  <div *ngIf="!loading.giveaway && giveaway" class="giveaway-details">
    <!-- Header Card -->
    <div class="card shadow-sm mb-4">
      <div class="card-body">
        <div class="d-flex justify-content-between align-items-start">
          <div>
            <h2 class="card-title mb-2">{{ giveaway.title }}</h2>
            <div class="d-flex align-items-center mb-3">
              <span class="badge me-3" [ngClass]="getStatusClass(giveaway.status)">
                {{ giveaway.status }}
              </span>
              <span class="text-muted">
                <i class="fas fa-calendar-alt me-1"></i> Created {{ formatDate(giveaway.createdAt) }}
              </span>
            </div>
          </div>
          <div class="action-buttons">
            <div class="btn-group">
              <a [routerLink]="['/admin/giveaways', giveaway.id, 'edit']" class="btn btn-outline-primary">
                <i class="fas fa-edit me-2"></i> Edit
              </a>
              <button *ngIf="giveaway.status === 'DRAFT'" (click)="publishGiveaway()" class="btn btn-outline-success">
                <i class="fas fa-check me-2"></i> Publish
              </button>
              <a *ngIf="canConductDraw()" [routerLink]="['/admin/giveaways', giveaway.id, 'draw']" class="btn btn-outline-info">
                <i class="fas fa-trophy me-2"></i> Conduct Draw
              </a>
              <button (click)="deleteGiveaway()" class="btn btn-outline-danger">
                <i class="fas fa-trash me-2"></i> Delete
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Tabs Navigation -->
    <ul class="nav nav-tabs mb-4">
      <li class="nav-item">
        <a class="nav-link" [class.active]="activeTab === 'overview'" (click)="setActiveTab('overview')" href="javascript:void(0)">
          <i class="fas fa-info-circle me-2"></i> Overview
        </a>
      </li>
      <li class="nav-item">
        <a class="nav-link" [class.active]="activeTab === 'prizes'" (click)="setActiveTab('prizes')" href="javascript:void(0)">
          <i class="fas fa-gift me-2"></i> Prizes
        </a>
      </li>
      <li class="nav-item">
        <a class="nav-link" [class.active]="activeTab === 'entries'" (click)="setActiveTab('entries')" href="javascript:void(0)">
          <i class="fas fa-ticket-alt me-2"></i> Entries
        </a>
      </li>
      <li class="nav-item">
        <a class="nav-link" [class.active]="activeTab === 'winners'" (click)="setActiveTab('winners')" href="javascript:void(0)">
          <i class="fas fa-trophy me-2"></i> Winners
        </a>
      </li>
    </ul>

    <!-- Tab Content -->
    <div class="tab-content">
      <!-- Overview Tab -->
      <div *ngIf="activeTab === 'overview'" class="tab-pane active">
        <div class="row">
          <!-- Giveaway Details -->
          <div class="col-lg-8">
            <div class="card shadow-sm mb-4">
              <div class="card-body">
                <h5 class="card-title mb-3">Giveaway Information</h5>
                
                <div class="row mb-3">
                  <div class="col-md-6">
                    <p class="mb-1 text-muted">Start Date</p>
                    <p class="mb-3 fw-medium">{{ formatDate(giveaway.startDate) }}</p>
                  </div>
                  <div class="col-md-6">
                    <p class="mb-1 text-muted">End Date</p>
                    <p class="mb-3 fw-medium">{{ formatDate(giveaway.endDate) }}</p>
                  </div>
                  <div class="col-md-6">
                    <p class="mb-1 text-muted">Draw Date</p>
                    <p class="mb-3 fw-medium">{{ formatDate(giveaway.drawDate) }}</p>
                  </div>
                  <div class="col-md-6">
                    <p class="mb-1 text-muted">Prize Value</p>
                    <p class="mb-3 fw-medium">{{ formatCurrency(giveaway.prizeValue) }}</p>
                  </div>
                  <div class="col-md-6">
                    <p class="mb-1 text-muted">Category</p>
                    <p class="mb-3 fw-medium">{{ giveaway.category || 'N/A' }}</p>
                  </div>
                  <div class="col-md-6">
                    <p class="mb-1 text-muted">Minimum Tier</p>
                    <p class="mb-3 fw-medium">{{ giveaway.minTier || 'None (Open to All)' }}</p>
                  </div>
                  <div class="col-md-6">
                    <p class="mb-1 text-muted">Maximum Entries per User</p>
                    <p class="mb-3 fw-medium">{{ giveaway.maxEntries }}</p>
                  </div>
                  <div class="col-md-6">
                    <p class="mb-1 text-muted">Active</p>
                    <p class="mb-3 fw-medium">
                      <span class="badge" [ngClass]="giveaway.isActive ? 'bg-success' : 'bg-danger'">
                        {{ giveaway.isActive ? 'Yes' : 'No' }}
                      </span>
                    </p>
                  </div>
                </div>
                
                <h6 class="fw-semibold mb-2">Description</h6>
                <p class="mb-4">{{ giveaway.description }}</p>
                
                <h6 class="fw-semibold mb-2">Rules</h6>
                <p class="mb-4">{{ giveaway.rules }}</p>
                
                <h6 class="fw-semibold mb-2">Terms and Conditions</h6>
                <p class="mb-0">{{ giveaway.termsAndConditions || 'No terms and conditions specified.' }}</p>
              </div>
            </div>
          </div>
          
          <!-- Sidebar -->
          <div class="col-lg-4">
            <!-- Stats Card -->
            <div class="card shadow-sm mb-4">
              <div class="card-body">
                <h5 class="card-title mb-3">Statistics</h5>
                
                <div class="stat-item mb-3">
                  <div class="d-flex justify-content-between align-items-center">
                    <span class="text-muted">Total Entries</span>
                    <span class="fw-semibold">{{ giveaway.entryCount || 0 }}</span>
                  </div>
                  <div class="progress mt-2" style="height: 6px;">
                    <div class="progress-bar bg-primary" role="progressbar" style="width: 100%"></div>
                  </div>
                </div>
                
                <div class="stat-item mb-3">
                  <div class="d-flex justify-content-between align-items-center">
                    <span class="text-muted">Total Prizes</span>
                    <span class="fw-semibold">{{ prizes.length }}</span>
                  </div>
                  <div class="progress mt-2" style="height: 6px;">
                    <div class="progress-bar bg-success" role="progressbar" style="width: 100%"></div>
                  </div>
                </div>
                
                <div class="stat-item">
                  <div class="d-flex justify-content-between align-items-center">
                    <span class="text-muted">Total Winners</span>
                    <span class="fw-semibold">{{ giveaway.status === 'COMPLETED' ? (winners.length || 0) : 'Not drawn yet' }}</span>
                  </div>
                  <div class="progress mt-2" style="height: 6px;">
                    <div class="progress-bar bg-info" role="progressbar" 
                         [style.width]="giveaway.status === 'COMPLETED' ? '100%' : '0%'"></div>
                  </div>
                </div>
              </div>
            </div>
            
            <!-- Image Card -->
            <div *ngIf="giveaway.imageUrl || giveaway.featuredImage" class="card shadow-sm mb-4">
              <div class="card-body">
                <h5 class="card-title mb-3">Images</h5>
                
                <div *ngIf="giveaway.imageUrl" class="mb-3">
                  <p class="mb-2 text-muted">Main Image</p>
                  <img [src]="giveaway.imageUrl" alt="Giveaway Image" class="img-fluid rounded">
                </div>
                
                <div *ngIf="giveaway.featuredImage">
                  <p class="mb-2 text-muted">Featured Image</p>
                  <img [src]="giveaway.featuredImage" alt="Featured Image" class="img-fluid rounded">
                </div>
              </div>
            </div>
            
            <!-- Tags Card -->
            <div *ngIf="giveaway.tags && giveaway.tags.length > 0" class="card shadow-sm">
              <div class="card-body">
                <h5 class="card-title mb-3">Tags</h5>
                
                <div class="tags">
                  <span *ngFor="let tag of giveaway.tags" class="badge bg-light text-dark me-2 mb-2">{{ tag }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Prizes Tab -->
      <div *ngIf="activeTab === 'prizes'" class="tab-pane active">
        <div class="card shadow-sm">
          <div class="card-body">
            <div class="d-flex justify-content-between align-items-center mb-4">
              <h5 class="card-title mb-0">Prizes</h5>
              <a [routerLink]="['/admin/giveaways', giveaway.id, 'edit']" class="btn btn-sm btn-primary">
                <i class="fas fa-plus me-2"></i> Add Prize
              </a>
            </div>
            
            <!-- Loading Indicator -->
            <div *ngIf="loading.prizes" class="text-center py-4">
              <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
              </div>
              <p class="mt-3 mb-0">Loading prizes...</p>
            </div>
            
            <!-- Prizes List -->
            <div *ngIf="!loading.prizes && prizes.length > 0" class="table-responsive">
              <table class="table table-hover">
                <thead>
                  <tr>
                    <th>Name</th>
                    <th>Description</th>
                    <th>Value</th>
                    <th>Quantity</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  <tr *ngFor="let prize of prizes">
                    <td>{{ prize.name }}</td>
                    <td>{{ prize.description || 'N/A' }}</td>
                    <td>{{ formatCurrency(prize.value) }}</td>
                    <td>{{ prize.quantity }}</td>
                    <td>
                      <div class="btn-group">
                        <button class="btn btn-sm btn-outline-secondary" title="Edit Prize">
                          <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-sm btn-outline-danger" title="Delete Prize">
                          <i class="fas fa-trash"></i>
                        </button>
                      </div>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
            
            <!-- No Prizes Message -->
            <div *ngIf="!loading.prizes && prizes.length === 0" class="text-center py-4">
              <p class="mb-0">No prizes have been added to this giveaway yet.</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Entries Tab -->
      <div *ngIf="activeTab === 'entries'" class="tab-pane active">
        <div class="card shadow-sm">
          <div class="card-body">
            <div class="d-flex justify-content-between align-items-center mb-4">
              <h5 class="card-title mb-0">Entries</h5>
              <button class="btn btn-sm btn-outline-primary">
                <i class="fas fa-download me-2"></i> Export Entries
              </button>
            </div>
            
            <!-- Loading Indicator -->
            <div *ngIf="loading.entries" class="text-center py-4">
              <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
              </div>
              <p class="mt-3 mb-0">Loading entries...</p>
            </div>
            
            <!-- Entries List -->
            <div *ngIf="!loading.entries && entries.length > 0" class="table-responsive">
              <table class="table table-hover">
                <thead>
                  <tr>
                    <th>Entry ID</th>
                    <th>User</th>
                    <th>Entry Date</th>
                    <th>Entry Method</th>
                    <th>Quantity</th>
                    <th>Winner</th>
                  </tr>
                </thead>
                <tbody>
                  <tr *ngFor="let entry of entries">
                    <td>{{ entry.id }}</td>
                    <td>
                      <span *ngIf="entry.user">{{ entry.user.firstName }} {{ entry.user.lastName }}</span>
                      <span *ngIf="!entry.user">{{ entry.userId }}</span>
                    </td>
                    <td>{{ formatDate(entry.entryDate) }}</td>
                    <td>{{ entry.entryMethod || 'Standard' }}</td>
                    <td>{{ entry.quantity }}</td>
                    <td>
                      <span *ngIf="entry.isWinner" class="badge bg-success">Winner</span>
                      <span *ngIf="!entry.isWinner">-</span>
                    </td>
                  </tr>
                </tbody>
              </table>
              
              <!-- Pagination -->
              <div *ngIf="totalPages > 1" class="d-flex justify-content-center mt-4">
                <nav aria-label="Entries pagination">
                  <ul class="pagination">
                    <li class="page-item" [class.disabled]="currentPage === 1">
                      <a class="page-link" href="javascript:void(0)" (click)="onPageChange(currentPage - 1)" aria-label="Previous">
                        <span aria-hidden="true">&laquo;</span>
                      </a>
                    </li>
                    <li class="page-item" *ngFor="let page of [].constructor(totalPages); let i = index" [class.active]="currentPage === i + 1">
                      <a class="page-link" href="javascript:void(0)" (click)="onPageChange(i + 1)">{{ i + 1 }}</a>
                    </li>
                    <li class="page-item" [class.disabled]="currentPage === totalPages">
                      <a class="page-link" href="javascript:void(0)" (click)="onPageChange(currentPage + 1)" aria-label="Next">
                        <span aria-hidden="true">&raquo;</span>
                      </a>
                    </li>
                  </ul>
                </nav>
              </div>
            </div>
            
            <!-- No Entries Message -->
            <div *ngIf="!loading.entries && entries.length === 0" class="text-center py-4">
              <p class="mb-0">No entries have been submitted for this giveaway yet.</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Winners Tab -->
      <div *ngIf="activeTab === 'winners'" class="tab-pane active">
        <div class="card shadow-sm">
          <div class="card-body">
            <div class="d-flex justify-content-between align-items-center mb-4">
              <h5 class="card-title mb-0">Winners</h5>
              <div>
                <button class="btn btn-sm btn-outline-primary me-2">
                  <i class="fas fa-download me-2"></i> Export Winners
                </button>
                <a *ngIf="canConductDraw()" [routerLink]="['/admin/giveaways', giveaway.id, 'draw']" class="btn btn-sm btn-primary">
                  <i class="fas fa-trophy me-2"></i> Conduct Draw
                </a>
              </div>
            </div>
            
            <!-- Loading Indicator -->
            <div *ngIf="loading.winners" class="text-center py-4">
              <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
              </div>
              <p class="mt-3 mb-0">Loading winners...</p>
            </div>
            
            <!-- Winners List -->
            <div *ngIf="!loading.winners && winners.length > 0" class="table-responsive">
              <table class="table table-hover">
                <thead>
                  <tr>
                    <th>Winner ID</th>
                    <th>User</th>
                    <th>Prize</th>
                    <th>Selection Date</th>
                    <th>Status</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  <tr *ngFor="let winner of winners">
                    <td>{{ winner.id }}</td>
                    <td>
                      <span *ngIf="winner.user">{{ winner.user.firstName }} {{ winner.user.lastName }}</span>
                      <span *ngIf="!winner.user">{{ winner.userId }}</span>
                    </td>
                    <td>
                      <span *ngIf="winner.prize">{{ winner.prize.name }}</span>
                      <span *ngIf="!winner.prize">N/A</span>
                    </td>
                    <td>{{ formatDate(winner.selectionDate) }}</td>
                    <td>
                      <span class="badge" [ngClass]="{
                        'bg-secondary': winner.status === 'SELECTED',
                        'bg-primary': winner.status === 'NOTIFIED',
                        'bg-success': winner.status === 'CLAIMED',
                        'bg-danger': winner.status === 'FORFEITED'
                      }">{{ winner.status }}</span>
                    </td>
                    <td>
                      <div class="btn-group">
                        <button class="btn btn-sm btn-outline-primary" title="View Details">
                          <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-sm btn-outline-secondary" title="Update Status">
                          <i class="fas fa-edit"></i>
                        </button>
                      </div>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
            
            <!-- No Winners Message -->
            <div *ngIf="!loading.winners && winners.length === 0" class="text-center py-4">
              <p *ngIf="giveaway.status !== 'COMPLETED'" class="mb-0">
                No winners have been selected yet. Conduct a draw to select winners.
              </p>
              <p *ngIf="giveaway.status === 'COMPLETED'" class="mb-0">
                No winners were selected for this giveaway.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
