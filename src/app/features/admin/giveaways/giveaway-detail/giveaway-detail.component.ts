import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, ActivatedRoute, Router } from '@angular/router';
import { Giveaway, GiveawayStatus, Prize, GiveawayWinner, Entry } from '../../../../core/models/giveaway.model';
import { AdminGiveawayService } from '../../services/admin-giveaway.service';
import { AdminPrizeService } from '../../services/admin-prize.service';
import { AdminEntryService } from '../../services/admin-entry.service';
import { AdminWinnerService } from '../../services/admin-winner.service';

@Component({
  selector: 'app-giveaway-detail',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule
  ],
  templateUrl: './giveaway-detail.component.html',
  styleUrls: ['./giveaway-detail.component.scss']
})
export class GiveawayDetailComponent implements OnInit {
  giveawayId: string = '';
  giveaway: Giveaway | null = null;
  prizes: Prize[] = [];
  entries: Entry[] = [];
  winners: GiveawayWinner[] = [];
  
  activeTab: 'overview' | 'prizes' | 'entries' | 'winners' = 'overview';
  
  loading = {
    giveaway: true,
    prizes: false,
    entries: false,
    winners: false
  };
  
  error = '';
  
  // Pagination for entries
  currentPage = 1;
  itemsPerPage = 10;
  totalEntries = 0;
  totalPages = 0;
  
  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private giveawayService: AdminGiveawayService,
    private prizeService: AdminPrizeService,
    private entryService: AdminEntryService,
    private winnerService: AdminWinnerService
  ) {}
  
  ngOnInit(): void {
    this.giveawayId = this.route.snapshot.paramMap.get('id') || '';
    
    if (!this.giveawayId) {
      this.error = 'Invalid giveaway ID';
      return;
    }
    
    this.loadGiveaway();
    
    // Get the active tab from query params if available
    this.route.queryParams.subscribe(params => {
      if (params['tab'] && ['overview', 'prizes', 'entries', 'winners'].includes(params['tab'])) {
        this.activeTab = params['tab'] as 'overview' | 'prizes' | 'entries' | 'winners';
        this.loadTabData();
      }
    });
  }
  
  loadGiveaway(): void {
    this.loading.giveaway = true;
    
    this.giveawayService.getGiveaway(this.giveawayId).subscribe({
      next: (giveaway) => {
        this.giveaway = giveaway;
        this.loading.giveaway = false;
        
        // Load prizes by default since they're shown in the overview
        this.loadPrizes();
      },
      error: (err) => {
        this.error = 'Failed to load giveaway. Please try again.';
        console.error('Error loading giveaway:', err);
        this.loading.giveaway = false;
      }
    });
  }
  
  loadPrizes(): void {
    this.loading.prizes = true;
    
    this.prizeService.getPrizesByGiveaway(this.giveawayId).subscribe({
      next: (prizes) => {
        this.prizes = prizes;
        this.loading.prizes = false;
      },
      error: (err) => {
        this.error = 'Failed to load prizes. Please try again.';
        console.error('Error loading prizes:', err);
        this.loading.prizes = false;
      }
    });
  }
  
  loadEntries(): void {
    this.loading.entries = true;
    
    this.entryService.getEntriesByGiveaway(this.giveawayId, this.currentPage, this.itemsPerPage).subscribe({
      next: (response) => {
        this.entries = response.entries;
        this.totalEntries = response.totalEntries;
        this.totalPages = response.meta.pages;
        this.loading.entries = false;
      },
      error: (err) => {
        this.error = 'Failed to load entries. Please try again.';
        console.error('Error loading entries:', err);
        this.loading.entries = false;
      }
    });
  }
  
  loadWinners(): void {
    this.loading.winners = true;
    
    this.winnerService.getWinnersByGiveaway(this.giveawayId).subscribe({
      next: (winners) => {
        this.winners = winners;
        this.loading.winners = false;
      },
      error: (err) => {
        this.error = 'Failed to load winners. Please try again.';
        console.error('Error loading winners:', err);
        this.loading.winners = false;
      }
    });
  }
  
  setActiveTab(tab: 'overview' | 'prizes' | 'entries' | 'winners'): void {
    this.activeTab = tab;
    
    // Update the URL query params
    this.router.navigate([], {
      relativeTo: this.route,
      queryParams: { tab },
      queryParamsHandling: 'merge'
    });
    
    this.loadTabData();
  }
  
  loadTabData(): void {
    switch (this.activeTab) {
      case 'prizes':
        if (this.prizes.length === 0) {
          this.loadPrizes();
        }
        break;
      case 'entries':
        if (this.entries.length === 0) {
          this.loadEntries();
        }
        break;
      case 'winners':
        if (this.winners.length === 0) {
          this.loadWinners();
        }
        break;
    }
  }
  
  onPageChange(page: number): void {
    this.currentPage = page;
    this.loadEntries();
  }
  
  deleteGiveaway(): void {
    if (confirm('Are you sure you want to delete this giveaway? This action cannot be undone.')) {
      this.giveawayService.deleteGiveaway(this.giveawayId).subscribe({
        next: () => {
          this.router.navigate(['/admin/giveaways']);
        },
        error: (err) => {
          this.error = 'Failed to delete giveaway. Please try again.';
          console.error('Error deleting giveaway:', err);
        }
      });
    }
  }
  
  publishGiveaway(): void {
    if (confirm('Are you sure you want to publish this giveaway? It will be visible to users.')) {
      this.giveawayService.publishGiveaway(this.giveawayId).subscribe({
        next: () => {
          this.loadGiveaway(); // Reload to get updated status
        },
        error: (err) => {
          this.error = 'Failed to publish giveaway. Please try again.';
          console.error('Error publishing giveaway:', err);
        }
      });
    }
  }
  
  getStatusClass(status: GiveawayStatus): string {
    switch (status) {
      case GiveawayStatus.DRAFT:
        return 'bg-secondary';
      case GiveawayStatus.ACTIVE:
        return 'bg-success';
      case GiveawayStatus.COMPLETED:
        return 'bg-primary';
      case GiveawayStatus.CANCELLED:
        return 'bg-danger';
      default:
        return 'bg-secondary';
    }
  }
  
  formatDate(date: Date): string {
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  }
  
  formatCurrency(value: number): string {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(value / 100); // Convert cents to dollars
  }
  
  canConductDraw(): boolean {
    if (!this.giveaway) return false;
    
    const now = new Date();
    return this.giveaway.status === GiveawayStatus.ACTIVE && now > this.giveaway.endDate;
  }
}
