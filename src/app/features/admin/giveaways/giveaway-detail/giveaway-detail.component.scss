// Giveaway detail component styles

// Card styles
.card {
  border-radius: 16px;
  border: none;
  overflow: hidden;
  margin-bottom: 1.5rem;
  
  .card-body {
    padding: 1.5rem;
  }
  
  .card-title {
    font-weight: 600;
    color: var(--admin-text);
    margin-bottom: 1rem;
  }
}

// Status badge styles
.badge {
  padding: 0.5rem 0.75rem;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  border-radius: 4px;
}

// Action buttons
.action-buttons {
  .btn-group {
    .btn {
      padding: 0.5rem 1rem;
      
      &:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
      }
    }
  }
}

// Tabs
.nav-tabs {
  border-bottom: 1px solid var(--admin-border);
  
  .nav-item {
    margin-bottom: -1px;
  }
  
  .nav-link {
    border: none;
    color: var(--admin-text-light);
    padding: 0.75rem 1.25rem;
    font-weight: 500;
    border-bottom: 2px solid transparent;
    transition: all 0.2s ease-in-out;
    
    &:hover {
      color: var(--admin-primary);
      border-bottom-color: rgba(var(--admin-primary-rgb), 0.3);
    }
    
    &.active {
      color: var(--admin-primary);
      border-bottom-color: var(--admin-primary);
      background-color: transparent;
    }
  }
}

// Table styles
.table {
  th {
    font-weight: 600;
    color: var(--admin-text);
    white-space: nowrap;
  }
  
  td {
    vertical-align: middle;
  }
}

// Stats card
.stat-item {
  .progress {
    border-radius: 10px;
    background-color: rgba(var(--admin-border-rgb), 0.3);
    
    .progress-bar {
      border-radius: 10px;
    }
  }
}

// Tags
.tags {
  .badge {
    padding: 0.5rem 0.75rem;
    font-size: 0.75rem;
    font-weight: 500;
    border-radius: 8px;
    background-color: var(--admin-hover);
    color: var(--admin-text);
    margin-right: 0.5rem;
    margin-bottom: 0.5rem;
  }
}

// Images
.img-fluid {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

// Pagination
.pagination {
  .page-link {
    color: var(--admin-primary);
    border-color: var(--admin-border);
    padding: 0.5rem 0.75rem;
    
    &:hover {
      background-color: rgba(var(--admin-primary-rgb), 0.1);
    }
  }
  
  .active .page-link {
    background-color: var(--admin-primary);
    border-color: var(--admin-primary);
  }
}
