<div class="container-fluid p-4">
  <div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="mb-0">Giveaway Statistics</h1>
    <a [routerLink]="['/admin/giveaways']" class="btn btn-outline-secondary">
      <i class="fas fa-arrow-left me-2"></i> Back to Giveaways
    </a>
  </div>

  <!-- Filters -->
  <div class="card shadow-sm mb-4">
    <div class="card-body">
      <form [formGroup]="filterForm">
        <div class="row g-3">
          <div class="col-md-4">
            <label for="dateRange" class="form-label">Date Range</label>
            <select id="dateRange" class="form-select" formControlName="dateRange">
              <option *ngFor="let option of dateRangeOptions" [value]="option.value">{{ option.label }}</option>
            </select>
          </div>
          <div class="col-md-8 d-flex align-items-end justify-content-end">
            <div class="btn-group">
              <button type="button" class="btn btn-outline-primary" (click)="generateReport(ReportFormat.CSV)" [disabled]="generatingReport">
                <i class="fas fa-file-csv me-2"></i> Export CSV
              </button>
              <button type="button" class="btn btn-outline-primary" (click)="generateReport(ReportFormat.EXCEL)" [disabled]="generatingReport">
                <i class="fas fa-file-excel me-2"></i> Export Excel
              </button>
              <button type="button" class="btn btn-outline-primary" (click)="generateReport(ReportFormat.PDF)" [disabled]="generatingReport">
                <i class="fas fa-file-pdf me-2"></i> Export PDF
              </button>
            </div>
          </div>
        </div>
      </form>
    </div>
  </div>

  <!-- Report Messages -->
  <div *ngIf="reportSuccess" class="alert alert-success alert-dismissible fade show mb-4" role="alert">
    {{ reportSuccess }}
    <button type="button" class="btn-close" (click)="reportSuccess = ''"></button>
  </div>

  <div *ngIf="reportError" class="alert alert-danger alert-dismissible fade show mb-4" role="alert">
    {{ reportError }}
    <button type="button" class="btn-close" (click)="reportError = ''"></button>
  </div>

  <!-- Error Alert -->
  <div *ngIf="error" class="alert alert-danger alert-dismissible fade show mb-4" role="alert">
    {{ error }}
    <button type="button" class="btn-close" (click)="error = ''"></button>
  </div>

  <!-- Loading Indicator -->
  <div *ngIf="loading" class="card shadow-sm mb-4">
    <div class="card-body text-center py-5">
      <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden">Loading...</span>
      </div>
      <p class="mt-3 mb-0">Loading statistics...</p>
    </div>
  </div>

  <!-- Statistics Dashboard -->
  <div *ngIf="!loading && stats" class="statistics-dashboard">
    <!-- Summary Cards -->
    <div class="row g-4 mb-4">
      <!-- Total Giveaways -->
      <div class="col-xl-3 col-md-6">
        <div class="card card-stats shadow-sm h-100">
          <div class="card-body">
            <div class="d-flex justify-content-between align-items-center">
              <div>
                <h6 class="text-muted mb-1">Total Giveaways</h6>
                <h3 class="mb-0 fw-bold">{{ stats.totalGiveaways }}</h3>
              </div>
              <div class="icon-circle bg-primary-light text-primary">
                <i class="fas fa-gift"></i>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Active Giveaways -->
      <div class="col-xl-3 col-md-6">
        <div class="card card-stats shadow-sm h-100">
          <div class="card-body">
            <div class="d-flex justify-content-between align-items-center">
              <div>
                <h6 class="text-muted mb-1">Active Giveaways</h6>
                <h3 class="mb-0 fw-bold">{{ stats.activeGiveaways }}</h3>
              </div>
              <div class="icon-circle bg-success-light text-success">
                <i class="fas fa-check-circle"></i>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Total Entries -->
      <div class="col-xl-3 col-md-6">
        <div class="card card-stats shadow-sm h-100">
          <div class="card-body">
            <div class="d-flex justify-content-between align-items-center">
              <div>
                <h6 class="text-muted mb-1">Total Entries</h6>
                <h3 class="mb-0 fw-bold">{{ stats.totalEntries }}</h3>
              </div>
              <div class="icon-circle bg-info-light text-info">
                <i class="fas fa-ticket-alt"></i>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Total Winners -->
      <div class="col-xl-3 col-md-6">
        <div class="card card-stats shadow-sm h-100">
          <div class="card-body">
            <div class="d-flex justify-content-between align-items-center">
              <div>
                <h6 class="text-muted mb-1">Total Winners</h6>
                <h3 class="mb-0 fw-bold">{{ stats.totalWinners }}</h3>
              </div>
              <div class="icon-circle bg-warning-light text-warning">
                <i class="fas fa-trophy"></i>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Status Distribution -->
    <div class="row mb-4">
      <div class="col-12">
        <div class="card shadow-sm">
          <div class="card-body">
            <h5 class="card-title mb-4">Giveaway Status Distribution</h5>

            <!-- Status Bars -->
            <div class="status-bars">
              <!-- Draft -->
              <div class="status-item mb-3">
                <div class="d-flex justify-content-between align-items-center mb-1">
                  <span class="status-label">
                    <span class="status-dot bg-secondary"></span>
                    Draft
                  </span>
                  <span class="status-value">
                    {{ getStatusCount(GiveawayStatus.DRAFT) }} ({{ getStatusPercentage(GiveawayStatus.DRAFT) | number:'1.0-0' }}%)
                  </span>
                </div>
                <div class="progress" style="height: 8px;">
                  <div class="progress-bar bg-secondary" role="progressbar"
                       [style.width]="getStatusPercentage(GiveawayStatus.DRAFT) + '%'"></div>
                </div>
              </div>

              <!-- Active -->
              <div class="status-item mb-3">
                <div class="d-flex justify-content-between align-items-center mb-1">
                  <span class="status-label">
                    <span class="status-dot bg-success"></span>
                    Active
                  </span>
                  <span class="status-value">
                    {{ getStatusCount(GiveawayStatus.ACTIVE) }} ({{ getStatusPercentage(GiveawayStatus.ACTIVE) | number:'1.0-0' }}%)
                  </span>
                </div>
                <div class="progress" style="height: 8px;">
                  <div class="progress-bar bg-success" role="progressbar"
                       [style.width]="getStatusPercentage(GiveawayStatus.ACTIVE) + '%'"></div>
                </div>
              </div>

              <!-- Completed -->
              <div class="status-item mb-3">
                <div class="d-flex justify-content-between align-items-center mb-1">
                  <span class="status-label">
                    <span class="status-dot bg-primary"></span>
                    Completed
                  </span>
                  <span class="status-value">
                    {{ getStatusCount(GiveawayStatus.COMPLETED) }} ({{ getStatusPercentage(GiveawayStatus.COMPLETED) | number:'1.0-0' }}%)
                  </span>
                </div>
                <div class="progress" style="height: 8px;">
                  <div class="progress-bar bg-primary" role="progressbar"
                       [style.width]="getStatusPercentage(GiveawayStatus.COMPLETED) + '%'"></div>
                </div>
              </div>

              <!-- Cancelled -->
              <div class="status-item">
                <div class="d-flex justify-content-between align-items-center mb-1">
                  <span class="status-label">
                    <span class="status-dot bg-danger"></span>
                    Cancelled
                  </span>
                  <span class="status-value">
                    {{ getStatusCount(GiveawayStatus.CANCELLED) }} ({{ getStatusPercentage(GiveawayStatus.CANCELLED) | number:'1.0-0' }}%)
                  </span>
                </div>
                <div class="progress" style="height: 8px;">
                  <div class="progress-bar bg-danger" role="progressbar"
                       [style.width]="getStatusPercentage(GiveawayStatus.CANCELLED) + '%'"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Additional Statistics -->
    <div class="row">
      <!-- Entries vs Winners -->
      <div class="col-md-6 mb-4">
        <div class="card shadow-sm h-100">
          <div class="card-body">
            <h5 class="card-title mb-4">Entries vs Winners</h5>

            <div class="comparison-chart">
              <div class="d-flex justify-content-between mb-3">
                <div class="comparison-item">
                  <div class="comparison-value">{{ stats.totalEntries }}</div>
                  <div class="comparison-label text-muted">Total Entries</div>
                </div>
                <div class="comparison-item">
                  <div class="comparison-value">{{ stats.totalWinners }}</div>
                  <div class="comparison-label text-muted">Total Winners</div>
                </div>
                <div class="comparison-item">
                  <div class="comparison-value">
                    {{ stats.totalEntries > 0 ? (stats.totalWinners / stats.totalEntries * 100 | number:'1.0-2') : 0 }}%
                  </div>
                  <div class="comparison-label text-muted">Win Rate</div>
                </div>
              </div>

              <div class="progress" style="height: 20px;">
                <div class="progress-bar bg-info" role="progressbar"
                     [style.width]="stats.totalEntries > 0 ? (stats.totalWinners / stats.totalEntries * 100) + '%' : '0%'">
                  {{ stats.totalEntries > 0 ? (stats.totalWinners / stats.totalEntries * 100 | number:'1.0-2') : 0 }}%
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Giveaway Completion Rate -->
      <div class="col-md-6 mb-4">
        <div class="card shadow-sm h-100">
          <div class="card-body">
            <h5 class="card-title mb-4">Giveaway Completion Rate</h5>

            <div class="completion-chart">
              <div class="d-flex justify-content-between mb-3">
                <div class="comparison-item">
                  <div class="comparison-value">{{ stats.completedGiveaways }}</div>
                  <div class="comparison-label text-muted">Completed</div>
                </div>
                <div class="comparison-item">
                  <div class="comparison-value">{{ stats.totalGiveaways }}</div>
                  <div class="comparison-label text-muted">Total</div>
                </div>
                <div class="comparison-item">
                  <div class="comparison-value">
                    {{ stats.totalGiveaways > 0 ? (stats.completedGiveaways / stats.totalGiveaways * 100 | number:'1.0-2') : 0 }}%
                  </div>
                  <div class="comparison-label text-muted">Completion Rate</div>
                </div>
              </div>

              <div class="progress" style="height: 20px;">
                <div class="progress-bar bg-primary" role="progressbar"
                     [style.width]="stats.totalGiveaways > 0 ? (stats.completedGiveaways / stats.totalGiveaways * 100) + '%' : '0%'">
                  {{ stats.totalGiveaways > 0 ? (stats.completedGiveaways / stats.totalGiveaways * 100 | number:'1.0-2') : 0 }}%
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
