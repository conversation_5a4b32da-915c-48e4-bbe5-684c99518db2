import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { FormBuilder, FormGroup, ReactiveFormsModule } from '@angular/forms';
import { GiveawayStatus } from '../../../../core/models/giveaway.model';
import { AdminGiveawayService } from '../../services/admin-giveaway.service';
import { AdminReportService, ReportFormat, ReportType } from '../../services/admin-report.service';

@Component({
  selector: 'app-giveaway-statistics',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    ReactiveFormsModule
  ],
  templateUrl: './giveaway-statistics.component.html',
  styleUrls: ['./giveaway-statistics.component.scss']
})
export class GiveawayStatisticsComponent implements OnInit {
  // Expose enums to the template
  GiveawayStatus = GiveawayStatus;
  ReportFormat = ReportFormat;

  filterForm: FormGroup;
  loading = true;
  error = '';

  // Statistics data
  stats: {
    totalGiveaways: number;
    activeGiveaways: number;
    completedGiveaways: number;
    giveawaysByStatus: Record<GiveawayStatus, number>;
    totalEntries: number;
    totalWinners: number;
  } | null = null;

  // Report generation
  generatingReport = false;
  reportSuccess = '';
  reportError = '';

  // Date range options
  dateRangeOptions = [
    { value: 'day', label: 'Today' },
    { value: 'week', label: 'This Week' },
    { value: 'month', label: 'This Month' },
    { value: 'year', label: 'This Year' },
    { value: 'all', label: 'All Time' }
  ];

  constructor(
    private fb: FormBuilder,
    private giveawayService: AdminGiveawayService,
    private reportService: AdminReportService
  ) {
    this.filterForm = this.fb.group({
      dateRange: ['month']
    });
  }

  ngOnInit(): void {
    this.loadStatistics();

    // Listen for filter changes
    this.filterForm.get('dateRange')?.valueChanges.subscribe(() => {
      this.loadStatistics();
    });
  }

  loadStatistics(): void {
    this.loading = true;

    const dateRange = this.filterForm.get('dateRange')?.value;

    this.giveawayService.getGiveawayStatistics(undefined, undefined, dateRange).subscribe({
      next: (stats) => {
        this.stats = stats;
        this.loading = false;
      },
      error: (err) => {
        this.error = 'Failed to load statistics. Please try again.';
        console.error('Error loading statistics:', err);
        this.loading = false;
      }
    });
  }

  generateReport(format: ReportFormat): void {
    this.generatingReport = true;
    this.reportSuccess = '';
    this.reportError = '';

    const dateRange = this.filterForm.get('dateRange')?.value;

    this.reportService.generateGiveawayReport(format, undefined, undefined, undefined).subscribe({
      next: (response) => {
        this.generatingReport = false;
        this.reportSuccess = 'Report generated successfully!';

        // Open the report URL in a new tab
        window.open(response.url, '_blank');
      },
      error: (err) => {
        this.generatingReport = false;
        this.reportError = 'Failed to generate report. Please try again.';
        console.error('Error generating report:', err);
      }
    });
  }

  getStatusPercentage(status: GiveawayStatus): number {
    if (!this.stats || this.stats.totalGiveaways === 0) {
      return 0;
    }

    return (this.stats.giveawaysByStatus[status] / this.stats.totalGiveaways) * 100;
  }

  getStatusCount(status: GiveawayStatus): number {
    if (!this.stats) {
      return 0;
    }

    return this.stats.giveawaysByStatus[status] || 0;
  }
}
