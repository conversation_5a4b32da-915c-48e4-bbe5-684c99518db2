// Giveaway statistics component styles

// Card styles
.card {
  border-radius: 16px;
  border: none;
  overflow: hidden;
  
  .card-body {
    padding: 1.5rem;
  }
  
  .card-title {
    font-weight: 600;
    color: var(--admin-text);
    margin-bottom: 1rem;
  }
}

// Stats card
.card-stats {
  transition: all 0.3s ease;
  
  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1) !important;
  }
  
  .icon-circle {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    
    i {
      font-size: 1.5rem;
    }
  }
}

// Background colors for icon circles
.bg-primary-light {
  background-color: rgba(var(--admin-primary-rgb), 0.15);
}

.bg-success-light {
  background-color: rgba(var(--bs-success-rgb), 0.15);
}

.bg-info-light {
  background-color: rgba(var(--bs-info-rgb), 0.15);
}

.bg-warning-light {
  background-color: rgba(var(--bs-warning-rgb), 0.15);
}

.bg-danger-light {
  background-color: rgba(var(--bs-danger-rgb), 0.15);
}

// Status bars
.status-bars {
  .status-item {
    margin-bottom: 1.5rem;
    
    .status-label {
      display: flex;
      align-items: center;
      font-weight: 500;
      
      .status-dot {
        width: 10px;
        height: 10px;
        border-radius: 50%;
        margin-right: 0.5rem;
      }
    }
    
    .status-value {
      font-weight: 600;
    }
  }
  
  .progress {
    border-radius: 10px;
    background-color: rgba(var(--admin-border-rgb), 0.3);
    
    .progress-bar {
      border-radius: 10px;
    }
  }
}

// Comparison chart
.comparison-chart, .completion-chart {
  .comparison-item {
    text-align: center;
    
    .comparison-value {
      font-size: 1.5rem;
      font-weight: 700;
      color: var(--admin-text);
      margin-bottom: 0.25rem;
    }
    
    .comparison-label {
      font-size: 0.875rem;
    }
  }
  
  .progress {
    border-radius: 10px;
    background-color: rgba(var(--admin-border-rgb), 0.3);
    
    .progress-bar {
      border-radius: 10px;
      font-weight: 600;
      font-size: 0.875rem;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}

// Form styles
.form-label {
  font-weight: 500;
  margin-bottom: 0.5rem;
}

.form-select {
  padding: 0.75rem 1rem;
  border-radius: 8px;
  border: 1px solid var(--admin-border);
  
  &:focus {
    border-color: var(--admin-primary);
    box-shadow: 0 0 0 0.25rem rgba(var(--admin-primary-rgb), 0.25);
  }
}

// Button styles
.btn {
  padding: 0.75rem 1.5rem;
  font-weight: 500;
  border-radius: 8px;
  transition: all 0.2s ease-in-out;
  
  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  }
  
  &:active {
    transform: translateY(0);
  }
  
  &:disabled {
    opacity: 0.7;
    cursor: not-allowed;
    transform: none !important;
    box-shadow: none !important;
  }
}

.btn-group {
  .btn {
    padding: 0.5rem 1rem;
  }
}
