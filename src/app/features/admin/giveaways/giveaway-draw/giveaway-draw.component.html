<div class="container-fluid p-4">
  <div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="mb-0">Conduct Draw</h1>
    <a [routerLink]="['/admin/giveaways', giveawayId]" class="btn btn-outline-secondary">
      <i class="fas fa-arrow-left me-2"></i> Back to Giveaway
    </a>
  </div>

  <!-- Loading Indicator -->
  <div *ngIf="loading.giveaway" class="card shadow-sm mb-4">
    <div class="card-body text-center py-5">
      <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden">Loading...</span>
      </div>
      <p class="mt-3 mb-0">Loading giveaway data...</p>
    </div>
  </div>

  <!-- Error Alert -->
  <div *ngIf="error" class="alert alert-danger alert-dismissible fade show" role="alert">
    {{ error }}
    <button type="button" class="btn-close" (click)="error = ''"></button>
  </div>

  <!-- Success Alert -->
  <div *ngIf="success" class="alert alert-success alert-dismissible fade show" role="alert">
    {{ success }}
    <button type="button" class="btn-close" (click)="success = ''"></button>
  </div>

  <!-- Giveaway Info -->
  <div *ngIf="!loading.giveaway && giveaway" class="card shadow-sm mb-4">
    <div class="card-body">
      <h5 class="card-title mb-3">Giveaway Information</h5>
      <div class="row">
        <div class="col-md-6">
          <p class="mb-1 text-muted">Title</p>
          <p class="mb-3 fw-medium">{{ giveaway.title }}</p>
        </div>
        <div class="col-md-6">
          <p class="mb-1 text-muted">Status</p>
          <p class="mb-3 fw-medium">{{ giveaway.status }}</p>
        </div>
        <div class="col-md-6">
          <p class="mb-1 text-muted">Start Date</p>
          <p class="mb-3 fw-medium">{{ formatDate(giveaway.startDate) }}</p>
        </div>
        <div class="col-md-6">
          <p class="mb-1 text-muted">End Date</p>
          <p class="mb-3 fw-medium">{{ formatDate(giveaway.endDate) }}</p>
        </div>
        <div class="col-md-6">
          <p class="mb-1 text-muted">Draw Date</p>
          <p class="mb-3 fw-medium">{{ formatDate(giveaway.drawDate) }}</p>
        </div>
        <div class="col-md-6">
          <p class="mb-1 text-muted">Prize Value</p>
          <p class="mb-3 fw-medium">{{ formatCurrency(giveaway.prizeValue) }}</p>
        </div>
        <div class="col-md-6">
          <p class="mb-1 text-muted">Total Entries</p>
          <p class="mb-0 fw-medium">
            <span *ngIf="loading.entries">Loading...</span>
            <span *ngIf="!loading.entries">{{ totalEntries }}</span>
          </p>
        </div>
      </div>
    </div>
  </div>

  <!-- Draw Form -->
  <div *ngIf="!loading.giveaway && giveaway && !drawResult" class="card shadow-sm mb-4">
    <div class="card-body">
      <h5 class="card-title mb-3">Winner Selection</h5>
      
      <form [formGroup]="drawForm" (ngSubmit)="conductDraw()">
        <div class="row mb-4">
          <div class="col-md-6">
            <label for="numberOfWinners" class="form-label">Number of Winners <span class="text-danger">*</span></label>
            <input type="number" id="numberOfWinners" class="form-control" formControlName="numberOfWinners"
              [ngClass]="{'is-invalid': isFieldInvalid('numberOfWinners')}">
            <div *ngIf="isFieldInvalid('numberOfWinners')" class="invalid-feedback">
              {{ getFieldError('numberOfWinners') }}
            </div>
            <small class="form-text text-muted">Maximum: {{ totalEntries }} (total entries)</small>
          </div>
        </div>
        
        <div class="alert alert-warning mb-4">
          <i class="fas fa-exclamation-triangle me-2"></i>
          <strong>Warning:</strong> Conducting a draw is a final action. Once winners are selected, the giveaway status will be changed to COMPLETED and no further entries will be accepted.
        </div>
        
        <div class="d-flex justify-content-end">
          <button type="submit" class="btn btn-primary" [disabled]="loading.draw || totalEntries === 0">
            <i class="fas fa-trophy me-2"></i>
            <span *ngIf="!loading.draw">Conduct Draw</span>
            <span *ngIf="loading.draw">Processing...</span>
          </button>
        </div>
      </form>
    </div>
  </div>

  <!-- Draw Results -->
  <div *ngIf="drawResult" class="card shadow-sm mb-4">
    <div class="card-body">
      <h5 class="card-title mb-3">Draw Results</h5>
      
      <div class="alert alert-success mb-4">
        <i class="fas fa-check-circle me-2"></i>
        <strong>Success!</strong> The draw has been completed and winners have been selected.
      </div>
      
      <h6 class="fw-semibold mb-3">Selected Winners</h6>
      
      <div class="table-responsive">
        <table class="table table-hover">
          <thead>
            <tr>
              <th>Winner</th>
              <th>Prize</th>
              <th>Selection Date</th>
              <th>Status</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let winner of drawResult.winners">
              <td>
                <span *ngIf="winner.user">{{ winner.user.firstName }} {{ winner.user.lastName }}</span>
                <span *ngIf="!winner.user">{{ winner.userId }}</span>
              </td>
              <td>
                <span *ngIf="winner.prize">{{ winner.prize.name }}</span>
                <span *ngIf="!winner.prize">N/A</span>
              </td>
              <td>{{ formatDate(winner.selectionDate) }}</td>
              <td>
                <span class="badge bg-secondary">{{ winner.status }}</span>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
      
      <div class="d-flex justify-content-end gap-2 mt-4">
        <a [routerLink]="['/admin/giveaways', giveawayId]" [queryParams]="{tab: 'winners'}" class="btn btn-primary">
          <i class="fas fa-list me-2"></i> View All Winners
        </a>
      </div>
    </div>
  </div>
  
  <!-- No Entries Message -->
  <div *ngIf="!loading.giveaway && !loading.entries && totalEntries === 0 && !drawResult" class="card shadow-sm">
    <div class="card-body text-center py-5">
      <i class="fas fa-exclamation-circle text-warning fa-3x mb-3"></i>
      <h5>No Entries Available</h5>
      <p class="mb-0">This giveaway has no entries. A draw cannot be conducted without entries.</p>
    </div>
  </div>
</div>
