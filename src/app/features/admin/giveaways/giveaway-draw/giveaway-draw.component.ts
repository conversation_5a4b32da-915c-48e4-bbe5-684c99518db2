import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, ActivatedRoute, Router } from '@angular/router';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { Giveaway, GiveawayWinner, WinnerSelectionResultDto } from '../../../../core/models/giveaway.model';
import { AdminGiveawayService } from '../../services/admin-giveaway.service';
import { AdminPrizeService } from '../../services/admin-prize.service';
import { AdminEntryService } from '../../services/admin-entry.service';

@Component({
  selector: 'app-giveaway-draw',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    ReactiveFormsModule
  ],
  templateUrl: './giveaway-draw.component.html',
  styleUrls: ['./giveaway-draw.component.scss']
})
export class GiveawayDrawComponent implements OnInit {
  giveawayId: string = '';
  giveaway: Giveaway | null = null;
  drawForm: FormGroup;
  
  loading = {
    giveaway: true,
    entries: false,
    draw: false
  };
  
  error = '';
  success = '';
  
  totalEntries = 0;
  drawResult: WinnerSelectionResultDto | null = null;
  
  constructor(
    private fb: FormBuilder,
    private route: ActivatedRoute,
    private router: Router,
    private giveawayService: AdminGiveawayService,
    private prizeService: AdminPrizeService,
    private entryService: AdminEntryService
  ) {
    this.drawForm = this.fb.group({
      numberOfWinners: [1, [Validators.required, Validators.min(1)]]
    });
  }
  
  ngOnInit(): void {
    this.giveawayId = this.route.snapshot.paramMap.get('id') || '';
    
    if (!this.giveawayId) {
      this.error = 'Invalid giveaway ID';
      return;
    }
    
    this.loadGiveaway();
  }
  
  loadGiveaway(): void {
    this.loading.giveaway = true;
    
    this.giveawayService.getGiveaway(this.giveawayId).subscribe({
      next: (giveaway) => {
        this.giveaway = giveaway;
        this.loading.giveaway = false;
        this.loadEntryCount();
      },
      error: (err) => {
        this.error = 'Failed to load giveaway. Please try again.';
        console.error('Error loading giveaway:', err);
        this.loading.giveaway = false;
      }
    });
  }
  
  loadEntryCount(): void {
    this.loading.entries = true;
    
    this.entryService.getEntryCount(this.giveawayId).subscribe({
      next: (count) => {
        this.totalEntries = count;
        this.loading.entries = false;
        
        // Set max winners to total entries
        this.drawForm.get('numberOfWinners')?.setValidators([
          Validators.required,
          Validators.min(1),
          Validators.max(this.totalEntries)
        ]);
        this.drawForm.get('numberOfWinners')?.updateValueAndValidity();
      },
      error: (err) => {
        this.error = 'Failed to load entry count. Please try again.';
        console.error('Error loading entry count:', err);
        this.loading.entries = false;
      }
    });
  }
  
  conductDraw(): void {
    if (this.drawForm.invalid) {
      // Mark all fields as touched to trigger validation messages
      Object.keys(this.drawForm.controls).forEach(key => {
        const control = this.drawForm.get(key);
        control?.markAsTouched();
      });
      return;
    }
    
    if (!confirm('Are you sure you want to conduct the draw? This action cannot be undone.')) {
      return;
    }
    
    this.loading.draw = true;
    
    const numberOfWinners = this.drawForm.get('numberOfWinners')?.value;
    
    this.giveawayService.conductDraw({
      giveawayId: this.giveawayId,
      numberOfWinners
    }).subscribe({
      next: (result) => {
        this.drawResult = result;
        this.success = `Successfully selected ${result.winners.length} winner(s)!`;
        this.loading.draw = false;
      },
      error: (err) => {
        this.error = 'Failed to conduct draw. Please try again.';
        console.error('Error conducting draw:', err);
        this.loading.draw = false;
      }
    });
  }
  
  formatDate(date: Date): string {
    return new Date(date).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  }
  
  formatCurrency(value: number): string {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(value / 100); // Convert cents to dollars
  }
  
  // Validation helpers
  isFieldInvalid(fieldName: string): boolean {
    const control = this.drawForm.get(fieldName);
    return !!control && control.invalid && (control.dirty || control.touched);
  }
  
  getFieldError(fieldName: string): string {
    const control = this.drawForm.get(fieldName);
    
    if (!control || !control.errors) {
      return '';
    }
    
    if (control.errors['required']) {
      return 'This field is required';
    }
    
    if (control.errors['min']) {
      return `Value must be at least ${control.errors['min'].min}`;
    }
    
    if (control.errors['max']) {
      return `Value cannot exceed ${control.errors['max'].max}`;
    }
    
    return 'Invalid value';
  }
}
