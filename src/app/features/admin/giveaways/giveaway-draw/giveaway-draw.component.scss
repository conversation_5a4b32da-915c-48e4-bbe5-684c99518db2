// Giveaway draw component styles

// Card styles
.card {
  border-radius: 16px;
  border: none;
  overflow: hidden;
  
  .card-body {
    padding: 1.5rem;
  }
  
  .card-title {
    font-weight: 600;
    color: var(--admin-text);
    margin-bottom: 1rem;
  }
}

// Form styles
.form-label {
  font-weight: 500;
  margin-bottom: 0.5rem;
}

.form-control {
  padding: 0.75rem 1rem;
  border-radius: 8px;
  border: 1px solid var(--admin-border);
  
  &:focus {
    border-color: var(--admin-primary);
    box-shadow: 0 0 0 0.25rem rgba(var(--admin-primary-rgb), 0.25);
  }
  
  &.is-invalid {
    border-color: var(--bs-danger);
    
    &:focus {
      box-shadow: 0 0 0 0.25rem rgba(var(--bs-danger-rgb), 0.25);
    }
  }
}

// Alert styles
.alert {
  border-radius: 8px;
  border: none;
  padding: 1rem 1.25rem;
  
  &.alert-warning {
    background-color: rgba(var(--bs-warning-rgb), 0.15);
    color: var(--bs-warning-text);
  }
  
  &.alert-success {
    background-color: rgba(var(--bs-success-rgb), 0.15);
    color: var(--bs-success-text);
  }
  
  &.alert-danger {
    background-color: rgba(var(--bs-danger-rgb), 0.15);
    color: var(--bs-danger-text);
  }
}

// Table styles
.table {
  th {
    font-weight: 600;
    color: var(--admin-text);
    white-space: nowrap;
  }
  
  td {
    vertical-align: middle;
  }
}

// Badge styles
.badge {
  padding: 0.5rem 0.75rem;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  border-radius: 4px;
}

// Button styles
.btn {
  padding: 0.75rem 1.5rem;
  font-weight: 500;
  border-radius: 8px;
  transition: all 0.2s ease-in-out;
  
  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  }
  
  &:active {
    transform: translateY(0);
  }
  
  &:disabled {
    opacity: 0.7;
    cursor: not-allowed;
    transform: none !important;
    box-shadow: none !important;
  }
}

.btn-primary {
  background-color: var(--admin-primary);
  border-color: var(--admin-primary);
  
  &:hover, &:focus {
    background-color: var(--admin-primary-dark);
    border-color: var(--admin-primary-dark);
  }
}

.btn-outline-secondary {
  color: var(--admin-text);
  border-color: var(--admin-border);
  
  &:hover, &:focus {
    background-color: var(--admin-hover);
    color: var(--admin-text);
    border-color: var(--admin-border);
  }
}
