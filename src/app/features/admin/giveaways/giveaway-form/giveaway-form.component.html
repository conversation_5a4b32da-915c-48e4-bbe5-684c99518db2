<div class="container-fluid p-4">
  <div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="mb-0">{{ isEditMode ? 'Edit' : 'Create' }} Giveaway</h1>
    <a [routerLink]="['/admin/giveaways']" class="btn btn-outline-secondary">
      <i class="fas fa-arrow-left me-2"></i> Back to Giveaways
    </a>
  </div>

  <!-- Loading Indicator -->
  <div *ngIf="loading" class="card shadow-sm mb-4">
    <div class="card-body text-center py-5">
      <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden">Loading...</span>
      </div>
      <p class="mt-3 mb-0">Loading giveaway data...</p>
    </div>
  </div>

  <!-- Error Alert -->
  <div *ngIf="error" class="alert alert-danger alert-dismissible fade show" role="alert">
    {{ error }}
    <button type="button" class="btn-close" (click)="error = ''"></button>
  </div>

  <!-- Success Alert -->
  <div *ngIf="success" class="alert alert-success alert-dismissible fade show" role="alert">
    {{ success }}
    <button type="button" class="btn-close" (click)="success = ''"></button>
  </div>

  <!-- Giveaway Form -->
  <div *ngIf="!loading" class="card shadow-sm">
    <div class="card-body">
      <form [formGroup]="giveawayForm" (ngSubmit)="onSubmit()">
        <!-- Basic Information -->
        <div class="mb-4">
          <h5 class="card-title mb-3">Basic Information</h5>
          <div class="row g-3">
            <div class="col-md-6">
              <label for="title" class="form-label">Title <span class="text-danger">*</span></label>
              <input type="text" id="title" class="form-control" formControlName="title" 
                [ngClass]="{'is-invalid': isFieldInvalid('title')}">
              <div *ngIf="isFieldInvalid('title')" class="invalid-feedback">
                {{ getFieldError('title') }}
              </div>
            </div>
            <div class="col-md-3">
              <label for="status" class="form-label">Status <span class="text-danger">*</span></label>
              <select id="status" class="form-select" formControlName="status"
                [ngClass]="{'is-invalid': isFieldInvalid('status')}">
                <option *ngFor="let option of statusOptions" [value]="option.value">{{ option.label }}</option>
              </select>
              <div *ngIf="isFieldInvalid('status')" class="invalid-feedback">
                {{ getFieldError('status') }}
              </div>
            </div>
            <div class="col-md-3">
              <label for="category" class="form-label">Category</label>
              <input type="text" id="category" class="form-control" formControlName="category">
            </div>
            <div class="col-12">
              <label for="description" class="form-label">Description <span class="text-danger">*</span></label>
              <textarea id="description" class="form-control" rows="4" formControlName="description"
                [ngClass]="{'is-invalid': isFieldInvalid('description')}"></textarea>
              <div *ngIf="isFieldInvalid('description')" class="invalid-feedback">
                {{ getFieldError('description') }}
              </div>
            </div>
            <div class="col-md-6">
              <label for="imageUrl" class="form-label">Image URL</label>
              <input type="text" id="imageUrl" class="form-control" formControlName="imageUrl">
            </div>
            <div class="col-md-6">
              <label for="featuredImage" class="form-label">Featured Image URL</label>
              <input type="text" id="featuredImage" class="form-control" formControlName="featuredImage">
            </div>
            <div class="col-12">
              <label for="tags" class="form-label">Tags (comma-separated)</label>
              <input type="text" id="tags" class="form-control" formControlName="tags" 
                placeholder="e.g. premium, electronics, exclusive">
            </div>
          </div>
        </div>

        <!-- Dates -->
        <div class="mb-4">
          <h5 class="card-title mb-3">Dates</h5>
          <div class="row g-3">
            <div class="col-md-4">
              <label for="startDate" class="form-label">Start Date <span class="text-danger">*</span></label>
              <input type="datetime-local" id="startDate" class="form-control" formControlName="startDate"
                [ngClass]="{'is-invalid': isFieldInvalid('startDate')}">
              <div *ngIf="isFieldInvalid('startDate')" class="invalid-feedback">
                {{ getFieldError('startDate') }}
              </div>
            </div>
            <div class="col-md-4">
              <label for="endDate" class="form-label">End Date <span class="text-danger">*</span></label>
              <input type="datetime-local" id="endDate" class="form-control" formControlName="endDate"
                [ngClass]="{'is-invalid': isFieldInvalid('endDate')}">
              <div *ngIf="isFieldInvalid('endDate')" class="invalid-feedback">
                {{ getFieldError('endDate') }}
              </div>
            </div>
            <div class="col-md-4">
              <label for="drawDate" class="form-label">Draw Date <span class="text-danger">*</span></label>
              <input type="datetime-local" id="drawDate" class="form-control" formControlName="drawDate"
                [ngClass]="{'is-invalid': isFieldInvalid('drawDate')}">
              <div *ngIf="isFieldInvalid('drawDate')" class="invalid-feedback">
                {{ getFieldError('drawDate') }}
              </div>
            </div>
          </div>
        </div>

        <!-- Prize Information -->
        <div class="mb-4">
          <h5 class="card-title mb-3">Prize Information</h5>
          <div class="row g-3">
            <div class="col-md-6">
              <label for="prizeValue" class="form-label">Prize Value (in cents) <span class="text-danger">*</span></label>
              <input type="number" id="prizeValue" class="form-control" formControlName="prizeValue"
                [ngClass]="{'is-invalid': isFieldInvalid('prizeValue')}">
              <div *ngIf="isFieldInvalid('prizeValue')" class="invalid-feedback">
                {{ getFieldError('prizeValue') }}
              </div>
              <small class="form-text text-muted">Enter value in cents (e.g. 10000 for $100.00)</small>
            </div>
            <div class="col-md-6">
              <label for="prizeDetails" class="form-label">Prize Details</label>
              <textarea id="prizeDetails" class="form-control" rows="3" formControlName="prizeDetails"></textarea>
            </div>
          </div>
        </div>

        <!-- Rules and Terms -->
        <div class="mb-4">
          <h5 class="card-title mb-3">Rules and Terms</h5>
          <div class="row g-3">
            <div class="col-12">
              <label for="rules" class="form-label">Rules <span class="text-danger">*</span></label>
              <textarea id="rules" class="form-control" rows="4" formControlName="rules"
                [ngClass]="{'is-invalid': isFieldInvalid('rules')}"></textarea>
              <div *ngIf="isFieldInvalid('rules')" class="invalid-feedback">
                {{ getFieldError('rules') }}
              </div>
            </div>
            <div class="col-12">
              <label for="termsAndConditions" class="form-label">Terms and Conditions</label>
              <textarea id="termsAndConditions" class="form-control" rows="4" formControlName="termsAndConditions"></textarea>
            </div>
          </div>
        </div>

        <!-- Entry Settings -->
        <div class="mb-4">
          <h5 class="card-title mb-3">Entry Settings</h5>
          <div class="row g-3">
            <div class="col-md-6">
              <label for="minTier" class="form-label">Minimum Membership Tier</label>
              <input type="text" id="minTier" class="form-control" formControlName="minTier">
              <small class="form-text text-muted">Leave empty to allow all tiers</small>
            </div>
            <div class="col-md-6">
              <label for="maxEntries" class="form-label">Maximum Entries per User <span class="text-danger">*</span></label>
              <input type="number" id="maxEntries" class="form-control" formControlName="maxEntries"
                [ngClass]="{'is-invalid': isFieldInvalid('maxEntries')}">
              <div *ngIf="isFieldInvalid('maxEntries')" class="invalid-feedback">
                {{ getFieldError('maxEntries') }}
              </div>
            </div>
            <div class="col-md-6">
              <div class="form-check form-switch mt-4">
                <input class="form-check-input" type="checkbox" id="isActive" formControlName="isActive">
                <label class="form-check-label" for="isActive">Active</label>
              </div>
            </div>
          </div>
        </div>

        <!-- Form Actions -->
        <div class="d-flex justify-content-end gap-2 mt-4">
          <button type="button" class="btn btn-outline-secondary" [routerLink]="['/admin/giveaways']">
            Cancel
          </button>
          <button *ngIf="isEditMode" type="submit" class="btn btn-primary" [disabled]="submitting">
            <i class="fas fa-save me-2"></i>
            <span *ngIf="!submitting">Update Giveaway</span>
            <span *ngIf="submitting">Updating...</span>
          </button>
          <button *ngIf="!isEditMode" type="submit" class="btn btn-primary" [disabled]="submitting">
            <i class="fas fa-plus me-2"></i>
            <span *ngIf="!submitting">Create Giveaway</span>
            <span *ngIf="submitting">Creating...</span>
          </button>
        </div>
      </form>
    </div>
  </div>
</div>
