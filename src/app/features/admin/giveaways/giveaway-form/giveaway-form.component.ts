import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, ActivatedRoute, Router } from '@angular/router';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { Giveaway, GiveawayStatus, GiveawayCreateDto } from '../../../../core/models/giveaway.model';
import { AdminGiveawayService } from '../../services/admin-giveaway.service';

@Component({
  selector: 'app-giveaway-form',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    ReactiveFormsModule
  ],
  templateUrl: './giveaway-form.component.html',
  styleUrls: ['./giveaway-form.component.scss']
})
export class GiveawayFormComponent implements OnInit {
  giveawayForm: FormGroup;
  isEditMode = false;
  giveawayId: string | null = null;
  loading = false;
  submitting = false;
  error = '';
  success = '';
  
  // Status options for dropdown
  statusOptions = [
    { value: GiveawayStatus.DRAFT, label: 'Draft' },
    { value: GiveawayStatus.ACTIVE, label: 'Active' },
    { value: GiveawayStatus.COMPLETED, label: 'Completed' },
    { value: GiveawayStatus.CANCELLED, label: 'Cancelled' }
  ];
  
  constructor(
    private fb: FormBuilder,
    private route: ActivatedRoute,
    private router: Router,
    private giveawayService: AdminGiveawayService
  ) {
    this.giveawayForm = this.createGiveawayForm();
  }
  
  ngOnInit(): void {
    this.giveawayId = this.route.snapshot.paramMap.get('id');
    this.isEditMode = !!this.giveawayId;
    
    if (this.isEditMode && this.giveawayId) {
      this.loadGiveaway(this.giveawayId);
    }
  }
  
  createGiveawayForm(): FormGroup {
    return this.fb.group({
      title: ['', [Validators.required, Validators.maxLength(100)]],
      description: ['', [Validators.required]],
      startDate: ['', [Validators.required]],
      endDate: ['', [Validators.required]],
      drawDate: ['', [Validators.required]],
      status: [GiveawayStatus.DRAFT, [Validators.required]],
      featuredImage: [''],
      prizeValue: [0, [Validators.required, Validators.min(1)]],
      prizeDetails: [''],
      imageUrl: [''],
      category: [''],
      tags: [''], // Will be split into array on submit
      rules: ['', [Validators.required]],
      termsAndConditions: [''],
      minTier: [''],
      maxEntries: [1, [Validators.required, Validators.min(1)]],
      isActive: [true]
    });
  }
  
  loadGiveaway(id: string): void {
    this.loading = true;
    
    this.giveawayService.getGiveaway(id).subscribe({
      next: (giveaway) => {
        // Format dates for input fields (YYYY-MM-DDThh:mm)
        const startDate = this.formatDateForInput(giveaway.startDate);
        const endDate = this.formatDateForInput(giveaway.endDate);
        const drawDate = this.formatDateForInput(giveaway.drawDate);
        
        // Format tags as comma-separated string
        const tags = giveaway.tags ? giveaway.tags.join(', ') : '';
        
        this.giveawayForm.patchValue({
          ...giveaway,
          startDate,
          endDate,
          drawDate,
          tags
        });
        
        this.loading = false;
      },
      error: (err) => {
        this.error = 'Failed to load giveaway. Please try again.';
        console.error('Error loading giveaway:', err);
        this.loading = false;
      }
    });
  }
  
  onSubmit(): void {
    if (this.giveawayForm.invalid) {
      // Mark all fields as touched to trigger validation messages
      Object.keys(this.giveawayForm.controls).forEach(key => {
        const control = this.giveawayForm.get(key);
        control?.markAsTouched();
      });
      return;
    }
    
    this.submitting = true;
    
    // Process form data
    const formData = { ...this.giveawayForm.value };
    
    // Convert tags from comma-separated string to array
    if (formData.tags) {
      formData.tags = formData.tags.split(',').map((tag: string) => tag.trim()).filter((tag: string) => tag);
    } else {
      formData.tags = [];
    }
    
    // Ensure prizeValue is a number
    formData.prizeValue = Number(formData.prizeValue);
    formData.maxEntries = Number(formData.maxEntries);
    
    if (this.isEditMode && this.giveawayId) {
      this.updateGiveaway(this.giveawayId, formData);
    } else {
      this.createGiveaway(formData);
    }
  }
  
  createGiveaway(data: GiveawayCreateDto): void {
    this.giveawayService.createGiveaway(data).subscribe({
      next: (response) => {
        this.success = 'Giveaway created successfully!';
        this.submitting = false;
        
        // Navigate to the giveaway list after a short delay
        setTimeout(() => {
          this.router.navigate(['/admin/giveaways']);
        }, 1500);
      },
      error: (err) => {
        this.error = 'Failed to create giveaway. Please try again.';
        console.error('Error creating giveaway:', err);
        this.submitting = false;
      }
    });
  }
  
  updateGiveaway(id: string, data: Partial<GiveawayCreateDto>): void {
    this.giveawayService.updateGiveaway(id, data).subscribe({
      next: (response) => {
        this.success = 'Giveaway updated successfully!';
        this.submitting = false;
        
        // Navigate to the giveaway details after a short delay
        setTimeout(() => {
          this.router.navigate(['/admin/giveaways', id]);
        }, 1500);
      },
      error: (err) => {
        this.error = 'Failed to update giveaway. Please try again.';
        console.error('Error updating giveaway:', err);
        this.submitting = false;
      }
    });
  }
  
  // Helper method to format date for datetime-local input
  formatDateForInput(date: Date): string {
    const d = new Date(date);
    return d.toISOString().slice(0, 16); // Format as YYYY-MM-DDThh:mm
  }
  
  // Validation helpers
  isFieldInvalid(fieldName: string): boolean {
    const control = this.giveawayForm.get(fieldName);
    return !!control && control.invalid && (control.dirty || control.touched);
  }
  
  getFieldError(fieldName: string): string {
    const control = this.giveawayForm.get(fieldName);
    
    if (!control || !control.errors) {
      return '';
    }
    
    if (control.errors['required']) {
      return 'This field is required';
    }
    
    if (control.errors['min']) {
      return `Value must be at least ${control.errors['min'].min}`;
    }
    
    if (control.errors['maxlength']) {
      return `Maximum length is ${control.errors['maxlength'].requiredLength} characters`;
    }
    
    return 'Invalid value';
  }
}
