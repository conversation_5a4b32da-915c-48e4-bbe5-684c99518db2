// Giveaway form component styles

// Card styles
.card {
  border-radius: 16px;
  border: none;
  overflow: hidden;
  
  .card-body {
    padding: 1.5rem;
  }
  
  .card-title {
    font-weight: 600;
    color: var(--admin-text);
    border-bottom: 1px solid var(--admin-border);
    padding-bottom: 0.75rem;
  }
}

// Form styles
.form-label {
  font-weight: 500;
  margin-bottom: 0.5rem;
}

.form-control, .form-select {
  padding: 0.75rem 1rem;
  border-radius: 8px;
  border: 1px solid var(--admin-border);
  
  &:focus {
    border-color: var(--admin-primary);
    box-shadow: 0 0 0 0.25rem rgba(var(--admin-primary-rgb), 0.25);
  }
  
  &.is-invalid {
    border-color: var(--bs-danger);
    
    &:focus {
      box-shadow: 0 0 0 0.25rem rgba(var(--bs-danger-rgb), 0.25);
    }
  }
}

textarea.form-control {
  min-height: 100px;
}

// Form check switch
.form-check-input {
  width: 3em;
  height: 1.5em;
  
  &:checked {
    background-color: var(--admin-primary);
    border-color: var(--admin-primary);
  }
  
  &:focus {
    border-color: var(--admin-primary);
    box-shadow: 0 0 0 0.25rem rgba(var(--admin-primary-rgb), 0.25);
  }
}

.form-check-label {
  font-weight: 500;
  padding-left: 0.5rem;
}

// Buttons
.btn {
  padding: 0.75rem 1.5rem;
  font-weight: 500;
  border-radius: 8px;
  transition: all 0.2s ease-in-out;
  
  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  }
  
  &:active {
    transform: translateY(0);
  }
  
  &:disabled {
    opacity: 0.7;
    cursor: not-allowed;
    transform: none !important;
    box-shadow: none !important;
  }
}

.btn-primary {
  background-color: var(--admin-primary);
  border-color: var(--admin-primary);
  
  &:hover, &:focus {
    background-color: var(--admin-primary-dark);
    border-color: var(--admin-primary-dark);
  }
}

.btn-outline-secondary {
  color: var(--admin-text);
  border-color: var(--admin-border);
  
  &:hover, &:focus {
    background-color: var(--admin-hover);
    color: var(--admin-text);
    border-color: var(--admin-border);
  }
}
