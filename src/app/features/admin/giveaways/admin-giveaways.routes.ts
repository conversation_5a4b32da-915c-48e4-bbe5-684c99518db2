import { Routes } from '@angular/router';
import { AuthGuard } from '../../../core/guards/auth.guard';
import { AdminGuard } from '../../../core/guards/admin.guard';

export const ADMIN_GIVEAWAYS_ROUTES: Routes = [
  {
    path: '',
    loadComponent: () => import('./giveaway-list/giveaway-list.component').then(m => m.GiveawayListComponent),
    canActivate: [AuthGuard, AdminGuard],
    title: 'Giveaway Management - Winners Society'
  },
  {
    path: 'create',
    loadComponent: () => import('./giveaway-form/giveaway-form.component').then(m => m.GiveawayFormComponent),
    canActivate: [AuthGuard, AdminGuard],
    title: 'Create Giveaway - Winners Society'
  },
  {
    path: ':id',
    loadComponent: () => import('./giveaway-detail/giveaway-detail.component').then(m => m.GiveawayDetailComponent),
    canActivate: [AuthGuard, AdminGuard],
    title: 'Giveaway Details - Winners Society'
  },
  {
    path: ':id/edit',
    loadComponent: () => import('./giveaway-form/giveaway-form.component').then(m => m.GiveawayFormComponent),
    canActivate: [AuthGuard, AdminGuard],
    title: 'Edit Giveaway - Winners Society'
  },
  {
    path: ':id/draw',
    loadComponent: () => import('./giveaway-draw/giveaway-draw.component').then(m => m.GiveawayDrawComponent),
    canActivate: [AuthGuard, AdminGuard],
    title: 'Conduct Draw - Winners Society'
  },
  {
    path: 'statistics',
    loadComponent: () => import('./giveaway-statistics/giveaway-statistics.component').then(m => m.GiveawayStatisticsComponent),
    canActivate: [AuthGuard, AdminGuard],
    title: 'Giveaway Statistics - Winners Society'
  }
];
