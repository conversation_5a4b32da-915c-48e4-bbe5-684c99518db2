:host {
  display: block;
  width: 100%;
  max-width: 100%;
}

.dashboard {
  width: 100%;
  max-width: 100%;
  padding: 0;
}

// Make charts responsive
.chart-area, .chart-pie {
  position: relative;
  width: 100%;
  
  canvas {
    max-width: 100%;
  }
}

// Card styles
.card {
  &.border-left-primary {
    border-left: 4px solid var(--bs-primary);
  }
  
  &.border-left-success {
    border-left: 4px solid var(--bs-success);
  }
  
  &.border-left-info {
    border-left: 4px solid var(--bs-info);
  }
  
  &.border-left-warning {
    border-left: 4px solid var(--bs-warning);
  }
}

.icon-circle {
  height: 60px;
  width: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  
  &.bg-primary-light {
    background-color: rgba(0, 123, 255, 0.1);
  }
  
  &.bg-success-light {
    background-color: rgba(40, 167, 69, 0.1);
  }
  
  &.bg-info-light {
    background-color: rgba(23, 162, 184, 0.1);
  }
  
  &.bg-warning-light {
    background-color: rgba(255, 193, 7, 0.1);
  }
}