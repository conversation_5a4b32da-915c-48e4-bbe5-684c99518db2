import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { environment } from '../../../../environments/environment';
import { Prize, PrizeCreateDto } from '../../../core/models/giveaway.model';

/**
 * Service for managing prizes in the admin panel
 */
@Injectable({
  providedIn: 'root'
})
export class AdminPrizeService {
  private apiUrl = environment.apiUrl;

  constructor(private http: HttpClient) { }

  /**
   * Get all prizes for a specific giveaway
   * @param giveawayId Giveaway ID
   * @returns Observable of prizes
   */
  getPrizesByGiveaway(giveawayId: string): Observable<Prize[]> {
    return this.http.get<{
      success: boolean;
      data: Prize[];
    }>(`${this.apiUrl}/admin/giveaways/${giveawayId}/prizes`)
    .pipe(
      map(response => response.data)
    );
  }

  /**
   * Get a specific prize by ID
   * @param id Prize ID
   * @returns Observable of the prize
   */
  getPrize(id: string): Observable<Prize> {
    return this.http.get<{
      success: boolean;
      data: Prize;
    }>(`${this.apiUrl}/admin/giveaways/prizes/${id}`)
    .pipe(
      map(response => response.data)
    );
  }

  /**
   * Create a new prize for a giveaway
   * @param giveawayId Giveaway ID
   * @param prize Prize data
   * @returns Observable of the created prize
   */
  createPrize(giveawayId: string, prize: PrizeCreateDto): Observable<{
    id: string;
    giveawayId: string;
    name: string;
    createdAt: Date;
  }> {
    return this.http.post<{
      success: boolean;
      data: {
        id: string;
        giveawayId: string;
        name: string;
        createdAt: string;
      }
    }>(`${this.apiUrl}/admin/giveaways/${giveawayId}/prizes`, prize)
    .pipe(
      map(response => ({
        id: response.data.id,
        giveawayId: response.data.giveawayId,
        name: response.data.name,
        createdAt: new Date(response.data.createdAt)
      }))
    );
  }

  /**
   * Update an existing prize
   * @param id Prize ID
   * @param prize Updated prize data
   * @returns Observable of the updated prize
   */
  updatePrize(id: string, prize: Partial<PrizeCreateDto>): Observable<{
    id: string;
    name: string;
    updatedAt: Date;
  }> {
    return this.http.put<{
      success: boolean;
      data: {
        id: string;
        name: string;
        updatedAt: string;
      }
    }>(`${this.apiUrl}/admin/giveaways/prizes/${id}`, prize)
    .pipe(
      map(response => ({
        id: response.data.id,
        name: response.data.name,
        updatedAt: new Date(response.data.updatedAt)
      }))
    );
  }

  /**
   * Delete a prize
   * @param id Prize ID
   * @returns Observable of the delete result
   */
  deletePrize(id: string): Observable<{ success: boolean; message: string }> {
    return this.http.delete<{ success: boolean; message: string }>(`${this.apiUrl}/admin/giveaways/prizes/${id}`);
  }
}
