import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { environment } from '../../../../environments/environment';
import { Entry } from '../../../core/models/giveaway.model';

/**
 * Service for managing entries in the admin panel
 */
@Injectable({
  providedIn: 'root'
})
export class AdminEntryService {
  private apiUrl = environment.apiUrl;

  constructor(private http: HttpClient) { }

  /**
   * Get all entries for a specific giveaway with pagination
   * @param giveawayId Giveaway ID
   * @param page Page number
   * @param limit Items per page
   * @returns Observable of paginated entries
   */
  getEntriesByGiveaway(giveawayId: string, page: number = 1, limit: number = 10): Observable<{
    totalEntries: number;
    entries: Entry[];
    meta: {
      total: number;
      page: number;
      limit: number;
      pages: number;
    }
  }> {
    const params = new HttpParams()
      .set('page', page.toString())
      .set('limit', limit.toString());

    return this.http.get<{
      success: boolean;
      data: Entry[];
      meta: {
        total: number;
        page: number;
        limit: number;
        pages: number;
      }
    }>(`${this.apiUrl}/admin/giveaways/${giveawayId}/entries`, { params })
    .pipe(
      map(response => {
        // Transform dates from strings to Date objects
        const entries = response.data.map(entry => ({
          ...entry,
          entryDate: new Date(entry.entryDate)
        }));

        return {
          totalEntries: response.meta.total,
          entries,
          meta: response.meta
        };
      })
    );
  }

  /**
   * Get the total number of entries for a specific giveaway
   * @param giveawayId Giveaway ID
   * @returns Observable of the entry count
   */
  getEntryCount(giveawayId: string): Observable<number> {
    return this.http.get<{
      success: boolean;
      data: {
        totalEntries: number;
      }
    }>(`${this.apiUrl}/admin/giveaways/${giveawayId}/entries/count`)
    .pipe(
      map(response => response.data.totalEntries)
    );
  }

  /**
   * Get all entries for a specific user in a giveaway
   * @param giveawayId Giveaway ID
   * @param userId User ID
   * @returns Observable of user entries
   */
  getUserEntries(giveawayId: string, userId: string): Observable<{
    entries: Entry[];
    totalEntries: number;
  }> {
    return this.http.get<{
      success: boolean;
      data: {
        entries: Entry[];
        totalEntries: number;
      }
    }>(`${this.apiUrl}/admin/giveaways/${giveawayId}/entries/user/${userId}`)
    .pipe(
      map(response => {
        // Transform dates from strings to Date objects
        const entries = response.data.entries.map(entry => ({
          ...entry,
          entryDate: new Date(entry.entryDate)
        }));

        return {
          entries,
          totalEntries: response.data.totalEntries
        };
      })
    );
  }
}
