import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { environment } from '../../../../environments/environment';
import {
  Giveaway,
  GiveawayCreateDto,
  GiveawayFilterParams,
  GiveawayStatus,
  WinnerSelectionDto,
  WinnerSelectionResultDto
} from '../../../core/models/giveaway.model';

/**
 * Service for managing giveaways in the admin panel
 */
@Injectable({
  providedIn: 'root'
})
export class AdminGiveawayService {
  private apiUrl = environment.apiUrl;

  constructor(private http: HttpClient) { }

  /**
   * Get all giveaways with optional filtering and pagination
   * @param params Filter and pagination parameters
   * @returns Observable of paginated giveaways
   */
  getGiveaways(params?: GiveawayFilterParams): Observable<{
    data: Giveaway[];
    meta: {
      total: number;
      page: number;
      limit: number;
      pages: number;
    }
  }> {
    let httpParams = new HttpParams();

    if (params) {
      Object.keys(params).forEach(key => {
        const value = params[key as keyof GiveawayFilterParams];
        if (value !== undefined && value !== null) {
          httpParams = httpParams.set(key, value.toString());
        }
      });
    }

    return this.http.get<{
      success: boolean;
      data: Giveaway[];
      meta: {
        total: number;
        page: number;
        limit: number;
        pages: number;
      }
    }>(`${this.apiUrl}/admin/giveaways`, { params: httpParams })
    .pipe(
      map(response => {
        // Transform dates from strings to Date objects
        const giveaways = response.data.map(giveaway => ({
          ...giveaway,
          startDate: new Date(giveaway.startDate),
          endDate: new Date(giveaway.endDate),
          drawDate: new Date(giveaway.drawDate),
          createdAt: new Date(giveaway.createdAt),
          updatedAt: new Date(giveaway.updatedAt)
        }));

        return {
          data: giveaways,
          meta: response.meta
        };
      })
    );
  }

  /**
   * Get a specific giveaway by ID
   * @param id Giveaway ID
   * @returns Observable of the giveaway
   */
  getGiveaway(id: string): Observable<Giveaway> {
    return this.http.get<{ success: boolean; data: Giveaway }>(`${this.apiUrl}/admin/giveaways/${id}`)
      .pipe(
        map(response => {
          const giveaway = response.data;
          return {
            ...giveaway,
            startDate: new Date(giveaway.startDate),
            endDate: new Date(giveaway.endDate),
            drawDate: new Date(giveaway.drawDate),
            createdAt: new Date(giveaway.createdAt),
            updatedAt: new Date(giveaway.updatedAt)
          };
        })
      );
  }

  /**
   * Create a new giveaway
   * @param giveaway Giveaway data
   * @returns Observable of the created giveaway
   */
  createGiveaway(giveaway: GiveawayCreateDto): Observable<{ id: string; title: string; status: GiveawayStatus; createdAt: Date }> {
    return this.http.post<{
      success: boolean;
      data: {
        id: string;
        title: string;
        status: string;
        createdAt: string;
      }
    }>(`${this.apiUrl}/admin/giveaways`, giveaway)
    .pipe(
      map(response => ({
        id: response.data.id,
        title: response.data.title,
        status: response.data.status as GiveawayStatus,
        createdAt: new Date(response.data.createdAt)
      }))
    );
  }

  /**
   * Update an existing giveaway
   * @param id Giveaway ID
   * @param giveaway Updated giveaway data
   * @returns Observable of the updated giveaway
   */
  updateGiveaway(id: string, giveaway: Partial<GiveawayCreateDto>): Observable<{ id: string; title: string; status: GiveawayStatus; updatedAt: Date }> {
    return this.http.put<{
      success: boolean;
      data: {
        id: string;
        title: string;
        status: string;
        updatedAt: string;
      }
    }>(`${this.apiUrl}/admin/giveaways/${id}`, giveaway)
    .pipe(
      map(response => ({
        id: response.data.id,
        title: response.data.title,
        status: response.data.status as GiveawayStatus,
        updatedAt: new Date(response.data.updatedAt)
      }))
    );
  }

  /**
   * Delete a giveaway
   * @param id Giveaway ID
   * @returns Observable of the delete result
   */
  deleteGiveaway(id: string): Observable<{ success: boolean; message: string }> {
    return this.http.delete<{ success: boolean; message: string }>(`${this.apiUrl}/admin/giveaways/${id}`);
  }

  /**
   * Publish a giveaway (change status from DRAFT to ACTIVE)
   * @param id Giveaway ID
   * @returns Observable of the published giveaway
   */
  publishGiveaway(id: string): Observable<{ id: string; title: string; status: GiveawayStatus; updatedAt: Date }> {
    return this.http.post<{
      success: boolean;
      data: {
        id: string;
        title: string;
        status: string;
        updatedAt: string;
      }
    }>(`${this.apiUrl}/admin/giveaways/${id}/publish`, {})
    .pipe(
      map(response => ({
        id: response.data.id,
        title: response.data.title,
        status: response.data.status as GiveawayStatus,
        updatedAt: new Date(response.data.updatedAt)
      }))
    );
  }

  /**
   * Conduct a draw for a giveaway
   * @param selectionData Winner selection data
   * @returns Observable of the draw result
   */
  conductDraw(selectionData: WinnerSelectionDto): Observable<WinnerSelectionResultDto> {
    return this.http.post<{
      success: boolean;
      data: WinnerSelectionResultDto;
    }>(`${this.apiUrl}/admin/giveaways/${selectionData.giveawayId}/draw`, { numberOfWinners: selectionData.numberOfWinners })
    .pipe(
      map(response => response.data)
    );
  }

  /**
   * Get giveaway statistics
   * @param startDate Optional start date for filtering
   * @param endDate Optional end date for filtering
   * @param dateRange Optional predefined date range
   * @returns Observable of giveaway statistics
   */
  getGiveawayStatistics(startDate?: string, endDate?: string, dateRange?: string): Observable<{
    totalGiveaways: number;
    activeGiveaways: number;
    completedGiveaways: number;
    giveawaysByStatus: Record<GiveawayStatus, number>;
    totalEntries: number;
    totalWinners: number;
  }> {
    let params = new HttpParams();

    if (startDate) {
      params = params.set('startDate', startDate);
    }

    if (endDate) {
      params = params.set('endDate', endDate);
    }

    if (dateRange) {
      params = params.set('dateRange', dateRange);
    }

    return this.http.get<{
      success: boolean;
      data: {
        totalGiveaways: number;
        activeGiveaways: number;
        completedGiveaways: number;
        giveawaysByStatus: Record<GiveawayStatus, number>;
        totalEntries: number;
        totalWinners: number;
      }
    }>(`${this.apiUrl}/admin/dashboard/stats/giveaways`, { params })
    .pipe(
      map(response => response.data)
    );
  }
}
