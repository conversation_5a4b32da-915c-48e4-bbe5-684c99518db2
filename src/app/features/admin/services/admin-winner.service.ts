import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { environment } from '../../../../environments/environment';
import { GiveawayWinner, WinnerStatus } from '../../../core/models/giveaway.model';

/**
 * Service for managing winners in the admin panel
 */
@Injectable({
  providedIn: 'root'
})
export class AdminWinnerService {
  private apiUrl = environment.apiUrl;

  constructor(private http: HttpClient) { }

  /**
   * Get all winners for a specific giveaway
   * @param giveawayId Giveaway ID
   * @returns Observable of winners
   */
  getWinnersByGiveaway(giveawayId: string): Observable<GiveawayWinner[]> {
    return this.http.get<{
      success: boolean;
      data: GiveawayWinner[];
    }>(`${this.apiUrl}/admin/giveaways/${giveawayId}/winners`)
    .pipe(
      map(response => {
        // Transform dates from strings to Date objects
        return response.data.map(winner => ({
          ...winner,
          selectionDate: new Date(winner.selectionDate),
          claimDate: winner.claimDate ? new Date(winner.claimDate) : undefined
        }));
      })
    );
  }

  /**
   * Get a specific winner by ID
   * @param id Winner ID
   * @returns Observable of the winner
   */
  getWinner(id: string): Observable<GiveawayWinner> {
    return this.http.get<{
      success: boolean;
      data: GiveawayWinner;
    }>(`${this.apiUrl}/admin/giveaways/winners/${id}`)
    .pipe(
      map(response => {
        const winner = response.data;
        return {
          ...winner,
          selectionDate: new Date(winner.selectionDate),
          claimDate: winner.claimDate ? new Date(winner.claimDate) : undefined
        };
      })
    );
  }

  /**
   * Update a winner's status
   * @param id Winner ID
   * @param status New status
   * @returns Observable of the updated winner
   */
  updateWinnerStatus(id: string, status: WinnerStatus): Observable<{
    id: string;
    status: WinnerStatus;
    updatedAt: Date;
  }> {
    return this.http.put<{
      success: boolean;
      data: {
        id: string;
        status: string;
        updatedAt: string;
      }
    }>(`${this.apiUrl}/admin/giveaways/winners/${id}/status`, { status })
    .pipe(
      map(response => ({
        id: response.data.id,
        status: response.data.status as WinnerStatus,
        updatedAt: new Date(response.data.updatedAt)
      }))
    );
  }
}
