import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { environment } from '../../../../environments/environment';

/**
 * Report types
 */
export enum ReportType {
  GIVEAWAY_REPORT = 'GIVEAWAY_REPORT',
  ENTRY_REPORT = 'ENTRY_REPORT',
  WINNER_REPORT = 'WINNER_REPORT'
}

/**
 * Report formats
 */
export enum ReportFormat {
  CSV = 'CSV',
  EXCEL = 'EXCEL',
  PDF = 'PDF'
}

/**
 * Report generation parameters
 */
export interface ReportGenerationParams {
  type: ReportType;
  format: ReportFormat;
  filters?: {
    startDate?: string;
    endDate?: string;
    status?: string;
    giveawayId?: string;
  };
}

/**
 * Report response
 */
export interface ReportResponse {
  reportId: string;
  type: ReportType;
  format: ReportFormat;
  url: string;
  generatedAt: Date;
  expiresAt: Date;
}

/**
 * Service for generating reports in the admin panel
 */
@Injectable({
  providedIn: 'root'
})
export class AdminReportService {
  private apiUrl = environment.apiUrl;

  constructor(private http: HttpClient) { }

  /**
   * Generate a report
   * @param params Report generation parameters
   * @returns Observable of the report response
   */
  generateReport(params: ReportGenerationParams): Observable<ReportResponse> {
    return this.http.post<{
      success: boolean;
      data: {
        reportId: string;
        type: string;
        format: string;
        url: string;
        generatedAt: string;
        expiresAt: string;
      }
    }>(`${this.apiUrl}/admin/dashboard/reports`, params)
    .pipe(
      map(response => ({
        reportId: response.data.reportId,
        type: response.data.type as ReportType,
        format: response.data.format as ReportFormat,
        url: response.data.url,
        generatedAt: new Date(response.data.generatedAt),
        expiresAt: new Date(response.data.expiresAt)
      }))
    );
  }

  /**
   * Generate a giveaway report
   * @param format Report format
   * @param startDate Optional start date
   * @param endDate Optional end date
   * @param status Optional status filter
   * @returns Observable of the report response
   */
  generateGiveawayReport(
    format: ReportFormat,
    startDate?: string,
    endDate?: string,
    status?: string
  ): Observable<ReportResponse> {
    const params: ReportGenerationParams = {
      type: ReportType.GIVEAWAY_REPORT,
      format,
      filters: {}
    };

    if (startDate) {
      params.filters!.startDate = startDate;
    }

    if (endDate) {
      params.filters!.endDate = endDate;
    }

    if (status) {
      params.filters!.status = status;
    }

    return this.generateReport(params);
  }

  /**
   * Generate an entry report for a specific giveaway
   * @param giveawayId Giveaway ID
   * @param format Report format
   * @returns Observable of the report response
   */
  generateEntryReport(giveawayId: string, format: ReportFormat): Observable<ReportResponse> {
    const params: ReportGenerationParams = {
      type: ReportType.ENTRY_REPORT,
      format,
      filters: {
        giveawayId
      }
    };

    return this.generateReport(params);
  }

  /**
   * Generate a winner report for a specific giveaway
   * @param giveawayId Giveaway ID
   * @param format Report format
   * @param status Optional status filter
   * @returns Observable of the report response
   */
  generateWinnerReport(
    giveawayId: string,
    format: ReportFormat,
    status?: string
  ): Observable<ReportResponse> {
    const params: ReportGenerationParams = {
      type: ReportType.WINNER_REPORT,
      format,
      filters: {
        giveawayId
      }
    };

    if (status) {
      params.filters!.status = status;
    }

    return this.generateReport(params);
  }
}
