import { Routes } from '@angular/router';
import { AuthGuard } from '../../core/guards/auth.guard';
import { AdminGuard } from '../../core/guards/admin.guard';
import { Component } from '@angular/core';
import { DashboardComponent } from './dashboard/dashboard.component';
import { USERS_ROUTES } from './users/users.routes';

// Placeholder component for admin pages until the real components are created
@Component({
  standalone: true,
  template: '<div class="p-4">This admin feature is under development</div>'
})
export class AdminPlaceholderComponent {}

export const ADMIN_ROUTES: Routes = [
  {
    path: '',
    redirectTo: 'dashboard',
    pathMatch: 'full'
  },
  {
    path: 'dashboard',
    component: DashboardComponent,
    canActivate: [AuthGuard, AdminGuard],
    title: 'Admin Dashboard - Winners Society'
  },
  {
    path: 'users',
    loadChildren: () => Promise.resolve(USERS_ROUTES),
    canActivate: [AuthGuard, AdminGuard],
    title: 'Manage Users - Winners Society'
  },
  {
    path: 'giveaways',
    loadChildren: () => import('./giveaways/admin-giveaways.routes').then(m => m.ADMIN_GIVEAWAYS_ROUTES),
    canActivate: [AuthGuard, AdminGuard]
  }
];
