// Modern Home Styles
.modern-home {
  width: 100%;
  overflow-x: hidden;
}

// Hero Section
.hero-section {
  position: relative;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  overflow: hidden;

  @media (max-width: 768px) {
    min-height: 90vh;
    padding: 2rem 0;
  }
}

.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  overflow: hidden;
  z-index: 1;
}

.gradient-orb {
  position: absolute;
  border-radius: 50%;
  filter: blur(60px);
  opacity: 0.3;
  animation: float 8s ease-in-out infinite;

  &.orb-1 {
    width: 300px;
    height: 300px;
    background: radial-gradient(circle, #ff6b6b, #ee5a24);
    top: 10%;
    left: 10%;
    animation-delay: 0s;
  }

  &.orb-2 {
    width: 200px;
    height: 200px;
    background: radial-gradient(circle, #4ecdc4, #44a08d);
    top: 60%;
    right: 15%;
    animation-delay: 2s;
  }

  &.orb-3 {
    width: 250px;
    height: 250px;
    background: radial-gradient(circle, #a8edea, #fed6e3);
    bottom: 20%;
    left: 20%;
    animation-delay: 4s;
  }
}

.hero-container {
  position: relative;
  z-index: 2;
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: center;

  @media (max-width: 992px) {
    grid-template-columns: 1fr;
    gap: 3rem;
    text-align: center;
    padding: 0 1.5rem;
  }
}

.hero-content {
  color: white;
}

.hero-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 50px;
  padding: 0.5rem 1rem;
  margin-bottom: 2rem;
  font-size: 0.9rem;
  font-weight: 500;

  .badge-icon {
    font-size: 1.2rem;
  }
}

.hero-title {
  font-size: 4rem;
  font-weight: 800;
  line-height: 1.1;
  margin-bottom: 1.5rem;

  @media (max-width: 1200px) {
    font-size: 3.5rem;
  }

  @media (max-width: 768px) {
    font-size: 2.5rem;
  }

  @media (max-width: 480px) {
    font-size: 2rem;
  }
}

.gradient-text {
  background: linear-gradient(135deg, #ffd89b 0%, #19547b 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.hero-description {
  font-size: 1.25rem;
  line-height: 1.6;
  margin-bottom: 2.5rem;
  opacity: 0.9;
  max-width: 500px;

  @media (max-width: 992px) {
    max-width: none;
  }

  @media (max-width: 768px) {
    font-size: 1.1rem;
  }
}

.hero-actions {
  display: flex;
  gap: 1rem;
  margin-bottom: 3rem;

  @media (max-width: 992px) {
    justify-content: center;
  }

  @media (max-width: 576px) {
    flex-direction: column;
    align-items: center;
  }
}

.primary-btn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  background: white;
  color: #667eea;
  padding: 1rem 2rem;
  border-radius: 16px;
  font-weight: 600;
  font-size: 1.1rem;
  text-decoration: none;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
    text-decoration: none;
    color: #667eea;
  }

  svg {
    transition: transform 0.3s ease;
  }

  &:hover svg {
    transform: translateX(4px);
  }
}

.secondary-btn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  background: rgba(255, 255, 255, 0.1);
  color: white;
  padding: 1rem 2rem;
  border-radius: 16px;
  font-weight: 600;
  font-size: 1.1rem;
  text-decoration: none;
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;

  &:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
    text-decoration: none;
    color: white;
  }
}

.hero-features {
  display: flex;
  gap: 2rem;

  @media (max-width: 992px) {
    justify-content: center;
  }

  @media (max-width: 576px) {
    flex-direction: column;
    gap: 1rem;
  }
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 0.95rem;
  font-weight: 500;
  opacity: 0.9;

  @media (max-width: 576px) {
    justify-content: center;
  }
}

.feature-icon {
  width: 32px;
  height: 32px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;

  svg {
    width: 18px;
    height: 18px;
    stroke: currentColor;
  }
}

// Hero Visual
.hero-visual {
  position: relative;

  @media (max-width: 992px) {
    order: -1;
  }
}

.visual-container {
  position: relative;
  width: 100%;
  height: 500px;

  @media (max-width: 768px) {
    height: 400px;
  }
}

.floating-card {
  position: absolute;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 16px;
  padding: 1rem;
  animation: float 6s ease-in-out infinite;

  &.card-1 {
    top: 10%;
    left: 10%;
    animation-delay: 0s;
  }

  &.card-2 {
    top: 20%;
    right: 10%;
    animation-delay: 2s;
  }

  &.card-3 {
    bottom: 30%;
    left: 5%;
    animation-delay: 4s;
  }
}

.card-content {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  color: white;
}

.card-icon {
  font-size: 1.5rem;
}

.card-title {
  font-weight: 600;
  font-size: 0.9rem;
}

.card-subtitle {
  font-size: 0.8rem;
  opacity: 0.8;
}

.main-visual {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 300px;
  height: 300px;

  @media (max-width: 768px) {
    width: 250px;
    height: 250px;
  }
}

.visual-glow {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
  border-radius: 50%;
  animation: pulse 4s ease-in-out infinite;
}

.visual-content {
  position: relative;
  z-index: 2;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.prize-showcase {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
  width: 200px;
  height: 200px;

  @media (max-width: 768px) {
    width: 150px;
    height: 150px;
  }
}

.showcase-item {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  animation: rotate 8s linear infinite;

  &:nth-child(1) { animation-delay: 0s; }
  &:nth-child(2) { animation-delay: 2s; }
  &:nth-child(3) { animation-delay: 4s; }
  &:nth-child(4) { animation-delay: 6s; }
}

// Stats Section
.stats-section {
  position: relative;
  z-index: 3;
  margin-top: -100px;

  @media (max-width: 768px) {
    margin-top: -50px;
  }
}

.stats-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 24px;
  padding: 3rem 2rem;
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 2rem;

  @media (max-width: 992px) {
    grid-template-columns: repeat(2, 1fr);
    padding: 2rem 1.5rem;
  }

  @media (max-width: 576px) {
    grid-template-columns: 1fr;
    gap: 1.5rem;
    padding: 2rem 1rem;
  }
}

.stat-item {
  text-align: center;
  color: white;
}

.stat-number {
  display: block;
  font-size: 3rem;
  font-weight: 800;
  margin-bottom: 0.5rem;
  background: linear-gradient(135deg, #ffd89b 0%, #19547b 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;

  @media (max-width: 768px) {
    font-size: 2.5rem;
  }
}

.stat-label {
  font-size: 1rem;
  font-weight: 500;
  opacity: 0.9;
}

// Animations
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 0.5;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.05);
  }
}

@keyframes rotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

// Section styles
.section {
  padding: 6rem 2rem;

  @media (max-width: 768px) {
    padding: 4rem 1.5rem;
  }
}

.section-container {
  max-width: 1200px;
  margin: 0 auto;
}

.section-header {
  text-align: center;
  margin-bottom: 4rem;

  @media (max-width: 768px) {
    margin-bottom: 3rem;
  }
}

.section-title {
  font-size: 3rem;
  font-weight: 800;
  margin-bottom: 1rem;
  color: #1a202c;

  @media (max-width: 768px) {
    font-size: 2.5rem;
  }
}

.section-subtitle {
  font-size: 1.25rem;
  color: #718096;
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;

  @media (max-width: 768px) {
    font-size: 1.1rem;
  }
}

// How It Works Section
.how-it-works {
  background-color: #f8f9fa;
}

.steps-container {
  display: flex;
  gap: 2rem;
  justify-content: center;

  @media (max-width: 992px) {
    flex-direction: column;
    align-items: center;
  }
}

.step-card {
  background-color: white;
  border-radius: 16px;
  padding: 2rem;
  text-align: center;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  flex: 1;
  max-width: 300px;
  transition: transform 0.3s ease;

  &:hover {
    transform: translateY(-10px);
  }

  @media (max-width: 992px) {
    max-width: 100%;
    width: 100%;
  }
}

.step-icon {
  width: 80px;
  height: 80px;
  background-color: var(--bs-primary);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1.5rem;
  position: relative;

  mat-icon {
    color: white;
    font-size: 36px;
    height: 36px;
    width: 36px;
  }
}

.step-number {
  position: absolute;
  top: -10px;
  right: -10px;
  width: 30px;
  height: 30px;
  background-color: #212529;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
}

.step-title {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: #212529;
}

.step-description {
  color: #6c757d;
  line-height: 1.6;
}

// Features Section
.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;

  @media (max-width: 576px) {
    grid-template-columns: 1fr;
  }
}

.feature-card {
  background-color: white;
  border-radius: 16px;
  padding: 2rem;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  transition: transform 0.3s ease;

  &:hover {
    transform: translateY(-10px);
  }
}

.feature-icon {
  width: 60px;
  height: 60px;
  background-color: rgba(var(--bs-primary-rgb), 0.1);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1.5rem;

  mat-icon {
    color: var(--bs-primary);
    font-size: 30px;
    height: 30px;
    width: 30px;
  }
}

.feature-title {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: #212529;
}

.feature-description {
  color: #6c757d;
  line-height: 1.6;
}

// Testimonials Section
.testimonials-section {
  background-color: #f8f9fa;
}

.testimonials-slider {
  display: flex;
  gap: 2rem;
  overflow-x: auto;
  padding: 1rem 0.5rem;
  scroll-snap-type: x mandatory;
  scrollbar-width: none;

  &::-webkit-scrollbar {
    display: none;
  }

  @media (max-width: 992px) {
    flex-direction: column;
    align-items: center;
  }
}

.testimonial-card {
  background-color: white;
  border-radius: 16px;
  padding: 2rem;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  flex: 0 0 calc(33.333% - 1.5rem);
  scroll-snap-align: start;

  @media (max-width: 992px) {
    flex: 0 0 100%;
    max-width: 500px;
    margin-bottom: 1.5rem;
  }
}

.testimonial-content {
  margin-bottom: 1.5rem;
}

.testimonial-text {
  font-size: 1.1rem;
  color: #495057;
  line-height: 1.6;
  font-style: italic;
}

.testimonial-author {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.author-image {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  object-fit: cover;
}

.author-name {
  font-size: 1.1rem;
  font-weight: 600;
  margin: 0;
  color: #212529;
}

.author-title {
  font-size: 0.9rem;
  color: #6c757d;
  margin: 0;
}

// CTA Section
.cta-section {
  background: linear-gradient(135deg, var(--bs-primary) 0%, #2c387e 100%);
  color: white;
  text-align: center;
}

.cta-container {
  max-width: 800px;
  margin: 0 auto;
}

.cta-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 1rem;

  @media (max-width: 768px) {
    font-size: 2rem;
  }
}

.cta-subtitle {
  font-size: 1.2rem;
  margin-bottom: 2rem;
  opacity: 0.9;

  @media (max-width: 768px) {
    font-size: 1rem;
  }
}

.cta-buttons {
  display: flex;
  gap: 1rem;
  justify-content: center;

  @media (max-width: 576px) {
    flex-direction: column;
    align-items: center;
  }

  .cta-button {
    background-color: white;
    color: var(--bs-primary);
  }

  .secondary-button {
    border-color: white;
    color: white;
  }
}

// Reduced Motion
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}
