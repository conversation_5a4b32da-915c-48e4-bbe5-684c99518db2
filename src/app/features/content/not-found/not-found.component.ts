import { Component } from '@angular/core';
import { RouterLink } from '@angular/router';
import { MatButtonModule } from '@angular/material/button';

@Component({
  selector: 'app-not-found',
  standalone: true,
  imports: [RouterLink, MatButtonModule],
  template: `
    <div class="not-found-container">
      <div class="not-found-content">
        <h1>404</h1>
        <h2>Page Not Found</h2>
        <p>The page you are looking for doesn't exist or has been moved.</p>
        <a mat-raised-button color="primary" routerLink="/">Return to Home</a>
      </div>
    </div>
  `,
  styles: [`
    .not-found-container {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 80vh;
      text-align: center;
    }
    
    .not-found-content {
      max-width: 500px;
      padding: 20px;
    }
    
    h1 {
      font-size: 8rem;
      margin-bottom: 0;
      color: #3f51b5;
      line-height: 1;
    }
    
    h2 {
      font-size: 2.5rem;
      margin: 0 0 20px;
      color: #333;
    }
    
    p {
      font-size: 1.1rem;
      margin-bottom: 30px;
      color: #666;
    }
  `]
})
export class NotFoundComponent {}
