.page-header {
  margin-bottom: 2rem;

  .page-title {
    font-size: 2rem;
    font-weight: 600;
    color: #333;
    margin: 0 0 0.5rem 0;
  }

  .page-subtitle {
    color: #666;
    margin: 0;
    font-size: 1rem;
  }
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  text-align: center;

  p {
    margin-top: 1rem;
    color: #666;
  }
}

.error-container,
.empty-container {
  display: flex;
  justify-content: center;
  padding: 3rem;

  .error-card,
  .empty-card {
    max-width: 400px;
    border-radius: 16px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    border: none;

    .error-content,
    .empty-content {
      text-align: center;
      padding: 2rem;

      .error-icon,
      .empty-icon {
        font-size: 3rem;
        width: 3rem;
        height: 3rem;
        margin-bottom: 1rem;
      }

      .error-icon {
        color: #f44336;
      }

      .empty-icon {
        color: #ff9800;
      }

      h3 {
        margin: 0 0 1rem 0;
        color: #333;
      }

      p {
        color: #666;
        margin-bottom: 2rem;
      }

      button {
        border-radius: 12px;
        height: 44px;
        font-weight: 600;

        mat-icon {
          margin-right: 0.5rem;
        }
      }
    }
  }
}

.winners-content {
  .winners-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;

    @media (max-width: 768px) {
      grid-template-columns: 1fr;
      gap: 1.5rem;
    }
  }
}

.winner-card {
  border-radius: 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border: none;
  transition: transform 0.3s ease, box-shadow 0.3s ease;

  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  }

  mat-card-content {
    padding: 1.5rem;
  }
}

.winner-avatar-section {
  display: flex;
  align-items: center;
  margin-bottom: 1.5rem;

  .winner-avatar {
    width: 60px;
    height: 60px;
    margin-right: 1rem;
    flex-shrink: 0;

    .avatar-placeholder {
      width: 100%;
      height: 100%;
      border-radius: 50%;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 1.5rem;
      font-weight: 600;
      color: white;
    }

    .avatar-image {
      width: 100%;
      height: 100%;
      border-radius: 50%;
      object-fit: cover;
    }
  }

  .winner-info {
    flex: 1;
    min-width: 0;

    .winner-name {
      font-size: 1.25rem;
      font-weight: 600;
      color: #333;
      margin: 0 0 0.25rem 0;
    }

    .win-date {
      color: #666;
      font-size: 0.875rem;
      margin: 0;
    }
  }
}

.prize-section {
  margin-bottom: 1.5rem;

  .prize-image,
  .prize-placeholder {
    width: 100%;
    height: 120px;
    border-radius: 12px;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
  }

  .prize-image {
    background-color: #f8f9fa;

    .prize-img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }

  .prize-placeholder {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);

    mat-icon {
      font-size: 3rem;
      width: 3rem;
      height: 3rem;
      color: #6c757d;
    }
  }

  .prize-details {
    .prize-name {
      font-size: 1.125rem;
      font-weight: 600;
      color: #333;
      margin: 0 0 0.5rem 0;
    }

    .prize-value {
      font-size: 1.25rem;
      font-weight: 700;
      color: #28a745;
      margin: 0 0 0.5rem 0;
    }

    .giveaway-name {
      color: #666;
      font-size: 0.875rem;
      margin: 0;
    }
  }
}

.status-section {
  display: flex;
  justify-content: center;

  .status-chip {
    font-weight: 600;
    border-radius: 20px;
    padding: 0.5rem 1rem;

    mat-icon {
      font-size: 16px;
      width: 16px;
      height: 16px;
      margin-right: 0.25rem;
    }

    &.status-selected {
      background-color: #e3f2fd;
      color: #1976d2;
    }

    &.status-notified {
      background-color: #fff3e0;
      color: #f57c00;
    }

    &.status-claimed {
      background-color: #e8f5e8;
      color: #2e7d32;
    }

    &.status-shipped {
      background-color: #f3e5f5;
      color: #7b1fa2;
    }
  }
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 2rem;
  padding-top: 2rem;
  border-top: 1px solid #e0e0e0;

  ::ng-deep .mat-mdc-paginator {
    background: transparent;
  }
}
