<!-- <PERSON>er -->
<div class="page-header">
  <h1 class="page-title">Winners Gallery</h1>
  <p class="page-subtitle">Celebrate our amazing winners and their incredible prizes</p>
</div>

<!-- Loading State -->
<div *ngIf="loading" class="loading-container">
  <mat-spinner diameter="40"></mat-spinner>
  <p>Loading winners...</p>
</div>

<!-- Error State -->
<div *ngIf="!loading && error" class="error-container">
  <mat-card class="error-card">
    <mat-card-content>
      <div class="error-content">
        <mat-icon class="error-icon">error</mat-icon>
        <h3>Unable to Load Winners</h3>
        <p>{{ errorMessage || 'There was an error loading the winners. Please try again.' }}</p>
        <button mat-raised-button color="primary" (click)="loadWinners()">
          <mat-icon>refresh</mat-icon>
          Retry
        </button>
      </div>
    </mat-card-content>
  </mat-card>
</div>

<!-- Winners Grid -->
<div *ngIf="!loading && !error && winners.length > 0" class="winners-content">
  <div class="winners-grid">
    <mat-card class="winner-card" *ngFor="let winner of winners">
      <mat-card-content>
        <!-- Winner Avatar -->
        <div class="winner-avatar-section">
          <div class="winner-avatar">
            <div class="avatar-placeholder" *ngIf="!winner.user.profileImage">
              {{ getUserInitials(winner) }}
            </div>
            <img *ngIf="winner.user.profileImage" [src]="winner.user.profileImage" [alt]="winner.user.firstName + ' ' + winner.user.lastName" class="avatar-image">
          </div>
          <div class="winner-info">
            <h3 class="winner-name">{{ winner.user.firstName }} {{ winner.user.lastName }}</h3>
            <p class="win-date">Won on {{ formatDate(winner.selectionDate) }}</p>
          </div>
        </div>

        <!-- Prize Information -->
        <div class="prize-section">
          <div class="prize-image" *ngIf="winner.prize.imageUrl">
            <img [src]="winner.prize.imageUrl" [alt]="winner.prize.name" class="prize-img">
          </div>
          <div class="prize-placeholder" *ngIf="!winner.prize.imageUrl">
            <mat-icon>card_giftcard</mat-icon>
          </div>
          
          <div class="prize-details">
            <h4 class="prize-name">{{ winner.prize.name }}</h4>
            <p class="prize-value">${{ formatPrizeValue(winner.prize.value) }}</p>
            <p class="giveaway-name">{{ winner.giveaway.title }}</p>
          </div>
        </div>

        <!-- Status Badge -->
        <div class="status-section">
          <mat-chip class="status-chip" [class]="getStatusClass(winner.status)">
            <mat-icon>{{ winner.status === 'SHIPPED' ? 'local_shipping' : winner.status === 'CLAIMED' ? 'check_circle' : winner.status === 'NOTIFIED' ? 'notifications' : 'star' }}</mat-icon>
            {{ getStatusText(winner.status) }}
          </mat-chip>
        </div>
      </mat-card-content>
    </mat-card>
  </div>

  <!-- Pagination -->
  <div class="pagination-container" *ngIf="totalWinners > pageSize">
    <mat-paginator
      [length]="totalWinners"
      [pageSize]="pageSize"
      [pageIndex]="currentPage"
      [pageSizeOptions]="[12, 24, 48]"
      (page)="onPageChange($event)"
      showFirstLastButtons>
    </mat-paginator>
  </div>
</div>

<!-- Empty State -->
<div *ngIf="!loading && !error && winners.length === 0" class="empty-container">
  <mat-card class="empty-card">
    <mat-card-content>
      <div class="empty-content">
        <mat-icon class="empty-icon">emoji_events</mat-icon>
        <h3>No Winners Yet</h3>
        <p>Check back soon to see our amazing winners and their prizes!</p>
      </div>
    </mat-card-content>
  </mat-card>
</div>
