import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatButtonModule } from '@angular/material/button';
import { MatChipsModule } from '@angular/material/chips';
import { MatPaginatorModule, PageEvent } from '@angular/material/paginator';
import { finalize } from 'rxjs/operators';

// Mock interfaces - replace with actual service imports when available
interface Winner {
  id: string;
  user: {
    firstName: string;
    lastName: string;
    profileImage?: string;
  };
  giveaway: {
    title: string;
    id: string;
  };
  prize: {
    name: string;
    value: number;
    imageUrl?: string;
  };
  selectionDate: Date;
  status: 'SELECTED' | 'NOTIFIED' | 'CLAIMED' | 'SHIPPED';
}

@Component({
  selector: 'app-winners',
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatIconModule,
    MatProgressSpinnerModule,
    MatButtonModule,
    MatChipsModule,
    MatPaginatorModule
  ],
  templateUrl: './winners.component.html',
  styleUrls: ['./winners.component.scss']
})
export class WinnersComponent implements OnInit {
  winners: Winner[] = [];
  loading = false;
  error = false;
  errorMessage = '';
  
  // Pagination
  totalWinners = 0;
  currentPage = 0;
  pageSize = 12;
  
  constructor() {}

  ngOnInit(): void {
    this.loadWinners();
  }

  /**
   * Load winners with pagination
   */
  loadWinners(): void {
    this.loading = true;
    this.error = false;
    
    // Mock data - replace with actual service call
    setTimeout(() => {
      this.winners = this.generateMockWinners();
      this.totalWinners = 50; // Mock total
      this.loading = false;
    }, 1000);
  }

  /**
   * Handle page change
   */
  onPageChange(event: PageEvent): void {
    this.currentPage = event.pageIndex;
    this.pageSize = event.pageSize;
    this.loadWinners();
  }

  /**
   * Get user initials for avatar fallback
   */
  getUserInitials(winner: Winner): string {
    const firstInitial = winner.user.firstName?.charAt(0) || '';
    const lastInitial = winner.user.lastName?.charAt(0) || '';
    return (firstInitial + lastInitial).toUpperCase();
  }

  /**
   * Format prize value
   */
  formatPrizeValue(value: number): string {
    return (value / 100).toFixed(2);
  }

  /**
   * Format date for display
   */
  formatDate(date: Date): string {
    return new Date(date).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  }

  /**
   * Get status badge class
   */
  getStatusClass(status: string): string {
    switch (status) {
      case 'CLAIMED':
        return 'status-claimed';
      case 'SHIPPED':
        return 'status-shipped';
      case 'NOTIFIED':
        return 'status-notified';
      default:
        return 'status-selected';
    }
  }

  /**
   * Get status display text
   */
  getStatusText(status: string): string {
    switch (status) {
      case 'CLAIMED':
        return 'Claimed';
      case 'SHIPPED':
        return 'Shipped';
      case 'NOTIFIED':
        return 'Notified';
      default:
        return 'Selected';
    }
  }

  /**
   * Generate mock winners data - remove when real service is available
   */
  private generateMockWinners(): Winner[] {
    const mockWinners: Winner[] = [];
    const firstNames = ['John', 'Jane', 'Mike', 'Sarah', 'David', 'Emily', 'Chris', 'Lisa', 'Tom', 'Anna'];
    const lastNames = ['Smith', 'Johnson', 'Williams', 'Brown', 'Jones', 'Garcia', 'Miller', 'Davis', 'Rodriguez', 'Martinez'];
    const prizes = [
      { name: 'iPhone 15 Pro', value: 99900 },
      { name: 'MacBook Air', value: 129900 },
      { name: 'PlayStation 5', value: 49999 },
      { name: 'Nintendo Switch', value: 29999 },
      { name: 'AirPods Pro', value: 24999 },
      { name: 'iPad Pro', value: 79999 }
    ];
    const giveaways = [
      'Tech Gadgets Giveaway',
      'Gaming Console Contest',
      'Apple Products Raffle',
      'Holiday Special Draw',
      'Summer Electronics Giveaway'
    ];
    const statuses: Winner['status'][] = ['SELECTED', 'NOTIFIED', 'CLAIMED', 'SHIPPED'];

    for (let i = 0; i < 12; i++) {
      const firstName = firstNames[Math.floor(Math.random() * firstNames.length)];
      const lastName = lastNames[Math.floor(Math.random() * lastNames.length)];
      const prize = prizes[Math.floor(Math.random() * prizes.length)];
      const giveaway = giveaways[Math.floor(Math.random() * giveaways.length)];
      const status = statuses[Math.floor(Math.random() * statuses.length)];

      mockWinners.push({
        id: `winner-${i + 1}`,
        user: {
          firstName,
          lastName
        },
        giveaway: {
          title: giveaway,
          id: `giveaway-${i + 1}`
        },
        prize: {
          name: prize.name,
          value: prize.value
        },
        selectionDate: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000),
        status
      });
    }

    return mockWinners;
  }
}
