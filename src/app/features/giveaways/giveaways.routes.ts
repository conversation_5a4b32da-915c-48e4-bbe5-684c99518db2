import { Routes } from '@angular/router';
import { AuthGuard } from '../../core/guards/auth.guard';

export const GIVEAWAYS_ROUTES: Routes = [
  /*{
    path: '',
    loadComponent: () => import('./giveaway-list/giveaway-list.component').then(c => c.GiveawayListComponent),
    title: 'Giveaways - Winners Society'
  },
  {
    path: ':id',
    loadComponent: () => import('./giveaway-detail/giveaway-detail.component').then(c => c.GiveawayDetailComponent),
    title: 'Giveaway Details - Winners Society'
  }*/
];
