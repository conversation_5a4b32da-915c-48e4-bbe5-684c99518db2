import { Component, inject } from '@angular/core';
import { Form<PERSON>uilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { Router, RouterLink } from '@angular/router';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatIconModule } from '@angular/material/icon';
import { NgIf } from '@angular/common';

import { AuthService } from '../../../../core/services/auth.service';
import { NotificationService } from '../../../../core/services/notification.service';

@Component({
  selector: 'app-admin-login',
  standalone: true,
  imports: [
    ReactiveFormsModule,
    MatCardModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatCheckboxModule,
    MatProgressSpinnerModule,
    MatIconModule,
    RouterLink,
    NgIf
  ],
  template: `
    <div class="admin-login-container">
      <div class="auth-wrapper py-5">
        <div class="container">
          <div class="row justify-content-center">
            <div class="col-lg-5 col-md-7">
              <div class="card admin-login-card shadow">
                <div class="p-4 text-center mb-3">
                  <div class="logo mb-4">
                    <div class="logo-icon mx-auto mb-3">
                      <i class="fas fa-trophy text-warning fa-3x"></i>
                    </div>
                    <h2 class="mb-1">Winners Society</h2>
                    <p class="text-muted">Admin Portal</p>
                  </div>
                  
                  <h3 class="mb-3 fw-bold">Administrator Login</h3>
                </div>
                
                <div class="card-body p-4">
                  <form [formGroup]="adminLoginForm" (ngSubmit)="onSubmit()">
                    <div class="mb-3">
                      <mat-form-field appearance="outline" class="w-100">
                        <mat-label>Email Address</mat-label>
                        <input matInput type="email" formControlName="email" placeholder="<EMAIL>" required>
                        <mat-icon matPrefix class="text-muted me-2">email</mat-icon>
                        <mat-error *ngIf="adminLoginForm.get('email')?.hasError('required')">Email is required</mat-error>
                        <mat-error *ngIf="adminLoginForm.get('email')?.hasError('email')">Please enter a valid email</mat-error>
                      </mat-form-field>
                    </div>
                    
                    <div class="mb-4">
                      <mat-form-field appearance="outline" class="w-100">
                        <mat-label>Password</mat-label>
                        <input matInput [type]="hidePassword ? 'password' : 'text'" formControlName="password" placeholder="Enter your password" required>
                        <mat-icon matPrefix class="text-muted me-2">lock</mat-icon>
                        <button type="button" mat-icon-button matSuffix (click)="hidePassword = !hidePassword">
                          <mat-icon>{{hidePassword ? 'visibility_off' : 'visibility'}}</mat-icon>
                        </button>
                        <mat-error *ngIf="adminLoginForm.get('password')?.hasError('required')">Password is required</mat-error>
                      </mat-form-field>
                    </div>
                    
                    <div class="d-flex justify-content-between mb-4">
                      <mat-checkbox formControlName="rememberMe" color="primary">Remember me</mat-checkbox>
                      <a routerLink="/auth/forgot-password" class="text-decoration-none">Forgot password?</a>
                    </div>
                    
                    <button mat-raised-button color="primary" type="submit" [disabled]="adminLoginForm.invalid || isLoading" class="w-100 py-2">
                      <ng-container *ngIf="!isLoading">Sign In to Admin</ng-container>
                      <mat-spinner *ngIf="isLoading" diameter="24" class="mx-auto"></mat-spinner>
                    </button>
                  </form>
                  
                  <div class="mt-4 text-center">
                    <a routerLink="/" class="text-decoration-none">
                      <i class="fas fa-arrow-left me-2"></i>Back to main site
                    </a>
                  </div>
                </div>
                
                <div class="card-footer bg-light p-3 text-center">
                  <div class="d-flex flex-column gap-2">
                    <p class="mb-0 small">Need help? <a href="mailto:<EMAIL>">Contact Support</a></p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  `,
  styles: `
    .admin-login-container {
      min-height: 100vh;
      background: linear-gradient(135deg, #2c3e50, #4c6273);
      display: flex;
      align-items: center;
    }
    
    .auth-wrapper {
      width: 100%;
    }
    
    .admin-login-card {
      border: none;
      border-radius: 10px;
      overflow: hidden;
    }
    
    .logo-icon {
      width: 80px;
      height: 80px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 50%;
      background-color: rgba(255, 193, 7, 0.1);
    }
    
    mat-form-field {
      width: 100%;
    }
    
    .mat-mdc-checkbox {
      display: inline-block;
      margin-right: 5px;
    }
  `
})
export class AdminLoginComponent {
  adminLoginForm: FormGroup;
  isLoading = false;
  hidePassword = true;
  
  private fb = inject(FormBuilder);
  private authService = inject(AuthService);
  private router = inject(Router);
  private notificationService = inject(NotificationService);
  
  constructor() {
    this.adminLoginForm = this.fb.group({
      email: ['', [Validators.required, Validators.email]],
      password: ['', [Validators.required]],
      rememberMe: [false]
    });
  }
  
  onSubmit(): void {
    if (this.adminLoginForm.invalid) {
      return;
    }
    
    this.isLoading = true;
    
    const { email, password } = this.adminLoginForm.value;
    
    this.authService.loginAdmin(email, password).subscribe({
      next: (user) => {
        // Save rememberMe preference if selected
        if (this.adminLoginForm.value.rememberMe) {
          localStorage.setItem('rememberAdminEmail', email);
        } else {
          localStorage.removeItem('rememberAdminEmail');
        }
        
        this.isLoading = false;
        
        // Navigate to admin dashboard or intended URL
        const redirectUrl = this.authService.adminRedirectUrl || '/admin/dashboard';
        this.router.navigate([redirectUrl]);
      },
      error: (error) => {
        console.error('Admin login failed:', error);
        this.isLoading = false;
        
        // Show specific error messages based on error response
        if (error.status === 401) {
          this.notificationService.error('Invalid email or password');
        } else if (error.status === 403) {
          this.notificationService.error('Your account does not have admin privileges');
        } else {
          this.notificationService.error('Login failed. Please try again later.');
        }
      }
    });
  }
}
