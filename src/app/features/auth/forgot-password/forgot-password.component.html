<div class="modern-auth-container">
  <!-- Background Elements -->
  <div class="auth-background">
    <div class="gradient-orb orb-1"></div>
    <div class="gradient-orb orb-2"></div>
    <div class="gradient-orb orb-3"></div>
  </div>

  <!-- Auth Content -->
  <div class="auth-content">
    <!-- Brand Section -->
    <div class="auth-brand">
      <div class="brand-logo">
        <svg width="48" height="48" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
          <circle cx="16" cy="16" r="16" fill="url(#authGradient)"/>
          <path d="M16 8L20 12L16 16L12 12L16 8Z" fill="white"/>
          <path d="M16 16L20 20L16 24L12 20L16 16Z" fill="white" opacity="0.8"/>
          <defs>
            <linearGradient id="authGradient" x1="0" y1="0" x2="32" y2="32" gradientUnits="userSpaceOnUse">
              <stop stop-color="#667eea"/>
              <stop offset="1" stop-color="#764ba2"/>
            </linearGradient>
          </defs>
        </svg>
        <span class="brand-text">Winners Society</span>
      </div>
    </div>

    <!-- Auth Card -->
    <div class="auth-card">
      <!-- Success State -->
      <div *ngIf="isSubmitted" class="success-state">
        <div class="success-icon">
          <svg width="64" height="64" viewBox="0 0 24 24" fill="none">
            <circle cx="12" cy="12" r="10" stroke="#00c896" stroke-width="2"/>
            <path d="M9 12L11 14L15 10" stroke="#00c896" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
        </div>
        <h1 class="auth-title">Check Your Email</h1>
        <p class="auth-subtitle">
          We've sent password reset instructions to <strong>{{ forgotPasswordForm.get('email')?.value }}</strong>
        </p>
        <p class="auth-subtitle">
          Please check your email and follow the link to reset your password. The link will expire in 1 hour.
        </p>
        
        <div class="success-actions">
          <button type="button" (click)="goBackToLogin()" class="auth-submit-btn">
            <span class="btn-content">
              <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
                <path d="M15.8333 10H4.16667M4.16667 10L9.16667 15M4.16667 10L9.16667 5" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
              <span>Back to Login</span>
            </span>
          </button>
          <button type="button" (click)="resendInstructions()" class="resend-btn">
            Didn't receive the email? Try again
          </button>
        </div>
      </div>

      <!-- Form State -->
      <div *ngIf="!isSubmitted">
        <div class="auth-header">
          <h1 class="auth-title">Forgot Password?</h1>
          <p class="auth-subtitle">
            No worries! Enter your email address and we'll send you instructions to reset your password.
          </p>
        </div>

        <form [formGroup]="forgotPasswordForm" (ngSubmit)="onSubmit()" class="auth-form">
          <!-- Email Field -->
          <div class="form-group">
            <label class="form-label">Email Address</label>
            <div class="input-wrapper">
              <div class="input-icon">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                  <path d="M4 4H20C21.1 4 22 4.9 22 6V18C22 19.1 21.1 20 20 20H4C2.9 20 2 19.1 2 18V6C2 4.9 2.9 4 4 4Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                  <polyline points="22,6 12,13 2,6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
              </div>
              <input
                type="email"
                formControlName="email"
                placeholder="Enter your email address"
                class="form-input"
                [class.error]="forgotPasswordForm.get('email')?.invalid && forgotPasswordForm.get('email')?.touched"
              >
            </div>
            <div class="form-error" *ngIf="forgotPasswordForm.get('email')?.hasError('required') && forgotPasswordForm.get('email')?.touched">
              Email is required
            </div>
            <div class="form-error" *ngIf="forgotPasswordForm.get('email')?.hasError('email') && forgotPasswordForm.get('email')?.touched">
              Please enter a valid email address
            </div>
          </div>

          <!-- Submit Button -->
          <button type="submit" [disabled]="forgotPasswordForm.invalid || isLoading" class="auth-submit-btn">
            <span *ngIf="!isLoading" class="btn-content">
              <span>Send Reset Instructions</span>
              <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
                <path d="M4.16667 10H15.8333M15.8333 10L10.8333 5M15.8333 10L10.8333 15" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </span>
            <div *ngIf="isLoading" class="loading-spinner">
              <div class="spinner"></div>
              <span>Sending...</span>
            </div>
          </button>
        </form>

        <!-- Auth Footer -->
        <div class="auth-footer">
          <p class="footer-text">
            Remember your password?
            <a routerLink="/auth/login" class="footer-link">Back to Login</a>
          </p>
        </div>
      </div>
    </div>
  </div>
</div>
