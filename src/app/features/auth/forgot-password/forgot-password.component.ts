import { Component, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { Router, RouterLink } from '@angular/router';

import { AuthService } from '../../../core/services/auth.service';
import { NotificationService } from '../../../core/services/notification.service';

@Component({
  selector: 'app-forgot-password',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    RouterLink
  ],
  templateUrl: './forgot-password.component.html',
  styleUrls: ['./forgot-password.component.scss']
})
export class ForgotPasswordComponent {
  forgotPasswordForm: FormGroup;
  isLoading = false;
  isSubmitted = false;

  private fb = inject(FormBuilder);
  private authService = inject(AuthService);
  private router = inject(Router);
  private notificationService = inject(NotificationService);

  constructor() {
    this.forgotPasswordForm = this.fb.group({
      email: ['', [Validators.required, Validators.email]]
    });
  }

  onSubmit(): void {
    if (this.forgotPasswordForm.invalid || this.isLoading) {
      this.forgotPasswordForm.markAllAsTouched();
      return;
    }

    this.isLoading = true;
    const email = this.forgotPasswordForm.get('email')?.value;

    this.authService.forgotPassword(email).subscribe({
      next: (response) => {
        this.isLoading = false;
        this.isSubmitted = true;
        this.notificationService.success('Password reset instructions have been sent to your email address.');
      },
      error: (error) => {
        this.isLoading = false;
        console.error('Forgot password failed:', error);
        
        if (error.status === 400) {
          this.notificationService.error('Please enter a valid email address.');
        } else if (error.status === 429) {
          this.notificationService.error('Too many requests. Please try again later.');
        } else {
          this.notificationService.error('Failed to send reset instructions. Please try again.');
        }
      }
    });
  }

  goBackToLogin(): void {
    this.router.navigate(['/auth/login']);
  }

  resendInstructions(): void {
    this.isSubmitted = false;
    this.forgotPasswordForm.reset();
  }
}
