import { Component, inject, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { Router, RouterLink, ActivatedRoute } from '@angular/router';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatIconModule } from '@angular/material/icon';

import { AuthService } from '../../../core/services/auth.service';
import { NotificationService } from '../../../core/services/notification.service';

@Component({
  selector: 'app-login',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatCardModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatCheckboxModule,
    MatProgressSpinnerModule,
    MatIconModule,
    RouterLink
  ],
  templateUrl: './login.component.html',
  styleUrls: ['./login.component.scss']
})
export class LoginComponent implements OnInit {
  loginForm: FormGroup;
  isLoading = false;
  hidePassword = true;
  showResendVerification = false;
  unverifiedEmail = '';

  private fb = inject(FormBuilder);
  private authService = inject(AuthService);
  private router = inject(Router);
  private route = inject(ActivatedRoute);
  private notificationService = inject(NotificationService);

  constructor() {
    this.loginForm = this.fb.group({
      email: ['', [Validators.required, Validators.email]],
      password: ['', [Validators.required]],
      rememberMe: [false]
    });
  }

  ngOnInit(): void {
    // Check for remembered email
    const rememberedEmail = localStorage.getItem('rememberEmail');
    if (rememberedEmail) {
      this.loginForm.patchValue({
        email: rememberedEmail,
        rememberMe: true
      });
    }

    // Check if redirected from verification page to resend verification
    this.route.queryParams.subscribe(params => {
      if (params['resendVerification'] === 'true') {
        this.showResendVerification = true;
      }
    });
  }

  onSubmit(): void {
    if (this.loginForm.invalid || this.isLoading) {
      return;
    }

    const { email, password, rememberMe } = this.loginForm.value;
    this.isLoading = true;

    this.authService.login(email, password).subscribe({
      next: () => {
        // Save rememberMe preference if selected
        if (rememberMe) {
          localStorage.setItem('rememberEmail', email);
        } else {
          localStorage.removeItem('rememberEmail');
        }

        // Navigate to the redirect URL or dashboard
        const redirectUrl = this.authService.redirectUrl || '/dashboard';
        this.router.navigateByUrl(redirectUrl);

        this.notificationService.success('Welcome back to Winners Society!');
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Login failed:', error);
        this.isLoading = false;

        // Show user-friendly error message
        if (error.status === 401) {
          this.notificationService.error('Invalid email or password');
        } else if (error.status === 403) {
          // Check if it's an email verification issue
          if (error.error?.message?.includes('verify') || error.error?.message?.includes('verification')) {
            this.unverifiedEmail = email;
            this.showResendVerification = true;
            this.notificationService.error('Please verify your email address before signing in.');
          } else {
            this.notificationService.error('Your account has been suspended. Please contact support.');
          }
        } else {
          this.notificationService.error('Login failed. Please try again later.');
        }
      }
    });
  }

  /**
   * Resend verification email
   */
  resendVerificationEmail(): void {
    const email = this.unverifiedEmail || this.loginForm.get('email')?.value;
    if (!email) {
      this.notificationService.error('Please enter your email address');
      return;
    }

    this.authService.resendVerificationEmailByEmail(email).subscribe({
      next: () => {
        this.notificationService.success('Verification email sent successfully!');
        this.showResendVerification = false;
      },
      error: (error) => {
        console.error('Failed to resend verification email:', error);
        this.notificationService.error('Failed to send verification email. Please try again.');
      }
    });
  }

  /**
   * Hide resend verification section
   */
  hideResendVerification(): void {
    this.showResendVerification = false;
    this.unverifiedEmail = '';
  }
}
