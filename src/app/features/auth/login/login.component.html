<div class="modern-auth-container">
  <!-- Background Elements -->
  <div class="auth-background">
    <div class="gradient-orb orb-1"></div>
    <div class="gradient-orb orb-2"></div>
    <div class="gradient-orb orb-3"></div>
  </div>

  <!-- Auth Content -->
  <div class="auth-content">
    <!-- Brand Section -->
    <div class="auth-brand">
      <div class="brand-logo">
        <svg width="48" height="48" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
          <circle cx="16" cy="16" r="16" fill="url(#authGradient)"/>
          <path d="M16 8L20 12L16 16L12 12L16 8Z" fill="white"/>
          <path d="M16 16L20 20L16 24L12 20L16 16Z" fill="white" opacity="0.8"/>
          <defs>
            <linearGradient id="authGradient" x1="0" y1="0" x2="32" y2="32" gradientUnits="userSpaceOnUse">
              <stop stop-color="#667eea"/>
              <stop offset="1" stop-color="#764ba2"/>
            </linearGradient>
          </defs>
        </svg>
        <span class="brand-text">Winners Society</span>
      </div>
    </div>

    <!-- Auth Card -->
    <div class="auth-card">
      <div class="auth-header">
        <h1 class="auth-title">Welcome Back</h1>
        <p class="auth-subtitle">Sign in to your Winners Society account and continue your winning journey</p>
      </div>

      <form [formGroup]="loginForm" (ngSubmit)="onSubmit()" class="auth-form">
        <!-- Email Field -->
        <div class="form-group">
          <label class="form-label">Email Address</label>
          <div class="input-wrapper">
            <div class="input-icon">
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                <path d="M4 4H20C21.1 4 22 4.9 22 6V18C22 19.1 21.1 20 20 20H4C2.9 20 2 19.1 2 18V6C2 4.9 2.9 4 4 4Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <polyline points="22,6 12,13 2,6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </div>
            <input
              type="email"
              formControlName="email"
              placeholder="Enter your email address"
              class="form-input"
              [class.error]="loginForm.get('email')?.invalid && loginForm.get('email')?.touched"
            >
          </div>
          <div class="form-error" *ngIf="loginForm.get('email')?.hasError('required') && loginForm.get('email')?.touched">
            Email is required
          </div>
          <div class="form-error" *ngIf="loginForm.get('email')?.hasError('email') && loginForm.get('email')?.touched">
            Please enter a valid email address
          </div>
        </div>

        <!-- Password Field -->
        <div class="form-group">
          <label class="form-label">Password</label>
          <div class="input-wrapper">
            <div class="input-icon">
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                <rect x="3" y="11" width="18" height="11" rx="2" ry="2" stroke="currentColor" stroke-width="2"/>
                <circle cx="12" cy="16" r="1" stroke="currentColor" stroke-width="2"/>
                <path d="M7 11V7C7 5.67392 7.52678 4.40215 8.46447 3.46447C9.40215 2.52678 10.6739 2 12 2C13.3261 2 14.5979 2.52678 15.5355 3.46447C16.4732 4.40215 17 5.67392 17 7V11" stroke="currentColor" stroke-width="2"/>
              </svg>
            </div>
            <input
              [type]="hidePassword ? 'password' : 'text'"
              formControlName="password"
              placeholder="Enter your password"
              class="form-input"
              [class.error]="loginForm.get('password')?.invalid && loginForm.get('password')?.touched"
            >
            <button type="button" class="password-toggle" (click)="hidePassword = !hidePassword">
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none" *ngIf="hidePassword">
                <path d="M1 12S5 4 12 4S23 12 23 12S19 20 12 20S1 12 1 12Z" stroke="currentColor" stroke-width="2"/>
                <circle cx="12" cy="12" r="3" stroke="currentColor" stroke-width="2"/>
              </svg>
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none" *ngIf="!hidePassword">
                <path d="M17.94 17.94C16.2306 19.243 14.1491 19.9649 12 20C5 20 1 12 1 12C2.24389 9.68192 4.028 7.66607 6.17 6.17M9.9 4.24C10.5883 4.0789 11.2931 3.99836 12 4C19 4 23 12 23 12C22.393 13.1356 21.6691 14.2048 20.84 15.19M14.12 14.12C13.8454 14.4148 13.5141 14.6512 13.1462 14.8151C12.7782 14.9791 12.3809 15.0673 11.9781 15.0744C11.5753 15.0815 11.1752 15.0074 10.8016 14.8565C10.4281 14.7056 10.0887 14.4811 9.80385 14.1962C9.51900 13.9113 9.29449 13.5719 9.14359 13.1984C8.99269 12.8248 8.91855 12.4247 8.92563 12.0219C8.93271 11.6191 9.02091 11.2218 9.18488 10.8538C9.34884 10.4858 9.58525 10.1546 9.88 9.88" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <line x1="1" y1="1" x2="23" y2="23" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </button>
          </div>
          <div class="form-error" *ngIf="loginForm.get('password')?.hasError('required') && loginForm.get('password')?.touched">
            Password is required
          </div>
        </div>

        <!-- Login Options -->
        <div class="form-options">
          <label class="checkbox-wrapper">
            <input type="checkbox" formControlName="rememberMe" class="checkbox-input">
            <span class="checkbox-custom"></span>
            <span class="checkbox-label">Remember me</span>
          </label>
          <a routerLink="/auth/forgot-password" class="forgot-link">Forgot password?</a>
        </div>

        <!-- Submit Button -->
        <button type="submit" [disabled]="loginForm.invalid || isLoading" class="auth-submit-btn">
          <span *ngIf="!isLoading" class="btn-content">
            <span>Sign In</span>
            <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
              <path d="M4.16667 10H15.8333M15.8333 10L10.8333 5M15.8333 10L10.8333 15" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
          </span>
          <div *ngIf="isLoading" class="loading-spinner">
            <div class="spinner"></div>
            <span>Signing In...</span>
          </div>
        </button>
      </form>

      <!-- Email Verification Section -->
      <div *ngIf="showResendVerification" class="verification-section">
        <div class="verification-header">
          <div class="verification-icon">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
              <path d="M4 4H20C21.1 4 22 4.9 22 6V18C22 19.1 21.1 20 20 20H4C2.9 20 2 19.1 2 18V6C2 4.9 2.9 4 4 4Z" stroke="#f59e0b" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              <polyline points="22,6 12,13 2,6" stroke="#f59e0b" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              <circle cx="18" cy="8" r="3" fill="#f59e0b"/>
              <path d="M16 8L17 9L20 6" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
          </div>
          <h3 class="verification-title">Email Verification Required</h3>
          <p class="verification-text">
            Please verify your email address before signing in. Check your inbox for a verification email.
          </p>
        </div>

        <div class="verification-actions">
          <button type="button" (click)="resendVerificationEmail()" class="resend-verification-btn">
            <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
              <path d="M4.16667 10H15.8333M15.8333 10L10.8333 5M15.8333 10L10.8333 15" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
            <span>Resend Verification Email</span>
          </button>
          <button type="button" (click)="hideResendVerification()" class="close-verification-btn">
            <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
              <path d="M12 4L4 12M4 4L12 12" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
          </button>
        </div>
      </div>

      <!-- Auth Footer -->
      <div class="auth-footer">
        <p class="footer-text">
          Don't have an account?
          <a routerLink="/auth/signup" class="footer-link">Create one now</a>
        </p>
      </div>
    </div>
  </div>
</div>
