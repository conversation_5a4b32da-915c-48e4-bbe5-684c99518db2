import { Component, inject, OnInit } from '@angular/core';
import { <PERSON><PERSON><PERSON>er, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { Router, RouterLink } from '@angular/router';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatIconModule } from '@angular/material/icon';
import { NgIf } from '@angular/common';

import { AuthService } from '../../../../core/services/auth.service';
import { NotificationService } from '../../../../core/services/notification.service';

@Component({
  selector: 'app-member-login',
  standalone: true,
  imports: [
    ReactiveFormsModule,
    MatCardModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatCheckboxModule,
    MatProgressSpinnerModule,
    MatIconModule,
    RouterLink,
    NgIf
  ],
  template: `
    <div class="member-login-container">
      <div class="auth-wrapper py-5">
        <div class="container">
          <div class="row justify-content-center">
            <div class="col-lg-10">
              <div class="card member-login-card shadow overflow-hidden">
                <div class="row g-0">
                  <!-- Left panel with image and text -->
                  <div class="col-md-6 d-none d-md-block">
                    <div class="login-banner h-100 d-flex flex-column justify-content-between text-white p-4">
                      <div class="banner-header">
                        <h2 class="mb-2">Winners Society</h2>
                        <p class="mb-0">Where winners gather</p>
                      </div>

                      <div class="banner-content my-4">
                        <h3>Member Benefits</h3>
                        <ul class="ps-3">
                          <li>Exclusive giveaways</li>
                          <li>Priority entries</li>
                          <li>Premium support</li>
                          <li>Special promotions</li>
                        </ul>
                        <p>Sign in to your account to access all these benefits and more!</p>
                      </div>

                      <div class="banner-footer small opacity-75">
                        © {{currentYear}} Winners Society. All rights reserved.
                      </div>
                    </div>
                  </div>

                  <!-- Right panel with login form -->
                  <div class="col-md-6">
                    <div class="p-5">
                      <div class="text-center mb-4">
                        <div class="d-md-none logo-icon mx-auto mb-3">
                          <i class="fas fa-trophy text-warning fa-3x"></i>
                        </div>
                        <h2 class="fw-bold mb-1">Welcome Back!</h2>
                        <p class="text-muted">Sign in to your member account</p>
                      </div>

                      <form [formGroup]="memberLoginForm" (ngSubmit)="onSubmit()">
                        <div class="mb-3">
                          <mat-form-field appearance="outline" class="w-100">
                            <mat-label>Email Address</mat-label>
                            <input matInput type="email" formControlName="email" placeholder="Your email" required>
                            <mat-icon matPrefix class="text-muted me-2">email</mat-icon>
                            <mat-error *ngIf="memberLoginForm.get('email')?.hasError('required')">Email is required</mat-error>
                            <mat-error *ngIf="memberLoginForm.get('email')?.hasError('email')">Please enter a valid email</mat-error>
                          </mat-form-field>
                        </div>

                        <div class="mb-3">
                          <mat-form-field appearance="outline" class="w-100">
                            <mat-label>Password</mat-label>
                            <input matInput [type]="hidePassword ? 'password' : 'text'" formControlName="password" placeholder="Your password" required>
                            <mat-icon matPrefix class="text-muted me-2">lock</mat-icon>
                            <button type="button" mat-icon-button matSuffix (click)="hidePassword = !hidePassword">
                              <mat-icon>{{hidePassword ? 'visibility_off' : 'visibility'}}</mat-icon>
                            </button>
                            <mat-error *ngIf="memberLoginForm.get('password')?.hasError('required')">Password is required</mat-error>
                          </mat-form-field>
                        </div>

                        <div class="d-flex justify-content-between mb-4">
                          <mat-checkbox formControlName="rememberMe" color="primary">Remember me</mat-checkbox>
                          <a routerLink="/auth/forgot-password" class="text-decoration-none">Forgot password?</a>
                        </div>

                        <button mat-raised-button color="primary" type="submit" [disabled]="memberLoginForm.invalid || isLoading" class="w-100 py-2">
                          <ng-container *ngIf="!isLoading">Sign In</ng-container>
                          <mat-spinner *ngIf="isLoading" diameter="24" class="mx-auto"></mat-spinner>
                        </button>
                      </form>

                      <div class="mt-4 text-center">
                        <p class="mb-0">Don't have an account? <a routerLink="/auth/signup" class="fw-bold text-decoration-none">Sign Up</a></p>
                      </div>

                      <div class="mt-4 pt-3 border-top">
                        <div class="text-center">
                          <p class="text-muted mb-0 small">By signing in, you agree to our <a href="/terms" class="text-decoration-none">Terms of Service</a> and <a href="/privacy" class="text-decoration-none">Privacy Policy</a></p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  `,
  styles: `
    .member-login-container {
      min-height: 100vh;
      background: linear-gradient(135deg, #f5f7fa, #e5e9f2);
      display: flex;
      align-items: center;
    }

    .auth-wrapper {
      width: 100%;
    }

    .member-login-card {
      border: none;
      border-radius: 12px;
    }

    .login-banner {
      background: linear-gradient(135deg, #4a148c, #7b1fa2);
      height: 100%;
    }

    .logo-icon {
      width: 70px;
      height: 70px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 50%;
      background-color: rgba(255, 193, 7, 0.1);
    }

    mat-form-field {
      width: 100%;
    }

    .mat-mdc-checkbox {
      display: inline-block;
      margin-right: 5px;
    }
  `
})
export class MemberLoginComponent implements OnInit {
  memberLoginForm: FormGroup;
  isLoading = false;
  hidePassword = true;
  currentYear = new Date().getFullYear();

  private fb = inject(FormBuilder);
  private authService = inject(AuthService);
  private router = inject(Router);
  private notificationService = inject(NotificationService);

  constructor() {
    this.memberLoginForm = this.fb.group({
      email: ['', [Validators.required, Validators.email]],
      password: ['', [Validators.required]],
      rememberMe: [false]
    });
  }

  ngOnInit(): void {
    // Check for remembered email
    const rememberedEmail = localStorage.getItem('rememberMemberEmail');
    if (rememberedEmail) {
      this.memberLoginForm.patchValue({
        email: rememberedEmail,
        rememberMe: true
      });
    }
  }

  onSubmit(): void {
    if (this.memberLoginForm.invalid) {
      return;
    }

    this.isLoading = true;

    const { email, password, rememberMe } = this.memberLoginForm.value;

    this.authService.login(email, password).subscribe({
      next: (user) => {
        // Save rememberMe preference if selected
        if (rememberMe) {
          localStorage.setItem('rememberMemberEmail', email);
        } else {
          localStorage.removeItem('rememberMemberEmail');
        }

        this.isLoading = false;

        // Navigate to dashboard or intended URL
        const redirectUrl = this.authService.redirectUrl || '/dashboard';
        this.router.navigate([redirectUrl]);
      },
      error: (error) => {
        console.error('Member login failed:', error);
        this.isLoading = false;

        // Show user-friendly error message
        if (error.status === 401) {
          this.notificationService.error('Invalid email or password');
        } else if (error.status === 403) {
          this.notificationService.error('Your account has been suspended. Please contact support.');
        } else {
          this.notificationService.error('Login failed. Please try again later.');
        }
      }
    });
  }
}
