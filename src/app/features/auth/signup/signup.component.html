<div class="modern-auth-container">
  <!-- Background Elements -->
  <div class="auth-background">
    <div class="gradient-orb orb-1"></div>
    <div class="gradient-orb orb-2"></div>
    <div class="gradient-orb orb-3"></div>
  </div>

  <!-- Auth Content -->
  <div class="auth-content">
    <!-- Brand Section -->
    <div class="auth-brand">
      <div class="brand-logo">
        <svg width="48" height="48" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
          <circle cx="16" cy="16" r="16" fill="url(#authGradient)"/>
          <path d="M16 8L20 12L16 16L12 12L16 8Z" fill="white"/>
          <path d="M16 16L20 20L16 24L12 20L16 16Z" fill="white" opacity="0.8"/>
          <defs>
            <linearGradient id="authGradient" x1="0" y1="0" x2="32" y2="32" gradientUnits="userSpaceOnUse">
              <stop stop-color="#667eea"/>
              <stop offset="1" stop-color="#764ba2"/>
            </linearGradient>
          </defs>
        </svg>
        <span class="brand-text">Winners Society</span>
      </div>
    </div>

    <!-- Auth Card -->
    <div class="auth-card">
      <!-- Registration Success State -->
      <div *ngIf="isRegistrationSuccess" class="success-state">
        <div class="success-icon">
          <svg width="64" height="64" viewBox="0 0 24 24" fill="none">
            <circle cx="12" cy="12" r="10" stroke="#00c896" stroke-width="2"/>
            <path d="M9 12L11 14L15 10" stroke="#00c896" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
        </div>
        <h1 class="auth-title">Account Created Successfully!</h1>
        <p class="auth-subtitle">
          We've sent a verification email to <strong>{{ userEmail }}</strong>
        </p>
        <p class="auth-subtitle">
          Please check your email and click the verification link to activate your account. You must verify your email before you can sign in.
        </p>

        <div class="success-actions">
          <button type="button" (click)="goToLogin()" class="auth-submit-btn">
            <span class="btn-content">
              <span>Continue to Login</span>
              <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
                <path d="M4.16667 10H15.8333M15.8333 10L10.8333 5M15.8333 10L10.8333 15" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </span>
          </button>
          <button type="button" (click)="resendVerificationEmail()" class="resend-btn">
            Didn't receive the email? Resend verification
          </button>
        </div>
      </div>

      <!-- Registration Form State -->
      <div *ngIf="!isRegistrationSuccess">
        <div class="auth-header">
          <h1 class="auth-title">Create Your Account</h1>
          <p class="auth-subtitle">Join Winners Society and start your winning journey today</p>
        </div>

      <form [formGroup]="signupForm" (ngSubmit)="onSubmit()" class="auth-form">
        <!-- Personal Information -->
        <div class="form-row">
          <div class="form-group">
            <label class="form-label">First Name</label>
            <div class="input-wrapper">
              <div class="input-icon">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                  <path d="M20 21V19C20 17.9391 19.5786 16.9217 18.8284 16.1716C18.0783 15.4214 17.0609 15 16 15H8C6.93913 15 5.92172 15.4214 5.17157 16.1716C4.42143 16.9217 4 17.9391 4 19V21" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                  <circle cx="12" cy="7" r="4" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
              </div>
              <input
                type="text"
                formControlName="firstName"
                placeholder="Enter your first name"
                class="form-input"
                [class.error]="signupForm.get('firstName')?.invalid && signupForm.get('firstName')?.touched"
              >
            </div>
            <div class="form-error" *ngIf="signupForm.get('firstName')?.hasError('required') && signupForm.get('firstName')?.touched">
              First name is required
            </div>
          </div>

          <div class="form-group">
            <label class="form-label">Last Name</label>
            <div class="input-wrapper">
              <div class="input-icon">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                  <path d="M20 21V19C20 17.9391 19.5786 16.9217 18.8284 16.1716C18.0783 15.4214 17.0609 15 16 15H8C6.93913 15 5.92172 15.4214 5.17157 16.1716C4.42143 16.9217 4 17.9391 4 19V21" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                  <circle cx="12" cy="7" r="4" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
              </div>
              <input
                type="text"
                formControlName="lastName"
                placeholder="Enter your last name"
                class="form-input"
                [class.error]="signupForm.get('lastName')?.invalid && signupForm.get('lastName')?.touched"
              >
            </div>
            <div class="form-error" *ngIf="signupForm.get('lastName')?.hasError('required') && signupForm.get('lastName')?.touched">
              Last name is required
            </div>
          </div>
        </div>

        <!-- Email Field -->
        <div class="form-group">
          <label class="form-label">Email Address</label>
          <div class="input-wrapper">
            <div class="input-icon">
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                <path d="M4 4H20C21.1 4 22 4.9 22 6V18C22 19.1 21.1 20 20 20H4C2.9 20 2 19.1 2 18V6C2 4.9 2.9 4 4 4Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <polyline points="22,6 12,13 2,6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </div>
            <input
              type="email"
              formControlName="email"
              placeholder="Enter your email address"
              class="form-input"
              [class.error]="signupForm.get('email')?.invalid && signupForm.get('email')?.touched"
            >
          </div>
          <div class="form-error" *ngIf="signupForm.get('email')?.hasError('required') && signupForm.get('email')?.touched">
            Email is required
          </div>
          <div class="form-error" *ngIf="signupForm.get('email')?.hasError('email') && signupForm.get('email')?.touched">
            Please enter a valid email address
          </div>
        </div>

        <!-- Password Fields -->
        <div class="form-row">
          <div class="form-group">
            <label class="form-label">Password</label>
            <div class="input-wrapper">
              <div class="input-icon">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                  <rect x="3" y="11" width="18" height="11" rx="2" ry="2" stroke="currentColor" stroke-width="2"/>
                  <circle cx="12" cy="16" r="1" stroke="currentColor" stroke-width="2"/>
                  <path d="M7 11V7C7 5.67392 7.52678 4.40215 8.46447 3.46447C9.40215 2.52678 10.6739 2 12 2C13.3261 2 14.5979 2.52678 15.5355 3.46447C16.4732 4.40215 17 5.67392 17 7V11" stroke="currentColor" stroke-width="2"/>
                </svg>
              </div>
              <input
                [type]="hidePassword ? 'password' : 'text'"
                formControlName="password"
                placeholder="Create a strong password"
                class="form-input"
                [class.error]="signupForm.get('password')?.invalid && signupForm.get('password')?.touched"
              >
              <button type="button" class="password-toggle" (click)="hidePassword = !hidePassword">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" *ngIf="hidePassword">
                  <path d="M1 12S5 4 12 4S23 12 23 12S19 20 12 20S1 12 1 12Z" stroke="currentColor" stroke-width="2"/>
                  <circle cx="12" cy="12" r="3" stroke="currentColor" stroke-width="2"/>
                </svg>
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" *ngIf="!hidePassword">
                  <path d="M17.94 17.94C16.2306 19.243 14.1491 19.9649 12 20C5 20 1 12 1 12C2.24389 9.68192 4.028 7.66607 6.17 6.17M9.9 4.24C10.5883 4.0789 11.2931 3.99836 12 4C19 4 23 12 23 12C22.393 13.1356 21.6691 14.2048 20.84 15.19M14.12 14.12C13.8454 14.4148 13.5141 14.6512 13.1462 14.8151C12.7782 14.9791 12.3809 15.0673 11.9781 15.0744C11.5753 15.0815 11.1752 15.0074 10.8016 14.8565C10.4281 14.7056 10.0887 14.4811 9.80385 14.1962C9.51900 13.9113 9.29449 13.5719 9.14359 13.1984C8.99269 12.8248 8.91855 12.4247 8.92563 12.0219C8.93271 11.6191 9.02091 11.2218 9.18488 10.8538C9.34884 10.4858 9.58525 10.1546 9.88 9.88" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                  <line x1="1" y1="1" x2="23" y2="23" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
              </button>
            </div>
            <div class="form-error" *ngIf="signupForm.get('password')?.hasError('required') && signupForm.get('password')?.touched">
              Password is required
            </div>
            <div class="form-error" *ngIf="signupForm.get('password')?.hasError('minlength') && signupForm.get('password')?.touched">
              Password must be at least 8 characters
            </div>
          </div>

          <div class="form-group">
            <label class="form-label">Confirm Password</label>
            <div class="input-wrapper">
              <div class="input-icon">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                  <rect x="3" y="11" width="18" height="11" rx="2" ry="2" stroke="currentColor" stroke-width="2"/>
                  <circle cx="12" cy="16" r="1" stroke="currentColor" stroke-width="2"/>
                  <path d="M7 11V7C7 5.67392 7.52678 4.40215 8.46447 3.46447C9.40215 2.52678 10.6739 2 12 2C13.3261 2 14.5979 2.52678 15.5355 3.46447C16.4732 4.40215 17 5.67392 17 7V11" stroke="currentColor" stroke-width="2"/>
                </svg>
              </div>
              <input
                [type]="hideConfirmPassword ? 'password' : 'text'"
                formControlName="confirmPassword"
                placeholder="Confirm your password"
                class="form-input"
                [class.error]="signupForm.get('confirmPassword')?.invalid && signupForm.get('confirmPassword')?.touched"
              >
              <button type="button" class="password-toggle" (click)="hideConfirmPassword = !hideConfirmPassword">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" *ngIf="hideConfirmPassword">
                  <path d="M1 12S5 4 12 4S23 12 23 12S19 20 12 20S1 12 1 12Z" stroke="currentColor" stroke-width="2"/>
                  <circle cx="12" cy="12" r="3" stroke="currentColor" stroke-width="2"/>
                </svg>
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" *ngIf="!hideConfirmPassword">
                  <path d="M17.94 17.94C16.2306 19.243 14.1491 19.9649 12 20C5 20 1 12 1 12C2.24389 9.68192 4.028 7.66607 6.17 6.17M9.9 4.24C10.5883 4.0789 11.2931 3.99836 12 4C19 4 23 12 23 12C22.393 13.1356 21.6691 14.2048 20.84 15.19M14.12 14.12C13.8454 14.4148 13.5141 14.6512 13.1462 14.8151C12.7782 14.9791 12.3809 15.0673 11.9781 15.0744C11.5753 15.0815 11.1752 15.0074 10.8016 14.8565C10.4281 14.7056 10.0887 14.4811 9.80385 14.1962C9.51900 13.9113 9.29449 13.5719 9.14359 13.1984C8.99269 12.8248 8.91855 12.4247 8.92563 12.0219C8.93271 11.6191 9.02091 11.2218 9.18488 10.8538C9.34884 10.4858 9.58525 10.1546 9.88 9.88" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                  <line x1="1" y1="1" x2="23" y2="23" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
              </button>
            </div>
            <div class="form-error" *ngIf="signupForm.get('confirmPassword')?.hasError('required') && signupForm.get('confirmPassword')?.touched">
              Please confirm your password
            </div>
            <div class="form-error" *ngIf="signupForm.hasError('passwordMismatch') && signupForm.get('confirmPassword')?.touched">
              Passwords do not match
            </div>
          </div>
        </div>

        <!-- Password Strength Indicator -->
        <div class="password-strength" *ngIf="signupForm.get('password')?.value">
          <div class="strength-bar">
            <div class="strength-fill" [class]="getPasswordStrengthClass()" [style.width.%]="passwordStrength"></div>
          </div>
          <span class="strength-text" [class]="getPasswordStrengthClass()">{{ getPasswordStrengthText() }}</span>
        </div>

        <!-- Address Information -->
        <div class="form-section">
          <h3 class="section-title">Address Information</h3>

          <!-- Address Line 1 -->
          <div class="form-group">
            <label class="form-label">Address Line 1</label>
            <div class="input-wrapper">
              <div class="input-icon">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                  <path d="M3 9L12 2L21 9V20C21 20.5304 20.7893 21.0391 20.4142 21.4142C20.0391 21.7893 19.5304 22 19 22H5C4.46957 22 3.96086 21.7893 3.58579 21.4142C3.21071 21.0391 3 20.5304 3 20V9Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                  <polyline points="9,22 9,12 15,12 15,22" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
              </div>
              <input
                type="text"
                formControlName="addressLine1"
                placeholder="Enter your street address"
                class="form-input"
                [class.error]="signupForm.get('addressLine1')?.invalid && signupForm.get('addressLine1')?.touched"
              >
            </div>
            <div class="form-error" *ngIf="signupForm.get('addressLine1')?.hasError('required') && signupForm.get('addressLine1')?.touched">
              Address is required
            </div>
          </div>

          <!-- City and State -->
          <div class="form-row">
            <div class="form-group">
              <label class="form-label">City</label>
              <div class="input-wrapper">
                <div class="input-icon">
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                    <path d="M21 10C21 17 12 23 12 23S3 17 3 10C3 7.61305 3.94821 5.32387 5.63604 3.63604C7.32387 1.94821 9.61305 1 12 1C14.3869 1 16.6761 1.94821 18.3639 3.63604C20.0518 5.32387 21 7.61305 21 10Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    <circle cx="12" cy="10" r="3" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                  </svg>
                </div>
                <input
                  type="text"
                  formControlName="city"
                  placeholder="Enter your city"
                  class="form-input"
                  [class.error]="signupForm.get('city')?.invalid && signupForm.get('city')?.touched"
                >
              </div>
              <div class="form-error" *ngIf="signupForm.get('city')?.hasError('required') && signupForm.get('city')?.touched">
                City is required
              </div>
            </div>

            <div class="form-group">
              <label class="form-label">State/Province</label>
              <div class="input-wrapper">
                <div class="input-icon">
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                    <path d="M21 10C21 17 12 23 12 23S3 17 3 10C3 7.61305 3.94821 5.32387 5.63604 3.63604C7.32387 1.94821 9.61305 1 12 1C14.3869 1 16.6761 1.94821 18.3639 3.63604C20.0518 5.32387 21 7.61305 21 10Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    <circle cx="12" cy="10" r="3" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                  </svg>
                </div>
                <input
                  type="text"
                  formControlName="state"
                  placeholder="Enter your state/province"
                  class="form-input"
                  [class.error]="signupForm.get('state')?.invalid && signupForm.get('state')?.touched"
                >
              </div>
              <div class="form-error" *ngIf="signupForm.get('state')?.hasError('required') && signupForm.get('state')?.touched">
                State/Province is required
              </div>
            </div>
          </div>

          <!-- Postal Code and Country -->
          <div class="form-row">
            <div class="form-group">
              <label class="form-label">Postal Code</label>
              <div class="input-wrapper">
                <div class="input-icon">
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                    <rect x="2" y="3" width="20" height="14" rx="2" ry="2" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    <line x1="8" y1="21" x2="16" y2="21" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    <line x1="12" y1="17" x2="12" y2="21" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                  </svg>
                </div>
                <input
                  type="text"
                  formControlName="postalCode"
                  placeholder="Enter postal code"
                  class="form-input"
                  [class.error]="signupForm.get('postalCode')?.invalid && signupForm.get('postalCode')?.touched"
                >
              </div>
              <div class="form-error" *ngIf="signupForm.get('postalCode')?.hasError('required') && signupForm.get('postalCode')?.touched">
                Postal code is required
              </div>
            </div>

            <div class="form-group">
              <label class="form-label">Country</label>
              <div class="input-wrapper">
                <div class="input-icon">
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                    <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    <line x1="2" y1="12" x2="22" y2="12" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    <path d="M12 2C14.5013 4.73835 15.9228 8.29203 16 12C15.9228 15.708 14.5013 19.2616 12 22C9.49872 19.2616 8.07725 15.708 8 12C8.07725 8.29203 9.49872 4.73835 12 2Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                  </svg>
                </div>
                <input
                  type="text"
                  formControlName="country"
                  placeholder="Enter your country"
                  class="form-input"
                  [class.error]="signupForm.get('country')?.invalid && signupForm.get('country')?.touched"
                >
              </div>
              <div class="form-error" *ngIf="signupForm.get('country')?.hasError('required') && signupForm.get('country')?.touched">
                Country is required
              </div>
            </div>
          </div>
        </div>

        <!-- Terms Agreement -->
        <div class="form-options">
          <label class="checkbox-wrapper">
            <input type="checkbox" formControlName="termsAgreed" class="checkbox-input">
            <span class="checkbox-custom"></span>
            <span class="checkbox-label">
              I agree to the <a href="/terms" target="_blank" class="terms-link">Terms of Service</a>
              and <a href="/privacy" target="_blank" class="terms-link">Privacy Policy</a>
            </span>
          </label>
        </div>
        <div class="form-error" *ngIf="signupForm.get('termsAgreed')?.hasError('required') && signupForm.get('termsAgreed')?.touched">
          You must agree to the terms to continue
        </div>

        <!-- Submit Button -->
        <button type="submit" [disabled]="signupForm.invalid || isLoading" class="auth-submit-btn">
          <span *ngIf="!isLoading" class="btn-content">
            <span>Create My Account</span>
            <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
              <path d="M4.16667 10H15.8333M15.8333 10L10.8333 5M15.8333 10L10.8333 15" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
          </span>
          <div *ngIf="isLoading" class="loading-spinner">
            <div class="spinner"></div>
            <span>Creating Account...</span>
          </div>
        </button>
      </form>

        <!-- Auth Footer -->
        <div class="auth-footer">
          <p class="footer-text">
            Already have an account?
            <a routerLink="/auth/login" class="footer-link">Sign in here</a>
          </p>
        </div>
      </div>
    </div>
  </div>
</div>
