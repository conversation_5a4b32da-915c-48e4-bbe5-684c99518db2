import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ReactiveFormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { of, throwError } from 'rxjs';

import { SignupComponent } from './signup.component';
import { AuthService } from '../../../core/services/auth.service';
import { NotificationService } from '../../../core/services/notification.service';

describe('SignupComponent', () => {
  let component: SignupComponent;
  let fixture: ComponentFixture<SignupComponent>;
  let mockAuthService: jasmine.SpyObj<AuthService>;
  let mockRouter: jasmine.SpyObj<Router>;
  let mockNotificationService: jasmine.SpyObj<NotificationService>;

  beforeEach(async () => {
    const authServiceSpy = jasmine.createSpyObj('AuthService', ['register', 'resendVerificationEmailByEmail']);
    const routerSpy = jasmine.createSpyObj('Router', ['navigate']);
    const notificationServiceSpy = jasmine.createSpyObj('NotificationService', ['success', 'error']);

    await TestBed.configureTestingModule({
      declarations: [SignupComponent],
      imports: [ReactiveFormsModule],
      providers: [
        { provide: AuthService, useValue: authServiceSpy },
        { provide: Router, useValue: routerSpy },
        { provide: NotificationService, useValue: notificationServiceSpy }
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(SignupComponent);
    component = fixture.componentInstance;
    mockAuthService = TestBed.inject(AuthService) as jasmine.SpyObj<AuthService>;
    mockRouter = TestBed.inject(Router) as jasmine.SpyObj<Router>;
    mockNotificationService = TestBed.inject(NotificationService) as jasmine.SpyObj<NotificationService>;
    
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize form with all required fields', () => {
    expect(component.signupForm.get('firstName')).toBeTruthy();
    expect(component.signupForm.get('lastName')).toBeTruthy();
    expect(component.signupForm.get('email')).toBeTruthy();
    expect(component.signupForm.get('password')).toBeTruthy();
    expect(component.signupForm.get('confirmPassword')).toBeTruthy();
    expect(component.signupForm.get('addressLine1')).toBeTruthy();
    expect(component.signupForm.get('city')).toBeTruthy();
    expect(component.signupForm.get('state')).toBeTruthy();
    expect(component.signupForm.get('postalCode')).toBeTruthy();
    expect(component.signupForm.get('country')).toBeTruthy();
    expect(component.signupForm.get('termsAgreed')).toBeTruthy();
  });

  it('should be invalid when form is empty', () => {
    expect(component.signupForm.invalid).toBeTruthy();
  });

  it('should be valid when all required fields are filled correctly', () => {
    component.signupForm.patchValue({
      firstName: 'John',
      lastName: 'Doe',
      email: '<EMAIL>',
      password: 'StrongPass123!',
      confirmPassword: 'StrongPass123!',
      addressLine1: '123 Main St',
      city: 'New York',
      state: 'NY',
      postalCode: '10001',
      country: 'USA',
      termsAgreed: true
    });

    expect(component.signupForm.valid).toBeTruthy();
  });

  it('should show password mismatch error when passwords do not match', () => {
    component.signupForm.patchValue({
      password: 'StrongPass123!',
      confirmPassword: 'DifferentPass123!'
    });

    expect(component.signupForm.hasError('passwordMismatch')).toBeTruthy();
  });

  it('should calculate password strength correctly', () => {
    // Weak password
    component.calculatePasswordStrength('weak');
    expect(component.passwordStrength).toBe(20);

    // Medium password
    component.calculatePasswordStrength('Medium123');
    expect(component.passwordStrength).toBe(80);

    // Strong password
    component.calculatePasswordStrength('StrongPass123!');
    expect(component.passwordStrength).toBe(100);
  });

  it('should call register service on valid form submission', () => {
    mockAuthService.register.and.returnValue(of({ success: true }));

    component.signupForm.patchValue({
      firstName: 'John',
      lastName: 'Doe',
      email: '<EMAIL>',
      password: 'StrongPass123!',
      confirmPassword: 'StrongPass123!',
      addressLine1: '123 Main St',
      city: 'New York',
      state: 'NY',
      postalCode: '10001',
      country: 'USA',
      termsAgreed: true
    });

    component.onSubmit();

    expect(mockAuthService.register).toHaveBeenCalledWith({
      email: '<EMAIL>',
      password: 'StrongPass123!',
      firstName: 'John',
      lastName: 'Doe',
      addressLine1: '123 Main St',
      city: 'New York',
      state: 'NY',
      postalCode: '10001',
      country: 'USA'
    });
  });

  it('should not submit when form is invalid', () => {
    component.signupForm.patchValue({
      firstName: 'John',
      // Missing required fields
    });

    component.onSubmit();

    expect(mockAuthService.register).not.toHaveBeenCalled();
  });

  it('should handle registration error', () => {
    mockAuthService.register.and.returnValue(throwError(() => ({ status: 409 })));

    component.signupForm.patchValue({
      firstName: 'John',
      lastName: 'Doe',
      email: '<EMAIL>',
      password: 'StrongPass123!',
      confirmPassword: 'StrongPass123!',
      addressLine1: '123 Main St',
      city: 'New York',
      state: 'NY',
      postalCode: '10001',
      country: 'USA',
      termsAgreed: true
    });

    component.onSubmit();

    expect(mockNotificationService.error).toHaveBeenCalledWith('This email is already registered. Please use a different email or try logging in.');
  });
});
