import { Component, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { Router, RouterLink } from '@angular/router';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatIconModule } from '@angular/material/icon';
import { MatStepperModule } from '@angular/material/stepper';

import { AuthService } from '../../../core/services/auth.service';
import { NotificationService } from '../../../core/services/notification.service';

@Component({
  selector: 'app-signup',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatCardModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatCheckboxModule,
    MatProgressSpinnerModule,
    MatIconModule,
    MatStepperModule,
    RouterLink
  ],
  templateUrl: './signup.component.html',
  styleUrls: ['./signup.component.scss']
})
export class SignupComponent {
  signupForm: FormGroup;
  isLoading = false;
  hidePassword = true;
  hideConfirmPassword = true;
  passwordStrength = 0;
  isRegistrationSuccess = false;
  userEmail = '';

  private fb = inject(FormBuilder);
  private authService = inject(AuthService);
  private router = inject(Router);
  private notificationService = inject(NotificationService);

  constructor() {
    this.signupForm = this.fb.group({
      // Essential Information
      firstName: ['', Validators.required],
      lastName: ['', Validators.required],
      email: ['', [Validators.required, Validators.email]],
      password: ['', [
        Validators.required,
        Validators.minLength(8),
        Validators.pattern(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/)
      ]],
      confirmPassword: ['', Validators.required],
      // Address Information
      addressLine1: ['', Validators.required],
      city: ['', Validators.required],
      state: ['', Validators.required],
      postalCode: ['', Validators.required],
      country: ['', Validators.required],
      termsAgreed: [false, Validators.requiredTrue]
    }, { validators: this.passwordMatchValidator });

    // Monitor password changes to calculate strength
    this.signupForm.get('password')?.valueChanges.subscribe(password => {
      this.calculatePasswordStrength(password);
    });
  }

  passwordMatchValidator(form: any) {
    const password = form.get('password')?.value;
    const confirmPassword = form.get('confirmPassword')?.value;
    return password === confirmPassword ? null : { passwordMismatch: true };
  }

  calculatePasswordStrength(password: string): void {
    if (!password) {
      this.passwordStrength = 0;
      return;
    }

    let strength = 0;

    // Length check
    if (password.length >= 8) strength += 20;

    // Character type checks
    if (/[A-Z]/.test(password)) strength += 20; // Uppercase
    if (/[a-z]/.test(password)) strength += 20; // Lowercase
    if (/[0-9]/.test(password)) strength += 20; // Numbers
    if (/[^A-Za-z0-9]/.test(password)) strength += 20; // Special characters

    this.passwordStrength = strength;
  }

  getPasswordStrengthClass(): string {
    if (this.passwordStrength < 40) return 'weak';
    if (this.passwordStrength < 80) return 'medium';
    return 'strong';
  }

  getPasswordStrengthText(): string {
    if (this.passwordStrength < 40) return 'Weak';
    if (this.passwordStrength < 80) return 'Medium';
    return 'Strong';
  }

  onSubmit(): void {
    if (this.signupForm.invalid || this.isLoading) {
      // Mark all fields as touched to show validation errors
      this.signupForm.markAllAsTouched();
      return;
    }

    this.isLoading = true;

    // Get form values safely
    const formValues = this.signupForm.value;

    const userData = {
      email: formValues.email || '',
      password: formValues.password || '',
      firstName: formValues.firstName || '',
      lastName: formValues.lastName || '',
      addressLine1: formValues.addressLine1 || '',
      city: formValues.city || '',
      state: formValues.state || '',
      postalCode: formValues.postalCode || '',
      country: formValues.country || ''
    };

    this.authService.register(userData).subscribe({
      next: (response) => {
        this.isLoading = false;
        this.userEmail = userData.email;
        this.isRegistrationSuccess = true;
        this.notificationService.success('Account created successfully! Please check your email to verify your account.');
      },
      error: (error) => {
        this.isLoading = false;
        console.error('Registration failed:', error);

        if (error.status === 409) {
          this.notificationService.error('This email is already registered. Please use a different email or try logging in.');
        } else if (error.status === 400) {
          this.notificationService.error('Please check your information and try again.');
        } else {
          this.notificationService.error('Registration failed. Please try again later.');
        }
      }
    });
  }

  /**
   * Navigate to login page
   */
  goToLogin(): void {
    this.router.navigate(['/auth/login']);
  }

  /**
   * Resend verification email
   */
  resendVerificationEmail(): void {
    if (!this.userEmail) return;

    this.authService.resendVerificationEmailByEmail(this.userEmail).subscribe({
      next: () => {
        this.notificationService.success('Verification email sent successfully!');
      },
      error: (error) => {
        console.error('Failed to resend verification email:', error);
        this.notificationService.error('Failed to send verification email. Please try again.');
      }
    });
  }
}
