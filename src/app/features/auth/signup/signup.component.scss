// Modern Auth Container (same as login)
.modern-auth-container {
  min-height: 100%;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: relative;
  overflow-x: hidden;
  padding: 2rem 1rem;
  margin: 0;
}

// Background Elements
.auth-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  overflow: hidden;
  z-index: 1;
}

.gradient-orb {
  position: absolute;
  border-radius: 50%;
  filter: blur(60px);
  opacity: 0.3;
  animation: float 8s ease-in-out infinite;

  &.orb-1 {
    width: 300px;
    height: 300px;
    background: radial-gradient(circle, #ff6b6b, #ee5a24);
    top: 10%;
    left: 10%;
    animation-delay: 0s;
  }

  &.orb-2 {
    width: 200px;
    height: 200px;
    background: radial-gradient(circle, #4ecdc4, #44a08d);
    top: 60%;
    right: 15%;
    animation-delay: 2s;
  }

  &.orb-3 {
    width: 250px;
    height: 250px;
    background: radial-gradient(circle, #a8edea, #fed6e3);
    bottom: 20%;
    left: 20%;
    animation-delay: 4s;
  }
}

// Auth Content
.auth-content {
  position: relative;
  z-index: 2;
  width: 100%;
  max-width: 680px;
  display: flex;
  flex-direction: column;
  gap: 2rem;

  @media (max-width: 768px) {
    max-width: 90vw;
  }

  @media (max-width: 576px) {
    max-width: 95vw;
  }
}

// Brand Section
.auth-brand {
  text-align: center;
}

.brand-logo {
  display: inline-flex;
  align-items: center;
  gap: 0.75rem;

  svg {
    filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.1));
  }
}

.brand-text {
  font-size: 1.5rem;
  font-weight: 700;
  color: white;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

// Auth Card
.auth-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 24px;
  padding: 2.5rem;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  animation: slideUp 0.6s ease-out;
  width: 100%;

  @media (max-width: 768px) {
    padding: 2rem;
    border-radius: 20px;
  }

  @media (max-width: 480px) {
    padding: 1.5rem;
    border-radius: 16px;
  }
}

// Auth Header
.auth-header {
  text-align: center;
  margin-bottom: 2rem;

  @media (max-width: 480px) {
    margin-bottom: 1.5rem;
  }
}

.auth-title {
  font-size: 2rem;
  font-weight: 800;
  color: #1a202c;
  margin-bottom: 0.75rem;

  @media (max-width: 480px) {
    font-size: 1.75rem;
  }
}

.auth-subtitle {
  color: #718096;
  font-size: 1rem;
  line-height: 1.5;
  margin: 0;

  @media (max-width: 480px) {
    font-size: 0.9rem;
  }
}

// Auth Form
.auth-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;

  @media (max-width: 480px) {
    gap: 1.25rem;
  }
}

// Form Row (for side-by-side fields)
.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;

  @media (max-width: 576px) {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
}

// Form Section
.form-section {
  margin: 2rem 0;
}

.section-title {
  font-size: 1.125rem;
  font-weight: 700;
  color: #374151;
  margin-bottom: 1.5rem;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid #e5e7eb;
}

// Form Group
.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-label {
  font-size: 0.95rem;
  font-weight: 600;
  color: #374151;
  margin-bottom: 0.25rem;
}

// Input Wrapper
.input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.input-icon {
  position: absolute;
  left: 1rem;
  z-index: 2;
  color: #9ca3af;
  display: flex;
  align-items: center;

  svg {
    width: 20px;
    height: 20px;
    stroke: currentColor;
  }
}

.form-input {
  width: 100%;
  padding: 1rem 1rem 1rem 3rem;
  border: 2px solid #e5e7eb;
  border-radius: 12px;
  font-size: 1rem;
  color: #374151;
  background-color: #f9fafb;
  transition: all 0.3s ease;

  &::placeholder {
    color: #9ca3af;
  }

  &:focus {
    outline: none;
    border-color: #667eea;
    background-color: white;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
  }

  &.error {
    border-color: #ef4444;
    background-color: #fef2f2;

    &:focus {
      border-color: #ef4444;
      box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
    }
  }
}

.password-toggle {
  position: absolute;
  right: 1rem;
  z-index: 2;
  background: none;
  border: none;
  color: #9ca3af;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 6px;
  transition: all 0.3s ease;

  &:hover {
    color: #667eea;
    background-color: rgba(102, 126, 234, 0.1);
  }

  svg {
    width: 20px;
    height: 20px;
    stroke: currentColor;
  }
}

.form-error {
  font-size: 0.875rem;
  color: #ef4444;
  margin-top: 0.25rem;
}

// Password Strength Indicator
.password-strength {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-top: 0.5rem;
}

.strength-bar {
  flex: 1;
  height: 4px;
  background-color: #e5e7eb;
  border-radius: 2px;
  overflow: hidden;
}

.strength-fill {
  height: 100%;
  transition: all 0.3s ease;
  border-radius: 2px;

  &.weak {
    background-color: #ef4444;
  }

  &.medium {
    background-color: #f59e0b;
  }

  &.strong {
    background-color: #10b981;
  }
}

.strength-text {
  font-size: 0.875rem;
  font-weight: 600;

  &.weak {
    color: #ef4444;
  }

  &.medium {
    color: #f59e0b;
  }

  &.strong {
    color: #10b981;
  }
}

// Form Options
.form-options {
  display: flex;
  justify-content: flex-start;
  align-items: flex-start;
  margin: 0.5rem 0;

  @media (max-width: 480px) {
    flex-direction: column;
    gap: 1rem;
    align-items: flex-start;
  }
}

// Custom Checkbox
.checkbox-wrapper {
  display: flex;
  align-items: flex-start;
  gap: 0.5rem;
  cursor: pointer;
  font-size: 0.95rem;
  color: #374151;
  line-height: 1.5;
}

.checkbox-input {
  display: none;
}

.checkbox-custom {
  width: 20px;
  height: 20px;
  border: 2px solid #d1d5db;
  border-radius: 4px;
  background-color: white;
  position: relative;
  transition: all 0.3s ease;
  flex-shrink: 0;
  margin-top: 1px;

  &::after {
    content: '';
    position: absolute;
    top: 2px;
    left: 6px;
    width: 4px;
    height: 8px;
    border: solid white;
    border-width: 0 2px 2px 0;
    transform: rotate(45deg);
    opacity: 0;
    transition: opacity 0.3s ease;
  }
}

.checkbox-input:checked + .checkbox-custom {
  background-color: #667eea;
  border-color: #667eea;

  &::after {
    opacity: 1;
  }
}

.checkbox-label {
  font-weight: 500;
}

.terms-link {
  color: #667eea;
  text-decoration: none;
  font-weight: 600;

  &:hover {
    text-decoration: underline;
  }
}

// Submit Button
.auth-submit-btn {
  width: 100%;
  padding: 1rem 2rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 12px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
  }

  &:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(102, 126, 234, 0.4);

    &::before {
      left: 100%;
    }
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
  }
}

.btn-content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;

  svg {
    transition: transform 0.3s ease;
  }
}

.auth-submit-btn:hover:not(:disabled) .btn-content svg {
  transform: translateX(4px);
}

.loading-spinner {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
}

.spinner {
  width: 20px;
  height: 20px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

// Success State
.success-state {
  text-align: center;
  padding: 1rem 0;
}

.success-icon {
  margin-bottom: 1.5rem;
  display: flex;
  justify-content: center;
}

.success-actions {
  margin-top: 2rem;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.resend-btn {
  background: none;
  border: none;
  color: #667eea;
  font-size: 0.95rem;
  font-weight: 600;
  cursor: pointer;
  padding: 0.75rem;
  border-radius: 8px;
  transition: all 0.3s ease;

  &:hover {
    background-color: rgba(102, 126, 234, 0.1);
    color: #5a67d8;
  }
}

// Auth Footer
.auth-footer {
  text-align: center;
  margin-top: 2rem;
  padding-top: 2rem;
  border-top: 1px solid #e5e7eb;
}

.footer-text {
  color: #6b7280;
  font-size: 0.95rem;
  margin: 0;
}

.footer-link {
  color: #667eea;
  text-decoration: none;
  font-weight: 600;
  transition: all 0.3s ease;

  &:hover {
    color: #5a67d8;
    text-decoration: underline;
  }
}

// Animations
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

// Responsive Design
@media (max-width: 480px) {
  .modern-auth-container {
    padding: 1rem;
  }

  .auth-content {
    gap: 1.5rem;
  }

  .brand-text {
    font-size: 1.25rem;
  }
}

// Reduced Motion
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}