import { Routes } from '@angular/router';
import { noAuthGuard } from '../../core/guards/no-auth.guard';

/**
 * Routes for authentication
 * Protected by noAuthGuard to prevent authenticated users from accessing
 */
export const AUTH_ROUTES: Routes = [
  {
    path: '',
    redirectTo: 'login',
    pathMatch: 'full'
  },
  {
    path: 'login',
    loadComponent: () => import('./login/login.component').then(c => c.LoginComponent),
    canActivate: [noAuthGuard],
    title: 'Login - Winners Society'
  },
  {
    path: 'admin/login',
    loadComponent: () => import('./admin/admin-login/admin-login.component').then(c => c.AdminLoginComponent),
    canActivate: [noAuthGuard],
    title: 'Admin Login - Winners Society'
  },
  {
    path: 'register',
    loadComponent: () => import('./signup/signup.component').then(c => c.SignupComponent),
    canActivate: [noAuthGuard],
    title: 'Create Account - Winners Society'
  },
  {
    path: 'signup',
    redirectTo: 'register',
    pathMatch: 'full'
  },
  {
    path: 'forgot-password',
    loadComponent: () => import('./forgot-password/forgot-password.component').then(c => c.ForgotPasswordComponent),
    canActivate: [noAuthGuard],
    title: 'Forgot Password - Winners Society'
  },
  {
    path: 'reset-password',
    loadComponent: () => import('./reset-password/reset-password.component').then(c => c.ResetPasswordComponent),
    canActivate: [noAuthGuard],
    title: 'Reset Password - Winners Society'
  },
  {
    path: 'verify-email',
    loadComponent: () => import('./verify-email/verify-email.component').then(c => c.VerifyEmailComponent),
    title: 'Verify Email - Winners Society'
  },
  {
    path: 'email-verified',
    loadComponent: () => import('./verify-email/verify-email.component').then(c => c.VerifyEmailComponent),
    title: 'Email Verified - Winners Society'
  }
];
