import { Component, inject, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router, ActivatedRoute } from '@angular/router';
import { Subject, takeUntil } from 'rxjs';

import { AuthService } from '../../../core/services/auth.service';
import { NotificationService } from '../../../core/services/notification.service';

export interface VerificationState {
  isLoading: boolean;
  isSuccess: boolean;
  isError: boolean;
  errorType: string;
  errorMessage: string;
  userEmail?: string;
  canResend: boolean;
  isAlreadyVerified: boolean;
}

@Component({
  selector: 'app-verify-email',
  standalone: true,
  imports: [
    CommonModule
  ],
  templateUrl: './verify-email.component.html',
  styleUrls: ['./verify-email.component.scss']
})
export class VerifyEmailComponent implements OnInit, OnDestroy {
  // Verification state
  state: VerificationState = {
    isLoading: true,
    isSuccess: false,
    isError: false,
    errorType: '',
    errorMessage: '',
    userEmail: undefined,
    canResend: false,
    isAlreadyVerified: false
  };

  // Component state
  token: string | null = null;
  isResendingEmail = false;
  private destroy$ = new Subject<void>();

  // Services
  private authService = inject(AuthService);
  private router = inject(Router);
  private route = inject(ActivatedRoute);
  private notificationService = inject(NotificationService);

  ngOnInit(): void {
    this.route.queryParams
      .pipe(takeUntil(this.destroy$))
      .subscribe(params => {
        this.handleQueryParams(params);
      });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private handleQueryParams(params: any): void {
    // Handle backend redirect with success
    if (params['success'] === 'true') {
      this.state = {
        ...this.state,
        isLoading: false,
        isSuccess: true,
        userEmail: params['email'] || undefined
      };
      this.notificationService.success('Email verified successfully!');
      return;
    }

    // Handle backend redirect with error
    if (params['error']) {
      this.handleBackendRedirectError(params['error']);
      return;
    }

    // Handle frontend verification with token
    this.token = params['token'];
    if (this.token) {
      this.verifyEmailWithToken(this.token);
    } else {
      this.state = {
        ...this.state,
        isLoading: false,
        isError: true,
        errorType: 'missing_token',
        errorMessage: 'Invalid verification link. No token provided.',
        canResend: false
      };
    }
  }

  private handleBackendRedirectError(errorType: string): void {
    this.state = {
      ...this.state,
      isLoading: false,
      isError: true,
      errorType: errorType
    };

    switch (errorType) {
      case 'expired':
        this.state.errorMessage = 'Your verification link has expired.';
        this.state.canResend = true;
        break;
      case 'already_verified':
        this.state.errorMessage = 'Your email address is already verified.';
        this.state.isAlreadyVerified = true;
        this.state.canResend = false;
        break;
      case 'invalid':
        this.state.errorMessage = 'The verification link is invalid.';
        this.state.canResend = true;
        break;
      case 'not_found':
        this.state.errorMessage = 'Verification token not found.';
        this.state.canResend = true;
        break;
      case 'missing_token':
        this.state.errorMessage = 'Invalid verification link. No token provided.';
        this.state.canResend = false;
        break;
      default:
        this.state.errorMessage = 'Email verification failed. Please try again.';
        this.state.canResend = true;
    }
  }

  private verifyEmailWithToken(token: string): void {
    this.authService.verifyEmailAPI(token)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response) => {
          this.state = {
            ...this.state,
            isLoading: false,
            isSuccess: true,
            userEmail: response.data?.email
          };
          this.notificationService.success('Email verified successfully!');

          // Update auth state if user is logged in
          this.authService.currentUser$.pipe(takeUntil(this.destroy$)).subscribe(user => {
            if (user) {
              this.authService.refreshUserData();
            }
          });
        },
        error: (error) => {
          console.error('Email verification failed:', error);
          this.handleVerificationError(error);
        }
      });
  }

  private handleVerificationError(error: any): void {
    this.state = {
      ...this.state,
      isLoading: false,
      isError: true
    };

    // Handle specific error types based on status code and error response
    if (error.error?.error) {
      this.state.errorType = error.error.error;
      this.state.errorMessage = error.error.message;
    } else {
      this.state.errorType = 'unknown';
      this.state.errorMessage = 'Email verification failed. Please try again.';
    }

    // Set additional state based on error type
    switch (error.status) {
      case 400: // Bad Request
        this.state.canResend = this.state.errorType !== 'missing_token';
        break;
      case 404: // Not Found
        this.state.canResend = true;
        break;
      case 409: // Conflict (already verified)
        this.state.isAlreadyVerified = true;
        this.state.canResend = false;
        break;
      case 410: // Gone (expired)
        this.state.canResend = true;
        break;
      default:
        this.state.canResend = true;
    }
  }

  // Public methods for template
  goToLogin(): void {
    this.router.navigate(['/auth/login']);
  }

  goToSignup(): void {
    this.router.navigate(['/auth/register']);
  }

  requestNewVerification(): void {
    if (this.state.userEmail) {
      this.resendVerificationEmail(this.state.userEmail);
    } else {
      // Redirect to login with resend flag
      this.router.navigate(['/auth/login'], {
        queryParams: { resendVerification: 'true' }
      });
    }
  }

  private resendVerificationEmail(email: string): void {
    if (this.isResendingEmail) return;

    this.isResendingEmail = true;
    this.authService.resendVerificationEmailByEmail(email)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: () => {
          this.isResendingEmail = false;
          this.notificationService.success('Verification email sent successfully! Please check your inbox.');
        },
        error: (error) => {
          this.isResendingEmail = false;
          console.error('Failed to resend verification email:', error);
          this.notificationService.error('Failed to send verification email. Please try again.');
        }
      });
  }

  // Getter methods for template
  get isLoading(): boolean {
    return this.state.isLoading;
  }

  get isSuccess(): boolean {
    return this.state.isSuccess;
  }

  get isError(): boolean {
    return this.state.isError;
  }

  get errorMessage(): string {
    return this.state.errorMessage;
  }

  get canResend(): boolean {
    return this.state.canResend;
  }

  get isAlreadyVerified(): boolean {
    return this.state.isAlreadyVerified;
  }

  get userEmail(): string | undefined {
    return this.state.userEmail;
  }
}
