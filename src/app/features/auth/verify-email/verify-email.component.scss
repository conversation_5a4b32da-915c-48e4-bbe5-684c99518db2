// Modern Auth Container
.modern-auth-container {
  min-height: 100%;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: relative;
  overflow-x: hidden;
  padding: 2rem 1rem;
  margin: 0;
}

// Background Elements
.auth-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  overflow: hidden;
  z-index: 1;
}

.gradient-orb {
  position: absolute;
  border-radius: 50%;
  filter: blur(40px);
  opacity: 0.7;
  animation: float 6s ease-in-out infinite;

  &.orb-1 {
    width: 300px;
    height: 300px;
    background: radial-gradient(circle, #ff6b6b, #feca57);
    top: 10%;
    right: 10%;
    animation-delay: 0s;
  }

  &.orb-2 {
    width: 200px;
    height: 200px;
    background: radial-gradient(circle, #48cae4, #023e8a);
    bottom: 30%;
    right: 30%;
    animation-delay: 2s;
  }

  &.orb-3 {
    width: 250px;
    height: 250px;
    background: radial-gradient(circle, #a8edea, #fed6e3);
    bottom: 20%;
    left: 20%;
    animation-delay: 4s;
  }
}

// Auth Content
.auth-content {
  position: relative;
  z-index: 2;
  width: 100%;
  max-width: 680px;
  display: flex;
  flex-direction: column;
  gap: 2rem;

  @media (max-width: 768px) {
    max-width: 90vw;
  }

  @media (max-width: 576px) {
    max-width: 95vw;
  }
}

// Brand Section
.auth-brand {
  text-align: center;
}

.brand-logo {
  display: inline-flex;
  align-items: center;
  gap: 0.75rem;

  svg {
    filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.1));
  }
}

.brand-text {
  font-size: 1.5rem;
  font-weight: 700;
  color: white;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

// Auth Card
.auth-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 24px;
  padding: 2.5rem;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  animation: slideUp 0.6s ease-out;
  width: 100%;

  @media (max-width: 768px) {
    padding: 2rem;
    border-radius: 20px;
  }

  @media (max-width: 480px) {
    padding: 1.5rem;
    border-radius: 16px;
  }
}

.auth-title {
  font-size: 2rem;
  font-weight: 800;
  color: #1a202c;
  margin-bottom: 0.75rem;
  text-align: center;

  @media (max-width: 480px) {
    font-size: 1.75rem;
  }
}

.auth-subtitle {
  color: #718096;
  font-size: 1rem;
  line-height: 1.5;
  margin: 0;
  text-align: center;

  @media (max-width: 480px) {
    font-size: 0.9rem;
  }

  strong {
    color: #667eea;
    font-weight: 600;
  }
}

// Loading State
.loading-state {
  text-align: center;
  padding: 2rem 0;
}

.loading-icon {
  margin-bottom: 1.5rem;
  display: flex;
  justify-content: center;
}

.spinner-large {
  width: 64px;
  height: 64px;
  border: 4px solid rgba(102, 126, 234, 0.3);
  border-top: 4px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

// Success State
.success-state {
  text-align: center;
  padding: 1rem 0;
}

.success-icon {
  margin-bottom: 1.5rem;
  display: flex;
  justify-content: center;
}

.success-features {
  margin: 2rem 0;
  padding: 1.5rem;
  background: rgba(0, 200, 150, 0.05);
  border: 1px solid rgba(0, 200, 150, 0.2);
  border-radius: 12px;
  text-align: left;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1rem;
  color: #374151;
  font-size: 0.875rem;

  &:last-child {
    margin-bottom: 0;
  }

  svg {
    color: #00c896;
    flex-shrink: 0;
  }

  span {
    font-weight: 500;
  }
}

.success-actions {
  margin-top: 2rem;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

// Error State
.error-state {
  text-align: center;
  padding: 1rem 0;
}

.error-icon {
  margin-bottom: 1.5rem;
  display: flex;
  justify-content: center;

  &.warning-icon svg {
    stroke: #f59e0b;
  }
}

.help-text {
  margin: 1.5rem 0;
  padding: 1rem;
  background: rgba(239, 68, 68, 0.05);
  border: 1px solid rgba(239, 68, 68, 0.2);
  border-radius: 8px;
  text-align: left;

  .help-description {
    color: #6b7280;
    font-size: 0.875rem;
    line-height: 1.5;
    margin: 0;
  }
}

// Special styling for already verified state
.error-state.already-verified .help-text {
  background: rgba(245, 158, 11, 0.05);
  border-color: rgba(245, 158, 11, 0.2);
}

.error-actions {
  margin-top: 2rem;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.secondary-btn {
  background: none;
  border: 2px solid #667eea;
  color: #667eea;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  padding: 1rem 2rem;
  border-radius: 12px;
  transition: all 0.3s ease;

  &:hover {
    background-color: #667eea;
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(102, 126, 234, 0.3);
  }
}

.outline-btn {
  background: none;
  border: 2px solid #9ca3af;
  color: #6b7280;
  font-size: 0.875rem;
  font-weight: 600;
  cursor: pointer;
  padding: 0.75rem 1.5rem;
  border-radius: 12px;
  transition: all 0.3s ease;

  &:hover {
    border-color: #667eea;
    color: #667eea;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.2);
  }

  .btn-content {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
  }
}

// Button Spinner
.button-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

// Submit Button
.auth-submit-btn {
  width: 100%;
  padding: 1rem 2rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 12px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
  }

  &:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(102, 126, 234, 0.4);

    &::before {
      left: 100%;
    }
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
  }
}

.btn-content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;

  svg {
    transition: transform 0.3s ease;
  }
}

.auth-submit-btn:hover:not(:disabled) .btn-content svg {
  transform: translateX(4px);
}

// Animations
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

// Responsive Design
@media (max-width: 480px) {
  .modern-auth-container {
    padding: 1rem;
  }

  .auth-content {
    gap: 1.5rem;
  }

  .brand-text {
    font-size: 1.25rem;
  }
}

// Reduced Motion
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}
