<div class="modern-auth-container">
  <!-- Background Elements -->
  <div class="auth-background">
    <div class="gradient-orb orb-1"></div>
    <div class="gradient-orb orb-2"></div>
    <div class="gradient-orb orb-3"></div>
  </div>

  <!-- Auth Content -->
  <div class="auth-content">
    <!-- Brand Section -->
    <div class="auth-brand">
      <div class="brand-logo">
        <svg width="48" height="48" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
          <circle cx="16" cy="16" r="16" fill="url(#authGradient)"/>
          <path d="M16 8L20 12L16 16L12 12L16 8Z" fill="white"/>
          <path d="M16 16L20 20L16 24L12 20L16 16Z" fill="white" opacity="0.8"/>
          <defs>
            <linearGradient id="authGradient" x1="0" y1="0" x2="32" y2="32" gradientUnits="userSpaceOnUse">
              <stop stop-color="#667eea"/>
              <stop offset="1" stop-color="#764ba2"/>
            </linearGradient>
          </defs>
        </svg>
        <span class="brand-text">Winners Society</span>
      </div>
    </div>

    <!-- Auth Card -->
    <div class="auth-card">
      <!-- Loading State -->
      <div *ngIf="isLoading" class="loading-state">
        <div class="loading-icon">
          <div class="spinner-large"></div>
        </div>
        <h1 class="auth-title">Verifying Your Email</h1>
        <p class="auth-subtitle">
          Please wait while we verify your email address...
        </p>
      </div>

      <!-- Success State -->
      <div *ngIf="isSuccess && !isLoading" class="success-state">
        <div class="success-icon">
          <svg width="64" height="64" viewBox="0 0 24 24" fill="none">
            <circle cx="12" cy="12" r="10" stroke="#00c896" stroke-width="2"/>
            <path d="M9 12L11 14L15 10" stroke="#00c896" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
        </div>
        <h1 class="auth-title">Email Verified Successfully!</h1>
        <p class="auth-subtitle">
          <span *ngIf="userEmail">Your email address <strong>{{ userEmail }}</strong> has been verified.</span>
          <span *ngIf="!userEmail">Your email address has been verified.</span>
          You can now sign in to your Winners Society account and start participating in giveaways.
        </p>

        <!-- Success Features -->
        <div class="success-features">
          <div class="feature-item">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
              <path d="m9 12 2 2 4-4" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
            <span>Access to exclusive giveaways</span>
          </div>
          <div class="feature-item">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
              <path d="m9 12 2 2 4-4" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
            <span>Member dashboard and statistics</span>
          </div>
          <div class="feature-item">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
              <path d="m9 12 2 2 4-4" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
            <span>Email notifications for new opportunities</span>
          </div>
        </div>

        <div class="success-actions">
          <button type="button" (click)="goToLogin()" class="auth-submit-btn">
            <span class="btn-content">
              <span>Continue to Login</span>
              <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
                <path d="M4.16667 10H15.8333M15.8333 10L10.8333 5M15.8333 10L10.8333 15" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </span>
          </button>
        </div>
      </div>

      <!-- Error State -->
      <div *ngIf="isError && !isLoading" class="error-state" [class.already-verified]="isAlreadyVerified">
        <div class="error-icon" [class.warning-icon]="isAlreadyVerified">
          <!-- Error Icon -->
          <svg *ngIf="!isAlreadyVerified" width="64" height="64" viewBox="0 0 24 24" fill="none">
            <circle cx="12" cy="12" r="10" stroke="#ef4444" stroke-width="2"/>
            <path d="M15 9L9 15M9 9L15 15" stroke="#ef4444" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
          <!-- Warning Icon for Already Verified -->
          <svg *ngIf="isAlreadyVerified" width="64" height="64" viewBox="0 0 24 24" fill="none">
            <path d="M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126zM12 15.75h.007v.008H12v-.008z" stroke="#f59e0b" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
        </div>

        <h1 class="auth-title">
          <span *ngIf="isAlreadyVerified">Already Verified</span>
          <span *ngIf="!isAlreadyVerified">Verification Failed</span>
        </h1>

        <p class="auth-subtitle">
          {{ errorMessage }}
        </p>

        <!-- Additional help text based on error type -->
        <div class="help-text" [ngSwitch]="state.errorType">
          <p *ngSwitchCase="'expired'" class="help-description">
            Verification links expire after 24 hours for security reasons. You can request a new verification email below.
          </p>
          <p *ngSwitchCase="'already_verified'" class="help-description">
            You can proceed to login and start using Winners Society.
          </p>
          <p *ngSwitchCase="'invalid'" class="help-description">
            The verification link may have been corrupted or modified. Please request a new verification email.
          </p>
          <p *ngSwitchCase="'not_found'" class="help-description">
            This verification token was not found in our system. Please request a new verification email or create a new account.
          </p>
          <p *ngSwitchDefault class="help-description">
            If this problem persists, please contact our support team for assistance.
          </p>
        </div>

        <div class="error-actions">
          <!-- Resend verification button -->
          <button
            *ngIf="canResend"
            type="button"
            class="auth-submit-btn"
            [disabled]="isResendingEmail"
            (click)="requestNewVerification()">
            <span class="btn-content">
              <div *ngIf="isResendingEmail" class="button-spinner"></div>
              <svg *ngIf="!isResendingEmail" width="20" height="20" viewBox="0 0 24 24" fill="none">
                <path d="M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M21 3v5h-5" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M3 21v-5h5" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
              <span *ngIf="!isResendingEmail">Request New Verification</span>
              <span *ngIf="isResendingEmail">Sending...</span>
            </span>
          </button>

          <!-- Login button -->
          <button type="button" (click)="goToLogin()" [class]="canResend ? 'secondary-btn' : 'auth-submit-btn'">
            <span class="btn-content" *ngIf="!canResend">
              <span>Continue to Login</span>
              <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
                <path d="M4.16667 10H15.8333M15.8333 10L10.8333 5M15.8333 10L10.8333 15" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </span>
            <span *ngIf="canResend">Back to Login</span>
          </button>

          <!-- Signup button for certain error types -->
          <button
            *ngIf="state.errorType === 'not_found'"
            type="button"
            class="outline-btn"
            (click)="goToSignup()">
            <span class="btn-content">
              <span>Create New Account</span>
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <circle cx="9" cy="7" r="4" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <line x1="19" y1="8" x2="19" y2="14" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <line x1="22" y1="11" x2="16" y2="11" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </span>
          </button>
        </div>
      </div>
    </div>
  </div>
</div>
