import { Component, OnInit, inject } from '@angular/core';
import { Router, RouterLink } from '@angular/router';
import { CommonModule } from '@angular/common';
import { AsyncPipe, <PERSON>If, <PERSON>For, DatePipe, TitleCasePipe } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { MatBadgeModule } from '@angular/material/badge';
import { MatChipsModule } from '@angular/material/chips';
import { MatDividerModule } from '@angular/material/divider';

import { AuthService } from '../../../core/services/auth.service';
import { MembershipService } from '../../../core/services/membership.service';
import { Subscription } from '../../../core/models/membership.model';
import { NotificationService } from '../../../core/services/notification.service';
import { Giveaway, Prize, GiveawayStatus } from '../../../core/models/giveaway.model';

// Interface for dashboard notifications
interface DashboardNotification {
  id: string;
  type: 'membership' | 'giveaway' | 'win' | 'system';
  message: string;
  time: string;
  read: boolean;
  icon: string;
  actionLink?: string;
  actionText?: string;
}

// Interface for activity items
interface ActivityItem {
  id: string;
  type: 'entry' | 'payment' | 'win' | 'membership';
  title: string;
  time: string;
  icon: string;
  details?: string;
  link?: string;
}

@Component({
  selector: 'app-dashboard',
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatProgressBarModule,
    MatBadgeModule,
    MatChipsModule,
    MatDividerModule,
    RouterLink,
    AsyncPipe,
    NgIf,
    NgFor,
    DatePipe,
    TitleCasePipe
  ],
  templateUrl: './dashboard.component.html',
  styleUrls: ['./dashboard.component.scss']
})
export class DashboardComponent implements OnInit {
  authService = inject(AuthService);
  membershipService = inject(MembershipService);
  notificationService = inject(NotificationService);
  router = inject(Router);

  loading = true;
  membership: Subscription | null = null;

  // Sample data for the dashboard
  activeGiveaways: Giveaway[] = [
    {
      id: '1',
      title: 'MacBook Pro 16"',
      slug: 'macbook-pro-16',
      description: 'Win the latest MacBook Pro with M3 Pro chip',
      imageUrl: 'assets/images/giveaways/macbook.jpg',
      startDate: new Date('2023-05-01'),
      endDate: new Date('2023-06-15'),
      drawDate: new Date('2023-06-16'),
      status: GiveawayStatus.ACTIVE,
      isActive: true,
      prizeValue: 299900, // $2,999.00
      prizes: [{
        id: '101',
        name: 'Apple MacBook Pro 16"',
        description: 'M3 Pro chip, 32GB RAM, 1TB SSD',
        value: 299900,
        quantity: 1,
        category: 'Electronics'
      }],
      maxEntries: 1,
      entryCount: 5432,
      createdAt: new Date('2023-04-15'),
      updatedAt: new Date('2023-04-15')
    },
    {
      id: '2',
      title: 'PlayStation 5',
      slug: 'playstation-5',
      description: 'Win a PlayStation 5 console with extra controller',
      imageUrl: 'assets/images/giveaways/ps5.jpg',
      startDate: new Date('2023-05-10'),
      endDate: new Date('2023-06-20'),
      drawDate: new Date('2023-06-21'),
      status: GiveawayStatus.ACTIVE,
      isActive: true,
      prizeValue: 49900, // $499.00
      prizes: [{
        id: '102',
        name: 'Sony PlayStation 5',
        description: 'Digital Edition with extra controller',
        value: 49900,
        quantity: 1,
        category: 'Gaming'
      }],
      maxEntries: 2,
      entryCount: 8765,
      createdAt: new Date('2023-05-01'),
      updatedAt: new Date('2023-05-01')
    },
    {
      id: '3',
      title: 'Beats Studio Pro',
      slug: 'beats-studio-pro',
      description: 'Win Beats Studio Pro wireless headphones',
      imageUrl: 'assets/images/giveaways/beats.jpg',
      startDate: new Date('2023-05-15'),
      endDate: new Date('2023-06-10'),
      drawDate: new Date('2023-06-11'),
      status: GiveawayStatus.ACTIVE,
      isActive: true,
      prizeValue: 34900, // $349.00
      prizes: [{
        id: '103',
        name: 'Beats Studio Pro',
        description: 'Wireless noise cancelling headphones',
        value: 34900,
        quantity: 1,
        category: 'Audio'
      }],
      maxEntries: 3,
      entryCount: 4321,
      createdAt: new Date('2023-05-05'),
      updatedAt: new Date('2023-05-05')
    }
  ];

  notifications: DashboardNotification[] = [
    {
      id: '1',
      type: 'giveaway',
      message: 'New giveaway available: MacBook Pro 16"',
      time: '5 minutes ago',
      read: false,
      icon: 'fa-gift'
    },
    {
      id: '2',
      type: 'membership',
      message: 'Your membership will expire in 7 days',
      time: '1 hour ago',
      read: false,
      icon: 'fa-id-card'
    },
    {
      id: '3',
      type: 'win',
      message: 'Congratulations! You won the iPhone giveaway',
      time: '2 days ago',
      read: true,
      icon: 'fa-trophy'
    }
  ];

  recentActivities: ActivityItem[] = [
    {
      id: '1',
      type: 'entry',
      title: 'Entered MacBook Pro Giveaway',
      time: '2 days ago',
      icon: 'fa-ticket-alt'
    },
    {
      id: '2',
      type: 'payment',
      title: 'Membership Payment',
      time: '7 days ago',
      icon: 'fa-credit-card'
    },
    {
      id: '3',
      type: 'entry',
      title: 'Entered PlayStation 5 Giveaway',
      time: '8 days ago',
      icon: 'fa-ticket-alt'
    }
  ];

  ngOnInit(): void {
    this.checkMembershipStatus();
  }

  checkMembershipStatus(): void {
    this.loading = true;
    this.membershipService.getCurrentMembership()
      .subscribe({
        next: (membership) => {
          this.membership = membership;
          this.loading = false;
        },
        error: (err) => {
          console.log('No active membership found:', err);
          this.membership = null;
          this.loading = false;
        }
      });
  }

  // Helper methods for the dashboard

  getStatusClass(status: string): string {
    switch (status) {
      case 'active':
        return 'status-active';
      case 'canceled':
        return 'status-canceled';
      case 'expired':
        return 'status-expired';
      default:
        return '';
    }
  }

  getMembershipProgress(membership: Subscription): number {
    if (!membership) return 0;

    const startDate = membership.startDate.getTime();
    const endDate = membership.endDate.getTime();
    const currentDate = new Date().getTime();

    // Calculate percentage of membership period elapsed
    const totalDuration = endDate - startDate;
    const elapsed = currentDate - startDate;

    // Ensure the value is between 0 and 100
    return Math.max(0, Math.min(100, (elapsed / totalDuration) * 100));
  }

  getEntriesPercentage(membership: Subscription): number {
    if (!membership) return 0;

    const used = membership.entriesTotal - membership.entriesRemaining;
    return (used / membership.entriesTotal) * 100;
  }

  getDaysUntilBilling(membership: Subscription): number {
    if (!membership || !membership.nextBillingDate) return 0;

    const today = new Date();
    const billingDate = new Date(membership.nextBillingDate);

    // Calculate days difference
    const diffTime = billingDate.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    return Math.max(0, diffDays);
  }

  getMembershipDuration(membership: Subscription): number {
    if (!membership) return 0;

    const startDate = new Date(membership.memberSince);
    const today = new Date();

    // Calculate days difference
    const diffTime = today.getTime() - startDate.getTime();
    const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));

    return diffDays;
  }

  getDaysRemaining(giveaway: Giveaway): number {
    const today = new Date();
    const endDate = new Date(giveaway.endDate);

    // Calculate days difference
    const diffTime = endDate.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    return Math.max(0, diffDays);
  }

  formatPrizeValue(cents: number): string {
    // Convert cents to dollars with 2 decimal places
    return (cents / 100).toFixed(2);
  }

  getNotificationTypeClass(type: string): string {
    switch (type) {
      case 'giveaway':
        return 'notification-giveaway';
      case 'membership':
        return 'notification-membership';
      case 'win':
        return 'notification-win';
      case 'system':
        return 'notification-system';
      default:
        return '';
    }
  }

  getActivityTypeClass(type: string): string {
    switch (type) {
      case 'entry':
        return 'activity-entry';
      case 'payment':
        return 'activity-payment';
      case 'win':
        return 'activity-win';
      case 'membership':
        return 'activity-membership';
      default:
        return '';
    }
  }
}