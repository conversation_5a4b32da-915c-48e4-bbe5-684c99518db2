<div class="dashboard-container">
  <!-- Loading State -->
  <div class="loading-container" *ngIf="loading">
    <div class="spinner-container">
      <div class="spinner"></div>
    </div>
    <p>Loading your membership information...</p>
  </div>

  <!-- No Membership State -->
  <div class="no-membership-container" *ngIf="!loading && !membership">
    <div class="no-membership-content">
      <div class="icon-container">
        <i class="fas fa-id-card"></i>
      </div>
      <h2>No Active Membership</h2>
      <p>You don't have an active Winners Society membership. Join now to get exclusive access to premium giveaways and special perks!</p>
      <button mat-raised-button color="primary" routerLink="/member/membership/plans">
        View Membership Plans
      </button>
    </div>
  </div>

  <!-- Dashboard Content -->
  <div class="dashboard-content" *ngIf="!loading && membership">
    <!-- Dashboard Header -->
    <div class="dashboard-header">
      <div class="welcome-section">
        <h1>Welcome, {{ (authService.currentUser$ | async)?.firstName || 'Member' }}!</h1>
        <div class="membership-badge">
          <span class="tier-name">{{ membership.tierName }}</span>
          <span class="status-indicator" [ngClass]="getStatusClass(membership.status)">
            {{ membership.status | titlecase }}
          </span>
        </div>
      </div>
      <div class="membership-period">
        <div class="period-info">
          <span class="period-label">Membership Period</span>
          <span class="period-dates">{{ membership.startDate | date:'MMM d, yyyy' }} - {{ membership.endDate | date:'MMM d, yyyy' }}</span>
        </div>
        <mat-progress-bar class="membership-progress" mode="determinate" [value]="getMembershipProgress(membership)"></mat-progress-bar>
      </div>
    </div>

    <!-- Stats Row -->
    <div class="stats-row">
      <div class="stat-card entries-stat">
        <div class="stat-icon">
          <i class="fas fa-ticket-alt"></i>
        </div>
        <div class="stat-content">
          <div class="stat-value-container">
            <span class="stat-value">{{ membership.entriesRemaining }}</span>
            <span class="stat-total">/ {{ membership.entriesTotal }}</span>
          </div>
          <p class="stat-label">Available Entries</p>
          <div class="entries-progress">
            <div class="progress-bar" [style.width.%]="getEntriesPercentage(membership)"></div>
          </div>
        </div>
      </div>

      <div class="stat-card giveaways-stat">
        <div class="stat-icon">
          <i class="fas fa-gift"></i>
        </div>
        <div class="stat-content">
          <span class="stat-value">{{ activeGiveaways.length }}</span>
          <p class="stat-label">Active Giveaways</p>
        </div>
      </div>

      <div class="stat-card billing-stat" *ngIf="membership.nextBillingDate">
        <div class="stat-icon">
          <i class="fas fa-credit-card"></i>
        </div>
        <div class="stat-content">
          <span class="stat-value">{{ getDaysUntilBilling(membership) }}</span>
          <p class="stat-label">Days Until Next Billing</p>
          <p class="billing-date">{{ membership.nextBillingDate | date:'MMM d, yyyy' }}</p>
        </div>
      </div>

      <div class="stat-card member-stat">
        <div class="stat-icon">
          <i class="fas fa-user-check"></i>
        </div>
        <div class="stat-content">
          <span class="stat-value">{{ getMembershipDuration(membership) }}</span>
          <p class="stat-label">Days as Member</p>
          <p class="member-since">Since {{ membership.memberSince | date:'MMM d, yyyy' }}</p>
        </div>
      </div>
    </div>

    <!-- Dashboard Grid -->
    <div class="dashboard-grid">
      <!-- Left Column -->
      <section class="giveaways-section">
        <div class="section-header">
          <h2><i class="fas fa-gift"></i> Active Giveaways</h2>
          <a class="view-all-link" routerLink="/giveaways">View All Giveaways</a>
        </div>
        <div class="giveaways-container">
          <div class="giveaway-card" *ngFor="let giveaway of activeGiveaways">
            <div class="giveaway-image" [style.background-image]="'url(' + giveaway.imageUrl + ')'">
              <div class="giveaway-overlay">
                <span class="ends-in">Ends in {{ getDaysRemaining(giveaway) }} days</span>
              </div>
            </div>
            <div class="giveaway-content">
              <h3 class="giveaway-title">{{ giveaway.title }}</h3>
              <p class="giveaway-prize">{{ giveaway.prizes && giveaway.prizes.length > 0 ? giveaway.prizes[0].name : 'Prize' }}</p>
              <p class="giveaway-value">${{ formatPrizeValue(giveaway.prizeValue) }}</p>
              <button mat-raised-button color="primary" class="entry-button"
                [routerLink]="['/giveaways', giveaway.slug]">
                Enter Now
              </button>
            </div>
          </div>
        </div>
      </section>

      <!-- Right Column -->
      <div class="dashboard-sidebar">
        <!-- Membership Summary Card -->
        <mat-card class="membership-card">
          <mat-card-header>
            <mat-card-title>
              <i class="fas fa-id-card me-2"></i>
              Your Membership
            </mat-card-title>
          </mat-card-header>
          <mat-card-content>
            <div class="membership-tier">
              <span class="tier-label">{{ membership.tierName }}</span>
              <span class="tier-status" [ngClass]="getStatusClass(membership.status)">
                {{ membership.status | titlecase }}
              </span>
            </div>

            <mat-divider class="my-3"></mat-divider>

            <div class="membership-details">
              <div class="detail-item">
                <span class="detail-label">Entries:</span>
                <span class="detail-value">{{ membership.entriesRemaining }} of {{ membership.entriesTotal }}</span>
              </div>

              <div class="detail-item" *ngIf="membership.nextBillingDate">
                <span class="detail-label">Next Billing:</span>
                <span class="detail-value">{{ membership.nextBillingDate | date:'MMM d, yyyy' }}</span>
              </div>

              <div class="detail-item">
                <span class="detail-label">Auto-Renew:</span>
                <span class="detail-value">{{ membership.autoRenew ? 'Enabled' : 'Disabled' }}</span>
              </div>

              <div class="detail-item">
                <span class="detail-label">Member Since:</span>
                <span class="detail-value">{{ membership.memberSince | date:'MMM d, yyyy' }}</span>
              </div>
            </div>
          </mat-card-content>
          <mat-card-actions>
            <button mat-button color="primary" routerLink="/membership/manage">
              Manage Membership
            </button>
            <button mat-button color="accent" routerLink="/membership/upgrade">
              Upgrade
            </button>
          </mat-card-actions>
        </mat-card>

        <!-- Notifications Card -->
        <mat-card class="notifications-card">
          <mat-card-header>
            <mat-card-title>
              <i class="fas fa-bell me-2"></i>
              Notifications
            </mat-card-title>
            <button mat-button class="mark-all-read">
              Mark All Read
            </button>
          </mat-card-header>
          <mat-card-content>
            <div class="notifications-list">
              <div class="notification-item" *ngFor="let notification of notifications" [class.unread]="!notification.read">
                <div class="notification-icon" [ngClass]="getNotificationTypeClass(notification.type)">
                  <i class="fas" [ngClass]="notification.icon"></i>
                </div>
                <div class="notification-content">
                  <p class="notification-message">{{ notification.message }}</p>
                  <span class="notification-time">{{ notification.time }}</span>
                </div>
              </div>

              <div class="empty-notifications" *ngIf="notifications.length === 0">
                <i class="fas fa-bell-slash"></i>
                <p>No new notifications</p>
              </div>
            </div>
          </mat-card-content>
          <mat-card-actions *ngIf="notifications.length > 0">
            <a mat-button color="primary" routerLink="/profile/notifications">
              View All Notifications
            </a>
          </mat-card-actions>
        </mat-card>

        <!-- Activity Summary Card -->
        <mat-card class="activity-card">
          <mat-card-header>
            <mat-card-title>
              <i class="fas fa-history me-2"></i>
              Recent Activity
            </mat-card-title>
          </mat-card-header>
          <mat-card-content>
            <div class="activity-timeline">
              <div class="activity-item" *ngFor="let activity of recentActivities">
                <div class="activity-icon" [ngClass]="getActivityTypeClass(activity.type)">
                  <i class="fas" [ngClass]="activity.icon"></i>
                </div>
                <div class="activity-content">
                  <p class="activity-title">{{ activity.title }}</p>
                  <span class="activity-time">{{ activity.time }}</span>
                </div>
              </div>

              <div class="empty-activity" *ngIf="recentActivities.length === 0">
                <i class="fas fa-history"></i>
                <p>No recent activity</p>
              </div>
            </div>
          </mat-card-content>
          <mat-card-actions *ngIf="recentActivities.length > 0">
            <a mat-button color="primary" routerLink="/profile/entries">
              View All Activity
            </a>
          </mat-card-actions>
        </mat-card>
      </div>
    </div>
  </div>
</div>
