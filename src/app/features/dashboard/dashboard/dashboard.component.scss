/* Main Container */
.dashboard-container {
  padding: 0;
  width: 100%;
  font-family: var(--bs-font-sans-serif);
}

/* Loading State */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  background-color: white;
  border-radius: 16px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
  text-align: center;
}

.spinner-container {
  margin-bottom: 1rem;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(var(--bs-primary-rgb), 0.1);
  border-radius: 50%;
  border-top-color: var(--bs-primary);
  animation: spin 0.8s linear infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* No Membership State */
.no-membership-container {
  background-color: white;
  border-radius: 16px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
  padding: 3rem 2rem;
  text-align: center;
  animation: fadeIn 0.3s ease-out;
}

.no-membership-content {
  max-width: 500px;
  margin: 0 auto;
}

.icon-container {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background-color: rgba(var(--bs-primary-rgb), 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1.5rem;
}

.icon-container i {
  font-size: 36px;
  color: var(--bs-primary);
}

.no-membership-container h2 {
  font-size: 1.75rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: #212529;
}

.no-membership-container p {
  font-size: 1.1rem;
  color: #6c757d;
  margin-bottom: 2rem;
}

/* Dashboard Content */
.dashboard-content {
  animation: fadeIn 0.3s ease-out;
}

/* Dashboard Header */
.dashboard-header {
  background-color: white;
  border-radius: 16px;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
  display: flex;
  flex-direction: column;
  gap: 1.25rem;
}

.welcome-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 1rem;
}

.welcome-section h1 {
  font-size: 1.75rem;
  font-weight: 600;
  margin: 0;
  color: #212529;
}

.membership-badge {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.tier-name {
  font-size: 1rem;
  font-weight: 600;
  color: var(--bs-primary);
  background-color: rgba(var(--bs-primary-rgb), 0.1);
  padding: 0.35rem 0.75rem;
  border-radius: 8px;
}

.status-indicator {
  font-size: 0.85rem;
  font-weight: 500;
  padding: 0.25rem 0.6rem;
  border-radius: 6px;
}

.status-active {
  background-color: rgba(var(--bs-success-rgb), 0.1);
  color: var(--bs-success);
}

.status-canceled {
  background-color: rgba(var(--bs-warning-rgb), 0.1);
  color: var(--bs-warning);
}

.status-expired {
  background-color: rgba(var(--bs-danger-rgb), 0.1);
  color: var(--bs-danger);
}

.membership-period {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.period-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.period-label {
  font-size: 0.85rem;
  color: #6c757d;
}

.period-dates {
  font-size: 0.85rem;
  font-weight: 500;
  color: #495057;
}

.membership-progress {
  height: 6px;
  border-radius: 3px;
}

/* Stats Row */
.stats-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.stat-card {
  background: white;
  border-radius: 16px;
  padding: 1.25rem;
  display: flex;
  align-items: flex-start;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.stat-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 1rem;
  flex-shrink: 0;
}

.entries-stat .stat-icon {
  background-color: rgba(var(--bs-primary-rgb), 0.1);
}

.entries-stat .stat-icon i {
  color: var(--bs-primary);
}

.giveaways-stat .stat-icon {
  background-color: rgba(var(--bs-success-rgb), 0.1);
}

.giveaways-stat .stat-icon i {
  color: var(--bs-success);
}

.billing-stat .stat-icon {
  background-color: rgba(var(--bs-warning-rgb), 0.1);
}

.billing-stat .stat-icon i {
  color: var(--bs-warning);
}

.member-stat .stat-icon {
  background-color: rgba(var(--bs-info-rgb), 0.1);
}

.member-stat .stat-icon i {
  color: var(--bs-info);
}

.stat-icon i {
  font-size: 20px;
}

.stat-content {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.stat-value-container {
  display: flex;
  align-items: baseline;
}

.stat-value {
  font-size: 1.75rem;
  font-weight: 700;
  margin: 0;
  line-height: 1.2;
  color: #212529;
}

.stat-total {
  font-size: 1rem;
  color: #6c757d;
  margin-left: 0.25rem;
}

.stat-label {
  color: #6c757d;
  margin: 0;
  font-size: 0.9rem;
}

.entries-progress {
  height: 4px;
  background-color: #e9ecef;
  border-radius: 2px;
  overflow: hidden;
  margin-top: 0.5rem;
}

.progress-bar {
  height: 100%;
  background-color: var(--bs-primary);
  border-radius: 2px;
  transition: width 0.3s ease;
}

.billing-date, .member-since {
  font-size: 0.8rem;
  color: #6c757d;
  margin: 0;
}

/* Dashboard Grid */
.dashboard-grid {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 1.5rem;
}

/* Giveaways Section */
.giveaways-section {
  background-color: white;
  border-radius: 16px;
  padding: 1.5rem;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.25rem;
}

.section-header h2 {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #212529;
}

.section-header h2 i {
  color: var(--bs-primary);
}

.view-all-link {
  font-size: 0.9rem;
  font-weight: 500;
}

.giveaways-container {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
  gap: 1.25rem;
}

.giveaway-card {
  border-radius: 12px;
  overflow: hidden;
  background-color: #fff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.giveaway-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.giveaway-image {
  height: 160px;
  background-size: cover;
  background-position: center;
  position: relative;
}

.giveaway-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 0.75rem;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.7), transparent);
  color: white;
}

.ends-in {
  font-size: 0.85rem;
  font-weight: 500;
}

.giveaway-content {
  padding: 1.25rem;
  display: flex;
  flex-direction: column;
  flex-grow: 1;
}

.giveaway-title {
  font-size: 1.1rem;
  font-weight: 600;
  margin: 0 0 0.5rem;
  color: #212529;
}

.giveaway-prize {
  font-size: 0.9rem;
  color: #495057;
  margin: 0 0 0.25rem;
}

.giveaway-value {
  font-size: 1rem;
  font-weight: 600;
  color: var(--bs-success);
  margin: 0 0 1rem;
}

.entry-button {
  margin-top: auto;
}

/* Dashboard Sidebar */
.dashboard-sidebar {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

/* Membership Card */
.membership-card {
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
}

.membership-tier {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.tier-label {
  font-size: 1.1rem;
  font-weight: 600;
  color: #212529;
}

.tier-status {
  font-size: 0.85rem;
  font-weight: 500;
  padding: 0.25rem 0.6rem;
  border-radius: 6px;
}

.membership-details {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.detail-label {
  font-size: 0.9rem;
  color: #6c757d;
}

.detail-value {
  font-size: 0.9rem;
  font-weight: 500;
  color: #212529;
}

/* Notifications Card */
.notifications-card {
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
}

.mark-all-read {
  font-size: 0.85rem;
  padding: 0;
  min-width: auto;
}

.notifications-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  max-height: 300px;
  overflow-y: auto;
}

.notification-item {
  display: flex;
  align-items: flex-start;
  padding: 0.75rem;
  border-radius: 8px;
  background-color: #f8f9fa;
  transition: background-color 0.2s ease;
}

.notification-item:hover {
  background-color: #f1f3f5;
}

.notification-item.unread {
  background-color: rgba(var(--bs-primary-rgb), 0.05);
}

.notification-icon {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 0.75rem;
  flex-shrink: 0;
}

.notification-giveaway {
  background-color: rgba(var(--bs-success-rgb), 0.1);
}

.notification-giveaway i {
  color: var(--bs-success);
}

.notification-membership {
  background-color: rgba(var(--bs-primary-rgb), 0.1);
}

.notification-membership i {
  color: var(--bs-primary);
}

.notification-win {
  background-color: rgba(var(--bs-warning-rgb), 0.1);
}

.notification-win i {
  color: var(--bs-warning);
}

.notification-system {
  background-color: rgba(var(--bs-secondary-rgb), 0.1);
}

.notification-system i {
  color: var(--bs-secondary);
}

.notification-icon i {
  font-size: 14px;
}

.notification-content {
  flex: 1;
}

.notification-message {
  font-size: 0.9rem;
  margin: 0 0 0.25rem;
  color: #212529;
}

.notification-time {
  font-size: 0.8rem;
  color: #6c757d;
}

.empty-notifications {
  text-align: center;
  padding: 2rem 1rem;
  color: #6c757d;
}

.empty-notifications i {
  font-size: 2rem;
  margin-bottom: 0.75rem;
  opacity: 0.5;
}

.empty-notifications p {
  margin: 0;
  font-size: 0.9rem;
}

/* Activity Card */
.activity-card {
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
}

.activity-timeline {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  max-height: 300px;
  overflow-y: auto;
}

.activity-item {
  display: flex;
  align-items: flex-start;
  padding: 0.75rem;
  border-radius: 8px;
  background-color: #f8f9fa;
  transition: background-color 0.2s ease;
}

.activity-item:hover {
  background-color: #f1f3f5;
}

.activity-icon {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 0.75rem;
  flex-shrink: 0;
}

.activity-entry {
  background-color: rgba(var(--bs-primary-rgb), 0.1);
}

.activity-entry i {
  color: var(--bs-primary);
}

.activity-payment {
  background-color: rgba(var(--bs-success-rgb), 0.1);
}

.activity-payment i {
  color: var(--bs-success);
}

.activity-win {
  background-color: rgba(var(--bs-warning-rgb), 0.1);
}

.activity-win i {
  color: var(--bs-warning);
}

.activity-membership {
  background-color: rgba(var(--bs-info-rgb), 0.1);
}

.activity-membership i {
  color: var(--bs-info);
}

.activity-icon i {
  font-size: 14px;
}

.activity-content {
  flex: 1;
}

.activity-title {
  font-size: 0.9rem;
  margin: 0 0 0.25rem;
  color: #212529;
}

.activity-time {
  font-size: 0.8rem;
  color: #6c757d;
}

.empty-activity {
  text-align: center;
  padding: 2rem 1rem;
  color: #6c757d;
}

.empty-activity i {
  font-size: 2rem;
  margin-bottom: 0.75rem;
  opacity: 0.5;
}

.empty-activity p {
  margin: 0;
  font-size: 0.9rem;
}

/* Animations */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

/* Responsive Styles */
@media (max-width: 1199.98px) {
  .dashboard-grid {
    grid-template-columns: 1fr;
  }

  .dashboard-sidebar {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  }
}

@media (max-width: 991.98px) {
  .stats-row {
    grid-template-columns: repeat(2, 1fr);
  }

  .giveaways-container {
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  }
}

@media (max-width: 767.98px) {
  .dashboard-header {
    padding: 1.25rem;
  }

  .welcome-section {
    flex-direction: column;
    align-items: flex-start;
  }

  .membership-badge {
    align-self: flex-start;
  }

  .giveaways-section {
    padding: 1.25rem;
  }

  .dashboard-sidebar {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 575.98px) {
  .stats-row {
    grid-template-columns: 1fr;
  }

  .giveaways-container {
    grid-template-columns: 1fr;
  }

  .section-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.75rem;
  }

  .view-all-link {
    align-self: flex-end;
  }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}