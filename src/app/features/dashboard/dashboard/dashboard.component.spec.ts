import { ComponentFixture, TestBed } from '@angular/core/testing';
import { Router } from '@angular/router';
import { RouterTestingModule } from '@angular/router/testing';
import { of, throwError } from 'rxjs';
import { DashboardComponent } from './dashboard.component';
import { MembershipService } from '../../../core/services/membership.service';
import { AuthService } from '../../../core/services/auth.service';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatDividerModule } from '@angular/material/divider';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { CommonModule } from '@angular/common';
import { Component } from '@angular/core';

// Mock component for testing routing
@Component({
  template: '<div>Membership Plans</div>'
})
class MockMembershipPlansComponent { }

describe('DashboardComponent', () => {
  let component: DashboardComponent;
  let fixture: ComponentFixture<DashboardComponent>;
  let router: Router;
  let membershipService: jasmine.SpyObj<MembershipService>;
  let authService: jasmine.SpyObj<AuthService>;

  beforeEach(async () => {
    const membershipServiceSpy = jasmine.createSpyObj('MembershipService', ['getCurrentMembership']);
    const authServiceSpy = jasmine.createSpyObj('AuthService', [], {
      currentUser$: of({ firstName: 'John', lastName: 'Doe', email: '<EMAIL>' })
    });

    await TestBed.configureTestingModule({
      imports: [
        DashboardComponent,
        RouterTestingModule.withRoutes([
          { path: 'member/membership/plans', component: MockMembershipPlansComponent }
        ]),
        MatButtonModule,
        MatCardModule,
        MatDividerModule,
        MatProgressBarModule,
        CommonModule
      ],
      providers: [
        { provide: MembershipService, useValue: membershipServiceSpy },
        { provide: AuthService, useValue: authServiceSpy }
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(DashboardComponent);
    component = fixture.componentInstance;
    router = TestBed.inject(Router);
    membershipService = TestBed.inject(MembershipService) as jasmine.SpyObj<MembershipService>;
    authService = TestBed.inject(AuthService) as jasmine.SpyObj<AuthService>;
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should show no membership state when user has no active membership', () => {
    // Mock no membership found
    membershipService.getCurrentMembership.and.returnValue(throwError(() => new Error('No active membership found')));
    
    fixture.detectChanges();
    
    expect(component.loading).toBeFalse();
    expect(component.membership).toBeNull();
    
    const compiled = fixture.nativeElement as HTMLElement;
    expect(compiled.querySelector('.no-membership-container')).toBeTruthy();
    expect(compiled.querySelector('h2')?.textContent).toContain('No Active Membership');
  });

  it('should have correct routerLink for View Membership Plans button', () => {
    // Mock no membership found to show the button
    membershipService.getCurrentMembership.and.returnValue(throwError(() => new Error('No active membership found')));
    
    fixture.detectChanges();
    
    const compiled = fixture.nativeElement as HTMLElement;
    const button = compiled.querySelector('button[routerLink]') as HTMLElement;
    expect(button).toBeTruthy();
    expect(button.getAttribute('routerLink')).toBe('/member/membership/plans');
    expect(button.textContent?.trim()).toBe('View Membership Plans');
  });

  it('should show dashboard content when user has active membership', () => {
    // Mock active membership
    const mockMembership = {
      id: '1',
      userId: 'user1',
      tierId: 'tier1',
      tierName: 'Premium',
      status: 'active' as const,
      startDate: new Date(),
      endDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
      nextBillingDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
      paymentMethod: 'card_123',
      autoRenew: true,
      entriesRemaining: 10,
      entriesTotal: 10,
      memberSince: new Date()
    };
    
    membershipService.getCurrentMembership.and.returnValue(of(mockMembership));
    
    fixture.detectChanges();
    
    expect(component.loading).toBeFalse();
    expect(component.membership).toEqual(mockMembership);
    
    const compiled = fixture.nativeElement as HTMLElement;
    expect(compiled.querySelector('.dashboard-content')).toBeTruthy();
    expect(compiled.querySelector('.no-membership-container')).toBeFalsy();
  });

  it('should show loading state initially', () => {
    // Don't call detectChanges yet to test initial loading state
    expect(component.loading).toBeTrue();
    
    const compiled = fixture.nativeElement as HTMLElement;
    fixture.detectChanges();
    
    // Since we haven't mocked the service call, it should still be loading
    expect(compiled.querySelector('.loading-container')).toBeTruthy();
  });
});
