<div class="settings-container">
  <!-- Page Header -->
  <div class="page-header">
    <h1 class="page-title">Account Settings</h1>
    <p class="page-subtitle">Manage your account preferences and security settings</p>
  </div>

  <!-- Loading State -->
  <div *ngIf="loading" class="loading-container">
    <mat-spinner diameter="40"></mat-spinner>
    <p>Loading settings...</p>
  </div>

  <!-- Settings Content -->
  <div *ngIf="!loading" class="settings-content">
    <mat-tab-group animationDuration="300ms" class="settings-tabs" [selectedIndex]="selectedTabIndex">

      <!-- Security Tab -->
      <mat-tab label="Security">
        <div class="tab-content">
          <mat-card class="settings-card">
            <mat-card-header>
              <mat-card-title>Change Password</mat-card-title>
              <mat-card-subtitle>Update your account password</mat-card-subtitle>
            </mat-card-header>

            <mat-card-content>
              <form [formGroup]="passwordForm" (ngSubmit)="onChangePassword()">
                <div class="form-group">
                  <mat-form-field appearance="outline" class="full-width">
                    <mat-label>Current Password</mat-label>
                    <input matInput type="password" formControlName="currentPassword" placeholder="Enter current password" autocomplete="current-password">
                    <mat-icon matSuffix>lock</mat-icon>
                    <mat-error *ngIf="passwordForm.get('currentPassword')?.invalid && passwordForm.get('currentPassword')?.touched">
                      {{ getPasswordErrorMessage('currentPassword') }}
                    </mat-error>
                  </mat-form-field>
                </div>

                <div class="form-row">
                  <div class="form-group">
                    <mat-form-field appearance="outline" class="full-width">
                      <mat-label>New Password</mat-label>
                      <input matInput type="password" formControlName="newPassword" placeholder="Enter new password" autocomplete="new-password">
                      <mat-icon matSuffix>lock_outline</mat-icon>
                      <mat-hint>Password must contain: uppercase, lowercase, number (8+ characters)</mat-hint>
                      <mat-error *ngIf="passwordForm.get('newPassword')?.invalid && passwordForm.get('newPassword')?.touched">
                        {{ getPasswordErrorMessage('newPassword') }}
                      </mat-error>
                    </mat-form-field>

                    <!-- Password Strength Indicator -->
                    <div class="password-requirements" *ngIf="passwordForm.get('newPassword')?.value">
                      <div class="requirement-item" [class.met]="checkPasswordLength()">
                        <mat-icon class="requirement-icon">{{ checkPasswordLength() ? 'check_circle' : 'radio_button_unchecked' }}</mat-icon>
                        <span>At least 8 characters</span>
                      </div>
                      <div class="requirement-item" [class.met]="checkPasswordUppercase()">
                        <mat-icon class="requirement-icon">{{ checkPasswordUppercase() ? 'check_circle' : 'radio_button_unchecked' }}</mat-icon>
                        <span>Uppercase letter</span>
                      </div>
                      <div class="requirement-item" [class.met]="checkPasswordLowercase()">
                        <mat-icon class="requirement-icon">{{ checkPasswordLowercase() ? 'check_circle' : 'radio_button_unchecked' }}</mat-icon>
                        <span>Lowercase letter</span>
                      </div>
                      <div class="requirement-item" [class.met]="checkPasswordNumber()">
                        <mat-icon class="requirement-icon">{{ checkPasswordNumber() ? 'check_circle' : 'radio_button_unchecked' }}</mat-icon>
                        <span>Number</span>
                      </div>
                    </div>
                  </div>

                  <div class="form-group">
                    <mat-form-field appearance="outline" class="full-width">
                      <mat-label>Confirm New Password</mat-label>
                      <input matInput type="password" formControlName="confirmPassword" placeholder="Confirm new password" autocomplete="new-password">
                      <mat-icon matSuffix>lock_outline</mat-icon>
                      <mat-error *ngIf="passwordForm.get('confirmPassword')?.invalid && passwordForm.get('confirmPassword')?.touched">
                        {{ getPasswordErrorMessage('confirmPassword') }}
                      </mat-error>
                    </mat-form-field>
                  </div>
                </div>

                <div class="form-actions">
                  <button
                    mat-raised-button
                    color="primary"
                    type="submit"
                    [disabled]="savingPassword || passwordForm.invalid"
                    class="save-button">
                    <mat-spinner *ngIf="savingPassword" diameter="20" class="button-spinner"></mat-spinner>
                    <span *ngIf="!savingPassword">Change Password</span>
                    <span *ngIf="savingPassword">Changing...</span>
                  </button>
                </div>
              </form>
            </mat-card-content>
          </mat-card>
        </div>
      </mat-tab>

      <!-- Notifications Tab -->
      <mat-tab label="Notifications">
        <div class="tab-content">
          <mat-card class="settings-card">
            <mat-card-header>
              <mat-card-title>Email Notifications</mat-card-title>
              <mat-card-subtitle>Choose which emails you'd like to receive</mat-card-subtitle>
            </mat-card-header>

            <mat-card-content>
              <form [formGroup]="preferencesForm">
                <div formGroupName="emailNotifications" class="notification-settings">
                  <div class="notification-item">
                    <div class="notification-info">
                      <h4>Giveaway Updates</h4>
                      <p>Notifications about new giveaways and entry confirmations</p>
                    </div>
                    <mat-slide-toggle formControlName="giveawayUpdates" color="primary"></mat-slide-toggle>
                  </div>

                  <div class="notification-item">
                    <div class="notification-info">
                      <h4>Winner Announcements</h4>
                      <p>Notifications when winners are announced</p>
                    </div>
                    <mat-slide-toggle formControlName="winnerAnnouncements" color="primary"></mat-slide-toggle>
                  </div>

                  <div class="notification-item">
                    <div class="notification-info">
                      <h4>Membership Updates</h4>
                      <p>Important updates about your membership status</p>
                    </div>
                    <mat-slide-toggle formControlName="membershipUpdates" color="primary"></mat-slide-toggle>
                  </div>

                  <div class="notification-item">
                    <div class="notification-info">
                      <h4>Promotional Emails</h4>
                      <p>Special offers and promotional content</p>
                    </div>
                    <mat-slide-toggle formControlName="promotionalEmails" color="primary"></mat-slide-toggle>
                  </div>
                </div>
              </form>
            </mat-card-content>
          </mat-card>
        </div>
      </mat-tab>

      <!-- Privacy Tab -->
      <mat-tab label="Privacy">
        <div class="tab-content">
          <mat-card class="settings-card">
            <mat-card-header>
              <mat-card-title>Privacy Settings</mat-card-title>
              <mat-card-subtitle>Control your privacy and visibility preferences</mat-card-subtitle>
            </mat-card-header>

            <mat-card-content>
              <form [formGroup]="preferencesForm">
                <div formGroupName="privacy" class="privacy-settings">
                  <div class="privacy-item">
                    <div class="privacy-info">
                      <h4>Show Profile in Winners List</h4>
                      <p>Display your name and profile when you win giveaways</p>
                    </div>
                    <mat-slide-toggle formControlName="showProfileInWinners" color="primary"></mat-slide-toggle>
                  </div>

                  <div class="privacy-item">
                    <div class="privacy-info">
                      <h4>Allow Contact from Other Members</h4>
                      <p>Let other members send you messages through the platform</p>
                    </div>
                    <mat-slide-toggle formControlName="allowContactFromOtherMembers" color="primary"></mat-slide-toggle>
                  </div>
                </div>
              </form>
            </mat-card-content>
          </mat-card>
        </div>
      </mat-tab>

      <!-- Display Tab -->
      <mat-tab label="Display">
        <div class="tab-content">
          <mat-card class="settings-card">
            <mat-card-header>
              <mat-card-title>Display Preferences</mat-card-title>
              <mat-card-subtitle>Customize your viewing experience</mat-card-subtitle>
            </mat-card-header>

            <mat-card-content>
              <form [formGroup]="preferencesForm">
                <div formGroupName="display" class="display-settings">
                  <div class="form-row">
                    <div class="form-group">
                      <mat-form-field appearance="outline" class="full-width">
                        <mat-label>Theme</mat-label>
                        <mat-select formControlName="theme">
                          <mat-option *ngFor="let theme of themes" [value]="theme.value">
                            {{ theme.label }}
                          </mat-option>
                        </mat-select>
                      </mat-form-field>
                    </div>

                    <div class="form-group">
                      <mat-form-field appearance="outline" class="full-width">
                        <mat-label>Language</mat-label>
                        <mat-select formControlName="language">
                          <mat-option *ngFor="let language of languages" [value]="language.value">
                            {{ language.label }}
                          </mat-option>
                        </mat-select>
                      </mat-form-field>
                    </div>
                  </div>

                  <div class="form-group">
                    <mat-form-field appearance="outline" class="full-width">
                      <mat-label>Timezone</mat-label>
                      <mat-select formControlName="timezone">
                        <mat-option *ngFor="let timezone of timezones" [value]="timezone.value">
                          {{ timezone.label }}
                        </mat-option>
                      </mat-select>
                    </mat-form-field>
                  </div>
                </div>
              </form>
            </mat-card-content>
          </mat-card>
        </div>
      </mat-tab>
    </mat-tab-group>

    <!-- Global Actions -->
    <div class="global-actions">
      <button
        mat-raised-button
        color="primary"
        (click)="onSavePreferences()"
        [disabled]="savingPreferences"
        class="save-preferences-button">
        <mat-spinner *ngIf="savingPreferences" diameter="20" class="button-spinner"></mat-spinner>
        <span *ngIf="!savingPreferences">Save Preferences</span>
        <span *ngIf="savingPreferences">Saving...</span>
      </button>

      <button
        mat-stroked-button
        color="warn"
        (click)="resetToDefaults()"
        [disabled]="savingPreferences"
        class="reset-button">
        <mat-icon>restore</mat-icon>
        Reset to Defaults
      </button>
    </div>
  </div>
</div>
