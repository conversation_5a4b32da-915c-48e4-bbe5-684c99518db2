import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';
import { MatDividerModule } from '@angular/material/divider';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { MatSelectModule } from '@angular/material/select';
import { MatTabsModule } from '@angular/material/tabs';
import { UserService, UserPreferences, ChangePasswordRequest } from '../../../core/services/user.service';
import { finalize } from 'rxjs/operators';

@Component({
  selector: 'app-settings',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatCardModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatIconModule,
    MatProgressSpinnerModule,
    MatSnackBarModule,
    MatDividerModule,
    MatSlideToggleModule,
    MatSelectModule,
    MatTabsModule
  ],
  templateUrl: './settings.component.html',
  styleUrls: ['./settings.component.scss']
})
export class SettingsComponent implements OnInit {
  passwordForm: FormGroup;
  preferencesForm: FormGroup;
  loading = false;
  savingPassword = false;
  savingPreferences = false;
  userPreferences: UserPreferences | null = null;
  selectedTabIndex = 0;

  languages = [
    { value: 'en', label: 'English' },
    { value: 'es', label: 'Spanish' },
    { value: 'fr', label: 'French' },
    { value: 'de', label: 'German' }
  ];

  timezones = [
    { value: 'America/New_York', label: 'Eastern Time (ET)' },
    { value: 'America/Chicago', label: 'Central Time (CT)' },
    { value: 'America/Denver', label: 'Mountain Time (MT)' },
    { value: 'America/Los_Angeles', label: 'Pacific Time (PT)' },
    { value: 'UTC', label: 'UTC' }
  ];

  themes = [
    { value: 'light', label: 'Light' },
    { value: 'dark', label: 'Dark' },
    { value: 'auto', label: 'Auto (System)' }
  ];

  constructor(
    private fb: FormBuilder,
    private userService: UserService,
    private snackBar: MatSnackBar,
    private route: ActivatedRoute,
    private router: Router
  ) {
    this.passwordForm = this.fb.group({
      currentPassword: ['', [Validators.required]],
      newPassword: ['', [Validators.required, Validators.minLength(8), this.passwordStrengthValidator]],
      confirmPassword: ['', [Validators.required]]
    }, { validators: this.passwordMatchValidator });

    this.preferencesForm = this.fb.group({
      emailNotifications: this.fb.group({
        giveawayUpdates: [true],
        winnerAnnouncements: [true],
        membershipUpdates: [true],
        promotionalEmails: [false]
      }),
      privacy: this.fb.group({
        showProfileInWinners: [true],
        allowContactFromOtherMembers: [false]
      }),
      display: this.fb.group({
        theme: ['light'],
        language: ['en'],
        timezone: ['America/New_York']
      })
    });
  }

  ngOnInit(): void {
    this.loadPreferences();

    // Check for fragment to determine which tab to open
    this.route.fragment.subscribe(fragment => {
      if (fragment === 'security' || fragment === 'password') {
        this.selectedTabIndex = 0; // Security tab
      } else if (fragment === 'notifications') {
        this.selectedTabIndex = 1; // Notifications tab
      } else if (fragment === 'privacy') {
        this.selectedTabIndex = 2; // Privacy tab
      } else if (fragment === 'display') {
        this.selectedTabIndex = 3; // Display tab
      }
    });
  }

  /**
   * Custom validator for password strength
   */
  passwordStrengthValidator(control: any) {
    const value = control.value;
    if (!value) return null;

    const hasNumber = /[0-9]/.test(value);
    const hasUpper = /[A-Z]/.test(value);
    const hasLower = /[a-z]/.test(value);
    const hasSpecial = /[#?!@$%^&*-]/.test(value);

    const valid = hasNumber && hasUpper && hasLower && value.length >= 8;

    if (!valid) {
      return {
        passwordStrength: {
          hasNumber,
          hasUpper,
          hasLower,
          hasSpecial,
          minLength: value.length >= 8
        }
      };
    }

    return null;
  }

  /**
   * Custom validator to check if passwords match
   */
  passwordMatchValidator(form: FormGroup) {
    const newPassword = form.get('newPassword');
    const confirmPassword = form.get('confirmPassword');

    if (newPassword && confirmPassword && newPassword.value !== confirmPassword.value) {
      confirmPassword.setErrors({ passwordMismatch: true });
      return { passwordMismatch: true };
    }

    if (confirmPassword?.hasError('passwordMismatch')) {
      delete confirmPassword.errors!['passwordMismatch'];
      if (Object.keys(confirmPassword.errors!).length === 0) {
        confirmPassword.setErrors(null);
      }
    }

    return null;
  }

  /**
   * Load user preferences
   */
  loadPreferences(): void {
    this.loading = true;
    this.userService.getPreferences()
      .pipe(finalize(() => this.loading = false))
      .subscribe({
        next: (response) => {
          if (response.success) {
            this.userPreferences = response.data;
            this.populatePreferencesForm();
          }
        },
        error: (error) => {
          console.error('Error loading preferences:', error);
          // Use default preferences if loading fails
          this.setDefaultPreferences();
        }
      });
  }

  /**
   * Populate preferences form with loaded data
   */
  populatePreferencesForm(): void {
    if (this.userPreferences) {
      this.preferencesForm.patchValue(this.userPreferences);
    }
  }

  /**
   * Set default preferences if loading fails
   */
  setDefaultPreferences(): void {
    this.userPreferences = {
      emailNotifications: {
        giveawayUpdates: true,
        winnerAnnouncements: true,
        membershipUpdates: true,
        promotionalEmails: false
      },
      privacy: {
        showProfileInWinners: true,
        allowContactFromOtherMembers: false
      },
      display: {
        theme: 'light',
        language: 'en',
        timezone: 'America/New_York'
      }
    };
    this.populatePreferencesForm();
  }

  /**
   * Change password
   */
  onChangePassword(): void {
    if (this.passwordForm.valid) {
      this.savingPassword = true;

      const passwordData: ChangePasswordRequest = this.passwordForm.value;

      this.userService.changePassword(passwordData)
        .pipe(finalize(() => this.savingPassword = false))
        .subscribe({
          next: (response) => {
            if (response.success) {
              this.snackBar.open('Password changed successfully! Please log in with your new password.', 'Close', {
                duration: 5000,
                panelClass: ['success-snackbar']
              });
              this.passwordForm.reset();

              // Optional: Navigate back to profile or show success state
              setTimeout(() => {
                this.router.navigate(['/member/profile']);
              }, 2000);
            }
          },
          error: (error) => {
            console.error('Error changing password:', error);
            let message = 'Failed to change password';

            // Handle specific error cases
            if (error.status === 400) {
              message = 'Current password is incorrect';
            } else if (error.status === 422) {
              message = 'New password does not meet requirements';
            } else if (error.error?.message) {
              message = error.error.message;
            }

            this.snackBar.open(message, 'Close', {
              duration: 5000,
              panelClass: ['error-snackbar']
            });
          }
        });
    } else {
      this.markFormGroupTouched(this.passwordForm);
    }
  }

  /**
   * Save preferences
   */
  onSavePreferences(): void {
    if (this.preferencesForm.valid) {
      this.savingPreferences = true;

      const preferences: UserPreferences = this.preferencesForm.value;

      this.userService.updatePreferences(preferences)
        .pipe(finalize(() => this.savingPreferences = false))
        .subscribe({
          next: (response) => {
            if (response.success) {
              this.userPreferences = response.data;
              this.snackBar.open('Preferences saved successfully', 'Close', { duration: 3000 });
            }
          },
          error: (error) => {
            console.error('Error saving preferences:', error);
            this.snackBar.open('Failed to save preferences', 'Close', { duration: 3000 });
          }
        });
    }
  }

  /**
   * Mark all form fields as touched to show validation errors
   */
  private markFormGroupTouched(formGroup: FormGroup): void {
    Object.keys(formGroup.controls).forEach(key => {
      const control = formGroup.get(key);
      if (control instanceof FormGroup) {
        this.markFormGroupTouched(control);
      } else {
        control?.markAsTouched();
      }
    });
  }

  /**
   * Get error message for password fields
   */
  getPasswordErrorMessage(fieldName: string): string {
    const control = this.passwordForm.get(fieldName);
    if (control?.hasError('required')) {
      return `${fieldName.replace(/([A-Z])/g, ' $1').toLowerCase()} is required`;
    }
    if (control?.hasError('minlength')) {
      return 'Password must be at least 8 characters';
    }
    if (control?.hasError('passwordStrength')) {
      const errors = control.errors!['passwordStrength'];
      const missing = [];
      if (!errors.hasUpper) missing.push('uppercase letter');
      if (!errors.hasLower) missing.push('lowercase letter');
      if (!errors.hasNumber) missing.push('number');
      if (missing.length > 0) {
        return `Password must contain: ${missing.join(', ')}`;
      }
    }
    if (control?.hasError('passwordMismatch')) {
      return 'Passwords do not match';
    }
    return '';
  }

  /**
   * Check password length requirement
   */
  checkPasswordLength(): boolean {
    const password = this.passwordForm.get('newPassword')?.value || '';
    return password.length >= 8;
  }

  /**
   * Check password uppercase requirement
   */
  checkPasswordUppercase(): boolean {
    const password = this.passwordForm.get('newPassword')?.value || '';
    return /[A-Z]/.test(password);
  }

  /**
   * Check password lowercase requirement
   */
  checkPasswordLowercase(): boolean {
    const password = this.passwordForm.get('newPassword')?.value || '';
    return /[a-z]/.test(password);
  }

  /**
   * Check password number requirement
   */
  checkPasswordNumber(): boolean {
    const password = this.passwordForm.get('newPassword')?.value || '';
    return /[0-9]/.test(password);
  }

  /**
   * Reset preferences to default
   */
  resetToDefaults(): void {
    this.setDefaultPreferences();
    this.snackBar.open('Preferences reset to defaults', 'Close', { duration: 3000 });
  }
}
