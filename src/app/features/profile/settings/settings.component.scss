.settings-container {
  // Remove container constraints - use full width of parent
  width: 100%;
}

.page-header {
  margin-bottom: 2rem;

  .page-title {
    font-size: 2rem;
    font-weight: 600;
    color: #333;
    margin: 0 0 0.5rem 0;
  }

  .page-subtitle {
    color: #666;
    margin: 0;
    font-size: 1rem;
  }
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  text-align: center;

  p {
    margin-top: 1rem;
    color: #666;
  }
}

.settings-content {
  .settings-tabs {
    background: white;
    border-radius: 16px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    overflow: hidden;

    .tab-content {
      padding: 0;
    }
  }
}

.settings-card {
  border: none;
  box-shadow: none;
  border-radius: 0;

  mat-card-header {
    padding: 2rem 2rem 1rem 2rem;

    mat-card-title {
      font-size: 1.5rem;
      font-weight: 600;
      color: #333;
    }

    mat-card-subtitle {
      color: #666;
      margin-top: 0.25rem;
    }
  }

  mat-card-content {
    padding: 0 2rem 2rem 2rem;
  }
}

.form-row {
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;

  @media (max-width: 768px) {
    flex-direction: column;
    gap: 0;
  }

  .form-group {
    flex: 1;
  }
}

.form-group {
  margin-bottom: 1rem;

  &.full-width {
    width: 100%;
  }
}

.full-width {
  width: 100%;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 2rem;

  .save-button {
    min-width: 160px;
    height: 44px;
    border-radius: 12px;
    font-weight: 600;

    .button-spinner {
      margin-right: 0.5rem;
    }
  }
}

// Password Requirements
.password-requirements {
  margin-top: 1rem;
  padding: 1rem;
  background-color: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;

  .requirement-item {
    display: flex;
    align-items: center;
    margin-bottom: 0.5rem;

    &:last-child {
      margin-bottom: 0;
    }

    .requirement-icon {
      font-size: 16px;
      width: 16px;
      height: 16px;
      margin-right: 0.5rem;
      color: #dc3545; // Red by default
    }

    span {
      font-size: 0.875rem;
      color: #6c757d;
    }

    &.met {
      .requirement-icon {
        color: #28a745; // Green when met
      }

      span {
        color: #28a745;
        font-weight: 500;
      }
    }
  }
}

// Notification Settings
.notification-settings {
  .notification-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem 0;
    border-bottom: 1px solid #f0f0f0;

    &:last-child {
      border-bottom: none;
    }

    .notification-info {
      flex: 1;
      margin-right: 1rem;

      h4 {
        margin: 0 0 0.25rem 0;
        font-size: 1rem;
        font-weight: 600;
        color: #333;
      }

      p {
        margin: 0;
        font-size: 0.875rem;
        color: #666;
        line-height: 1.4;
      }
    }

    mat-slide-toggle {
      flex-shrink: 0;
    }
  }
}

// Privacy Settings
.privacy-settings {
  .privacy-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem 0;
    border-bottom: 1px solid #f0f0f0;

    &:last-child {
      border-bottom: none;
    }

    .privacy-info {
      flex: 1;
      margin-right: 1rem;

      h4 {
        margin: 0 0 0.25rem 0;
        font-size: 1rem;
        font-weight: 600;
        color: #333;
      }

      p {
        margin: 0;
        font-size: 0.875rem;
        color: #666;
        line-height: 1.4;
      }
    }

    mat-slide-toggle {
      flex-shrink: 0;
    }
  }
}

// Display Settings
.display-settings {
  .form-row {
    margin-bottom: 1.5rem;
  }

  .form-group {
    margin-bottom: 1.5rem;
  }
}

// Global Actions
.global-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 2rem;
  padding: 1.5rem;
  background: #f8f9fa;
  border-radius: 12px;

  @media (max-width: 768px) {
    flex-direction: column;
    gap: 1rem;
  }

  .save-preferences-button {
    min-width: 160px;
    height: 44px;
    border-radius: 12px;
    font-weight: 600;

    .button-spinner {
      margin-right: 0.5rem;
    }
  }

  .reset-button {
    height: 44px;
    border-radius: 12px;
    font-weight: 500;

    mat-icon {
      margin-right: 0.5rem;
    }
  }
}

// Material Customization
::ng-deep {
  .mat-mdc-form-field {
    .mat-mdc-text-field-wrapper {
      border-radius: 12px;
    }

    .mat-mdc-form-field-subscript-wrapper {
      margin-top: 0.5rem;
    }
  }

  .mat-mdc-tab-group {
    .mat-mdc-tab-header {
      border-bottom: 1px solid #e0e0e0;

      .mat-mdc-tab-label {
        min-width: 120px;
        height: 60px;
        font-weight: 500;
        font-size: 0.95rem;

        &.mdc-tab--active {
          font-weight: 600;
        }
      }
    }

    .mat-mdc-tab-body-wrapper {
      .mat-mdc-tab-body {
        .mat-mdc-tab-body-content {
          padding: 0;
          overflow: visible;
        }
      }
    }
  }

  .mat-mdc-slide-toggle {
    .mdc-switch {
      .mdc-switch__track {
        border-radius: 14px;
      }

      .mdc-switch__handle {
        .mdc-switch__handle-track {
          border-radius: 50%;
        }
      }
    }

    &.mat-checked {
      .mdc-switch {
        .mdc-switch__track {
          background-color: rgba(103, 126, 234, 0.3);
        }

        .mdc-switch__handle {
          .mdc-switch__handle-track {
            background-color: #677eea;
          }
        }
      }
    }
  }

  .mat-mdc-card {
    .mat-mdc-card-header {
      .mat-mdc-card-title {
        font-size: inherit;
        font-weight: inherit;
        margin: 0;
      }

      .mat-mdc-card-subtitle {
        font-size: inherit;
        margin: 0;
      }
    }
  }

  .mat-mdc-select {
    .mat-mdc-select-trigger {
      border-radius: 12px;
    }
  }
}
