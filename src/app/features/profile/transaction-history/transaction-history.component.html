<div class="transaction-history-container">
  <!-- Page Header -->
  <div class="page-header">
    <div class="header-content">
      <h1 class="page-title">Transaction History</h1>
      <p class="page-subtitle">View all your billing and payment transactions</p>
    </div>
    <div class="header-actions">
      <button mat-stroked-button (click)="refresh()" [disabled]="loading">
        <mat-icon>refresh</mat-icon>
        Refresh
      </button>
    </div>
  </div>

  <!-- Filters Card -->
  <mat-card class="filters-card">
    <mat-card-content>
      <div class="filters-row">
        <mat-form-field appearance="outline" class="filter-field">
          <mat-label>Transaction Type</mat-label>
          <mat-select [(value)]="selectedType" (selectionChange)="onFilterChange()">
            <mat-option *ngFor="let type of transactionTypes" [value]="type.value">
              {{ type.label }}
            </mat-option>
          </mat-select>
        </mat-form-field>

        <mat-form-field appearance="outline" class="filter-field">
          <mat-label>Status</mat-label>
          <mat-select [(value)]="selectedStatus" (selectionChange)="onFilterChange()">
            <mat-option *ngFor="let status of transactionStatuses" [value]="status.value">
              {{ status.label }}
            </mat-option>
          </mat-select>
        </mat-form-field>
      </div>
    </mat-card-content>
  </mat-card>

  <!-- Transactions Table Card -->
  <mat-card class="transactions-card">
    <mat-card-header>
      <mat-card-title>
        <mat-icon class="title-icon">receipt_long</mat-icon>
        Your Transactions
      </mat-card-title>
      <mat-card-subtitle *ngIf="!loading">
        {{ totalTransactions }} total transactions
      </mat-card-subtitle>
    </mat-card-header>

    <mat-card-content>
      <!-- Loading State -->
      <div *ngIf="loading" class="loading-container">
        <mat-spinner diameter="40"></mat-spinner>
        <p>Loading your transactions...</p>
      </div>

      <!-- Transactions Table -->
      <div *ngIf="!loading && transactions.length > 0" class="table-container">
        <table mat-table [dataSource]="getFilteredTransactions()" class="transactions-table">
          
          <!-- Date Column -->
          <ng-container matColumnDef="date">
            <th mat-header-cell *matHeaderCellDef>Date</th>
            <td mat-cell *matCellDef="let transaction">
              <div class="date-cell">
                {{ formatDate(transaction.date) }}
              </div>
            </td>
          </ng-container>

          <!-- Type Column -->
          <ng-container matColumnDef="type">
            <th mat-header-cell *matHeaderCellDef>Type</th>
            <td mat-cell *matCellDef="let transaction">
              <div class="type-cell">
                <mat-icon [style.color]="getTypeColor(transaction.type)">
                  {{ getTypeIcon(transaction.type) }}
                </mat-icon>
                <span class="type-label">{{ transaction.type | titlecase }}</span>
              </div>
            </td>
          </ng-container>

          <!-- Description Column -->
          <ng-container matColumnDef="description">
            <th mat-header-cell *matHeaderCellDef>Description</th>
            <td mat-cell *matCellDef="let transaction">
              <div class="description-cell">
                <div class="description-text">{{ transaction.description }}</div>
                <div *ngIf="transaction.paymentMethod" class="payment-method">
                  {{ transaction.paymentMethod }}
                </div>
              </div>
            </td>
          </ng-container>

          <!-- Amount Column -->
          <ng-container matColumnDef="amount">
            <th mat-header-cell *matHeaderCellDef>Amount</th>
            <td mat-cell *matCellDef="let transaction">
              <div class="amount-cell">
                <span 
                  class="amount-value" 
                  [style.color]="getAmountColor(transaction.type)">
                  {{ formatAmount(transaction.amount, transaction.currency) }}
                </span>
              </div>
            </td>
          </ng-container>

          <!-- Status Column -->
          <ng-container matColumnDef="status">
            <th mat-header-cell *matHeaderCellDef>Status</th>
            <td mat-cell *matCellDef="let transaction">
              <mat-chip-set>
                <mat-chip [color]="getStatusColor(transaction.status)" selected>
                  <mat-icon matChipAvatar>{{ getStatusIcon(transaction.status) }}</mat-icon>
                  {{ getStatusText(transaction.status) }}
                </mat-chip>
              </mat-chip-set>
            </td>
          </ng-container>

          <!-- Actions Column -->
          <ng-container matColumnDef="actions">
            <th mat-header-cell *matHeaderCellDef>Actions</th>
            <td mat-cell *matCellDef="let transaction">
              <div class="actions-cell">
                <button 
                  *ngIf="transaction.invoiceUrl"
                  mat-icon-button 
                  color="primary"
                  (click)="downloadInvoice(transaction)"
                  matTooltip="Download Invoice">
                  <mat-icon>download</mat-icon>
                </button>
              </div>
            </td>
          </ng-container>

          <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
          <tr mat-row *matRowDef="let row; columns: displayedColumns;" class="transaction-row"></tr>
        </table>

        <!-- Pagination -->
        <mat-paginator
          [length]="totalTransactions"
          [pageSize]="pageSize"
          [pageSizeOptions]="[5, 10, 25, 50]"
          [pageIndex]="currentPage - 1"
          (page)="onPageChange($event)"
          showFirstLastButtons
          class="transactions-paginator">
        </mat-paginator>
      </div>

      <!-- Empty State -->
      <div *ngIf="!loading && transactions.length === 0" class="empty-state">
        <div class="empty-content">
          <mat-icon class="empty-icon">receipt_long</mat-icon>
          <h3>No Transactions Yet</h3>
          <p>You don't have any transactions yet. Start by purchasing a membership or entering giveaways!</p>
          <button mat-raised-button color="primary" routerLink="/member/membership">
            <mat-icon>card_membership</mat-icon>
            View Membership Plans
          </button>
        </div>
      </div>
    </mat-card-content>
  </mat-card>

  <!-- Transaction Statistics Card -->
  <mat-card class="stats-card" *ngIf="!loading && transactions.length > 0">
    <mat-card-header>
      <mat-card-title>
        <mat-icon class="title-icon">analytics</mat-icon>
        Transaction Summary
      </mat-card-title>
    </mat-card-header>

    <mat-card-content>
      <div class="stats-grid">
        <div class="stat-item">
          <div class="stat-value">{{ totalTransactions }}</div>
          <div class="stat-label">Total Transactions</div>
        </div>

        <div class="stat-item">
          <div class="stat-value">{{ getStatusCount('completed') }}</div>
          <div class="stat-label">Completed</div>
        </div>

        <div class="stat-item">
          <div class="stat-value">{{ getStatusCount('pending') }}</div>
          <div class="stat-label">Pending</div>
        </div>

        <div class="stat-item">
          <div class="stat-value">{{ formatAmount(getTotalAmount()) }}</div>
          <div class="stat-label">Net Amount</div>
        </div>
      </div>
    </mat-card-content>
  </mat-card>
</div>
