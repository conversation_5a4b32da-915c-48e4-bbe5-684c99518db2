import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatTableModule } from '@angular/material/table';
import { MatPaginatorModule, PageEvent } from '@angular/material/paginator';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatChipsModule } from '@angular/material/chips';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatSelectModule } from '@angular/material/select';
import { MatFormFieldModule } from '@angular/material/form-field';
import { UserService, UserTransaction } from '../../../core/services/user.service';
import { finalize } from 'rxjs/operators';

@Component({
  selector: 'app-transaction-history',
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatTableModule,
    MatPaginatorModule,
    MatButtonModule,
    MatIconModule,
    MatProgressSpinnerModule,
    MatChipsModule,
    MatTooltipModule,
    MatSelectModule,
    MatFormFieldModule
  ],
  templateUrl: './transaction-history.component.html',
  styleUrls: ['./transaction-history.component.scss']
})
export class TransactionHistoryComponent implements OnInit {
  transactions: UserTransaction[] = [];
  loading = false;
  totalTransactions = 0;
  currentPage = 1;
  pageSize = 10;
  selectedType = 'all';
  selectedStatus = 'all';
  displayedColumns: string[] = ['date', 'type', 'description', 'amount', 'status', 'actions'];

  transactionTypes = [
    { value: 'all', label: 'All Types' },
    { value: 'membership', label: 'Membership' },
    { value: 'entry', label: 'Entry Fee' },
    { value: 'refund', label: 'Refund' }
  ];

  transactionStatuses = [
    { value: 'all', label: 'All Statuses' },
    { value: 'pending', label: 'Pending' },
    { value: 'completed', label: 'Completed' },
    { value: 'failed', label: 'Failed' },
    { value: 'refunded', label: 'Refunded' }
  ];

  constructor(private userService: UserService) {}

  ngOnInit(): void {
    this.loadTransactions();
  }

  /**
   * Load user transactions with pagination
   */
  loadTransactions(): void {
    this.loading = true;
    this.userService.getUserTransactions(this.currentPage, this.pageSize)
      .pipe(finalize(() => this.loading = false))
      .subscribe({
        next: (response) => {
          if (response.success) {
            this.transactions = response.data.transactions;
            this.totalTransactions = response.data.total;
          }
        },
        error: (error) => {
          console.error('Error loading transactions:', error);
          this.transactions = [];
        }
      });
  }

  /**
   * Handle page change
   */
  onPageChange(event: PageEvent): void {
    this.currentPage = event.pageIndex + 1;
    this.pageSize = event.pageSize;
    this.loadTransactions();
  }

  /**
   * Handle filter change
   */
  onFilterChange(): void {
    this.currentPage = 1;
    this.loadTransactions();
  }

  /**
   * Get status chip color
   */
  getStatusColor(status: string): string {
    switch (status) {
      case 'completed':
        return 'primary';
      case 'pending':
        return 'warn';
      case 'failed':
        return 'warn';
      case 'refunded':
        return 'accent';
      default:
        return '';
    }
  }

  /**
   * Get status display text
   */
  getStatusText(status: string): string {
    switch (status) {
      case 'completed':
        return 'Completed';
      case 'pending':
        return 'Pending';
      case 'failed':
        return 'Failed';
      case 'refunded':
        return 'Refunded';
      default:
        return status;
    }
  }

  /**
   * Get status icon
   */
  getStatusIcon(status: string): string {
    switch (status) {
      case 'completed':
        return 'check_circle';
      case 'pending':
        return 'schedule';
      case 'failed':
        return 'error';
      case 'refunded':
        return 'undo';
      default:
        return 'help';
    }
  }

  /**
   * Get transaction type icon
   */
  getTypeIcon(type: string): string {
    switch (type) {
      case 'membership':
        return 'card_membership';
      case 'entry':
        return 'confirmation_number';
      case 'refund':
        return 'money_off';
      default:
        return 'payment';
    }
  }

  /**
   * Get transaction type color
   */
  getTypeColor(type: string): string {
    switch (type) {
      case 'membership':
        return '#677eea';
      case 'entry':
        return '#f57c00';
      case 'refund':
        return '#4caf50';
      default:
        return '#666';
    }
  }

  /**
   * Format date for display
   */
  formatDate(date: Date): string {
    return new Date(date).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  }

  /**
   * Format amount for display
   */
  formatAmount(amount: number, currency: string = 'USD'): string {
    const isNegative = amount < 0;
    const absAmount = Math.abs(amount);
    const formatted = new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency
    }).format(absAmount / 100); // Convert cents to dollars
    
    return isNegative ? `-${formatted}` : formatted;
  }

  /**
   * Get amount color based on transaction type
   */
  getAmountColor(type: string): string {
    switch (type) {
      case 'refund':
        return '#4caf50'; // Green for refunds (money back)
      case 'membership':
      case 'entry':
        return '#d32f2f'; // Red for payments (money out)
      default:
        return '#333';
    }
  }

  /**
   * Download invoice (if available)
   */
  downloadInvoice(transaction: UserTransaction): void {
    if (transaction.invoiceUrl) {
      window.open(transaction.invoiceUrl, '_blank');
    }
  }

  /**
   * Get filtered transactions
   */
  getFilteredTransactions(): UserTransaction[] {
    return this.transactions.filter(transaction => {
      const typeMatch = this.selectedType === 'all' || transaction.type === this.selectedType;
      const statusMatch = this.selectedStatus === 'all' || transaction.status === this.selectedStatus;
      return typeMatch && statusMatch;
    });
  }

  /**
   * Get total amount for filtered transactions
   */
  getTotalAmount(): number {
    return this.getFilteredTransactions().reduce((total, transaction) => {
      return total + (transaction.type === 'refund' ? transaction.amount : -transaction.amount);
    }, 0);
  }

  /**
   * Get count by status
   */
  getStatusCount(status: string): number {
    return this.transactions.filter(t => t.status === status).length;
  }

  /**
   * Refresh transactions
   */
  refresh(): void {
    this.currentPage = 1;
    this.loadTransactions();
  }
}
