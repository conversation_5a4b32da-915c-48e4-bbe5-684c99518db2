import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatTableModule } from '@angular/material/table';
import { MatPaginatorModule, PageEvent } from '@angular/material/paginator';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatChipsModule } from '@angular/material/chips';
import { MatTooltipModule } from '@angular/material/tooltip';
import { RouterModule } from '@angular/router';
import { UserService, UserEntry } from '../../../core/services/user.service';
import { finalize } from 'rxjs/operators';

@Component({
  selector: 'app-entry-history',
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatTableModule,
    MatPaginatorModule,
    MatButtonModule,
    MatIconModule,
    MatProgressSpinnerModule,
    MatChipsModule,
    MatTooltipModule,
    RouterModule
  ],
  templateUrl: './entry-history.component.html',
  styleUrls: ['./entry-history.component.scss']
})
export class EntryHistoryComponent implements OnInit {
  entries: UserEntry[] = [];
  loading = false;
  totalEntries = 0;
  currentPage = 1;
  pageSize = 10;
  displayedColumns: string[] = ['giveaway', 'entryDate', 'status', 'entryMethod', 'actions'];

  constructor(private userService: UserService) {}

  ngOnInit(): void {
    this.loadEntries();
  }

  /**
   * Load user entries with pagination
   */
  loadEntries(): void {
    this.loading = true;
    this.userService.getUserEntries(this.currentPage, this.pageSize)
      .pipe(finalize(() => this.loading = false))
      .subscribe({
        next: (response) => {
          if (response.success) {
            this.entries = response.data.entries;
            this.totalEntries = response.data.total;
          }
        },
        error: (error) => {
          console.error('Error loading entries:', error);
          this.entries = [];
        }
      });
  }

  /**
   * Handle page change
   */
  onPageChange(event: PageEvent): void {
    this.currentPage = event.pageIndex + 1;
    this.pageSize = event.pageSize;
    this.loadEntries();
  }

  /**
   * Get status chip color
   */
  getStatusColor(status: string): string {
    switch (status) {
      case 'active':
        return 'primary';
      case 'winner':
        return 'accent';
      case 'expired':
        return 'warn';
      default:
        return '';
    }
  }

  /**
   * Get status display text
   */
  getStatusText(status: string): string {
    switch (status) {
      case 'active':
        return 'Active';
      case 'winner':
        return 'Winner';
      case 'expired':
        return 'Expired';
      default:
        return status;
    }
  }

  /**
   * Get status icon
   */
  getStatusIcon(status: string): string {
    switch (status) {
      case 'active':
        return 'check_circle';
      case 'winner':
        return 'emoji_events';
      case 'expired':
        return 'schedule';
      default:
        return 'help';
    }
  }

  /**
   * Format date for display
   */
  formatDate(date: Date): string {
    return new Date(date).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  }

  /**
   * Format entry method for display
   */
  formatEntryMethod(method: string): string {
    return method.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase());
  }

  /**
   * Navigate to giveaway details
   */
  viewGiveaway(giveawayId: string): void {
    // Navigate to giveaway details page
    // This would typically use Router.navigate()
    console.log('Navigate to giveaway:', giveawayId);
  }

  /**
   * Refresh entries
   */
  refresh(): void {
    this.currentPage = 1;
    this.loadEntries();
  }

  /**
   * Get count of active entries
   */
  getActiveEntriesCount(): number {
    return this.entries.filter(entry => entry.status === 'active').length;
  }

  /**
   * Get count of winning entries
   */
  getWinnerEntriesCount(): number {
    return this.entries.filter(entry => entry.status === 'winner').length;
  }

  /**
   * Calculate win rate percentage
   */
  getWinRate(): number {
    if (this.totalEntries === 0) return 0;
    const winCount = this.getWinnerEntriesCount();
    return Math.round((winCount / this.totalEntries) * 100);
  }
}
