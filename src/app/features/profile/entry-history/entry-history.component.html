<div class="entry-history-container">
  <!-- Page Header -->
  <div class="page-header">
    <div class="header-content">
      <h1 class="page-title">Entry History</h1>
      <p class="page-subtitle">View all your giveaway entries and their status</p>
    </div>
    <div class="header-actions">
      <button mat-stroked-button (click)="refresh()" [disabled]="loading">
        <mat-icon>refresh</mat-icon>
        Refresh
      </button>
    </div>
  </div>

  <!-- Entries Table Card -->
  <mat-card class="entries-card">
    <mat-card-header>
      <mat-card-title>
        <mat-icon class="title-icon">history</mat-icon>
        Your Entries
      </mat-card-title>
      <mat-card-subtitle *ngIf="!loading">
        {{ totalEntries }} total entries
      </mat-card-subtitle>
    </mat-card-header>

    <mat-card-content>
      <!-- Loading State -->
      <div *ngIf="loading" class="loading-container">
        <mat-spinner diameter="40"></mat-spinner>
        <p>Loading your entries...</p>
      </div>

      <!-- Entries Table -->
      <div *ngIf="!loading && entries.length > 0" class="table-container">
        <table mat-table [dataSource]="entries" class="entries-table">
          
          <!-- Giveaway Column -->
          <ng-container matColumnDef="giveaway">
            <th mat-header-cell *matHeaderCellDef>Giveaway</th>
            <td mat-cell *matCellDef="let entry">
              <div class="giveaway-cell">
                <div class="giveaway-title">{{ entry.giveawayTitle }}</div>
                <div class="giveaway-id">ID: {{ entry.giveawayId }}</div>
              </div>
            </td>
          </ng-container>

          <!-- Entry Date Column -->
          <ng-container matColumnDef="entryDate">
            <th mat-header-cell *matHeaderCellDef>Entry Date</th>
            <td mat-cell *matCellDef="let entry">
              <div class="date-cell">
                <div class="date-primary">{{ formatDate(entry.entryDate) }}</div>
              </div>
            </td>
          </ng-container>

          <!-- Status Column -->
          <ng-container matColumnDef="status">
            <th mat-header-cell *matHeaderCellDef>Status</th>
            <td mat-cell *matCellDef="let entry">
              <mat-chip-set>
                <mat-chip [color]="getStatusColor(entry.status)" selected>
                  <mat-icon matChipAvatar>{{ getStatusIcon(entry.status) }}</mat-icon>
                  {{ getStatusText(entry.status) }}
                </mat-chip>
              </mat-chip-set>
            </td>
          </ng-container>

          <!-- Entry Method Column -->
          <ng-container matColumnDef="entryMethod">
            <th mat-header-cell *matHeaderCellDef>Entry Method</th>
            <td mat-cell *matCellDef="let entry">
              <div class="method-cell">
                <span class="method-badge">{{ formatEntryMethod(entry.entryMethod) }}</span>
              </div>
            </td>
          </ng-container>

          <!-- Actions Column -->
          <ng-container matColumnDef="actions">
            <th mat-header-cell *matHeaderCellDef>Actions</th>
            <td mat-cell *matCellDef="let entry">
              <div class="actions-cell">
                <button 
                  mat-icon-button 
                  color="primary"
                  (click)="viewGiveaway(entry.giveawayId)"
                  matTooltip="View Giveaway Details">
                  <mat-icon>visibility</mat-icon>
                </button>
              </div>
            </td>
          </ng-container>

          <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
          <tr mat-row *matRowDef="let row; columns: displayedColumns;" class="entry-row"></tr>
        </table>

        <!-- Pagination -->
        <mat-paginator
          [length]="totalEntries"
          [pageSize]="pageSize"
          [pageSizeOptions]="[5, 10, 25, 50]"
          [pageIndex]="currentPage - 1"
          (page)="onPageChange($event)"
          showFirstLastButtons
          class="entries-paginator">
        </mat-paginator>
      </div>

      <!-- Empty State -->
      <div *ngIf="!loading && entries.length === 0" class="empty-state">
        <div class="empty-content">
          <mat-icon class="empty-icon">inbox</mat-icon>
          <h3>No Entries Yet</h3>
          <p>You haven't entered any giveaways yet. Start exploring our available giveaways!</p>
          <button mat-raised-button color="primary" routerLink="/member/giveaways">
            <mat-icon>explore</mat-icon>
            Browse Giveaways
          </button>
        </div>
      </div>
    </mat-card-content>
  </mat-card>

  <!-- Entry Statistics Card -->
  <mat-card class="stats-card" *ngIf="!loading && entries.length > 0">
    <mat-card-header>
      <mat-card-title>
        <mat-icon class="title-icon">analytics</mat-icon>
        Entry Statistics
      </mat-card-title>
    </mat-card-header>

    <mat-card-content>
      <div class="stats-grid">
        <div class="stat-item">
          <div class="stat-value">{{ totalEntries }}</div>
          <div class="stat-label">Total Entries</div>
        </div>

        <div class="stat-item">
          <div class="stat-value">{{ getActiveEntriesCount() }}</div>
          <div class="stat-label">Active Entries</div>
        </div>

        <div class="stat-item">
          <div class="stat-value">{{ getWinnerEntriesCount() }}</div>
          <div class="stat-label">Winning Entries</div>
        </div>

        <div class="stat-item">
          <div class="stat-value">{{ getWinRate() }}%</div>
          <div class="stat-label">Win Rate</div>
        </div>
      </div>
    </mat-card-content>
  </mat-card>
</div>
