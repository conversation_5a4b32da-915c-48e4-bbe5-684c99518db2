import { Routes } from '@angular/router';
import { AuthGuard } from '../../core/guards/auth.guard';

export const PROFILE_ROUTES: Routes = [
  {
    path: '',
    loadComponent: () => import('./profile/profile.component').then(c => c.ProfileComponent),
    canActivate: [AuthGuard],
    title: 'My Profile - Winners Society'
  },
  {
    path: 'settings',
    loadComponent: () => import('./settings/settings.component').then(c => c.SettingsComponent),
    canActivate: [AuthGuard],
    title: 'Account Settings - Winners Society'
  },
  {
    path: 'entries',
    loadComponent: () => import('./entry-history/entry-history.component').then(c => c.EntryHistoryComponent),
    canActivate: [AuthGuard],
    title: 'My Entries - Winners Society'
  },
  {
    path: 'wins',
    loadComponent: () => import('./win-history/win-history.component').then(c => c.WinHistoryComponent),
    canActivate: [AuthGuard],
    title: 'My Wins - Winners Society'
  },
  {
    path: 'transactions',
    loadComponent: () => import('./transaction-history/transaction-history.component').then(c => c.TransactionHistoryComponent),
    canActivate: [AuthGuard],
    title: 'Transaction History - Winners Society'
  }
];
