import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatTableModule } from '@angular/material/table';
import { MatPaginatorModule, PageEvent } from '@angular/material/paginator';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatChipsModule } from '@angular/material/chips';
import { MatTooltipModule } from '@angular/material/tooltip';
import { RouterModule } from '@angular/router';
import { UserService, UserWin } from '../../../core/services/user.service';
import { finalize } from 'rxjs/operators';

@Component({
  selector: 'app-win-history',
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatTableModule,
    MatPaginatorModule,
    MatButtonModule,
    MatIconModule,
    MatProgressSpinnerModule,
    MatChipsModule,
    MatTooltipModule,
    RouterModule
  ],
  templateUrl: './win-history.component.html',
  styleUrls: ['./win-history.component.scss']
})
export class WinHistoryComponent implements OnInit {
  wins: UserWin[] = [];
  loading = false;
  totalWins = 0;
  currentPage = 1;
  pageSize = 10;
  displayedColumns: string[] = ['giveaway', 'prize', 'winDate', 'status', 'actions'];

  constructor(private userService: UserService) {}

  ngOnInit(): void {
    this.loadWins();
  }

  /**
   * Load user wins with pagination
   */
  loadWins(): void {
    this.loading = true;
    this.userService.getUserWins(this.currentPage, this.pageSize)
      .pipe(finalize(() => this.loading = false))
      .subscribe({
        next: (response) => {
          if (response.success) {
            this.wins = response.data.wins;
            this.totalWins = response.data.total;
          }
        },
        error: (error) => {
          console.error('Error loading wins:', error);
          this.wins = [];
        }
      });
  }

  /**
   * Handle page change
   */
  onPageChange(event: PageEvent): void {
    this.currentPage = event.pageIndex + 1;
    this.pageSize = event.pageSize;
    this.loadWins();
  }

  /**
   * Get status chip color
   */
  getStatusColor(status: string): string {
    switch (status) {
      case 'pending':
        return 'warn';
      case 'claimed':
        return 'primary';
      case 'shipped':
        return 'accent';
      case 'delivered':
        return 'primary';
      default:
        return '';
    }
  }

  /**
   * Get status display text
   */
  getStatusText(status: string): string {
    switch (status) {
      case 'pending':
        return 'Pending Claim';
      case 'claimed':
        return 'Claimed';
      case 'shipped':
        return 'Shipped';
      case 'delivered':
        return 'Delivered';
      default:
        return status;
    }
  }

  /**
   * Get status icon
   */
  getStatusIcon(status: string): string {
    switch (status) {
      case 'pending':
        return 'schedule';
      case 'claimed':
        return 'check_circle';
      case 'shipped':
        return 'local_shipping';
      case 'delivered':
        return 'done_all';
      default:
        return 'help';
    }
  }

  /**
   * Format date for display
   */
  formatDate(date: Date): string {
    return new Date(date).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  }

  /**
   * Format prize value for display
   */
  formatPrizeValue(value: number): string {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(value / 100); // Convert cents to dollars
  }

  /**
   * Navigate to giveaway details
   */
  viewGiveaway(giveawayId: string): void {
    // Navigate to giveaway details page
    console.log('Navigate to giveaway:', giveawayId);
  }

  /**
   * Claim prize (if status is pending)
   */
  claimPrize(winId: string): void {
    // Implement claim prize functionality
    console.log('Claim prize:', winId);
  }

  /**
   * Check if prize can be claimed
   */
  canClaimPrize(win: UserWin): boolean {
    if (win.status !== 'pending') return false;
    if (!win.claimDeadline) return true;
    return new Date() < new Date(win.claimDeadline);
  }

  /**
   * Check if claim deadline is approaching (within 7 days)
   */
  isClaimDeadlineApproaching(win: UserWin): boolean {
    if (!win.claimDeadline || win.status !== 'pending') return false;
    const deadline = new Date(win.claimDeadline);
    const now = new Date();
    const daysUntilDeadline = Math.ceil((deadline.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
    return daysUntilDeadline <= 7 && daysUntilDeadline > 0;
  }

  /**
   * Get total prize value
   */
  getTotalPrizeValue(): number {
    return this.wins.reduce((total, win) => total + win.prizeValue, 0);
  }

  /**
   * Get count of pending claims
   */
  getPendingClaimsCount(): number {
    return this.wins.filter(win => win.status === 'pending').length;
  }

  /**
   * Get count of delivered prizes
   */
  getDeliveredCount(): number {
    return this.wins.filter(win => win.status === 'delivered').length;
  }

  /**
   * Refresh wins
   */
  refresh(): void {
    this.currentPage = 1;
    this.loadWins();
  }
}
