<div class="win-history-container">
  <!-- Page Header -->
  <div class="page-header">
    <div class="header-content">
      <h1 class="page-title">Win History</h1>
      <p class="page-subtitle">View all your prize wins and claim status</p>
    </div>
    <div class="header-actions">
      <button mat-stroked-button (click)="refresh()" [disabled]="loading">
        <mat-icon>refresh</mat-icon>
        Refresh
      </button>
    </div>
  </div>

  <!-- Wins Table Card -->
  <mat-card class="wins-card">
    <mat-card-header>
      <mat-card-title>
        <mat-icon class="title-icon">emoji_events</mat-icon>
        Your Wins
      </mat-card-title>
      <mat-card-subtitle *ngIf="!loading">
        {{ totalWins }} total wins
      </mat-card-subtitle>
    </mat-card-header>

    <mat-card-content>
      <!-- Loading State -->
      <div *ngIf="loading" class="loading-container">
        <mat-spinner diameter="40"></mat-spinner>
        <p>Loading your wins...</p>
      </div>

      <!-- Wins Table -->
      <div *ngIf="!loading && wins.length > 0" class="table-container">
        <table mat-table [dataSource]="wins" class="wins-table">
          
          <!-- Giveaway Column -->
          <ng-container matColumnDef="giveaway">
            <th mat-header-cell *matHeaderCellDef>Giveaway</th>
            <td mat-cell *matCellDef="let win">
              <div class="giveaway-cell">
                <div class="giveaway-title">{{ win.giveawayTitle }}</div>
                <div class="giveaway-id">ID: {{ win.giveawayId }}</div>
              </div>
            </td>
          </ng-container>

          <!-- Prize Column -->
          <ng-container matColumnDef="prize">
            <th mat-header-cell *matHeaderCellDef>Prize</th>
            <td mat-cell *matCellDef="let win">
              <div class="prize-cell">
                <div class="prize-title">{{ win.prizeTitle }}</div>
                <div class="prize-value">{{ formatPrizeValue(win.prizeValue) }}</div>
              </div>
            </td>
          </ng-container>

          <!-- Win Date Column -->
          <ng-container matColumnDef="winDate">
            <th mat-header-cell *matHeaderCellDef>Win Date</th>
            <td mat-cell *matCellDef="let win">
              <div class="date-cell">
                <div class="date-primary">{{ formatDate(win.winDate) }}</div>
                <div *ngIf="win.claimDeadline && win.status === 'pending'" class="deadline-info">
                  <span class="deadline-label" [class.urgent]="isClaimDeadlineApproaching(win)">
                    Claim by: {{ formatDate(win.claimDeadline) }}
                  </span>
                </div>
              </div>
            </td>
          </ng-container>

          <!-- Status Column -->
          <ng-container matColumnDef="status">
            <th mat-header-cell *matHeaderCellDef>Status</th>
            <td mat-cell *matCellDef="let win">
              <mat-chip-set>
                <mat-chip [color]="getStatusColor(win.status)" selected>
                  <mat-icon matChipAvatar>{{ getStatusIcon(win.status) }}</mat-icon>
                  {{ getStatusText(win.status) }}
                </mat-chip>
              </mat-chip-set>
            </td>
          </ng-container>

          <!-- Actions Column -->
          <ng-container matColumnDef="actions">
            <th mat-header-cell *matHeaderCellDef>Actions</th>
            <td mat-cell *matCellDef="let win">
              <div class="actions-cell">
                <button 
                  mat-icon-button 
                  color="primary"
                  (click)="viewGiveaway(win.giveawayId)"
                  matTooltip="View Giveaway Details">
                  <mat-icon>visibility</mat-icon>
                </button>
                
                <button 
                  *ngIf="canClaimPrize(win)"
                  mat-mini-fab 
                  color="accent"
                  (click)="claimPrize(win.id)"
                  matTooltip="Claim Prize"
                  class="claim-button">
                  <mat-icon>redeem</mat-icon>
                </button>
              </div>
            </td>
          </ng-container>

          <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
          <tr mat-row *matRowDef="let row; columns: displayedColumns;" class="win-row"></tr>
        </table>

        <!-- Pagination -->
        <mat-paginator
          [length]="totalWins"
          [pageSize]="pageSize"
          [pageSizeOptions]="[5, 10, 25, 50]"
          [pageIndex]="currentPage - 1"
          (page)="onPageChange($event)"
          showFirstLastButtons
          class="wins-paginator">
        </mat-paginator>
      </div>

      <!-- Empty State -->
      <div *ngIf="!loading && wins.length === 0" class="empty-state">
        <div class="empty-content">
          <mat-icon class="empty-icon">emoji_events</mat-icon>
          <h3>No Wins Yet</h3>
          <p>You haven't won any giveaways yet. Keep entering for your chance to win!</p>
          <button mat-raised-button color="primary" routerLink="/member/giveaways">
            <mat-icon>explore</mat-icon>
            Browse Giveaways
          </button>
        </div>
      </div>
    </mat-card-content>
  </mat-card>

  <!-- Win Statistics Card -->
  <mat-card class="stats-card" *ngIf="!loading && wins.length > 0">
    <mat-card-header>
      <mat-card-title>
        <mat-icon class="title-icon">analytics</mat-icon>
        Win Statistics
      </mat-card-title>
    </mat-card-header>

    <mat-card-content>
      <div class="stats-grid">
        <div class="stat-item">
          <div class="stat-value">{{ totalWins }}</div>
          <div class="stat-label">Total Wins</div>
        </div>

        <div class="stat-item">
          <div class="stat-value">{{ formatPrizeValue(getTotalPrizeValue()) }}</div>
          <div class="stat-label">Total Prize Value</div>
        </div>

        <div class="stat-item">
          <div class="stat-value">{{ getPendingClaimsCount() }}</div>
          <div class="stat-label">Pending Claims</div>
        </div>

        <div class="stat-item">
          <div class="stat-value">{{ getDeliveredCount() }}</div>
          <div class="stat-label">Delivered Prizes</div>
        </div>
      </div>
    </mat-card-content>
  </mat-card>
</div>
