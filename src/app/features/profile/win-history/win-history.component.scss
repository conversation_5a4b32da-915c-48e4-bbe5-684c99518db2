.win-history-container {
  // Remove container constraints - use full width of parent
  width: 100%;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 2rem;

  @media (max-width: 768px) {
    flex-direction: column;
    gap: 1rem;
  }

  .header-content {
    .page-title {
      font-size: 2rem;
      font-weight: 600;
      color: #333;
      margin: 0 0 0.5rem 0;
    }

    .page-subtitle {
      color: #666;
      margin: 0;
      font-size: 1rem;
    }
  }

  .header-actions {
    button {
      border-radius: 12px;
      height: 40px;
      font-weight: 500;

      mat-icon {
        margin-right: 0.5rem;
      }
    }
  }
}

.wins-card {
  border-radius: 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border: none;
  margin-bottom: 2rem;

  mat-card-header {
    padding-bottom: 1rem;

    mat-card-title {
      display: flex;
      align-items: center;
      font-size: 1.5rem;
      font-weight: 600;
      color: #333;

      .title-icon {
        margin-right: 0.5rem;
        color: #f57c00;
      }
    }

    mat-card-subtitle {
      color: #666;
      margin-top: 0.25rem;
    }
  }
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  text-align: center;

  p {
    margin-top: 1rem;
    color: #666;
  }
}

.table-container {
  .wins-table {
    width: 100%;
    background: white;

    .mat-mdc-header-cell {
      font-weight: 600;
      color: #333;
      border-bottom: 2px solid #f0f0f0;
    }

    .win-row {
      transition: background-color 0.2s ease;

      &:hover {
        background-color: #f8f9fa;
      }
    }

    .giveaway-cell {
      .giveaway-title {
        font-weight: 600;
        color: #333;
        margin-bottom: 0.25rem;
      }

      .giveaway-id {
        font-size: 0.75rem;
        color: #666;
        font-family: monospace;
      }
    }

    .prize-cell {
      .prize-title {
        font-weight: 600;
        color: #333;
        margin-bottom: 0.25rem;
      }

      .prize-value {
        font-size: 0.875rem;
        color: #f57c00;
        font-weight: 600;
      }
    }

    .date-cell {
      .date-primary {
        font-weight: 500;
        color: #333;
        margin-bottom: 0.25rem;
      }

      .deadline-info {
        .deadline-label {
          font-size: 0.75rem;
          color: #666;
          padding: 0.25rem 0.5rem;
          background-color: #e3f2fd;
          border-radius: 12px;

          &.urgent {
            background-color: #ffebee;
            color: #d32f2f;
            font-weight: 600;
          }
        }
      }
    }

    .actions-cell {
      display: flex;
      gap: 0.5rem;
      align-items: center;

      .claim-button {
        width: 32px;
        height: 32px;

        mat-icon {
          font-size: 18px;
          width: 18px;
          height: 18px;
        }
      }
    }
  }

  .wins-paginator {
    border-top: 1px solid #f0f0f0;
    margin-top: 1rem;
  }
}

.empty-state {
  display: flex;
  justify-content: center;
  padding: 3rem;

  .empty-content {
    text-align: center;
    max-width: 400px;

    .empty-icon {
      font-size: 4rem;
      width: 4rem;
      height: 4rem;
      color: #f57c00;
      margin-bottom: 1rem;
    }

    h3 {
      margin: 0 0 1rem 0;
      color: #333;
      font-weight: 600;
    }

    p {
      color: #666;
      margin-bottom: 2rem;
      line-height: 1.5;
    }

    button {
      border-radius: 12px;
      height: 44px;
      font-weight: 600;

      mat-icon {
        margin-right: 0.5rem;
      }
    }
  }
}

.stats-card {
  border-radius: 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border: none;

  mat-card-header {
    mat-card-title {
      display: flex;
      align-items: center;
      font-size: 1.25rem;
      font-weight: 600;
      color: #333;

      .title-icon {
        margin-right: 0.5rem;
        color: #677eea;
      }
    }
  }

  .stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 2rem;
    margin-top: 1rem;

    @media (max-width: 768px) {
      grid-template-columns: repeat(2, 1fr);
      gap: 1rem;
    }

    .stat-item {
      text-align: center;
      padding: 1rem;
      background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
      border-radius: 12px;

      .stat-value {
        font-size: 2rem;
        font-weight: 700;
        color: #f57c00;
        margin-bottom: 0.5rem;
      }

      .stat-label {
        font-size: 0.875rem;
        color: #666;
        font-weight: 500;
      }
    }
  }
}

// Material Customization
::ng-deep {
  .mat-mdc-table {
    .mat-mdc-cell,
    .mat-mdc-header-cell {
      padding: 1rem 0.75rem;
      border-bottom-color: #f0f0f0;
    }

    .mat-mdc-header-cell {
      font-size: 0.875rem;
      letter-spacing: 0.5px;
    }

    .mat-mdc-cell {
      font-size: 0.875rem;
    }
  }

  .mat-mdc-chip-set {
    .mat-mdc-chip {
      border-radius: 20px;
      font-weight: 600;
      font-size: 0.75rem;

      .mat-mdc-chip-avatar {
        font-size: 14px;
        width: 18px;
        height: 18px;
      }

      &.mat-primary {
        background-color: #e8f5e8;
        color: #2e7d32;
      }

      &.mat-accent {
        background-color: #fff3e0;
        color: #f57c00;
      }

      &.mat-warn {
        background-color: #ffebee;
        color: #d32f2f;
      }
    }
  }

  .mat-mdc-paginator {
    .mat-mdc-paginator-range-label {
      margin: 0 2rem;
    }

    .mat-mdc-icon-button {
      border-radius: 8px;
    }
  }

  .mat-mdc-card {
    .mat-mdc-card-header {
      .mat-mdc-card-title {
        font-size: inherit;
        font-weight: inherit;
        margin: 0;
      }

      .mat-mdc-card-subtitle {
        font-size: inherit;
        margin: 0;
      }
    }
  }
}
