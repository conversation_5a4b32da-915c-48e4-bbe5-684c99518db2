import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { RouterModule } from '@angular/router';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';
import { MatDividerModule } from '@angular/material/divider';
import { UserService, UserProfile, UpdateProfileRequest } from '../../../core/services/user.service';
import { AuthService } from '../../../core/services/auth.service';
import { finalize } from 'rxjs/operators';

@Component({
  selector: 'app-profile',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    RouterModule,
    MatCardModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatIconModule,
    MatProgressSpinnerModule,
    MatSnackBarModule,
    MatDividerModule
  ],
  templateUrl: './profile.component.html',
  styleUrls: ['./profile.component.scss']
})
export class ProfileComponent implements OnInit {
  profileForm: FormGroup;
  addressForm: FormGroup;
  loading = false;
  saving = false;
  userProfile: UserProfile | null = null;
  userAddress: any = null;
  selectedFile: File | null = null;
  imagePreview: string | null = null;

  constructor(
    private fb: FormBuilder,
    private userService: UserService,
    private authService: AuthService,
    private snackBar: MatSnackBar
  ) {
    this.profileForm = this.fb.group({
      firstName: ['', [Validators.required, Validators.minLength(2)]],
      lastName: ['', [Validators.required, Validators.minLength(2)]],
      email: [{ value: '', disabled: true }] // Email is read-only
    });

    this.addressForm = this.fb.group({
      addressLine1: ['', [Validators.required]],
      city: ['', [Validators.required]],
      state: ['', [Validators.required]],
      postalCode: ['', [Validators.required]],
      country: ['', [Validators.required]]
    });
  }

  ngOnInit(): void {
    this.loadProfile();
  }

  /**
   * Load user profile data
   */
  loadProfile(): void {
    this.loading = true;
    this.userService.getFullProfile()
      .pipe(finalize(() => this.loading = false))
      .subscribe({
        next: (response) => {
          if (response.success) {
            this.userProfile = response.data.user;
            this.userAddress = response.data.profile;
            this.populateForm();
          }
        },
        error: (error) => {
          console.error('Error loading profile:', error);
          this.snackBar.open('Failed to load profile', 'Close', { duration: 3000 });
        }
      });
  }

  /**
   * Populate form with user data
   */
  populateForm(): void {
    if (this.userProfile) {
      this.profileForm.patchValue({
        firstName: this.userProfile.firstName,
        lastName: this.userProfile.lastName,
        email: this.userProfile.email
      });

      // Set image preview if profile image exists
      if (this.userProfile.profileImage) {
        this.imagePreview = this.userProfile.profileImage;
      }
    }

    if (this.userAddress) {
      this.addressForm.patchValue({
        addressLine1: this.userAddress.address || '',
        city: this.userAddress.city || '',
        state: this.userAddress.state || '',
        postalCode: this.userAddress.zipCode || '',
        country: this.userAddress.country || ''
      });
    }
  }

  /**
   * Handle file selection for profile image
   */
  onFileSelected(event: Event): void {
    const input = event.target as HTMLInputElement;
    if (input.files && input.files[0]) {
      const file = input.files[0];

      // Validate file type
      if (!file.type.startsWith('image/')) {
        this.snackBar.open('Please select a valid image file', 'Close', { duration: 3000 });
        return;
      }

      // Validate file size (5MB max)
      if (file.size > 5 * 1024 * 1024) {
        this.snackBar.open('Image size must be less than 5MB', 'Close', { duration: 3000 });
        return;
      }

      this.selectedFile = file;

      // Create preview
      const reader = new FileReader();
      reader.onload = (e) => {
        this.imagePreview = e.target?.result as string;
      };
      reader.readAsDataURL(file);
    }
  }

  /**
   * Trigger file input click
   */
  triggerFileInput(): void {
    const fileInput = document.getElementById('profileImageInput') as HTMLInputElement;
    if (fileInput) {
      fileInput.click();
    }
  }

  /**
   * Remove selected image
   */
  removeImage(): void {
    this.selectedFile = null;
    this.imagePreview = this.userProfile?.profileImage || null;

    // Reset file input
    const fileInput = document.getElementById('profileImageInput') as HTMLInputElement;
    if (fileInput) {
      fileInput.value = '';
    }
  }

  /**
   * Save profile changes
   */
  onSubmit(): void {
    if (this.profileForm.valid && this.addressForm.valid) {
      this.saving = true;

      // First upload image if selected
      if (this.selectedFile) {
        this.uploadImage();
      } else {
        this.updateProfile();
      }
    } else {
      this.markFormGroupTouched(this.profileForm);
      this.markFormGroupTouched(this.addressForm);
    }
  }

  /**
   * Upload profile image
   */
  private uploadImage(): void {
    if (!this.selectedFile) {
      this.updateProfile();
      return;
    }

    this.userService.uploadProfileImage(this.selectedFile)
      .subscribe({
        next: (response) => {
          if (response.success) {
            // Update local profile data
            if (this.userProfile) {
              this.userProfile.profileImage = response.data.profileImage;
            }
            // Update auth service with new profile image
            this.authService.updateUserProfile(response.data);
            this.updateProfile();
          }
        },
        error: (error) => {
          console.error('Error uploading image:', error);
          this.snackBar.open('Failed to upload image', 'Close', { duration: 3000 });
          this.saving = false;
        }
      });
  }

  /**
   * Update profile data
   */
  private updateProfile(): void {
    const formValue = this.profileForm.value;
    const updateData: UpdateProfileRequest = {
      firstName: formValue.firstName,
      lastName: formValue.lastName
    };

    this.userService.updateProfile(updateData)
      .pipe(finalize(() => this.saving = false))
      .subscribe({
        next: (response) => {
          if (response.success) {
            this.userProfile = response.data;
            // Update auth service with new profile data
            this.authService.updateUserProfile(response.data);
            this.snackBar.open('Profile updated successfully', 'Close', { duration: 3000 });
            this.selectedFile = null;
          }
        },
        error: (error) => {
          console.error('Error updating profile:', error);
          this.snackBar.open('Failed to update profile', 'Close', { duration: 3000 });
        }
      });
  }

  /**
   * Mark all form fields as touched to show validation errors
   */
  private markFormGroupTouched(formGroup: FormGroup): void {
    Object.keys(formGroup.controls).forEach(key => {
      const control = formGroup.get(key);
      control?.markAsTouched();
    });
  }

  /**
   * Get error message for form field
   */
  getErrorMessage(fieldName: string): string {
    const control = this.profileForm.get(fieldName);
    if (control?.hasError('required')) {
      return `${fieldName.charAt(0).toUpperCase() + fieldName.slice(1)} is required`;
    }
    if (control?.hasError('minlength')) {
      return `${fieldName.charAt(0).toUpperCase() + fieldName.slice(1)} must be at least 2 characters`;
    }
    return '';
  }

  /**
   * Get user initials for avatar fallback
   */
  getUserInitials(): string {
    if (this.userProfile) {
      const firstInitial = this.userProfile.firstName?.charAt(0) || '';
      const lastInitial = this.userProfile.lastName?.charAt(0) || '';
      return (firstInitial + lastInitial).toUpperCase();
    }
    return 'U';
  }

  /**
   * Format date for display
   */
  formatDate(date: Date): string {
    return new Date(date).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  }
}
