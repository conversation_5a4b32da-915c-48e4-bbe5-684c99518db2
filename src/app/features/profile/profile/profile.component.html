<!-- <PERSON> Header -->
<div class="page-header">
  <h1 class="page-title">My Profile</h1>
  <p class="page-subtitle">Manage your personal information and account settings</p>
</div>

<!-- Loading State -->
<div *ngIf="loading" class="loading-container">
  <mat-spinner diameter="40"></mat-spinner>
  <p>Loading profile...</p>
</div>

<!-- Profile Content -->
<div *ngIf="!loading && userProfile" class="profile-content">
    <div class="row">
      <!-- Profile Information Card -->
      <div class="col-lg-8 col-md-12">
        <mat-card class="profile-card">
          <mat-card-header>
            <mat-card-title>Personal Information</mat-card-title>
            <mat-card-subtitle>Update your personal details</mat-card-subtitle>
          </mat-card-header>

          <mat-card-content>
            <form [formGroup]="profileForm" (ngSubmit)="onSubmit()">
              <div class="form-row">
                <div class="form-group">
                  <mat-form-field appearance="outline" class="full-width">
                    <mat-label>First Name</mat-label>
                    <input matInput formControlName="firstName" placeholder="Enter your first name">
                    <mat-error *ngIf="profileForm.get('firstName')?.invalid && profileForm.get('firstName')?.touched">
                      {{ getErrorMessage('firstName') }}
                    </mat-error>
                  </mat-form-field>
                </div>

                <div class="form-group">
                  <mat-form-field appearance="outline" class="full-width">
                    <mat-label>Last Name</mat-label>
                    <input matInput formControlName="lastName" placeholder="Enter your last name">
                    <mat-error *ngIf="profileForm.get('lastName')?.invalid && profileForm.get('lastName')?.touched">
                      {{ getErrorMessage('lastName') }}
                    </mat-error>
                  </mat-form-field>
                </div>
              </div>

              <div class="form-row">
                <div class="form-group full-width">
                  <mat-form-field appearance="outline" class="full-width">
                    <mat-label>Email Address</mat-label>
                    <input matInput formControlName="email" readonly>
                    <mat-hint>Email address cannot be changed</mat-hint>
                  </mat-form-field>
                </div>
              </div>

              <mat-divider class="form-divider"></mat-divider>

              <!-- Address Information Section -->
              <div class="section-header">
                <h3>Address Information</h3>
                <p>Update your address details</p>
              </div>

              <form [formGroup]="addressForm">
                <div class="form-row">
                  <div class="form-group full-width">
                    <mat-form-field appearance="outline" class="full-width">
                      <mat-label>Address Line 1</mat-label>
                      <input matInput formControlName="addressLine1" placeholder="Enter your street address">
                      <mat-error *ngIf="addressForm.get('addressLine1')?.invalid && addressForm.get('addressLine1')?.touched">
                        Address is required
                      </mat-error>
                    </mat-form-field>
                  </div>
                </div>

                <div class="form-row">
                  <div class="form-group">
                    <mat-form-field appearance="outline" class="full-width">
                      <mat-label>City</mat-label>
                      <input matInput formControlName="city" placeholder="Enter your city">
                      <mat-error *ngIf="addressForm.get('city')?.invalid && addressForm.get('city')?.touched">
                        City is required
                      </mat-error>
                    </mat-form-field>
                  </div>

                  <div class="form-group">
                    <mat-form-field appearance="outline" class="full-width">
                      <mat-label>State/Province</mat-label>
                      <input matInput formControlName="state" placeholder="Enter your state or province">
                      <mat-error *ngIf="addressForm.get('state')?.invalid && addressForm.get('state')?.touched">
                        State/Province is required
                      </mat-error>
                    </mat-form-field>
                  </div>
                </div>

                <div class="form-row">
                  <div class="form-group">
                    <mat-form-field appearance="outline" class="full-width">
                      <mat-label>Postal/Zip Code</mat-label>
                      <input matInput formControlName="postalCode" placeholder="Enter your postal or zip code">
                      <mat-error *ngIf="addressForm.get('postalCode')?.invalid && addressForm.get('postalCode')?.touched">
                        Postal/Zip Code is required
                      </mat-error>
                    </mat-form-field>
                  </div>

                  <div class="form-group">
                    <mat-form-field appearance="outline" class="full-width">
                      <mat-label>Country</mat-label>
                      <input matInput formControlName="country" placeholder="Enter your country">
                      <mat-error *ngIf="addressForm.get('country')?.invalid && addressForm.get('country')?.touched">
                        Country is required
                      </mat-error>
                    </mat-form-field>
                  </div>
                </div>
              </form>

              <mat-divider class="form-divider"></mat-divider>

              <div class="form-actions">
                <button
                  mat-raised-button
                  color="primary"
                  type="submit"
                  [disabled]="saving || profileForm.invalid"
                  class="save-button">
                  <mat-spinner *ngIf="saving" diameter="20" class="button-spinner"></mat-spinner>
                  <span *ngIf="!saving">Save Changes</span>
                  <span *ngIf="saving">Saving...</span>
                </button>
              </div>
            </form>
          </mat-card-content>
        </mat-card>
      </div>

      <!-- Profile Image Card -->
      <div class="col-lg-4 col-md-12">
        <mat-card class="profile-image-card">
          <mat-card-header>
            <mat-card-title>Profile Picture</mat-card-title>
            <mat-card-subtitle>Upload a profile picture</mat-card-subtitle>
          </mat-card-header>

          <mat-card-content>
            <div class="image-upload-section">
              <!-- Current/Preview Image -->
              <div class="image-preview-container">
                <div class="image-preview" *ngIf="imagePreview; else noImage">
                  <img [src]="imagePreview" alt="Profile Picture" class="profile-image">
                </div>
                <ng-template #noImage>
                  <div class="no-image-placeholder">
                    <div class="avatar-placeholder">
                      {{ getUserInitials() }}
                    </div>
                  </div>
                </ng-template>
              </div>

              <!-- Upload Controls -->
              <div class="upload-controls">
                <input
                  type="file"
                  id="profileImageInput"
                  accept="image/*"
                  (change)="onFileSelected($event)"
                  style="display: none;">

                <button
                  mat-stroked-button
                  color="primary"
                  (click)="triggerFileInput()"
                  class="upload-button">
                  <mat-icon>cloud_upload</mat-icon>
                  Choose Image
                </button>

                <button
                  *ngIf="selectedFile || userProfile?.profileImage"
                  mat-stroked-button
                  color="warn"
                  (click)="removeImage()"
                  class="remove-button">
                  <mat-icon>delete</mat-icon>
                  Remove
                </button>
              </div>

              <div class="upload-hints">
                <p class="hint-text">
                  <mat-icon class="hint-icon">info</mat-icon>
                  Supported formats: JPG, PNG, GIF
                </p>
                <p class="hint-text">
                  <mat-icon class="hint-icon">info</mat-icon>
                  Maximum size: 5MB
                </p>
              </div>
            </div>
          </mat-card-content>
        </mat-card>

        <!-- Account Information Card -->
        <mat-card class="account-info-card">
          <mat-card-header>
            <mat-card-title>Account Information</mat-card-title>
          </mat-card-header>

          <mat-card-content>
            <div class="info-item">
              <div class="info-label">Member Since</div>
              <div class="info-value">{{ formatDate(userProfile.createdAt) }}</div>
            </div>

            <div class="info-item">
              <div class="info-label">Account Status</div>
              <div class="info-value">
                <span class="status-badge" [class.verified]="userProfile.isVerified" [class.unverified]="!userProfile.isVerified">
                  <mat-icon class="status-icon">{{ userProfile.isVerified ? 'verified' : 'warning' }}</mat-icon>
                  {{ userProfile.isVerified ? 'Verified' : 'Unverified' }}
                </span>
              </div>
            </div>

            <div class="info-item">
              <div class="info-label">Role</div>
              <div class="info-value">
                <span class="role-badge">{{ userProfile.role | titlecase }}</span>
              </div>
            </div>

            <div class="info-item" *ngIf="userProfile.lastLoginAt">
              <div class="info-label">Last Login</div>
              <div class="info-value">{{ formatDate(userProfile.lastLoginAt) }}</div>
            </div>
          </mat-card-content>

          <mat-card-actions>
            <button mat-stroked-button color="primary" routerLink="/member/profile/settings" fragment="security">
              <mat-icon>lock</mat-icon>
              Change Password
            </button>
          </mat-card-actions>
        </mat-card>
      </div>
    </div>
  </div>

  <!-- Error State -->
  <div *ngIf="!loading && !userProfile" class="error-container">
    <mat-card class="error-card">
      <mat-card-content>
        <div class="error-content">
          <mat-icon class="error-icon">error</mat-icon>
          <h3>Unable to Load Profile</h3>
          <p>There was an error loading your profile information. Please try again.</p>
          <button mat-raised-button color="primary" (click)="loadProfile()">
            <mat-icon>refresh</mat-icon>
            Retry
          </button>
        </div>
      </mat-card-content>
    </mat-card>
  </div>
