.page-header {
  margin-bottom: 2rem;

  .page-title {
    font-size: 2rem;
    font-weight: 600;
    color: #333;
    margin: 0 0 0.5rem 0;
  }

  .page-subtitle {
    color: #666;
    margin: 0;
    font-size: 1rem;
  }
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  text-align: center;

  p {
    margin-top: 1rem;
    color: #666;
  }
}

.profile-content {
  .row {
    display: flex;
    gap: 2rem;
    margin: 0;

    @media (max-width: 992px) {
      flex-direction: column;
      gap: 1.5rem;
    }

    .col-lg-8 {
      flex: 1;
      min-width: 0;
    }

    .col-lg-4 {
      flex: 0 0 350px;

      @media (max-width: 992px) {
        flex: 1;
      }
    }
  }
}

// Profile Information Card
.profile-card {
  border-radius: 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border: none;

  mat-card-header {
    padding-bottom: 1rem;

    mat-card-title {
      font-size: 1.5rem;
      font-weight: 600;
      color: #333;
    }

    mat-card-subtitle {
      color: #666;
      margin-top: 0.25rem;
    }
  }

  mat-card-content {
    padding-top: 0;
  }
}

.form-row {
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;

  @media (max-width: 768px) {
    flex-direction: column;
    gap: 0;
  }

  .form-group {
    flex: 1;

    &.full-width {
      flex: 1 1 100%;
    }
  }
}

.full-width {
  width: 100%;
}

.form-divider {
  margin: 2rem 0;
}

.section-header {
  margin: 2rem 0 1.5rem 0;

  h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: #333;
    margin: 0 0 0.5rem 0;
  }

  p {
    color: #666;
    margin: 0;
    font-size: 0.875rem;
  }
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 2rem;

  .save-button {
    min-width: 140px;
    height: 44px;
    border-radius: 12px;
    font-weight: 600;

    .button-spinner {
      margin-right: 0.5rem;
    }
  }
}

// Profile Image Card
.profile-image-card {
  border-radius: 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border: none;
  margin-bottom: 1.5rem;

  mat-card-header {
    padding-bottom: 1rem;

    mat-card-title {
      font-size: 1.25rem;
      font-weight: 600;
      color: #333;
    }

    mat-card-subtitle {
      color: #666;
      margin-top: 0.25rem;
    }
  }
}

.image-upload-section {
  text-align: center;

  .image-preview-container {
    margin-bottom: 1.5rem;

    .image-preview {
      width: 120px;
      height: 120px;
      margin: 0 auto;
      border-radius: 50%;
      overflow: hidden;
      border: 4px solid #f0f0f0;

      .profile-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }

    .no-image-placeholder {
      width: 120px;
      height: 120px;
      margin: 0 auto;

      .avatar-placeholder {
        width: 100%;
        height: 100%;
        border-radius: 50%;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 2rem;
        font-weight: 600;
        color: white;
        border: 4px solid #f0f0f0;
      }
    }
  }

  .upload-controls {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
    margin-bottom: 1.5rem;

    .upload-button,
    .remove-button {
      border-radius: 12px;
      height: 40px;
      font-weight: 500;

      mat-icon {
        margin-right: 0.5rem;
      }
    }
  }

  .upload-hints {
    .hint-text {
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 0.875rem;
      color: #666;
      margin: 0.25rem 0;

      .hint-icon {
        font-size: 16px;
        width: 16px;
        height: 16px;
        margin-right: 0.5rem;
        color: #999;
      }
    }
  }
}

// Account Information Card
.account-info-card {
  border-radius: 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border: none;

  mat-card-header {
    padding-bottom: 1rem;

    mat-card-title {
      font-size: 1.25rem;
      font-weight: 600;
      color: #333;
    }
  }
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 0;
  border-bottom: 1px solid #f0f0f0;

  &:last-child {
    border-bottom: none;
  }

  .info-label {
    font-weight: 500;
    color: #666;
    font-size: 0.875rem;
  }

  .info-value {
    font-weight: 600;
    color: #333;
    text-align: right;

    .status-badge {
      display: inline-flex;
      align-items: center;
      padding: 0.25rem 0.75rem;
      border-radius: 20px;
      font-size: 0.75rem;
      font-weight: 600;

      .status-icon {
        font-size: 14px;
        width: 14px;
        height: 14px;
        margin-right: 0.25rem;
      }

      &.verified {
        background-color: #e8f5e8;
        color: #2e7d32;
      }

      &.unverified {
        background-color: #fff3e0;
        color: #f57c00;
      }
    }

    .role-badge {
      display: inline-block;
      padding: 0.25rem 0.75rem;
      background-color: #f0f0f0;
      color: #333;
      border-radius: 20px;
      font-size: 0.75rem;
      font-weight: 600;
    }
  }
}

// Error State
.error-container {
  display: flex;
  justify-content: center;
  padding: 3rem;

  .error-card {
    max-width: 400px;
    border-radius: 16px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    border: none;

    .error-content {
      text-align: center;
      padding: 2rem;

      .error-icon {
        font-size: 3rem;
        width: 3rem;
        height: 3rem;
        color: #f44336;
        margin-bottom: 1rem;
      }

      h3 {
        margin: 0 0 1rem 0;
        color: #333;
      }

      p {
        color: #666;
        margin-bottom: 2rem;
      }

      button {
        border-radius: 12px;
        height: 44px;
        font-weight: 600;

        mat-icon {
          margin-right: 0.5rem;
        }
      }
    }
  }
}

// Material Form Field Customization
::ng-deep {
  .mat-mdc-form-field {
    .mat-mdc-text-field-wrapper {
      border-radius: 12px;
    }

    .mat-mdc-form-field-subscript-wrapper {
      margin-top: 0.5rem;
    }
  }

  .mat-mdc-card {
    .mat-mdc-card-header {
      .mat-mdc-card-title {
        font-size: inherit;
        font-weight: inherit;
        margin: 0;
      }

      .mat-mdc-card-subtitle {
        font-size: inherit;
        margin: 0;
      }
    }
  }
}
