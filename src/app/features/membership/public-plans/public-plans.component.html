<div class="modern-membership-container">
  <!-- Background Elements -->
  <div class="membership-background">
    <div class="gradient-orb orb-1"></div>
    <div class="gradient-orb orb-2"></div>
    <div class="gradient-orb orb-3"></div>
  </div>

  <!-- Header Section -->
  <section class="header-section">
    <div class="header-content">
      <h1 class="page-title">Choose Your Membership Plan</h1>
      <p class="page-subtitle">Join Winners Society and get access to exclusive giveaways and amazing prizes</p>
    </div>
  </section>

  <!-- Plans Section -->
  <section class="plans-section">
    <!-- Loading State -->
    <div *ngIf="loading" class="loading-container">
      <mat-spinner diameter="40"></mat-spinner>
      <p class="loading-text">Loading membership plans...</p>
    </div>

    <!-- Error State -->
    <div *ngIf="error" class="error-container">
      <mat-icon class="error-icon">error_outline</mat-icon>
      <h2 class="error-title">Oops! Something went wrong</h2>
      <p class="error-message">{{ errorMessage }}</p>
      <button mat-flat-button color="primary" (click)="loadMembershipPlans()">Try Again</button>
    </div>

    <!-- Plans Display -->
    <div *ngIf="!loading && !error" class="plans-container">
      <div class="plans-grid">
        <div class="modern-plan-card" *ngFor="let plan of membershipTiers" [class.popular]="plan.isPopular">
          <div *ngIf="plan.isPopular" class="popular-badge">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
              <path d="M12 2L15.09 8.26L22 9L17 14L18.18 21L12 17.77L5.82 21L7 14L2 9L8.91 8.26L12 2Z" fill="currentColor"/>
            </svg>
            Most Popular
          </div>

          <div class="plan-header">
            <h2 class="plan-name">{{ plan.name }}</h2>
            <div class="plan-price">
              <span class="currency">$</span>
              <span class="amount">{{ formatPrice(plan.price) }}</span>
              <span class="period">/{{ plan.billingPeriod | lowercase }}</span>
            </div>
            <p class="plan-description">{{ plan.description }}</p>
          </div>

          <div class="plan-features">
            <h3 class="features-title">What's included:</h3>
            <ul class="features-list">
              <li *ngFor="let feature of plan.features" class="feature-item">
                <div class="feature-icon">
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                    <path d="M20 6L9 17L4 12" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                  </svg>
                </div>
                <span class="feature-text">{{ feature }}</span>
              </li>
            </ul>
          </div>

          <div class="plan-entries">
            <div class="entries-header">
              <span class="entries-label">Monthly Entries</span>
              <span class="entries-value">{{ plan.entryAllocation }}</span>
            </div>
            <div class="entries-progress">
              <div class="progress-bar" [style.width.%]="getEntryPercentage(plan)"></div>
            </div>
          </div>

          <div class="plan-action">
            <button class="modern-signup-button" (click)="selectPlan(plan)">
              <span>Get Started</span>
              <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
                <path d="M4.16667 10H15.8333M15.8333 10L10.8333 5M15.8333 10L10.8333 15" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </button>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Features Comparison Section -->
  <section class="comparison-section" *ngIf="!loading && !error && membershipTiers.length > 0">
    <div class="comparison-header">
      <h2 class="comparison-title">Compare Membership Features</h2>
      <p class="comparison-subtitle">See which plan is right for you</p>
    </div>

    <div class="modern-comparison-container">
      <div class="comparison-table">
        <div class="table-header">
          <div class="feature-column-header">Feature</div>
          <div *ngFor="let plan of membershipTiers" class="plan-column-header" [class.popular-column]="plan.isPopular">
            {{ plan.name }}
          </div>
        </div>

        <div class="table-body">
          <div class="table-row">
            <div class="feature-name">Monthly Price</div>
            <div *ngFor="let plan of membershipTiers" class="feature-value" [class.popular-column]="plan.isPopular">
              ${{ formatPrice(plan.price) }}
            </div>
          </div>

          <div class="table-row">
            <div class="feature-name">Monthly Entries</div>
            <div *ngFor="let plan of membershipTiers" class="feature-value" [class.popular-column]="plan.isPopular">
              {{ plan.entryAllocation }}
            </div>
          </div>

          <div class="table-row">
            <div class="feature-name">Access to Giveaways</div>
            <div *ngFor="let plan of membershipTiers" class="feature-value" [class.popular-column]="plan.isPopular">
              <div class="check-icon">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                  <path d="M20 6L9 17L4 12" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
              </div>
            </div>
          </div>

          <div class="table-row">
            <div class="feature-name">Exclusive Rewards</div>
            <div *ngFor="let plan of membershipTiers" class="feature-value" [class.popular-column]="plan.isPopular">
              <div *ngIf="plan.name !== 'Basic'" class="check-icon">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                  <path d="M20 6L9 17L4 12" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
              </div>
              <div *ngIf="plan.name === 'Basic'" class="x-icon">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                  <path d="M18 6L6 18M6 6L18 18" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
              </div>
            </div>
          </div>

          <div class="table-row">
            <div class="feature-name">Early Access</div>
            <div *ngFor="let plan of membershipTiers" class="feature-value" [class.popular-column]="plan.isPopular">
              <div *ngIf="plan.name === 'VIP'" class="check-icon">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                  <path d="M20 6L9 17L4 12" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
              </div>
              <div *ngIf="plan.name !== 'VIP'" class="x-icon">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                  <path d="M18 6L6 18M6 6L18 18" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
              </div>
            </div>
          </div>

          <div class="table-row">
            <div class="feature-name">Priority Support</div>
            <div *ngFor="let plan of membershipTiers" class="feature-value" [class.popular-column]="plan.isPopular">
              <div *ngIf="plan.name !== 'Basic'" class="check-icon">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                  <path d="M20 6L9 17L4 12" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
              </div>
              <div *ngIf="plan.name === 'Basic'" class="x-icon">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                  <path d="M18 6L6 18M6 6L18 18" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
              </div>
            </div>
          </div>

          <div class="table-row action-row">
            <div class="feature-name"></div>
            <div *ngFor="let plan of membershipTiers" class="feature-value" [class.popular-column]="plan.isPopular">
              <button class="modern-table-button" (click)="selectPlan(plan)">
                Select Plan
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- FAQ Section -->
  <section class="faq-section">
    <div class="faq-header">
      <h2 class="faq-title">Frequently Asked Questions</h2>
      <p class="faq-subtitle">Find answers to common questions about our membership plans</p>
    </div>

    <div class="modern-faq-container">
      <div class="faq-item">
        <div class="faq-question">
          <h3>How do membership entries work?</h3>
          <div class="faq-icon">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
              <path d="M6 9L12 15L18 9" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
          </div>
        </div>
        <div class="faq-answer">
          <p>Each membership plan comes with a specific number of entries per month. You can use these entries to participate in any active giveaways. Once you use an entry, it's deducted from your total. Entries reset at the beginning of each billing cycle.</p>
        </div>
      </div>

      <div class="faq-item">
        <div class="faq-question">
          <h3>Can I upgrade my membership plan?</h3>
          <div class="faq-icon">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
              <path d="M6 9L12 15L18 9" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
          </div>
        </div>
        <div class="faq-answer">
          <p>Yes, you can upgrade your membership plan at any time. When you upgrade, you'll be charged the prorated difference for the remainder of your current billing cycle, and you'll immediately receive the benefits of your new plan.</p>
        </div>
      </div>

      <div class="faq-item">
        <div class="faq-question">
          <h3>How do I cancel my membership?</h3>
          <div class="faq-icon">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
              <path d="M6 9L12 15L18 9" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
          </div>
        </div>
        <div class="faq-answer">
          <p>You can cancel your membership at any time from your account settings. When you cancel, you'll continue to have access to your membership benefits until the end of your current billing cycle. No refunds are provided for partial months.</p>
        </div>
      </div>

      <div class="faq-item">
        <div class="faq-question">
          <h3>Are there any hidden fees?</h3>
          <div class="faq-icon">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
              <path d="M6 9L12 15L18 9" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
          </div>
        </div>
        <div class="faq-answer">
          <p>No, there are no hidden fees. The price you see is the price you pay. All taxes and processing fees are included in the displayed price.</p>
        </div>
      </div>
    </div>
  </section>

  <!-- CTA Section -->
  <section class="cta-section">
    <div class="modern-cta-container">
      <div class="cta-content">
        <h2 class="cta-title">Ready to Start Winning?</h2>
        <p class="cta-subtitle">Join Winners Society today and get access to exclusive giveaways and amazing prizes.</p>
        <button class="modern-cta-button" routerLink="/auth/signup">
          <span>Create Your Account</span>
          <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
            <path d="M4.16667 10H15.8333M15.8333 10L10.8333 5M15.8333 10L10.8333 15" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
        </button>
      </div>
    </div>
  </section>
</div>
