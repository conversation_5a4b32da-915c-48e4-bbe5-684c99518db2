// Modern Membership Container
.modern-membership-container {
  width: 100vw;
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: relative;
  overflow-x: hidden;
}

// Background Elements
.membership-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  overflow: hidden;
  z-index: 1;
}

.gradient-orb {
  position: absolute;
  border-radius: 50%;
  filter: blur(60px);
  opacity: 0.3;
  animation: float 8s ease-in-out infinite;

  &.orb-1 {
    width: 400px;
    height: 400px;
    background: radial-gradient(circle, #ff6b6b, #ee5a24);
    top: 10%;
    left: 10%;
    animation-delay: 0s;
  }

  &.orb-2 {
    width: 300px;
    height: 300px;
    background: radial-gradient(circle, #4ecdc4, #44a08d);
    top: 50%;
    right: 15%;
    animation-delay: 2s;
  }

  &.orb-3 {
    width: 350px;
    height: 350px;
    background: radial-gradient(circle, #a8edea, #fed6e3);
    bottom: 20%;
    left: 20%;
    animation-delay: 4s;
  }
}

// Header Section
.header-section {
  position: relative;
  z-index: 2;
  color: white;
  padding: 6rem 2rem 4rem;
  text-align: center;

  @media (max-width: 768px) {
    padding: 4rem 1rem 3rem;
  }
}

.header-content {
  max-width: 800px;
  margin: 0 auto;
}

.page-title {
  font-size: 3rem;
  font-weight: 800;
  margin-bottom: 1.5rem;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);

  @media (max-width: 768px) {
    font-size: 2.25rem;
  }
}

.page-subtitle {
  font-size: 1.25rem;
  opacity: 0.95;
  line-height: 1.6;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);

  @media (max-width: 768px) {
    font-size: 1.1rem;
  }
}

// Plans Section
.plans-section {
  position: relative;
  z-index: 2;
  padding: 4rem 2rem;

  @media (max-width: 768px) {
    padding: 3rem 1rem;
  }
}

// Loading State
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 0;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 24px;
  margin: 0 auto;
  max-width: 500px;
}

.loading-text {
  margin-top: 1rem;
  color: #6b7280;
  font-weight: 500;
}

// Error State
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  text-align: center;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 24px;
  margin: 0 auto;
  max-width: 600px;
}

.error-icon {
  font-size: 3rem;
  height: 3rem;
  width: 3rem;
  color: #ef4444;
  margin-bottom: 1.5rem;
}

.error-title {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
  color: #1f2937;
}

.error-message {
  color: #6b7280;
  margin-bottom: 2rem;
  max-width: 400px;
  line-height: 1.6;
}

// Plans Container
.plans-container {
  max-width: 1400px;
  margin: 0 auto;
}

.plans-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;

  @media (max-width: 992px) {
    grid-template-columns: 1fr;
    max-width: 500px;
    margin: 0 auto;
  }

  @media (max-width: 576px) {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
}

// Modern Plan Card
.modern-plan-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 24px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transition: all 0.3s ease;
  position: relative;
  display: flex;
  flex-direction: column;
  animation: slideUp 0.6s ease-out;

  &:hover {
    transform: translateY(-8px);
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
  }

  &.popular {
    border: 2px solid #667eea;
    transform: scale(1.02);
    z-index: 3;

    &:hover {
      transform: translateY(-8px) scale(1.02);
    }
  }
}

// Popular Badge
.popular-badge {
  position: absolute;
  top: -1px;
  right: -1px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 0.75rem 1.25rem;
  font-size: 0.875rem;
  font-weight: 600;
  border-radius: 0 24px 0 24px;
  z-index: 4;
  display: flex;
  align-items: center;
  gap: 0.5rem;

  svg {
    width: 16px;
    height: 16px;
    fill: currentColor;
  }
}

// Plan Header
.plan-header {
  padding: 2.5rem 2rem 2rem;
  text-align: center;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.plan-name {
  font-size: 1.75rem;
  font-weight: 800;
  margin-bottom: 1.5rem;
  color: #1f2937;
}

.plan-price {
  margin-bottom: 1.5rem;
}

.currency {
  font-size: 1.5rem;
  vertical-align: top;
  position: relative;
  top: 0.5rem;
  color: #667eea;
}

.amount {
  font-size: 3.5rem;
  font-weight: 800;
  color: #667eea;
}

.period {
  font-size: 1.1rem;
  color: #6b7280;
  font-weight: 500;
}

.plan-description {
  color: #6b7280;
  margin-bottom: 0;
  line-height: 1.6;
  font-size: 1rem;
}

// Plan Features
.plan-features {
  padding: 2rem;
  flex-grow: 1;
}

.features-title {
  font-size: 1.2rem;
  font-weight: 700;
  margin-bottom: 1.5rem;
  color: #1f2937;
}

.features-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.feature-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 1rem;
  padding: 0.5rem 0;
}

.feature-icon {
  color: #10b981;
  margin-right: 0.75rem;
  flex-shrink: 0;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;

  svg {
    width: 20px;
    height: 20px;
    stroke: currentColor;
  }
}

.feature-text {
  color: #374151;
  line-height: 1.6;
  font-weight: 500;
}

// Plan Entries
.plan-entries {
  padding: 0 2rem 1.5rem;
}

.entries-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 0.75rem;
}

.entries-label {
  font-size: 0.95rem;
  color: #6b7280;
  font-weight: 500;
}

.entries-value {
  font-size: 0.95rem;
  font-weight: 700;
  color: #1f2937;
}

.entries-progress {
  height: 8px;
  background-color: #e5e7eb;
  border-radius: 4px;
  overflow: hidden;

  .progress-bar {
    height: 100%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 4px;
    transition: width 0.3s ease;
  }
}

// Plan Action
.plan-action {
  padding: 0 2rem 2.5rem;
  text-align: center;
}

.modern-signup-button {
  width: 100%;
  padding: 1rem 2rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 12px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
  }

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(102, 126, 234, 0.4);

    &::before {
      left: 100%;
    }

    svg {
      transform: translateX(4px);
    }
  }

  svg {
    width: 20px;
    height: 20px;
    stroke: currentColor;
    transition: transform 0.3s ease;
  }
}

// Comparison Section
.comparison-section {
  position: relative;
  z-index: 2;
  padding: 4rem 2rem;

  @media (max-width: 768px) {
    padding: 3rem 1rem;
  }
}

.comparison-header {
  text-align: center;
  margin-bottom: 3rem;

  @media (max-width: 768px) {
    margin-bottom: 2rem;
  }
}

.comparison-title {
  font-size: 2.5rem;
  font-weight: 800;
  margin-bottom: 1rem;
  color: white;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);

  @media (max-width: 768px) {
    font-size: 2rem;
  }
}

.comparison-subtitle {
  font-size: 1.2rem;
  color: rgba(255, 255, 255, 0.9);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);

  @media (max-width: 768px) {
    font-size: 1rem;
  }
}

.comparison-table-container {
  max-width: 1000px;
  margin: 0 auto;
  overflow-x: auto;
}

.comparison-table {
  width: 100%;
  border-collapse: collapse;

  th, td {
    padding: 1rem;
    text-align: center;
    border-bottom: 1px solid #f1f1f1;

    @media (max-width: 768px) {
      padding: 0.75rem 0.5rem;
    }
  }

  th {
    font-weight: 600;
    color: #212529;
  }

  .feature-column {
    text-align: left;
    width: 25%;
  }

  .plan-column {
    width: 25%;

    &.popular-column {
      background-color: rgba(var(--bs-primary-rgb), 0.05);
      position: relative;

      &::after {
        content: 'Popular';
        position: absolute;
        top: -10px;
        left: 50%;
        transform: translateX(-50%);
        background-color: var(--bs-primary);
        color: white;
        padding: 0.25rem 0.75rem;
        border-radius: 20px;
        font-size: 0.75rem;
        font-weight: 600;
      }
    }
  }

  .feature-name {
    text-align: left;
    font-weight: 500;
    color: #495057;
  }

  .check-icon {
    color: #28a745;
  }

  .x-icon {
    color: #dc3545;
  }

  .action-row {
    td {
      padding-top: 1.5rem;
    }
  }

  .table-button {
    min-width: 120px;
    font-weight: 500;
  }
}

// FAQ Section
.faq-section {
  position: relative;
  z-index: 2;
  padding: 4rem 2rem;

  @media (max-width: 768px) {
    padding: 3rem 1rem;
  }
}

.faq-header {
  text-align: center;
  margin-bottom: 3rem;

  @media (max-width: 768px) {
    margin-bottom: 2rem;
  }
}

.faq-title {
  font-size: 2.5rem;
  font-weight: 800;
  margin-bottom: 1rem;
  color: white;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);

  @media (max-width: 768px) {
    font-size: 2rem;
  }
}

.faq-subtitle {
  font-size: 1.2rem;
  color: rgba(255, 255, 255, 0.9);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);

  @media (max-width: 768px) {
    font-size: 1rem;
  }
}

// CTA Section
.cta-section {
  position: relative;
  z-index: 2;
  padding: 4rem 2rem;

  @media (max-width: 768px) {
    padding: 3rem 1rem;
  }
}

// Modern Comparison Container
.modern-comparison-container {
  max-width: 1200px;
  margin: 0 auto;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 24px;
  padding: 2rem;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  overflow-x: auto;

  @media (max-width: 768px) {
    padding: 1.5rem;
    border-radius: 20px;
  }

  @media (max-width: 576px) {
    padding: 1rem;
    border-radius: 16px;
    overflow-x: visible;
  }
}

.comparison-table {
  width: 100%;
  min-width: 600px;

  @media (max-width: 576px) {
    min-width: auto;
  }
}

.table-header {
  display: grid;
  grid-template-columns: 2fr repeat(3, 1fr);
  gap: 1rem;
  margin-bottom: 1rem;
  padding-bottom: 1rem;
  border-bottom: 2px solid #e5e7eb;

  @media (max-width: 768px) {
    grid-template-columns: 1.5fr repeat(3, 1fr);
    gap: 0.5rem;
  }

  @media (max-width: 576px) {
    grid-template-columns: 1fr;
    gap: 0.5rem;
    text-align: center;
  }
}

.feature-column-header {
  font-size: 1.1rem;
  font-weight: 700;
  color: #1f2937;
  padding: 1rem;

  @media (max-width: 768px) {
    font-size: 1rem;
    padding: 0.75rem;
  }

  @media (max-width: 576px) {
    font-size: 1.2rem;
    padding: 1rem 0;
    text-align: center;
    border-bottom: 2px solid #667eea;
    margin-bottom: 1rem;
  }
}

.plan-column-header {
  font-size: 1.1rem;
  font-weight: 700;
  color: #1f2937;
  text-align: center;
  padding: 1rem;
  border-radius: 12px;

  &.popular-column {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
  }

  @media (max-width: 768px) {
    font-size: 1rem;
    padding: 0.75rem;
  }

  @media (max-width: 576px) {
    display: none;
  }
}

.table-body {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.table-row {
  display: grid;
  grid-template-columns: 2fr repeat(3, 1fr);
  gap: 1rem;
  padding: 1rem;
  border-radius: 12px;
  transition: background-color 0.3s ease;
  align-items: center;

  &:hover {
    background-color: rgba(102, 126, 234, 0.05);
  }

  &.action-row {
    margin-top: 1rem;
    padding-top: 2rem;
    border-top: 1px solid #e5e7eb;
  }

  @media (max-width: 768px) {
    grid-template-columns: 1.5fr repeat(3, 1fr);
    gap: 0.5rem;
    padding: 0.75rem;
  }

  @media (max-width: 576px) {
    grid-template-columns: 1fr;
    gap: 0.5rem;
    text-align: center;
    padding: 1rem 0.5rem;
    border: 1px solid #e5e7eb;
    margin-bottom: 1rem;

    .feature-name {
      font-weight: 700;
      margin-bottom: 1rem;
      padding-bottom: 0.5rem;
      border-bottom: 2px solid #667eea;
      color: #667eea;
      font-size: 1.1rem;
    }

    .feature-value {
      margin-bottom: 0.75rem;
      padding: 0.5rem;
      background: rgba(102, 126, 234, 0.05);
      border-radius: 8px;
      position: relative;

      &:last-child {
        margin-bottom: 0;
      }

      &.popular-column {
        background: rgba(102, 126, 234, 0.15);
        border: 2px solid #667eea;
        font-weight: 600;
      }

      // Add plan name before each value on mobile
      &:nth-child(2)::before {
        content: 'Basic: ';
        font-weight: 600;
        color: #667eea;
      }

      &:nth-child(3)::before {
        content: 'Premium: ';
        font-weight: 600;
        color: #667eea;
      }

      &:nth-child(4)::before {
        content: 'VIP: ';
        font-weight: 600;
        color: #667eea;
      }
    }
  }
}

.feature-name {
  font-weight: 600;
  color: #374151;
  display: flex;
  align-items: center;
}

.feature-value {
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 500;
  color: #1f2937;

  &.popular-column {
    background: rgba(102, 126, 234, 0.1);
    border-radius: 8px;
    padding: 0.5rem;
  }
}

.check-icon {
  color: #10b981;
  display: flex;
  align-items: center;
  justify-content: center;

  svg {
    width: 20px;
    height: 20px;
    stroke: currentColor;
  }
}

.x-icon {
  color: #ef4444;
  display: flex;
  align-items: center;
  justify-content: center;

  svg {
    width: 20px;
    height: 20px;
    stroke: currentColor;
  }
}

.modern-table-button {
  padding: 0.75rem 1.5rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
  }
}

// Modern FAQ Container
.modern-faq-container {
  max-width: 800px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.faq-item {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
  }
}

.faq-question {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1.5rem 2rem;
  cursor: pointer;
  transition: background-color 0.3s ease;

  &:hover {
    background-color: rgba(102, 126, 234, 0.05);
  }

  h3 {
    font-size: 1.1rem;
    font-weight: 600;
    color: #1f2937;
    margin: 0;
  }
}

.faq-icon {
  color: #667eea;
  transition: transform 0.3s ease;

  svg {
    width: 24px;
    height: 24px;
    stroke: currentColor;
  }
}

.faq-answer {
  padding: 0 2rem 1.5rem;

  p {
    color: #6b7280;
    line-height: 1.6;
    margin: 0;
  }
}

// Modern CTA Container
.modern-cta-container {
  position: relative;
  z-index: 2;
  max-width: 800px;
  margin: 0 auto;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 24px;
  padding: 4rem 2rem;
  text-align: center;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.cta-title {
  font-size: 2.5rem;
  font-weight: 800;
  margin-bottom: 1rem;
  color: #1f2937;

  @media (max-width: 768px) {
    font-size: 2rem;
  }
}

.cta-subtitle {
  font-size: 1.2rem;
  margin-bottom: 2rem;
  color: #6b7280;
  line-height: 1.6;

  @media (max-width: 768px) {
    font-size: 1rem;
  }
}

.modern-cta-button {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem 2rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 12px;
  font-size: 1.1rem;
  font-weight: 600;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
  }

  &:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 32px rgba(102, 126, 234, 0.4);

    &::before {
      left: 100%;
    }

    svg {
      transform: translateX(4px);
    }
  }

  svg {
    width: 20px;
    height: 20px;
    stroke: currentColor;
    transition: transform 0.3s ease;
  }
}

// Animations
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// Reduced Motion
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}
