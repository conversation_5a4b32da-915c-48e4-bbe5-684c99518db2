import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router, RouterLink } from '@angular/router';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatExpansionModule } from '@angular/material/expansion';

import { MembershipService } from '../../../core/services/membership.service';
import { NotificationService } from '../../../core/services/notification.service';
import { MembershipTier } from '../../../core/models/membership.model';

@Component({
  selector: 'app-public-plans',
  standalone: true,
  imports: [
    CommonModule,
    RouterLink,
    MatButtonModule,
    MatIconModule,
    MatProgressBarModule,
    MatProgressSpinnerModule,
    MatExpansionModule
  ],
  templateUrl: './public-plans.component.html',
  styleUrls: ['./public-plans.component.scss']
})
export class PublicPlansComponent implements OnInit {
  // Loading state
  loading = true;
  error = false;
  errorMessage = 'Failed to load membership plans. Please try again.';

  // Membership tiers
  membershipTiers: MembershipTier[] = [];

  constructor(
    private membershipService: MembershipService,
    private notificationService: NotificationService,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.loadMembershipPlans();
  }

  /**
   * Load available membership tiers from the API
   */
  loadMembershipPlans(): void {
    this.loading = true;
    this.error = false;

    this.membershipService.getPublicMembershipTiers().subscribe({
      next: (tiers) => {
        this.membershipTiers = tiers;
        this.loading = false;

        // Log the loaded tiers for debugging
        console.log('Loaded membership tiers:', tiers);
      },
      error: (err) => {
        console.error('Error loading membership plans:', err);
        this.loading = false;
        this.error = true;
        this.errorMessage = 'Failed to load membership plans. Please try again.';
      }
    });
  }

  /**
   * Calculate the percentage of entries for the progress bar
   * @param plan The membership tier
   * @returns The percentage value for the progress bar
   */
  getEntryPercentage(plan: MembershipTier): number {
    const maxEntries = this.getMaxEntries();
    return (plan.entryAllocation / maxEntries) * 100;
  }

  /**
   * Get the maximum number of entries across all plans
   * @returns The maximum number of entries
   */
  getMaxEntries(): number {
    if (this.membershipTiers.length === 0) return 100;
    return Math.max(...this.membershipTiers.map(plan => plan.entryAllocation));
  }

  /**
   * Format the price to display correctly
   * @param price The price in cents
   * @returns The formatted price
   */
  formatPrice(price: number): string {
    // API returns price in cents (29 instead of $0.29)
    // Convert to dollars for display
    return (price / 100).toFixed(2);
  }

  /**
   * Handle plan selection
   * @param plan The selected membership tier
   */
  selectPlan(plan: MembershipTier): void {
    // Check if user is logged in
    // If not, redirect to signup page with plan ID as query param
    this.router.navigate(['/auth/signup'], {
      queryParams: { plan: plan.id }
    });

    // Show notification
    this.notificationService.info(`You've selected the ${plan.name} plan. Create an account to continue.`);
  }
}
