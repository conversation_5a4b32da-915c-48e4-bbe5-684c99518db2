import { Routes } from '@angular/router';
import { AuthGuard } from '../../core/guards/auth.guard';

export const MEMBER_DASHBOARD_ROUTES: Routes = [
  {
    path: '',
    loadComponent: () => import('./membership-overview/membership-overview.component').then(c => c.MembershipOverviewComponent),
    canActivate: [AuthGuard],
    title: 'My Membership - Winners Society'
  },
  {
    path: 'plans',
    loadComponent: () => import('./membership-plans/membership-plans.component').then(c => c.MembershipPlansComponent),
    canActivate: [AuthGuard],
    title: 'Membership Plans - Winners Society'
  },
  {
    path: 'upgrade',
    loadComponent: () => import('./membership-upgrade/membership-upgrade.component').then(c => c.MembershipUpgradeComponent),
    canActivate: [AuthGuard],
    title: 'Upgrade Membership - Winners Society'
  },
  {
    path: 'payment-success',
    loadComponent: () => import('./payment-success/payment-success.component').then(c => c.PaymentSuccessComponent),
    canActivate: [AuthGuard],
    title: 'Payment Successful - Winners Society'
  }
];
