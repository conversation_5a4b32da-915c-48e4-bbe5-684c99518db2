import { Component, OnInit, OnD<PERSON>roy, ViewChild, ElementRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { MembershipService } from '../../../core/services/membership.service';
import { NotificationService } from '../../../core/services/notification.service';
import { PaymentService } from '../../../core/services/payment.service';
import { StripeService } from '../../../core/services/stripe.service';
import { MembershipTier } from '../../../core/models/membership.model';
import { PaymentIntent, ProcessPaymentRequest, CreatePaymentIntentRequest } from '../../../core/models/payment.model';
import { FormBuilder, FormGroup, ReactiveFormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { MatSnackBarModule } from '@angular/material/snack-bar';
import { PaymentFormComponent } from '../../../shared/components/payment-form/payment-form.component';
import { MembershipContainerComponent } from '../../../shared/components/membership-container/membership-container.component';
import { Subscription } from 'rxjs';

@Component({
  selector: 'app-membership-plans',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    ReactiveFormsModule,
    MatSnackBarModule,
    PaymentFormComponent,
    MembershipContainerComponent
  ],
  templateUrl: './membership-plans.component.html',
  styleUrls: ['./membership-plans.component.scss']
})
export class MembershipPlansComponent implements OnInit, OnDestroy {
  loading = true;
  availablePlans: MembershipTier[] = [];
  selectedPlan: MembershipTier | null = null;
  paymentForm: FormGroup;
  submitting = false;

  // Step tracking
  currentStep: 'select' | 'payment' | 'confirmation' = 'select';

  // Payment processing
  paymentIntent: PaymentIntent | null = null;
  paymentError: string | null = null;
  paymentMethodId: string | null = null;
  savedPaymentMethods: any[] = [];

  // Animation
  @ViewChild('paymentCard') paymentCardRef?: ElementRef<HTMLDivElement>;
  reducedMotion = false;

  // Subscriptions
  private subscriptions = new Subscription();

  constructor(
    private membershipService: MembershipService,
    private paymentService: PaymentService,
    private stripeService: StripeService,
    private notificationService: NotificationService,
    private fb: FormBuilder,
    private router: Router
  ) {
    this.paymentForm = this.fb.group({
      autoRenew: [true],
      couponCode: ['']
    });
  }

  ngOnInit(): void {
    this.loadMembershipPlans();
    this.loadSavedPaymentMethods();
    this.checkReducedMotionPreference();

    // Initialize Stripe
    this.stripeService.loadStripe().subscribe({
      next: () => {
        console.log('Stripe initialized successfully');
      },
      error: (error) => {
        console.error('Failed to initialize Stripe', error);
        this.notificationService.error('Failed to initialize payment processor. Please try again later.');
      }
    });
  }

  /**
   * Check if the user prefers reduced motion
   */
  private checkReducedMotionPreference(): void {
    // Check if the user prefers reduced motion
    const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;
    if (prefersReducedMotion) {
      this.reducedMotion = true;
    }
  }

  ngOnDestroy(): void {
    // Clean up subscriptions
    this.subscriptions.unsubscribe();
  }

  loadMembershipPlans(): void {
    this.loading = true;
    this.membershipService.getAvailableTiers()
      .subscribe({
        next: (plans) => {
          this.availablePlans = plans;
          this.loading = false;
        },
        error: (err) => {
          console.error('Error loading membership plans', err);
          this.notificationService.error('Failed to load membership plans');
          this.loading = false;
        }
      });
  }

  loadSavedPaymentMethods(): void {
    this.paymentService.getPaymentMethods().subscribe({
      next: (methods) => {
        this.savedPaymentMethods = methods;
      },
      error: (error) => {
        console.error('Error loading payment methods', error);
      }
    });
  }

  getMaxEntries(): number {
    if (this.availablePlans.length === 0) return 100;
    return Math.max(...this.availablePlans.map(plan => plan.entryAllocation));
  }

  selectPlan(plan: MembershipTier): void {
    this.selectedPlan = plan;
    this.paymentIntent = null;
    this.paymentError = null;

    // Create payment intent for the selected plan
    this.createPaymentIntent(plan.id);

    // Move to payment step
    this.currentStep = 'payment';

    // Scroll to top after DOM update
    setTimeout(() => {
      window.scrollTo({ top: 0, behavior: 'smooth' });
    }, 100);
  }

  backToPlans(): void {
    this.currentStep = 'select';
    // Keep the selected plan in memory but reset the payment process
    this.paymentIntent = null;
    this.paymentError = null;
    this.paymentMethodId = null;
  }

  cancelSelection(): void {
    this.selectedPlan = null;
    this.paymentIntent = null;
    this.paymentError = null;
    this.paymentMethodId = null;
    this.currentStep = 'select';
    this.paymentForm.reset({
      autoRenew: true,
      couponCode: ''
    });
  }

  /**
   * Create a payment intent for the selected plan
   */
  createPaymentIntent(planId: string): void {
    // Get coupon code for potential future use
    // const couponCode = this.paymentForm.get('couponCode')?.value;

    const request: CreatePaymentIntentRequest = {
      planId: planId,
      description: 'Membership subscription'
    };

    this.paymentService.createPaymentIntent(request).subscribe({
      next: (paymentIntent) => {
        // The backend returns the amount in dollars, but Stripe Elements expects cents
        // We need to convert it for display purposes in the payment form
        if (paymentIntent.amount < 100) {
          // If amount is less than 100, it's likely in dollars and needs to be converted to cents for Stripe
          console.log('Converting payment amount from dollars to cents for Stripe display');
          paymentIntent.amount = Math.round(paymentIntent.amount * 100);
        }
        this.paymentIntent = paymentIntent;
      },
      error: (error) => {
        console.error('Error creating payment intent', error);

        // Handle specific error messages from the API
        let errorMessage = 'Failed to initialize payment. Please try again.';

        if (error.error && error.error.message) {
          // Use the specific error message from the API
          errorMessage = error.error.message;

          // Handle specific error cases
          if (errorMessage.includes('Plan not found')) {
            errorMessage = 'The selected membership plan is no longer available. Please choose another plan.';
          }
        } else if (error.message) {
          errorMessage = error.message;
        }

        this.paymentError = errorMessage;
      }
    });
  }

  /**
   * Handle payment method creation from the payment form
   */
  onPaymentMethodCreated(paymentMethodId: string): void {
    this.paymentMethodId = paymentMethodId;
    this.processPayment();
  }

  /**
   * Handle payment method selection from saved methods
   */
  onPaymentMethodSelected(paymentMethodId: string): void {
    this.paymentMethodId = paymentMethodId;
    this.processPayment();
  }

  /**
   * Handle payment form errors
   */
  onPaymentFormError(error: string): void {
    this.paymentError = error;
    this.notificationService.error(error);
  }

  /**
   * Process the payment with the selected payment method
   */
  processPayment(): void {
    if (!this.selectedPlan || !this.paymentIntent || !this.paymentMethodId || this.submitting) {
      return;
    }

    this.submitting = true;
    this.paymentError = null;

    // Include paymentIntentId and paymentMethodId in the request
    // The backend will retrieve the amount based on the payment intent
    const request: ProcessPaymentRequest = {
      paymentIntentId: this.paymentIntent.id,
      paymentMethodId: this.paymentMethodId
    };

    this.paymentService.processPayment(request).subscribe({
      next: (transaction) => {
        this.notificationService.success('Payment successful!');

        // Set the step to confirmation before navigating
        this.currentStep = 'confirmation';

        // Navigate to success page with transaction ID
        this.router.navigate(['/membership/payment-success'], {
          queryParams: { transaction_id: transaction.id }
        });
      },
      error: (error) => {
        console.error('Payment processing failed', error);

        // Handle specific error messages from the API
        let errorMessage = 'Payment processing failed. Please try again.';

        if (error.error && error.error.message) {
          // Use the specific error message from the API
          errorMessage = error.error.message;

          // Handle specific error cases
          if (errorMessage.includes('Payment intent ID is required')) {
            errorMessage = 'Payment initialization failed. Please try again.';
          } else if (errorMessage.includes('Payment method ID is required')) {
            errorMessage = 'Payment method information is missing. Please try again.';
          } else if (errorMessage.includes('Payment has already been processed')) {
            errorMessage = 'This payment has already been processed. Please check your membership status.';
            // Redirect to membership page after a delay
            setTimeout(() => {
              this.router.navigate(['/membership']);
            }, 3000);
          } else if (errorMessage.includes('Payment intent not found or invalid')) {
            errorMessage = 'Payment session has expired. Please try again.';
            // Reset the payment intent
            this.paymentIntent = null;
            // Recreate the payment intent after a delay
            setTimeout(() => {
              if (this.selectedPlan) {
                this.createPaymentIntent(this.selectedPlan.id);
              }
            }, 1000);
          }
        } else if (error.message) {
          errorMessage = error.message;
        }

        this.paymentError = errorMessage;
        this.notificationService.error(errorMessage);
        this.submitting = false;
      }
    });
  }

  /**
   * Apply coupon code and recreate payment intent
   * Note: The current backend API doesn't support coupon codes directly.
   * This is a placeholder for future implementation.
   */
  applyCoupon(): void {
    if (!this.selectedPlan) return;

    // For now, just show a notification that coupons are not supported
    const couponCode = this.paymentForm.get('couponCode')?.value;
    if (couponCode) {
      this.notificationService.info(`Coupon codes are not currently supported. The standard price will be applied.`);
    }

    // Recreate payment intent (without coupon for now)
    this.createPaymentIntent(this.selectedPlan.id);
  }

  /**
   * Format currency for display
   * @param amount Amount in dollars
   * @param currency Currency code
   * @returns Formatted currency string
   */
  formatCurrency(amount: number, currency: string = 'USD'): string {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency
    }).format(amount);
  }
}