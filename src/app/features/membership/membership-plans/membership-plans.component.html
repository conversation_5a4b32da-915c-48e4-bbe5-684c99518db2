<app-membership-container [currentStep]="currentStep" [reducedMotion]="reducedMotion">
      <!-- Step 1: Plan Selection -->
      <div *ngIf="currentStep === 'select'" class="step-content">
        <div class="row mb-2">
          <div class="col">
            <h2 class="mb-2">Choose Your Membership Plan</h2>
            <p class="text-muted small">Select the membership tier that best suits your needs and start winning amazing prizes.</p>
          </div>
        </div>

        <!-- Loading State -->
        <div *ngIf="loading" class="text-center py-3">
          <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">Loading...</span>
          </div>
          <p class="mt-2 small">Loading membership plans...</p>
        </div>

        <!-- Plans Display -->
        <div *ngIf="!loading" class="row row-cols-1 row-cols-md-3 g-3 mb-3">
          <div class="col" *ngFor="let plan of availablePlans">
            <div class="card h-100 plan-card compact" [class.popular-plan]="plan.isPopular">
              <div class="position-absolute top-0 start-50 translate-middle badge rounded-pill bg-primary px-2 py-1 small"
                    *ngIf="plan.isPopular">Most Popular</div>
              <div class="card-header text-center py-2 position-relative">
                <h3 class="card-title">{{ plan.name }}</h3>
                <div class="price-container">
                  <span class="currency">$</span>
                  <span class="price">{{ plan.price | number:'1.2-2' }}</span>
                  <span class="billing-cycle">/ {{ plan.billingPeriod | lowercase }}</span>
                </div>
              </div>
              <div class="card-body py-2">
                <p class="card-text small">{{ plan.description }}</p>
                <hr class="my-2">
                <h6 class="mb-2 small fw-bold">Features:</h6>
                <ul class="feature-list compact">
                  <li *ngFor="let feature of plan.features">
                    <i class="fas fa-check-circle text-success me-1 small"></i>
                    <span class="small">{{ feature }}</span>
                  </li>
                </ul>
                <div class="entry-allocation mt-2 mb-2">
                  <div class="d-flex justify-content-between mb-1 small">
                    <span>Entry Allocation</span>
                    <span class="fw-bold">{{ plan.entryAllocation }} entries</span>
                  </div>
                  <div class="progress" style="height: 4px;">
                    <div class="progress-bar" role="progressbar"
                        [style.width.%]="(plan.entryAllocation / getMaxEntries()) * 100"></div>
                  </div>
                </div>
              </div>
              <div class="card-footer text-center border-0 bg-transparent py-2">
                <button class="btn btn-primary w-100 py-2" (click)="selectPlan(plan)">
                  Select Plan
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Step 2: Payment -->
      <div *ngIf="currentStep === 'payment' && selectedPlan" class="step-content">
        <div #paymentCard
             class="payment-form-container"
             [ngClass]="{'fade-in': true}">

          <h2 class="payment-title">Complete Your Subscription</h2>

          <!-- Payment Error Alert -->
          <div *ngIf="paymentError" class="alert alert-danger mb-2 py-2 small">
            {{ paymentError }}
          </div>

          <div class="payment-content-container">
            <!-- Order Summary Section -->
            <div class="order-summary">
              <h3 class="compact-title">Order Summary</h3>
              <div class="plan-details">
                <div class="plan-header">
                  <div class="plan-name-badge">
                    <span class="plan-badge">{{ selectedPlan.name }}</span>
                  </div>
                  <div class="plan-price">
                    <span class="price">${{ selectedPlan.price | number:'1.2-2' }}</span>
                    <span class="billing-cycle">/{{ selectedPlan.billingPeriod | lowercase }}</span>
                  </div>
                </div>

                <div class="plan-features compact">
                  <h4 class="small fw-bold mb-1">Plan Includes:</h4>
                  <ul class="compact-list">
                    <li *ngFor="let feature of selectedPlan.features">
                      <i class="check-icon small"></i> <span class="small">{{ feature }}</span>
                    </li>
                  </ul>
                </div>

                <div class="entry-allocation mt-2 mb-2">
                  <div class="d-flex justify-content-between mb-1 small">
                    <span>Entry Allocation</span>
                    <span class="fw-bold">{{ selectedPlan.entryAllocation }} entries</span>
                  </div>
                  <div class="progress" style="height: 4px;">
                    <div class="progress-bar" role="progressbar"
                        [style.width.%]="selectedPlan.entryAllocation / getMaxEntries() * 100"></div>
                  </div>
                </div>

                <div class="plan-total">
                  <div class="subtotal small">
                    <span>Subtotal</span>
                    <span>${{ selectedPlan.price | number:'1.2-2' }}</span>
                  </div>
                  <div class="total">
                    <span>Total</span>
                    <span>${{ selectedPlan.price | number:'1.2-2' }}</span>
                  </div>
                </div>
              </div>
            </div>

            <!-- Payment Details Section -->
            <div class="payment-details">
              <h3 class="compact-title">Payment Details</h3>

              <form [formGroup]="paymentForm" class="payment-form">
                <div class="subscription-options">
                  <div class="form-group mb-2">
                    <label class="checkbox-container small">
                      <input type="checkbox" formControlName="autoRenew" id="autoRenew">
                      <span class="checkmark"></span>
                      Enable auto-renewal (your subscription will automatically renew when it expires)
                    </label>
                  </div>

                  <div class="form-group mb-2">
                    <label for="couponCode" class="small mb-1">Coupon Code (Optional)</label>
                    <div class="coupon-input">
                      <input type="text" id="couponCode" formControlName="couponCode" placeholder="Enter coupon code" class="py-1">
                      <button class="btn-apply py-1" type="button" (click)="applyCoupon()">Apply</button>
                    </div>
                  </div>
                </div>

                <!-- Loading Payment Intent -->
                <div *ngIf="!paymentIntent && !paymentError" class="text-center py-2">
                  <div class="spinner-border spinner-border-sm text-primary" role="status">
                    <span class="visually-hidden">Loading payment...</span>
                  </div>
                  <p class="mt-1 mb-0 small">Initializing payment...</p>
                </div>

                <!-- Payment Form Component -->
                <div *ngIf="paymentIntent" class="payment-method">
                  <app-payment-form
                    [amount]="paymentIntent.amount"
                    [currency]="paymentIntent.currency"
                    [savedPaymentMethods]="savedPaymentMethods"
                    [processingPayment]="submitting"
                    (paymentMethodCreated)="onPaymentMethodCreated($event)"
                    (paymentMethodSelected)="onPaymentMethodSelected($event)"
                    (paymentFormError)="onPaymentFormError($event)">
                  </app-payment-form>
                </div>

                <div class="payment-actions">
                  <button type="button" class="btn-secondary py-1" (click)="backToPlans()">
                    Back to Plans
                  </button>
                  <!-- Pay Now button is inside the payment-form component -->
                </div>
              </form>
            </div>
          </div>

          <!-- Security Notice -->
          <div class="security-notice py-1">
            <i class="lock-icon"></i>
            <p class="small mb-0">Your payment information is secure. We use encryption to protect your personal and payment details.</p>
          </div>
        </div>
      </div>
    </app-membership-container>