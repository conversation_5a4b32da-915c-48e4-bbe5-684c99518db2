// Animations
@keyframes fadeIn {
  0% {
    opacity: 0;
    transform: translateY(10px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  0% {
    transform: scale(0.95);
    opacity: 0;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes slideIn {
  0% {
    transform: translateX(20px);
    opacity: 0;
  }
  100% {
    transform: translateX(0);
    opacity: 1;
  }
}

// Animation classes
.fade-in {
  animation: fadeIn 0.4s ease-out forwards;
}

// Plan selection cards
.plan-card {
  position: relative;
  border-radius: 0.75rem;
  transition: all 0.3s ease;
  border: 1px solid rgba(0,0,0,0.1);
  overflow: visible; // Ensure badge is not clipped
  padding-top: 2.5rem; // Add space for the badge
  background-color: #ffffff;

  &.compact {
    padding-top: 3rem; // Increased padding for compact cards to accommodate badge properly

    .card-title {
      font-size: 1.25rem;
      margin-bottom: 0.5rem;
    }
  }

  &:before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: 0.75rem;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: -1;
  }

  &:hover:before {
    opacity: 1;
  }
}

.plan-card:hover {
  transform: translateY(-5px);
  border-color: rgba(0,0,0,0.15);
}

.popular-plan {
  border: 2px solid var(--bs-primary);
  transform: scale(1.02);
  z-index: 2;
  box-shadow: 0 5px 15px rgba(0,0,0,0.1);

  &:after {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    border-radius: 0.75rem;
    border: 2px solid transparent;
    background: linear-gradient(135deg, var(--bs-primary), #4dabf7) border-box;
    -webkit-mask:
      linear-gradient(#fff 0 0) padding-box,
      linear-gradient(#fff 0 0);
    -webkit-mask-composite: xor;
    mask-composite: exclude;
    opacity: 0.3;
    pointer-events: none;
  }
}

.popular-plan:hover {
  transform: translateY(-5px) scale(1.02);
}

// Most Popular Badge
.badge.position-absolute {
  z-index: 15; // Increased z-index to ensure visibility
  white-space: nowrap;
  font-weight: 600;
  font-size: 0.875rem; // Slightly larger for better readability
  box-shadow: 0 4px 12px rgba(0,0,0,0.2);
  border: 2px solid white;
  min-height: 28px; // Ensure minimum height for visibility
  display: flex;
  align-items: center;
  justify-content: center;

  &.top-0.start-50.translate-middle {
    top: -12px !important; // Move further up to ensure full visibility
    transform: translateX(-50%) translateY(0) !important; // Override translate-middle to prevent clipping
  }

  // Responsive adjustments
  @media (max-width: 768px) {
    font-size: 0.75rem;
    min-height: 24px;
    padding: 0.25rem 0.75rem;

    &.top-0.start-50.translate-middle {
      top: -10px !important;
    }
  }

  @media (max-width: 576px) {
    font-size: 0.7rem;
    min-height: 22px;
    padding: 0.2rem 0.6rem;

    &.top-0.start-50.translate-middle {
      top: -8px !important;
    }
  }
}

.card-header {
  background-color: transparent;
  border-bottom: 1px solid rgba(0,0,0,0.1);
  padding-top: 2rem;

  .compact & {
    padding-top: 1rem;
  }
}

.price-container {
  margin-top: 1rem;
  font-weight: 300;

  .compact & {
    margin-top: 0.5rem;
  }
}

.currency {
  display: inline-block;
  font-size: 1.2rem;
  position: relative;
  top: -0.3em;
  margin-right: 0.1rem;
  vertical-align: baseline;

  .compact & {
    font-size: 1rem;
  }
}

.price {
  font-size: 2.5rem;
  font-weight: 700;

  .compact & {
    font-size: 2rem;
  }
}

.billing-cycle {
  font-size: 1rem;
  color: #6c757d;

  .compact & {
    font-size: 0.875rem;
  }
}

.feature-list {
  list-style: none;
  padding-left: 0;

  &.compact {
    margin-bottom: 0.5rem;
  }
}

.feature-list li {
  margin-bottom: 0.75rem;

  .compact & {
    margin-bottom: 0.5rem;
  }
}

.feature-list.compact li {
  margin-bottom: 0.25rem;
}

.progress {
  height: 6px;
  border-radius: 3px;
}

// Step Content
.step-content {
  min-height: 300px;
  transition: all 0.3s ease;
  overflow: visible; // Ensure badges are not clipped
  padding-top: 1rem; // Add top padding to provide space for badges

  // Ensure Bootstrap containers don't clip badges
  .row {
    overflow: visible;
  }

  .col {
    overflow: visible;
  }
}

// Payment Form Container
.payment-form-container {
  padding: 1.5rem;
  transition: all 0.3s ease;
}

// Title
.payment-title {
  text-align: center;
  margin-bottom: 1.5rem;
  color: #333;
  font-weight: 600;

  .compact & {
    margin-bottom: 1rem;
    font-size: 1.5rem;
  }
}

// Compact title
.compact-title {
  margin-top: 0;
  margin-bottom: 1rem;
  font-size: 1.1rem;
  color: #333;
  border-bottom: 1px solid #dee2e6;
  padding-bottom: 0.5rem;
}

// Two-column layout
.payment-content-container {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;

  @media (min-width: 768px) {
    flex-direction: row;

    .order-summary, .payment-details {
      flex: 1;
    }
  }
}

// Order Summary
.order-summary {
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 1rem;
  margin-bottom: 1rem;
}

.order-summary h3 {
  margin-top: 0;
  margin-bottom: 1rem;
  font-size: 1.25rem;
  color: #333;
  border-bottom: 1px solid #dee2e6;
  padding-bottom: 0.5rem;
}

.plan-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.plan-badge {
  background-color: var(--bs-primary);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-weight: 500;
}

.plan-price {
  text-align: right;
}

.plan-features {
  margin-bottom: 1.5rem;

  &.compact {
    margin-bottom: 0.75rem;
  }
}

.plan-features h4 {
  font-size: 1rem;
  margin-bottom: 0.75rem;
  color: #495057;
}

.plan-features ul {
  list-style: none;
  padding-left: 0;
  margin: 0;
}

.plan-features li {
  padding: 0.5rem 0;
  display: flex;
  align-items: center;

  .compact & {
    padding: 0.25rem 0;
  }
}

.compact-list li {
  padding: 0.25rem 0;
}

.check-icon {
  display: inline-block;
  width: 18px;
  height: 18px;
  background-color: #28a745;
  border-radius: 50%;
  margin-right: 10px;
  position: relative;

  &.small {
    width: 14px;
    height: 14px;
    margin-right: 6px;
  }
}

.check-icon::after {
  content: '';
  position: absolute;
  width: 5px;
  height: 10px;
  border: solid white;
  border-width: 0 2px 2px 0;
  top: 3px;
  left: 6px;
  transform: rotate(45deg);
}

.check-icon.small::after {
  width: 4px;
  height: 8px;
  top: 2px;
  left: 5px;
}

.plan-total {
  border-top: 1px solid #dee2e6;
  padding-top: 1rem;
}

.subtotal, .total {
  display: flex;
  justify-content: space-between;
  margin-bottom: 0.5rem;
}

.total {
  font-weight: 600;
  font-size: 1.1rem;
  margin-top: 0.5rem;
}

// Payment Details
.payment-details {
  padding: 1rem;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  margin-bottom: 1rem;
}

.payment-details h3 {
  margin-top: 0;
  margin-bottom: 1rem;
  font-size: 1.25rem;
  color: #333;
  border-bottom: 1px solid #dee2e6;
  padding-bottom: 0.5rem;
}

.subscription-options {
  margin-bottom: 1rem;
}

.form-group {
  margin-bottom: 1rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.25rem;
  font-weight: 500;
  color: #495057;
}

.checkbox-container {
  display: flex;
  align-items: center;
  cursor: pointer;
  font-weight: normal;

  &.small {
    font-size: 0.875rem;
  }
}

.checkbox-container input {
  margin-right: 8px;
}

.coupon-input {
  display: flex;
}

.coupon-input input {
  flex: 1;
  padding: 0.5rem 0.75rem;
  border: 1px solid #ced4da;
  border-radius: 4px 0 0 4px;
  font-size: 0.875rem;
}

.btn-apply {
  padding: 0.5rem 1rem;
  background-color: #6c757d;
  color: white;
  border: none;
  border-radius: 0 4px 4px 0;
  cursor: pointer;
  font-weight: 500;
  font-size: 0.875rem;
}

.btn-apply:hover {
  background-color: #5a6268;
}

input[type="text"] {
  width: 100%;
  padding: 0.5rem 0.75rem;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 0.875rem;
}

.payment-actions {
  display: flex;
  justify-content: space-between;
  margin-top: 1rem;
}

.btn-secondary {
  padding: 0.5rem 1rem;
  background-color: white;
  color: #6c757d;
  border: 1px solid #6c757d;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
  font-size: 0.875rem;
}

.btn-primary {
  padding: 0.5rem 1.5rem;
  background-color: var(--bs-primary);
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
  font-size: 0.875rem;
}

.btn-primary:hover {
  background-color: var(--bs-primary);
  filter: brightness(90%);
}

// Security Notice
.security-notice {
  display: flex;
  align-items: center;
  background-color: #f8f9fa;
  padding: 0.5rem;
  border-radius: 4px;
  margin-top: 0.5rem;
}

.lock-icon {
  display: inline-block;
  width: 20px;
  height: 20px;
  background-color: #28a745;
  border-radius: 50%;
  margin-right: 8px;
  position: relative;
  flex-shrink: 0;
}

.lock-icon::after {
  content: '🔒';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 10px;
}

.security-notice p {
  margin: 0;
  font-size: 0.75rem;
  color: #6c757d;
}