.plan-card {
  border-radius: 0.75rem;
  transition: all 0.3s ease;
  border: 1px solid rgba(0,0,0,0.1);
  position: relative;
  overflow: hidden;
}

.plan-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 10px 20px rgba(0,0,0,0.1);
}

.popular-plan {
  border: 2px solid var(--bs-primary);
  transform: scale(1.05);
  z-index: 2;
  box-shadow: 0 10px 20px rgba(0,0,0,0.1);
}

.popular-plan:hover {
  transform: translateY(-10px) scale(1.05);
}

.ribbon {
  position: absolute;
  top: 0;
  right: 0;
  transform: rotate(45deg) translateX(30%) translateY(-50%);
  width: 150px;
  background-color: var(--bs-primary);
  color: white;
  text-align: center;
  padding: 5px 0;
  font-size: 0.8rem;
  font-weight: bold;
  z-index: 5;
}

.card-header {
  background-color: transparent;
  border-bottom: 1px solid rgba(0,0,0,0.1);
}

.price-container {
  margin-top: 1rem;
  font-weight: 300;
}

.currency {
  vertical-align: top;
  font-size: 1.2rem;
  margin-right: 0.2rem;
}

.price {
  font-size: 2.5rem;
  font-weight: 700;
}

.billing-cycle {
  font-size: 1rem;
  color: #6c757d;
}

.entries-box {
  width: 80px;
  text-align: center;
}

.entries-arrow {
  font-size: 1.2rem;
}

.bg-primary-subtle {
  background-color: rgba(var(--bs-primary-rgb), 0.1);
}

.feature-list {
  list-style: none;
  padding-left: 0;
}

.feature-list li {
  margin-bottom: 0.75rem;
}