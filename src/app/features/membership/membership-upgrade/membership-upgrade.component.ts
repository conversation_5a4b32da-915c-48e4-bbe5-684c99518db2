import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, Router } from '@angular/router';
import { MembershipService } from '../../../core/services/membership.service';
import { NotificationService } from '../../../core/services/notification.service';
import { MembershipTier, Subscription } from '../../../core/models/membership.model';
import { Observable, forkJoin, map, of, switchMap } from 'rxjs';
import { MatSnackBarModule } from '@angular/material/snack-bar';

@Component({
  selector: 'app-membership-upgrade',
  standalone: true,
  imports: [CommonModule, RouterModule, MatSnackBarModule],
  templateUrl: './membership-upgrade.component.html',
  styleUrls: ['./membership-upgrade.component.scss']
})
export class MembershipUpgradeComponent implements OnInit {
  loading = true;
  currentMembership: Subscription | null = null;
  allPlans: MembershipTier[] = [];
  upgradePlans: MembershipTier[] = [];

  constructor(
    private membershipService: MembershipService,
    private notificationService: NotificationService,
    private router: Router
  ) { }

  ngOnInit(): void {
    this.loadMembershipData();
  }

  loadMembershipData(): void {
    this.loading = true;
    
    // Load both current membership and available plans in parallel
    forkJoin({
      membership: this.membershipService.getCurrentMembership(),
      plans: this.membershipService.getAvailableTiers()
    }).subscribe({
      next: (data) => {
        this.currentMembership = data.membership;
        this.allPlans = data.plans;
        this.filterUpgradePlans();
        this.loading = false;
      },
      error: (err) => {
        console.error('Error loading membership data', err);
        this.notificationService.error('Failed to load membership data');
        this.loading = false;
        this.router.navigate(['/membership']);
      }
    });
  }

  filterUpgradePlans(): void {
    if (!this.currentMembership || !this.allPlans.length) {
      this.upgradePlans = [];
      return;
    }

    // Find current tier in available plans to get its price
    const currentTier = this.allPlans.find(plan => plan.id === this.currentMembership?.tierId);
    
    if (!currentTier) {
      this.upgradePlans = this.allPlans;
      return;
    }

    // Filter plans that have higher price or more entries than current plan
    this.upgradePlans = this.allPlans.filter(plan => 
      plan.price > currentTier.price || 
      plan.entryAllocation > currentTier.entryAllocation
    );
  }

  upgradeTo(plan: MembershipTier): void {
    if (confirm(`Are you sure you want to upgrade to the ${plan.name} plan? This will be effective immediately.`)) {
      this.membershipService.upgradeMembership(plan.id)
        .subscribe({
          next: (subscription) => {
            this.notificationService.success(`Successfully upgraded to ${plan.name}`);
            this.router.navigate(['/membership']);
          },
          error: (err) => {
            console.error('Error upgrading membership', err);
            this.notificationService.error('Failed to upgrade membership');
          }
        });
    }
  }
}