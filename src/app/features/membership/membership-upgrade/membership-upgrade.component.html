<div class="container py-4">
  <div class="row mb-4">
    <div class="col">
      <h2 class="mb-3">Upgrade Your Membership</h2>
      <p class="text-muted">
        Upgrade to a higher tier to get more entries and exclusive benefits.
        Your upgrade will be effective immediately, and we'll prorate your billing accordingly.
      </p>
    </div>
  </div>

  <!-- Loading State -->
  <div *ngIf="loading" class="text-center py-5">
    <div class="spinner-border text-primary" role="status">
      <span class="visually-hidden">Loading...</span>
    </div>
    <p class="mt-3">Loading available upgrades...</p>
  </div>

  <!-- Current Plan -->
  <div *ngIf="!loading && currentMembership" class="row mb-4">
    <div class="col">
      <div class="card">
        <div class="card-body">
          <h4 class="card-title">Your Current Plan</h4>
          <div class="current-plan-details mt-3">
            <div class="badge bg-primary mb-3">{{ currentMembership.tierName }}</div>
            <p class="mb-2">Entries per month: <strong>{{ currentMembership.entriesTotal }}</strong></p>
            <p class="mb-2">Status: <span class="badge bg-success">{{ currentMembership.status | titlecase }}</span></p>
            <p class="mb-0">Valid until: <strong>{{ currentMembership.endDate | date }}</strong></p>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- No Upgrade Options -->
  <div *ngIf="!loading && upgradePlans.length === 0" class="text-center py-5">
    <div class="mb-4">
      <i class="fas fa-crown fa-3x text-warning mb-3"></i>
      <h4>You're already on our highest tier!</h4>
      <p class="text-muted">There are no higher tier plans available for upgrade at this time.</p>
    </div>
    <button class="btn btn-primary" routerLink="/membership">
      <i class="fas fa-arrow-left me-2"></i>Back to Membership
    </button>
  </div>

  <!-- Upgrade Options -->
  <div *ngIf="!loading && upgradePlans.length > 0" class="row row-cols-1 row-cols-md-3 g-4 mb-5">
    <div class="col" *ngFor="let plan of upgradePlans">
      <div class="card h-100 plan-card" [class.popular-plan]="plan.isPopular">
        <div class="ribbon" *ngIf="plan.isPopular">
          <span>Best Value</span>
        </div>
        <div class="card-header text-center py-3">
          <h3 class="card-title">{{ plan.name }}</h3>
          <div class="price-container">
            <span class="currency">{{ plan.currency }}</span>
            <span class="price">{{ plan.price }}</span>
            <span class="billing-cycle">/ {{ plan.billingPeriod | lowercase }}</span>
          </div>
        </div>
        <div class="card-body">
          <p class="card-text">{{ plan.description }}</p>
          
          <div class="entries-comparison my-4">
            <h6 class="mb-3">Entry Increase:</h6>
            <div class="d-flex align-items-center mb-2">
              <div class="current-entries me-2">
                <div class="entries-box bg-light px-3 py-2 rounded">
                  <small class="d-block text-muted">Current</small>
                  <strong>{{ currentMembership?.entriesTotal || 0 }}</strong>
                </div>
              </div>
              <div class="entries-arrow px-2">
                <i class="fas fa-long-arrow-alt-right text-primary"></i>
              </div>
              <div class="new-entries ms-2">
                <div class="entries-box bg-primary-subtle px-3 py-2 rounded">
                  <small class="d-block text-muted">New</small>
                  <strong>{{ plan.entryAllocation }}</strong>
                </div>
              </div>
            </div>
            <div class="badge bg-success">
              +{{ plan.entryAllocation - (currentMembership?.entriesTotal || 0) }} entries
            </div>
          </div>
          
          <hr>
          
          <h6 class="mb-3">Features:</h6>
          <ul class="feature-list">
            <li *ngFor="let feature of plan.features">
              <i class="fas fa-check-circle text-success me-2"></i> {{ feature }}
            </li>
          </ul>
          
          <div class="benefits mt-4" *ngIf="plan.benefits && plan.benefits.length > 0">
            <h6 class="mb-3">Additional Benefits:</h6>
            <ul class="feature-list">
              <li *ngFor="let benefit of plan.benefits">
                <i class="fas fa-star text-warning me-2"></i> {{ benefit }}
              </li>
            </ul>
          </div>
        </div>
        <div class="card-footer text-center border-0 bg-transparent py-4">
          <button class="btn btn-primary w-100" (click)="upgradeTo(plan)">
            Upgrade Now
          </button>
        </div>
      </div>
    </div>
  </div>
</div>