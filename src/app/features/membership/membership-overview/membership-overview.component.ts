import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { MembershipService } from '../../../core/services/membership.service';
import { NotificationService } from '../../../core/services/notification.service';
import { MembershipTier, Subscription, MembershipHistoryItem } from '../../../core/models/membership.model';
import { Observable, catchError, of, switchMap } from 'rxjs';
import { MatSnackBarModule } from '@angular/material/snack-bar';

@Component({
  selector: 'app-membership-overview',
  standalone: true,
  imports: [CommonModule, RouterModule, MatSnackBarModule],
  templateUrl: './membership-overview.component.html',
  styleUrls: ['./membership-overview.component.scss']
})
export class MembershipOverviewComponent implements OnInit {
  loading = true;
  activeMembership: Subscription | null = null;
  membershipHistory: MembershipHistoryItem[] = [];
  entriesPercentage = 0;

  constructor(
    private membershipService: MembershipService,
    private notificationService: NotificationService
  ) { }

  ngOnInit(): void {
    this.loadMembershipData();
  }

  loadMembershipData(): void {
    this.loading = true;

    this.membershipService.getCurrentMembership()
      .pipe(
        switchMap(membership => {
          this.activeMembership = membership;
          this.calculateEntriesPercentage();
          return this.membershipService.getMembershipHistory();
        }),
        catchError(err => {
          this.activeMembership = null;
          return this.membershipService.getMembershipHistory();
        })
      )
      .subscribe({
        next: (history) => {
          this.membershipHistory = history;
          this.loading = false;
        },
        error: (err) => {
          this.loading = false;
        }
      });
  }

  calculateEntriesPercentage(): void {
    if (this.activeMembership) {
      this.entriesPercentage = (this.activeMembership.entriesRemaining / this.activeMembership.entriesTotal) * 100;
    }
  }

  toggleAutoRenewal(): void {
    if (!this.activeMembership) return;

    const newAutoRenewState = !this.activeMembership.autoRenew;

    this.membershipService.updateAutoRenewal(newAutoRenewState)
      .subscribe({
        next: (updatedMembership) => {
          this.activeMembership = updatedMembership;
          this.notificationService.success(
            `Auto-renewal has been ${newAutoRenewState ? 'enabled' : 'disabled'}`
          );
        },
        error: (err) => {
          console.error('Error updating auto-renewal', err);
          this.notificationService.error('Failed to update auto-renewal settings');
        }
      });
  }

  cancelMembership(): void {
    if (confirm('Are you sure you want to cancel your membership? You will still have access until the end of your current billing period.')) {
      this.membershipService.cancelMembership()
        .subscribe({
          next: (updatedMembership) => {
            this.activeMembership = updatedMembership;
            this.notificationService.success(
              'Your membership has been canceled. You will have access until ' +
              new Date(updatedMembership.endDate).toLocaleDateString()
            );
          },
          error: (err) => {
            console.error('Error canceling membership', err);
            this.notificationService.error('Failed to cancel membership');
          }
        });
    }
  }

  /**
   * Force reload membership data from the API
   */
  reloadMembershipData(): void {
    this.loading = true;
    this.activeMembership = null;
    this.membershipHistory = [];

    // Add a small delay to make the loading state visible
    setTimeout(() => {
      this.loadMembershipData();
    }, 500);
  }
}