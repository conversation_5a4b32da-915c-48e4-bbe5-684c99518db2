<div class="container py-4">
  <div class="row">
    <div class="col-12 mb-4">
      <div class="card">
        <div class="card-body">
          <div class="d-flex justify-content-between align-items-center mb-4">
            <h2 class="card-title mb-0">Membership</h2>
            <button class="btn btn-sm btn-outline-primary" (click)="reloadMembershipData()" [disabled]="loading">
              <i class="fas fa-sync-alt me-1" [class.fa-spin]="loading"></i>
              Refresh
            </button>
          </div>

          <!-- Loading state -->
          <div *ngIf="loading" class="text-center py-5">
            <div class="spinner-border text-primary" role="status">
              <span class="visually-hidden">Loading...</span>
            </div>
            <p class="mt-3">Loading membership information...</p>
          </div>

          <!-- Active Membership Section -->
          <div *ngIf="activeMembership && !loading" class="active-membership-section">
            <div class="row">
              <div class="col-md-8">
                <div class="d-flex align-items-center mb-3">
                  <div class="membership-badge me-3 p-2 px-3 rounded-pill bg-primary text-white">
                    <i class="fas fa-id-card me-2"></i>{{ activeMembership.tierName }}
                  </div>
                  <span class="badge me-2" [ngClass]="{
                    'bg-success': activeMembership.status === 'active',
                    'bg-warning': activeMembership.status === 'pending',
                    'bg-danger': activeMembership.status === 'canceled' || activeMembership.status === 'expired'
                  }">{{ activeMembership.status | titlecase }}</span>
                  <small *ngIf="activeMembership.autoRenew" class="text-success">
                    <i class="fas fa-sync-alt me-1"></i>Auto-renewal enabled
                  </small>
                  <small *ngIf="!activeMembership.autoRenew" class="text-warning">
                    <i class="fas fa-times-circle me-1"></i>Auto-renewal disabled
                  </small>
                </div>

                <div class="mb-4">
                  <p class="mb-1"><strong>Member since:</strong> {{ activeMembership.memberSince | date }}</p>
                  <p class="mb-1"><strong>Current period:</strong> {{ activeMembership.startDate | date }} - {{ activeMembership.endDate | date }}</p>
                  <p class="mb-1" *ngIf="activeMembership.nextBillingDate && activeMembership.autoRenew">
                    <strong>Next billing date:</strong> {{ activeMembership.nextBillingDate | date }}
                  </p>
                </div>

                <div class="mb-4">
                  <h5>Entries</h5>
                  <div class="progress mb-2">
                    <div class="progress-bar" role="progressbar"
                         [style.width.%]="entriesPercentage"
                         [attr.aria-valuenow]="activeMembership.entriesRemaining"
                         aria-valuemin="0"
                         [attr.aria-valuemax]="activeMembership.entriesTotal">
                    </div>
                  </div>
                  <small>{{ activeMembership.entriesRemaining }} entries remaining out of {{ activeMembership.entriesTotal }}</small>
                </div>

                <div class="d-flex flex-wrap mt-4">
                  <button class="btn btn-outline-primary me-2 mb-2" (click)="toggleAutoRenewal()">
                    <i class="fas" [ngClass]="activeMembership.autoRenew ? 'fa-toggle-off' : 'fa-toggle-on'"></i>
                    {{ activeMembership.autoRenew ? 'Disable' : 'Enable' }} Auto-renewal
                  </button>
                  <button class="btn btn-outline-primary me-2 mb-2" routerLink="/membership/upgrade">
                    <i class="fas fa-level-up-alt me-1"></i>Upgrade Plan
                  </button>
                  <button class="btn btn-outline-danger me-2 mb-2" (click)="cancelMembership()" *ngIf="activeMembership.status === 'active'">
                    <i class="fas fa-times-circle me-1"></i>Cancel Membership
                  </button>
                </div>
              </div>

              <div class="col-md-4 mt-4 mt-md-0">
                <div class="card card-body border-primary-subtle">
                  <h5 class="card-title">Membership Benefits</h5>
                  <ul class="list-group list-group-flush mt-3 border-0">
                    <li class="list-group-item border-0 ps-0">
                      <i class="fas fa-check-circle text-success me-2"></i>{{ activeMembership.entriesTotal }} entries per billing cycle
                    </li>
                    <li class="list-group-item border-0 ps-0">
                      <i class="fas fa-check-circle text-success me-2"></i>Access to all standard giveaways
                    </li>
                    <li class="list-group-item border-0 ps-0">
                      <i class="fas fa-check-circle text-success me-2"></i>Early access to new features
                    </li>
                    <li class="list-group-item border-0 ps-0">
                      <i class="fas fa-check-circle text-success me-2"></i>Member-only promotions
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </div>

          <!-- No Membership Section -->
          <div *ngIf="!activeMembership && !loading" class="no-membership-section">
            <div class="text-center py-4">
              <div class="mb-4">
                <i class="fas fa-id-card fa-4x text-muted mb-3"></i>
                <h4>You don't have an active membership</h4>
                <p class="text-muted">Subscribe to a membership plan to participate in giveaways and win amazing prizes!</p>
              </div>
              <button class="btn btn-primary" routerLink="/membership/plans">
                <i class="fas fa-arrow-circle-right me-2"></i>View Membership Plans
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Membership History Section -->
    <div class="col-12" *ngIf="membershipHistory && membershipHistory.length > 0 && !loading">
      <div class="card">
        <div class="card-body">
          <h3 class="card-title mb-4">Membership History</h3>

          <div class="table-responsive">
            <table class="table table-hover">
              <thead>
                <tr>
                  <th>Plan</th>
                  <th>Status</th>
                  <th>Start Date</th>
                  <th>End Date</th>
                  <th>Price</th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let item of membershipHistory">
                  <td>{{ item.tierName }}</td>
                  <td>
                    <span class="badge" [ngClass]="{
                      'bg-success': item.status === 'active',
                      'bg-danger': item.status === 'canceled' || item.status === 'expired'
                    }">{{ item.status | titlecase }}</span>
                  </td>
                  <td>{{ item.startDate ? (item.startDate | date) : 'N/A' }}</td>
                  <td>{{ item.endDate ? (item.endDate | date) : 'N/A' }}</td>
                  <td>{{ item.price | currency:item.currency }}</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>


  </div>
</div>