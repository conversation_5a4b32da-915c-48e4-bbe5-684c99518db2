<app-membership-container [currentStep]="'confirmation'" [reducedMotion]="reducedMotion">
      <div class="step-content">
        <div class="row justify-content-center">
          <div class="col-md-8">
            <div class="success-container fade-in">
              <div class="text-center">
                <!-- Loading State -->
                <div *ngIf="loading" class="py-5">
                  <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Loading...</span>
                  </div>
                  <p class="mt-3">Loading your membership details...</p>
                </div>

                <!-- Error State -->
                <div *ngIf="error && !loading" class="py-5">
                  <div class="error-icon mb-4">
                    <i class="fas fa-exclamation-circle fa-4x text-danger"></i>
                  </div>
                  <h2 class="mb-3">Something went wrong</h2>
                  <p class="mb-4">We couldn't load your membership details. Please try refreshing the page or contact support.</p>
                  <button class="btn btn-primary" (click)="loadMembershipDetails()">Try Again</button>
                </div>

                <!-- Success State -->
                <div *ngIf="!loading && !error && subscription" class="py-4">
                  <div class="success-icon mb-4">
                    <i class="fas fa-check-circle fa-4x text-success"></i>
                  </div>
                  <h2 class="mb-3">Payment Successful!</h2>
                  <p class="lead mb-4">Thank you for joining Winners Society!</p>

                  <div *ngIf="transactionId" class="transaction-info mb-4">
                    <p class="text-muted">Transaction ID: {{ transactionId }}</p>
                  </div>

                  <div class="membership-details mb-4">
                    <div class="card bg-light">
                      <div class="card-body">
                        <h5 class="card-title">{{ subscription.tierName }} Membership</h5>
                        <ul class="list-group list-group-flush">
                          <li class="list-group-item bg-transparent">
                            <strong>Status:</strong> <span class="badge bg-success">{{ subscription.status === 'active' ? 'Active' : subscription.status }}</span>
                          </li>
                          <li class="list-group-item bg-transparent">
                            <strong>Start Date:</strong> {{ formatDate(subscription.startDate) }}
                          </li>
                          <li class="list-group-item bg-transparent">
                            <strong>End Date:</strong> {{ formatDate(subscription.endDate) }}
                          </li>
                          <li class="list-group-item bg-transparent">
                            <strong>Auto-Renew:</strong> {{ subscription.autoRenew ? 'Yes' : 'No' }}
                          </li>
                          <li class="list-group-item bg-transparent">
                            <strong>Entries:</strong> {{ subscription.entriesTotal }} entries
                          </li>
                        </ul>
                      </div>
                    </div>
                  </div>

                  <div class="d-grid gap-2">
                    <button class="btn btn-primary btn-lg" (click)="goToDashboard()">
                      Go to Dashboard
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </app-membership-container>
