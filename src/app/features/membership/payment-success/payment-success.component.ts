import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, Router, ActivatedRoute } from '@angular/router';
import { MembershipService } from '../../../core/services/membership.service';
import { Subscription } from '../../../core/models/membership.model';
import { MembershipContainerComponent } from '../../../shared/components/membership-container/membership-container.component';

// Define interface for API response (used in type assertions)
interface ApiResponse<T> {
  success: boolean;
  data: T;
}

// Define interface for membership data from API
interface MembershipData {
  id: string;
  userId: string;
  membershipTierId: string;
  startDate: string;
  endDate: string;
  status: string;
  autoRenew: boolean;
  createdAt: string;
  updatedAt: string;
  paymentMethodId: string;
  membershipTier?: {
    id: string;
    name: string;
    description: string;
    price: number;
    currency: string;
    billingPeriod: string;
    entryAllocation: number;
    duration: number;
    features: string[];
    benefits: string[];
    isActive: boolean;
    displayOrder: number;
    createdAt: string;
    updatedAt: string;
  };
}

@Component({
  selector: 'app-payment-success',
  standalone: true,
  imports: [CommonModule, RouterModule, MembershipContainerComponent],
  templateUrl: './payment-success.component.html',
  styleUrls: ['./payment-success.component.scss']
})
export class PaymentSuccessComponent implements OnInit {
  subscription: Subscription | null = null;
  loading = true;
  error = false;
  transactionId: string | null = null;
  reducedMotion = false;

  constructor(
    private membershipService: MembershipService,
    private router: Router,
    private route: ActivatedRoute
  ) { }

  ngOnInit(): void {
    // Check for reduced motion preference
    this.checkReducedMotionPreference();

    // Get transaction ID from route params if available
    this.route.queryParams.subscribe(params => {
      this.transactionId = params['transaction_id'] || null;
      this.loadMembershipDetails();
    });
  }

  /**
   * Check if the user prefers reduced motion
   */
  private checkReducedMotionPreference(): void {
    // Check if the user prefers reduced motion
    const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;
    if (prefersReducedMotion) {
      this.reducedMotion = true;
    }
  }

  /**
   * Load membership details after successful payment
   * @param retryCount Number of retries attempted (for async membership activation)
   */
  loadMembershipDetails(retryCount: number = 0): void {
    this.loading = true;
    this.error = false;

    this.membershipService.getCurrentMembership().subscribe({
      next: (response: any) => {
        // Check if the response is wrapped in a data property (API response format)
        if (response && 'data' in response && response.data) {
          // Extract the subscription from the data property
          const subscriptionData = response.data as MembershipData;

          // Map the API response to our Subscription model
          this.subscription = {
            id: subscriptionData.id,
            userId: subscriptionData.userId,
            tierId: subscriptionData.membershipTierId,
            tierName: subscriptionData.membershipTier?.name || 'Premium',
            status: subscriptionData.status === 'ACTIVE' ? 'active' : 'canceled',
            startDate: new Date(subscriptionData.startDate),
            endDate: new Date(subscriptionData.endDate),
            autoRenew: subscriptionData.autoRenew,
            entriesRemaining: subscriptionData.membershipTier?.entryAllocation || 0,
            entriesTotal: subscriptionData.membershipTier?.entryAllocation || 0,
            memberSince: new Date(subscriptionData.createdAt)
          };
        } else {
          // If the response is already in the expected format
          this.subscription = response as Subscription;
        }

        this.loading = false;
        console.log('Subscription loaded:', this.subscription);

        // If we have a transaction ID but no active subscription,
        // it might be that the membership activation is still processing
        if (this.transactionId &&
            (!this.subscription || this.subscription.status !== 'active') &&
            retryCount < 3) {
          console.log(`Membership not yet active, retrying in 2 seconds (attempt ${retryCount + 1}/3)`);
          setTimeout(() => {
            this.loadMembershipDetails(retryCount + 1);
          }, 2000);
        }
      },
      error: (err) => {
        console.error('Error loading membership details', err);

        // If we have a transaction ID but can't load membership details,
        // it might be that the membership activation is still processing
        if (this.transactionId && retryCount < 3) {
          console.log(`Failed to load membership, retrying in 2 seconds (attempt ${retryCount + 1}/3)`);
          setTimeout(() => {
            this.loadMembershipDetails(retryCount + 1);
          }, 2000);
        } else {
          this.error = true;
          this.loading = false;
        }
      }
    });
  }

  /**
   * Navigate to membership dashboard
   */
  goToDashboard(): void {
    this.router.navigate(['/dashboard']);
  }

  /**
   * Format date for display
   */
  formatDate(date: Date | string): string {
    if (!date) {
      return 'N/A';
    }

    try {
      return new Date(date).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });
    } catch (error) {
      console.error('Error formatting date:', error);
      return 'Invalid Date';
    }
  }
}
