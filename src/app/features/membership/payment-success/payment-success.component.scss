// Success component styles

.success-container {
  border-radius: 12px;
  overflow: hidden;
  background-color: #fff;
  padding: 1.5rem;

  &.fade-in {
    animation: fadeIn 0.4s ease-out forwards;
  }
}

.success-icon {
  color: #28a745;
  animation: scale-in 0.5s ease-out;

  i {
    filter: drop-shadow(0 4px 6px rgba(40, 167, 69, 0.2));
  }
}

.error-icon {
  color: #dc3545;

  i {
    filter: drop-shadow(0 4px 6px rgba(220, 53, 69, 0.2));
  }
}

.transaction-info {
  background-color: #f8f9fa;
  padding: 0.75rem 1rem;
  border-radius: 8px;
  font-size: 0.9rem;
  border: 1px solid rgba(0,0,0,0.05);
  box-shadow: inset 0 1px 3px rgba(0,0,0,0.05);
}

.membership-details {
  animation: fadeIn 0.5s ease-out 0.2s both;

  .card {
    border-radius: 8px;
    border: 1px solid rgba(0,0,0,0.1);
    box-shadow: 0 2px 8px rgba(0,0,0,0.05);
  }

  .list-group-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 1rem;
    border-color: rgba(0,0,0,0.05);

    strong {
      color: #495057;
      font-weight: 600;
    }
  }
}

@keyframes scale-in {
  0% {
    transform: scale(0);
    opacity: 0;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes fadeIn {
  0% {
    opacity: 0;
    transform: translateY(10px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scale-in {
  0% {
    transform: scale(0.8);
    opacity: 0;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}
