import { Routes } from '@angular/router';
import { noAuthGuard } from '../../core/guards/no-auth.guard';

/**
 * Routes for public pages
 * No authentication required
 */
export const PUBLIC_ROUTES: Routes = [
  {
    path: '',
    loadComponent: () => import('../content/home/<USER>').then(c => c.HomeComponent),
    title: 'Winners Society - Win Amazing Prizes'
  },
  {
    path: 'membership',
    loadComponent: () => import('../membership/public-plans/public-plans.component').then(c => c.PublicPlansComponent),
    title: 'Membership Plans - Winners Society'
  },
  {
    path: 'giveaways',
    loadComponent: () => import('../giveaways/public-giveaways/public-giveaways.component').then(c => c.PublicGiveawaysComponent),
    title: 'Giveaways - Winners Society'
  },
  {
    path: 'winners',
    loadComponent: () => import('./winners/public-winners.component').then(c => c.PublicWinnersComponent),
    title: 'Winners - Winners Society'
  },
  {
    path: 'about',
    loadComponent: () => import('./about/about.component').then(c => c.AboutComponent),
    title: 'About Us - Winners Society'
  },
  {
    path: 'contact',
    loadComponent: () => import('./contact/contact.component').then(c => c.ContactComponent),
    title: 'Contact Us - Winners Society'
  },
  {
    path: 'faq',
    loadComponent: () => import('./faq/faq.component').then(c => c.FaqComponent),
    title: 'FAQ - Winners Society'
  },
  {
    path: 'terms',
    loadComponent: () => import('./legal/terms.component').then(c => c.TermsComponent),
    title: 'Terms and Conditions - Winners Society'
  },
  {
    path: 'privacy',
    loadComponent: () => import('./legal/privacy.component').then(c => c.PrivacyComponent),
    title: 'Privacy Policy - Winners Society'
  }
];
