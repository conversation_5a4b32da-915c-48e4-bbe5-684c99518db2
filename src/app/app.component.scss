// Main app container - Full width implementation
.app-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  width: 100%;
  background-color: #f5f7fa;
  position: relative;
  overflow-x: hidden;
}

// Member layout
.member-layout {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100vw;
  height: 100vh;
  z-index: 1050;
}

// Auth layout - Full screen with header, no footer
.auth-layout {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100vw;
  height: 100vh;
  z-index: 1040;
  display: flex;
  flex-direction: column;
  overflow: hidden;

  // Header styling within auth layout
  .app-header {
    position: relative;
    top: 0;
    z-index: 1041;
    width: 100%;
    flex-shrink: 0;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    background-color: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
  }

  // Auth main content area
  .auth-main {
    flex: 1;
    width: 100%;
    height: 100%;
    overflow-y: auto;
    overflow-x: hidden;
  }
}

// Main layout structure - Full width
.main-layout {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  width: 100%;
}

// Header styling - Full width
.app-header {
  position: sticky;
  top: 0;
  z-index: 1000;
  width: 100%;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  transition: box-shadow 0.3s ease;
}

// Main content area - Full width
.app-main {
  flex: 1;
  width: 100%;
  padding: 0;
}

// Content container - Full width by default for modern pages
.content-container {
  width: 100%;

  // Apply container styling only for non-full-width pages (like auth pages)
  &:not(.full-width) {
    max-width: 1200px;
    margin: 0 auto;
    background-color: white;
    border-radius: 16px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
    padding: 2rem;
    animation: fadeIn 0.3s ease-out;

    @media (max-width: 768px) {
      padding: 1.5rem;
      border-radius: 12px;
      margin: 0 1rem;
    }

    @media (max-width: 576px) {
      padding: 1.25rem;
      border-radius: 10px;
      margin: 0 0.5rem;
    }
  }

  // Full-width pages (home, modern pages)
  &.full-width {
    max-width: none;
    padding: 0;
    background-color: transparent;
    box-shadow: none;
    border-radius: 0;
    margin: 0;
  }
}

// Footer styling
.app-footer {
  margin-top: auto;
}

// Admin layout
.admin-layout {
  min-height: 100vh;
  width: 100%;
}

// Animations
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

// Reduced Motion
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}