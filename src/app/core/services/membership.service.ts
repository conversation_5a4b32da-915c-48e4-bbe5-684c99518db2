import { Injectable } from '@angular/core';
import { Observable, of, throwError } from 'rxjs';
import { catchError, map } from 'rxjs/operators';
import { ApiService } from './api.service';
import {
  MembershipTier,
  Subscription,
  MembershipHistoryItem,
  SubscriptionRequest,
  MembershipTiersResponse,
  AutoRenewRequest
} from '../models/membership.model';

/**
 * Service for managing user memberships
 */
@Injectable({
  providedIn: 'root'
})
export class MembershipService {
  private readonly BASE_PATH = 'memberships';

  constructor(private apiService: ApiService) { }

  /**
   * Get user's current active membership
   * Returns the current membership details or throws a 404 error if not found
   */
  getCurrentMembership(): Observable<Subscription> {
    return this.apiService.get<{success: boolean, data: any}>(`${this.BASE_PATH}/current`)
      .pipe(
        map(response => {
          if (response.success && response.data) {
            const transformedData = this.transformMembershipData(response.data);
            return transformedData;
          }
          throw new Error('Invalid membership data format');
        }),
        catchError(error => {
          if (error.status === 404) {
            return throwError(() => new Error('No active membership found'));
          }
          return throwError(() => error);
        })
      );
  }

  /**
   * Get available membership tiers
   * Returns all active membership tiers available for purchase
   */
  getAvailableTiers(): Observable<MembershipTier[]> {
    return this.apiService.get<{success: boolean, data: any[]}>(`${this.BASE_PATH}/available`)
      .pipe(
        map(response => {
          if (response.success && Array.isArray(response.data)) {
            // Add frontend specific properties to each tier
            return response.data.map(tier => ({
              ...tier,
              currency: tier.currency || 'USD', // Use API currency or default to USD
              isPopular: tier.name === 'Premium', // Mark Premium as popular
              benefits: tier.features.length > 2 ? tier.features.slice(2) : [] // Use some features as benefits
            }));
          }
          return [];
        })
      );
  }

  /**
   * Get public membership tiers
   * Returns all active membership tiers available for public viewing
   * This endpoint doesn't require authentication
   */
  getPublicMembershipTiers(): Observable<MembershipTier[]> {
    return this.apiService.get<{success: boolean, data: any[]}>('membership-tiers')
      .pipe(
        map(response => {
          if (response.success && Array.isArray(response.data)) {
            // Add frontend specific properties to each tier
            return response.data
              .filter(tier => tier.isActive) // Only show active tiers
              .map(tier => ({
                ...tier,
                currency: tier.currency || 'USD', // Use API currency or default to USD
                isPopular: tier.name === 'Premium', // Mark Premium as popular
                benefits: tier.features.length > 2 ? tier.features.slice(2) : [] // Use some features as benefits
              }));
          }
          return [];
        })
      );
  }

  /**
   * Subscribe to a membership plan
   * @param subscriptionRequest The subscription request containing tier ID and payment info
   */
  subscribeToPlan(subscriptionRequest: SubscriptionRequest): Observable<Subscription> {
    return this.apiService.post<{success: boolean, data: any}>(`${this.BASE_PATH}/subscribe`, subscriptionRequest)
      .pipe(
        map(response => {
          if (response.success && response.data) {
            return this.transformMembershipData(response.data);
          }
          throw new Error('Failed to subscribe to plan');
        })
      );
  }

  /**
   * Cancel current membership
   * This will cancel the membership (stops auto-renewal) but doesn't end it immediately
   */
  cancelMembership(): Observable<Subscription> {
    return this.apiService.post<{success: boolean, data: any}>(`${this.BASE_PATH}/cancel`, {})
      .pipe(
        map(response => {
          if (response.success && response.data) {
            return this.transformMembershipData(response.data);
          }
          throw new Error('Failed to cancel membership');
        })
      );
  }

  /**
   * Update auto-renewal settings
   * @param autoRenew Whether to auto-renew the membership
   */
  updateAutoRenewal(autoRenew: boolean): Observable<Subscription> {
    const request: AutoRenewRequest = { autoRenew };
    return this.apiService.put<{success: boolean, data: any}>(`${this.BASE_PATH}/auto-renew`, request)
      .pipe(
        map(response => {
          if (response.success && response.data) {
            return this.transformMembershipData(response.data);
          }
          throw new Error('Failed to update auto-renewal settings');
        })
      );
  }

  /**
   * Helper method to transform API membership data to our Subscription model
   * @param data Raw API membership data
   * @returns Formatted Subscription object
   */
  private transformMembershipData(data: any): Subscription {
    const tierData = data.membershipTier;

    // Validate all dates
    const startDate = this.validateDate(data.startDate);
    const endDate = this.validateDate(data.endDate);
    const memberSince = this.validateDate(data.createdAt);
    const nextBillingDate = data.endDate ? this.validateDate(data.endDate) : null;

    return {
      id: data.id,
      userId: data.userId,
      tierId: data.membershipTierId,
      tierName: tierData?.name || 'Unknown',
      status: (data.status || '').toLowerCase() as 'active' | 'canceled' | 'expired' | 'pending',
      startDate: startDate || new Date(), // Fallback to current date if invalid
      endDate: endDate || new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // Fallback to 30 days from now if invalid
      nextBillingDate: nextBillingDate,
      paymentMethod: data.paymentMethodId,
      autoRenew: data.autoRenew,
      entriesRemaining: tierData?.entryAllocation || 0,
      entriesTotal: tierData?.entryAllocation || 0,
      memberSince: memberSince || new Date() // Fallback to current date if invalid
    };
  }

  /**
   * Upgrade membership to a higher tier
   * @param tierId The ID of the new tier to upgrade to
   */
  upgradeMembership(tierId: string): Observable<Subscription> {
    return this.apiService.post<{success: boolean, data: any}>(`${this.BASE_PATH}/upgrade`, { tierId })
      .pipe(
        map(response => {
          if (response.success && response.data) {
            return this.transformMembershipData(response.data);
          }
          throw new Error('Failed to upgrade membership');
        })
      );
  }

  /**
   * Get membership history
   * Returns the user's membership history including past memberships
   */
  getMembershipHistory(): Observable<MembershipHistoryItem[]> {
    return this.apiService.get<{success: boolean, data: any[]}>(`${this.BASE_PATH}/history`)
      .pipe(
        map(response => {
          if (response.success && Array.isArray(response.data)) {
            return response.data.map(item => {
              // Validate dates before creating Date objects
              const startDate = this.validateDate(item.startDate);
              const endDate = this.validateDate(item.endDate);

              return {
                id: item.id,
                tierName: item.membershipTier?.name || 'Unknown',
                startDate: startDate,
                endDate: endDate,
                status: (item.status || '').toLowerCase() as 'active' | 'canceled' | 'expired',
                price: item.membershipTier?.price || 0,
                currency: item.membershipTier?.currency || 'USD'
              };
            });
          }
          return [];
        })
      );
  }

  /**
   * Helper method to validate and parse dates
   * @param dateStr Date string to validate
   * @returns Valid Date object or null if invalid
   */
  private validateDate(dateStr: string | null | undefined): Date | null {
    if (!dateStr) {
      return null;
    }

    const date = new Date(dateStr);
    return isNaN(date.getTime()) ? null : date;
  }

  /**
   * Check if a user has an active membership
   * Returns true if the user has an active membership, false otherwise
   */
  hasActiveMembership(): Observable<boolean> {
    return this.getCurrentMembership().pipe(
      map(() => true),
      catchError(() => of(false))
    );
  }
}