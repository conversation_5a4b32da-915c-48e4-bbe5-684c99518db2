import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { AuthUser } from '../models/user.model';
import { Subscription } from '../models/membership.model';
import { MembershipService } from './membership.service';
import { AuthService } from './auth.service';

/**
 * Shared user context for membership info (sidebar/header)
 */
@Injectable({ providedIn: 'root' })
export class UserContextService {
  private membershipSubject = new BehaviorSubject<Subscription | null>(null);
  public membership$ = this.membershipSubject.asObservable();

  constructor(
    private membershipService: MembershipService,
    private authService: AuthService
  ) {
    // Listen for user changes and refresh membership
    this.authService.currentUser$.subscribe(user => {
      if (user) {
        this.refreshMembership();
      } else {
        this.membershipSubject.next(null);
      }
    });
  }

  refreshMembership() {
    this.membershipService.getCurrentMembership().subscribe({
      next: (membership) => this.membershipSubject.next(membership),
      error: () => this.membershipSubject.next(null)
    });
  }
}
