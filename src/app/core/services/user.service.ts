import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../../environments/environment';

export interface UserProfile {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  profileImage?: string;
  role: string;
  isVerified: boolean;
  preferences?: Record<string, any>;
  lastLoginAt?: Date;
  createdAt: Date;
  updatedAt: Date;
}

export interface UpdateProfileRequest {
  firstName?: string;
  lastName?: string;
  profileImage?: string;
  preferences?: Record<string, any>;
}

export interface ChangePasswordRequest {
  currentPassword: string;
  newPassword: string;
  confirmPassword: string;
}

export interface UserPreferences {
  emailNotifications: {
    giveawayUpdates: boolean;
    winnerAnnouncements: boolean;
    membershipUpdates: boolean;
    promotionalEmails: boolean;
  };
  privacy: {
    showProfileInWinners: boolean;
    allowContactFromOtherMembers: boolean;
  };
  display: {
    theme: 'light' | 'dark' | 'auto';
    language: string;
    timezone: string;
  };
}

export interface UserEntry {
  id: string;
  giveawayId: string;
  giveawayTitle: string;
  entryDate: Date;
  status: 'active' | 'expired' | 'winner';
  entryMethod: string;
}

export interface UserWin {
  id: string;
  giveawayId: string;
  giveawayTitle: string;
  prizeTitle: string;
  prizeValue: number;
  winDate: Date;
  status: 'pending' | 'claimed' | 'shipped' | 'delivered';
  claimDeadline?: Date;
}

export interface UserTransaction {
  id: string;
  type: 'membership' | 'entry' | 'refund';
  amount: number;
  currency: string;
  status: 'pending' | 'completed' | 'failed' | 'refunded';
  description: string;
  date: Date;
  paymentMethod?: string;
  invoiceUrl?: string;
}

@Injectable({
  providedIn: 'root'
})
export class UserService {
  private apiUrl = environment.apiUrl;

  constructor(private http: HttpClient) { }

  // Get users with pagination and filters for admin dashboard
  getAdminUsers(params: any): Observable<any> {
    let httpParams = new HttpParams();

    // Add each parameter to the HttpParams object
    Object.keys(params).forEach(key => {
      if (params[key] !== null && params[key] !== undefined && params[key] !== '') {
        httpParams = httpParams.set(key, params[key]);
      }
    });

    return this.http.get(`${this.apiUrl}/admin/dashboard/stats/users`, { params: httpParams });
  }

  // Get user details by ID
  getUserById(id: string): Observable<any> {
    return this.http.get(`${this.apiUrl}/admin/users/${id}`);
  }

  // Create a new user
  createUser(userData: any): Observable<any> {
    return this.http.post(`${this.apiUrl}/admin/users`, userData);
  }

  // Update user
  updateUser(id: string, userData: any): Observable<any> {
    return this.http.put(`${this.apiUrl}/admin/users/${id}`, userData);
  }

  // Delete user
  deleteUser(id: string): Observable<any> {
    return this.http.delete(`${this.apiUrl}/admin/users/${id}`);
  }

  // Change user role
  changeUserRole(id: string, role: string): Observable<any> {
    return this.http.put(`${this.apiUrl}/admin/users/${id}`, { role });
  }

  // Toggle user status (active/inactive)
  toggleUserStatus(id: string, status: string): Observable<any> {
    return this.http.put(`${this.apiUrl}/admin/users/${id}`, { status });
  }

  // Profile management methods for authenticated users

  /**
   * Get current user's profile
   */
  getProfile(): Observable<{ success: boolean; data: UserProfile }> {
    return this.http.get<{ success: boolean; data: UserProfile }>(`${this.apiUrl}/users/profile`);
  }

  /**
   * Get full profile (user + profile data)
   */
  getFullProfile(): Observable<{ success: boolean; data: { user: UserProfile; profile: any } }> {
    return this.http.get<{ success: boolean; data: { user: UserProfile; profile: any } }>(`${this.apiUrl}/users/full-profile`);
  }

  /**
   * Update user profile
   */
  updateProfile(profileData: UpdateProfileRequest): Observable<{ success: boolean; data: UserProfile }> {
    return this.http.put<{ success: boolean; data: UserProfile }>(`${this.apiUrl}/users/profile`, profileData);
  }

  /**
   * Change password
   */
  changePassword(passwordData: ChangePasswordRequest): Observable<{ success: boolean; message: string }> {
    return this.http.post<{ success: boolean; message: string }>(`${this.apiUrl}/users/profile/password`, passwordData);
  }

  /**
   * Get user preferences
   */
  getPreferences(): Observable<{ success: boolean; data: UserPreferences }> {
    return this.http.get<{ success: boolean; data: UserPreferences }>(`${this.apiUrl}/users/preferences`);
  }

  /**
   * Update user preferences
   */
  updatePreferences(preferences: UserPreferences): Observable<{ success: boolean; data: UserPreferences }> {
    return this.http.put<{ success: boolean; data: UserPreferences }>(`${this.apiUrl}/users/preferences`, preferences);
  }

  /**
   * Upload profile image
   */
  uploadProfileImage(imageFile: File): Observable<{ success: boolean; data: UserProfile }> {
    const formData = new FormData();
    formData.append('image', imageFile);
    return this.http.post<{ success: boolean; data: UserProfile }>(`${this.apiUrl}/users/profile/image`, formData);
  }

  /**
   * Get user's giveaway entries
   */
  getUserEntries(page = 1, limit = 10): Observable<{ success: boolean; data: { entries: UserEntry[]; total: number; page: number; totalPages: number } }> {
    const params = new HttpParams()
      .set('page', page.toString())
      .set('limit', limit.toString());
    return this.http.get<{ success: boolean; data: { entries: UserEntry[]; total: number; page: number; totalPages: number } }>(`${this.apiUrl}/users/entries`, { params });
  }

  /**
   * Get user's wins
   */
  getUserWins(page = 1, limit = 10): Observable<{ success: boolean; data: { wins: UserWin[]; total: number; page: number; totalPages: number } }> {
    const params = new HttpParams()
      .set('page', page.toString())
      .set('limit', limit.toString());
    return this.http.get<{ success: boolean; data: { wins: UserWin[]; total: number; page: number; totalPages: number } }>(`${this.apiUrl}/users/wins`, { params });
  }

  /**
   * Get user's transaction history
   */
  getUserTransactions(page = 1, limit = 10): Observable<{ success: boolean; data: { transactions: UserTransaction[]; total: number; page: number; totalPages: number } }> {
    const params = new HttpParams()
      .set('page', page.toString())
      .set('limit', limit.toString());
    return this.http.get<{ success: boolean; data: { transactions: UserTransaction[]; total: number; page: number; totalPages: number } }>(`${this.apiUrl}/users/transactions`, { params });
  }
}