import { Injectable, Inject } from '@angular/core';
import { DOCUMENT } from '@angular/common';
import { Observable, from, of, throwError } from 'rxjs';
import { switchMap, catchError } from 'rxjs/operators';
import { environment } from '../../../environments/environment';
import { loadStripe, Stripe, StripeElements, StripeCardElement } from '@stripe/stripe-js';

/**
 * Service for handling Stripe.js integration
 */
@Injectable({
  providedIn: 'root'
})
export class StripeService {
  private stripe: Stripe | null = null;
  private elements: StripeElements | null = null;
  private cardElement: StripeCardElement | null = null;
  private stripeLoaded = false;
  private readonly STRIPE_PUBLISHABLE_KEY = environment.stripe.publishableKey;
  private readonly STRIPE_OPTIONS = environment.stripe.options;

  constructor(@Inject(DOCUMENT) private document: Document) {}

  /**
   * Load the Stripe.js library
   * @returns Observable that resolves when Stripe is loaded
   */
  loadStripe(): Observable<boolean> {
    if (this.stripeLoaded && this.stripe) {
      return of(true);
    }

    return from(loadStripe(this.STRIPE_PUBLISHABLE_KEY)).pipe(
      switchMap(stripe => {
        if (stripe) {
          this.stripe = stripe;
          this.stripeLoaded = true;
          return of(true);
        } else {
          return throwError(() => new Error('Failed to load Stripe'));
        }
      }),
      catchError(error => {
        console.error('Error loading Stripe', error);
        return throwError(() => error);
      })
    );
  }

  /**
   * Create Stripe Elements
   * @param options Optional configuration for Elements
   * @returns The created Elements instance
   */
  createElements(options: any = {}): StripeElements | null {
    if (!this.stripe) {
      throw new Error('Stripe not initialized. Call loadStripe() first.');
    }

    // Merge default options from environment with provided options
    const elementOptions = { ...environment.stripe.options, ...options };
    this.elements = this.stripe.elements(elementOptions);
    return this.elements;
  }

  /**
   * Create a card Element
   * @param elementOptions Options for the card Element
   * @param containerSelector CSS selector for the container element
   * @returns The created card Element
   */
  createCardElement(elementOptions: any = {}, containerSelector: string): StripeCardElement | null {
    if (!this.elements) {
      this.createElements();
    }

    if (!this.elements) {
      console.error('Failed to create Stripe Elements');
      return null;
    }

    // Default styling options
    const defaultOptions = {
      style: {
        base: {
          color: '#32325d',
          fontFamily: '"Helvetica Neue", Helvetica, sans-serif',
          fontSmoothing: 'antialiased',
          fontSize: '16px',
          '::placeholder': {
            color: '#aab7c4'
          }
        },
        invalid: {
          color: '#fa755a',
          iconColor: '#fa755a'
        }
      }
    };

    // Merge default options with provided options
    const options = { ...defaultOptions, ...elementOptions };

    // Create the card Element
    this.cardElement = this.elements.create('card', options) as StripeCardElement;

    // Mount the card Element to the container
    const container = this.document.querySelector(containerSelector);
    if (container) {
      this.cardElement.mount(containerSelector);
    } else {
      console.error(`Container element not found: ${containerSelector}`);
    }

    return this.cardElement;
  }

  /**
   * Create a payment method from the card Element
   * @param billingDetails Optional billing details
   * @returns Observable of the created payment method
   */
  createPaymentMethod(billingDetails: any = {}): Observable<any> {
    if (!this.stripe || !this.cardElement) {
      return throwError(() => new Error('Stripe or card element not initialized'));
    }

    return from(this.stripe.createPaymentMethod({
      type: 'card',
      card: this.cardElement,
      billing_details: billingDetails
    })).pipe(
      switchMap((result: any) => {
        if (result.error) {
          // Format Stripe error for better user experience
          const errorMessage = this.formatStripeError(result.error);
          return throwError(() => new Error(errorMessage));
        }
        return of(result.paymentMethod);
      })
    );
  }

  /**
   * Format Stripe error messages for better user experience
   * @param error The Stripe error object
   * @returns A user-friendly error message
   */
  private formatStripeError(error: any): string {
    // Default error message
    let message = 'An error occurred while processing your payment.';

    if (error) {
      if (error.type === 'card_error') {
        switch (error.code) {
          case 'card_declined':
            message = 'Your card was declined. Please try another payment method.';
            break;
          case 'expired_card':
            message = 'Your card has expired. Please try another card.';
            break;
          case 'incorrect_cvc':
            message = 'The security code (CVC) is incorrect. Please check and try again.';
            break;
          case 'processing_error':
            message = 'An error occurred while processing your card. Please try again.';
            break;
          case 'incorrect_number':
            message = 'The card number is incorrect. Please check and try again.';
            break;
          default:
            message = error.message || message;
        }
      } else {
        message = error.message || message;
      }
    }

    return message;
  }

  /**
   * Confirm a card payment with the provided client secret
   * @param clientSecret The client secret from the PaymentIntent
   * @param paymentMethodId Optional payment method ID (if not using the card Element)
   * @returns Observable of the payment confirmation result
   */
  confirmCardPayment(clientSecret: string, paymentMethodId?: string): Observable<any> {
    if (!this.stripe) {
      return throwError(() => new Error('Stripe not initialized'));
    }

    const paymentOptions: any = {
      payment_method: paymentMethodId ? { payment_method: paymentMethodId } : { card: this.cardElement }
    };

    return from(this.stripe.confirmCardPayment(clientSecret, paymentOptions)).pipe(
      switchMap((result: any) => {
        if (result.error) {
          // Format Stripe error for better user experience
          const errorMessage = this.formatStripeError(result.error);
          return throwError(() => new Error(errorMessage));
        }
        return of(result.paymentIntent);
      })
    );
  }

  /**
   * Clean up resources
   */
  destroyCardElement(): void {
    if (this.cardElement) {
      try {
        this.cardElement.unmount();
      } catch (error) {
        console.error('Error unmounting card element:', error);
      }
      this.cardElement = null;
    }
  }
}
