import { Injectable } from '@angular/core';
import { MatSnackBar } from '@angular/material/snack-bar';

/**
 * Service for displaying notification messages
 */
@Injectable({
  providedIn: 'root'
})
export class NotificationService {
  // Default duration for notifications
  private duration = 5000;

  constructor(private snackBar: MatSnackBar) { }

  /**
   * Display a success notification
   * @param message The message to display
   * @param duration Optional duration in milliseconds
   */
  success(message: string, duration?: number): void {
    this.showNotification(message, 'success-notification', duration);
  }

  /**
   * Display an error notification
   * @param message The message to display
   * @param duration Optional duration in milliseconds
   */
  error(message: string, duration?: number): void {
    this.showNotification(message, 'error-notification', duration);
  }

  /**
   * Display a warning notification
   * @param message The message to display
   * @param duration Optional duration in milliseconds
   */
  warning(message: string, duration?: number): void {
    this.showNotification(message, 'warning-notification', duration);
  }

  /**
   * Display an info notification
   * @param message The message to display
   * @param duration Optional duration in milliseconds
   */
  info(message: string, duration?: number): void {
    this.showNotification(message, 'info-notification', duration);
  }

  /**
   * Shows a notification using Angular Material snackbar
   * @param message The message to display
   * @param panelClass CSS class for styling
   * @param duration Optional duration in milliseconds
   */
  private showNotification(message: string, panelClass: string, duration?: number): void {
    this.snackBar.open(message, 'Close', {
      duration: duration || this.duration,
      horizontalPosition: 'center',
      verticalPosition: 'bottom',
      panelClass: [panelClass]
    });
  }
}
