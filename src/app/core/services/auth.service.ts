import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable, of, throwError } from 'rxjs';
import { tap, map, catchError } from 'rxjs/operators';
import { Router } from '@angular/router';

import { ApiService } from './api.service';
import { NotificationService } from './notification.service';
import { AuthUser, User } from '../models/user.model';

/**
 * Service for handling authentication-related operations
 */
@Injectable({
  providedIn: 'root'
})
export class AuthService {
  // BehaviorSubject to track current user state
  private currentUserSubject = new BehaviorSubject<AuthUser | null>(null);
  public currentUser$ = this.currentUserSubject.asObservable();

  // Observable for checking if user is authenticated
  public isAuthenticated$ = this.currentUser$.pipe(
    map(user => !!user)
  );

  // Observable for checking if user is an admin
  public isAdmin$ = this.currentUser$.pipe(
    map(user => !!user && (user.role === 'admin' || user.role === 'ADMIN'))
  );

  // Store URL so we can redirect after logging in
  public redirectUrl: string | null = null;

  // Store URL for admin redirects
  public adminRedirectUrl: string | null = null;

  constructor(
    private apiService: ApiService,
    private notificationService: NotificationService,
    private router: Router
  ) {
    // Load user from localStorage on service initialization
    this.loadUserFromStorage();
  }

  /**
   * Log in a regular member with email and password
   * @param email User email
   * @param password User password
   * @returns Observable of the authenticated user
   */
  login(email: string, password: string): Observable<AuthUser> {
    return this.apiService.post<{ success: boolean, data: { user: AuthUser, accessToken: string, refreshToken: string, expiresIn: number } }>('auth/login', { email, password })
      .pipe(
        tap(response => {
          // Check if user has member/USER role
          const user = response.data.user;
          if (user.role === 'ADMIN' || user.role === 'admin') {
            throw new Error('Invalid role for member login');
          }

          // Store token and user data
          this.storeAuthData(response.data.accessToken, user, response.data.refreshToken, response.data.expiresIn);
          this.notificationService.success('Login successful');
        }),
        map(response => response.data.user),
        catchError(error => {
          if (error.message === 'Invalid role for member login') {
            this.notificationService.error('Please use the admin login page for administrator access.');
          } else {
            this.notificationService.error('Login failed. Please check your credentials.');
          }
          return throwError(() => error);
        })
      );
  }

  /**
   * Log in an admin user with email and password
   * @param email Admin email
   * @param password Admin password
   * @returns Observable of the authenticated admin user
   */
  loginAdmin(email: string, password: string): Observable<AuthUser> {
    return this.apiService.post<{ success: boolean, data: { user: AuthUser, accessToken: string, refreshToken: string, expiresIn: number } }>('auth/login', { email, password })
      .pipe(
        tap(response => {
          // Verify the user has admin role
          const user = response.data.user;
          if (!user.role || (user.role !== 'ADMIN' && user.role !== 'admin')) {
            throw new Error('User is not an administrator');
          }

          // Store token and admin user data
          this.storeAuthData(response.data.accessToken, user, response.data.refreshToken, response.data.expiresIn);
          this.notificationService.success('Admin login successful');
        }),
        map(response => response.data.user),
        catchError(error => {
          if (error.message === 'User is not an administrator') {
            this.notificationService.error('This account does not have administrator privileges.');
          } else {
            this.notificationService.error('Login failed. Please check your credentials.');
          }
          return throwError(() => error);
        })
      );
  }

  /**
   * Register a new user
   * @param userData User registration data
   * @returns Observable of the registration result
   */
  register(userData: any): Observable<any> {
    return this.apiService.post<{ success: boolean, data: { user: AuthUser, accessToken?: string, refreshToken?: string, expiresIn?: number } }>('auth/register', userData)
      .pipe(
        tap(response => {
          // Do not auto-login after registration - user must verify email first
          // The user will need to login manually after email verification
          console.log('Registration successful, user must verify email before logging in');
        }),
        catchError(error => {
          let errorMessage = 'Registration failed. Please try again.';

          if (error.status === 409) {
            errorMessage = 'This email is already registered. Please use a different email or try logging in.';
          } else if (error.status === 400) {
            errorMessage = 'Please check your information and try again.';
          }

          this.notificationService.error(errorMessage);
          return throwError(() => error);
        })
      );
  }

  /**
   * Set the session data after successful authentication
   * @param authData Authentication data including user and tokens
   */
  setSession(authData: { user: AuthUser, accessToken: string, refreshToken: string, expiresIn: number }): void {
    this.storeAuthData(
      authData.accessToken,
      authData.user,
      authData.refreshToken,
      authData.expiresIn
    );
  }

  /**
   * Log out the current user
   */
  logout(): void {
    // Clear stored auth data
    localStorage.removeItem('auth_token');
    localStorage.removeItem('user');

    // Clear current user subject
    this.currentUserSubject.next(null);

    // Navigate to home page
    this.router.navigate(['/']);

    this.notificationService.info('You have been logged out');
  }

  /**
   * Send a password reset request for the given email
   * @param email User's email address
   * @returns Observable of the request result
   */
  forgotPassword(email: string): Observable<any> {
    return this.apiService.post<any>('auth/forgot-password', { email })
      .pipe(
        tap(() => {
          this.notificationService.success('Password reset link sent to your email');
        }),
        catchError(error => {
          this.notificationService.error('Failed to send reset link. Please try again.');
          return throwError(() => error);
        })
      );
  }

  /**
   * Reset a user's password using a token
   * @param token Reset token from email
   * @param newPassword New password
   * @param confirmPassword Confirmation of new password
   * @returns Observable of the reset result
   */
  resetPassword(token: string, newPassword: string, confirmPassword?: string): Observable<any> {
    const payload = {
      token,
      newPassword,
      confirmPassword: confirmPassword || newPassword
    };

    return this.apiService.post<any>('auth/reset-password', payload)
      .pipe(
        tap(() => {
          this.notificationService.success('Password has been reset successfully');
        }),
        catchError(error => {
          this.notificationService.error('Failed to reset password. Token may be invalid or expired.');
          return throwError(() => error);
        })
      );
  }

  /**
   * Verify a user's email using a token (redirect endpoint)
   * @param token Verification token from email
   * @returns Observable of the verification result
   */
  verifyEmail(token: string): Observable<any> {
    return this.apiService.get<any>(`auth/verify-email/${token}`)
      .pipe(
        tap(() => {
          this.notificationService.success('Email verified successfully');
          // Refresh user data if user is logged in
          if (this.currentUserSubject.value) {
            this.refreshUserData();
          }
        }),
        catchError(error => {
          this.notificationService.error('Email verification failed. Token may be invalid or expired.');
          return throwError(() => error);
        })
      );
  }

  /**
   * Verify a user's email using a token (JSON API endpoint)
   * @param token Verification token from email
   * @returns Observable of the verification result with detailed response
   */
  verifyEmailAPI(token: string): Observable<{ success: boolean; message: string; data?: { email: string; isVerified: boolean } }> {
    return this.apiService.get<{ success: boolean; message: string; data?: { email: string; isVerified: boolean } }>(`auth/api/verify-email/${token}`)
      .pipe(
        tap((response) => {
          // Don't show notification here - let the component handle it
          // Refresh user data if user is logged in
          if (this.currentUserSubject.value && response.data?.isVerified) {
            this.refreshUserData();
          }
        }),
        catchError(error => {
          // Don't show notification here - let the component handle it
          return throwError(() => error);
        })
      );
  }

  /**
   * Resend email verification for the current user
   * @returns Observable of the resend result
   */
  resendVerificationEmail(): Observable<any> {
    return this.apiService.post<any>('auth/resend-verification', {})
      .pipe(
        tap(() => {
          this.notificationService.success('Verification email sent successfully');
        }),
        catchError(error => {
          this.notificationService.error('Failed to send verification email. Please try again.');
          return throwError(() => error);
        })
      );
  }

  /**
   * Resend email verification for a specific email (public endpoint)
   * @param email User's email address
   * @returns Observable of the resend result
   */
  resendVerificationEmailByEmail(email: string): Observable<any> {
    return this.apiService.post<any>('auth/resend-verification-public', { email })
      .pipe(
        tap(() => {
          this.notificationService.success('Verification email sent successfully');
        }),
        catchError(error => {
          this.notificationService.error('Failed to send verification email. Please try again.');
          return throwError(() => error);
        })
      );
  }

  /**
   * Get the current authentication token
   * @returns The current JWT token or null if not authenticated
   */
  getToken(): string | null {
    return localStorage.getItem('auth_token');
  }

  /**
   * Check if the current token is valid
   * @returns Observable boolean indicating validity
   */
  validateToken(): Observable<boolean> {
    const token = this.getToken();
    if (!token) {
      return of(false);
    }

    return this.apiService.get<{ valid: boolean }>('auth/validate-token')
      .pipe(
        map(response => response.valid),
        catchError(() => {
          // Clear invalid auth data
          this.logout();
          return of(false);
        })
      );
  }

  /**
   * Refresh the user's data from the server
   */
  refreshUserData(): void {
    if (!this.getToken()) {
      return;
    }

    this.apiService.get<{ user: AuthUser }>('auth/me')
      .pipe(
        catchError(() => {
          this.logout();
          return of(null);
        })
      )
      .subscribe(response => {
        if (response && response.user) {
          this.storeUser(response.user);
        }
      });
  }

  /**
   * Store authentication data in localStorage and update current user
   * @param token JWT token
   * @param user User data
   * @param refreshToken Optional refresh token
   * @param expiresIn Optional token expiration time in seconds
   */
  private storeAuthData(token: string, user: AuthUser, refreshToken?: string, expiresIn?: number): void {
    localStorage.setItem('auth_token', token);

    if (refreshToken) {
      localStorage.setItem('refresh_token', refreshToken);
    }

    if (expiresIn) {
      const expiryTime = new Date().getTime() + expiresIn * 1000;
      localStorage.setItem('token_expiry', expiryTime.toString());
    }

    this.storeUser(user);
  }

  /**
   * Update user profile data in the current user state
   * @param profileData Updated profile data
   */
  updateUserProfile(profileData: any): void {
    const currentUser = this.currentUserSubject.value;
    if (currentUser) {
      const updatedUser = {
        ...currentUser,
        firstName: profileData.firstName || currentUser.firstName,
        lastName: profileData.lastName || currentUser.lastName,
        profileImage: profileData.profileImage || currentUser.profileImage,
        isVerified: profileData.isVerified !== undefined ? profileData.isVerified : currentUser.isVerified
      };
      this.storeUser(updatedUser);
    }
  }

  /**
   * Store user data in localStorage and update current user subject
   * @param user User data
   */
  private storeUser(user: AuthUser): void {
    localStorage.setItem('user', JSON.stringify(user));
    this.currentUserSubject.next(user);
  }

  /**
   * Load user data from localStorage on service initialization
   */
  private loadUserFromStorage(): void {
    const userJson = localStorage.getItem('user');
    if (userJson) {
      try {
        const user = JSON.parse(userJson);
        this.currentUserSubject.next(user);
      } catch (e) {
        // Invalid JSON in localStorage, clear it
        localStorage.removeItem('user');
        localStorage.removeItem('auth_token');
      }
    }
  }
}
