import { Injectable } from '@angular/core';
import { Observable, throwError } from 'rxjs';
import { catchError, map } from 'rxjs/operators';
import { ApiService } from './api.service';
import { NotificationService } from './notification.service';
import {
  PaymentIntent,
  PaymentMethod,
  Transaction,
  SavePaymentMethodRequest,
  ProcessPaymentRequest,
  CreatePaymentIntentRequest
} from '../models/payment.model';

/**
 * Service for handling payment operations with Stripe
 */
@Injectable({
  providedIn: 'root'
})
export class PaymentService {
  private readonly BASE_PATH = 'payments';

  constructor(
    private apiService: ApiService,
    private notificationService: NotificationService
  ) { }

  /**
   * Create a payment intent for a membership plan
   * @param request Payment intent creation request
   * @returns Observable of the payment intent
   */
  createPaymentIntent(request: CreatePaymentIntentRequest): Observable<PaymentIntent> {
    return this.apiService.post<{ success: boolean, data: PaymentIntent }>(`${this.BASE_PATH}/payment-intent`, request)
      .pipe(
        map(response => {
          if (response.success && response.data) {
            return response.data;
          }
          throw new Error('Failed to create payment intent');
        }),
        catchError(error => {
          this.notificationService.error('Failed to initialize payment. Please try again.');
          return throwError(() => error);
        })
      );
  }

  /**
   * Process a payment with Stripe
   * @param request Payment process request
   * @returns Observable of the transaction
   */
  processPayment(request: ProcessPaymentRequest): Observable<Transaction> {
    return this.apiService.post<{ success: boolean, data: Transaction }>(`${this.BASE_PATH}/process`, request)
      .pipe(
        map(response => {
          if (response.success && response.data) {
            return response.data;
          }
          throw new Error('Failed to process payment');
        }),
        catchError(error => {
          this.notificationService.error('Payment processing failed. Please try again or contact support.');
          return throwError(() => error);
        })
      );
  }

  /**
   * Save a payment method for future use
   * @param request Save payment method request
   * @returns Observable of the saved payment method
   */
  savePaymentMethod(request: SavePaymentMethodRequest): Observable<PaymentMethod> {
    return this.apiService.post<{ success: boolean, data: PaymentMethod }>(`${this.BASE_PATH}/payment-methods`, request)
      .pipe(
        map(response => {
          if (response.success && response.data) {
            return response.data;
          }
          throw new Error('Failed to save payment method');
        }),
        catchError(error => {
          this.notificationService.error('Failed to save payment method. Please try again.');
          return throwError(() => error);
        })
      );
  }

  /**
   * Get saved payment methods for the current user
   * @returns Observable of payment methods
   */
  getPaymentMethods(): Observable<PaymentMethod[]> {
    return this.apiService.get<{ success: boolean, data: PaymentMethod[] }>(`${this.BASE_PATH}/payment-methods`)
      .pipe(
        map(response => {
          if (response.success && response.data) {
            return response.data;
          }
          return [];
        }),
        catchError(error => {
          console.error('Failed to fetch payment methods', error);
          return throwError(() => error);
        })
      );
  }

  /**
   * Delete a saved payment method
   * @param paymentMethodId The ID of the payment method to delete
   * @returns Observable of the deletion result
   */
  deletePaymentMethod(paymentMethodId: string): Observable<boolean> {
    return this.apiService.delete<{ success: boolean }>(`${this.BASE_PATH}/payment-methods/${paymentMethodId}`)
      .pipe(
        map(response => response.success),
        catchError(error => {
          this.notificationService.error('Failed to delete payment method. Please try again.');
          return throwError(() => error);
        })
      );
  }

  /**
   * Set a payment method as default
   * @param paymentMethodId The ID of the payment method to set as default
   * @returns Observable of the updated payment method
   */
  setDefaultPaymentMethod(paymentMethodId: string): Observable<PaymentMethod> {
    return this.apiService.put<{ success: boolean, data: PaymentMethod }>(
      `${this.BASE_PATH}/payment-methods/${paymentMethodId}/default`,
      {}
    ).pipe(
      map(response => {
        if (response.success && response.data) {
          return response.data;
        }
        throw new Error('Failed to set default payment method');
      }),
      catchError(error => {
        this.notificationService.error('Failed to update default payment method. Please try again.');
        return throwError(() => error);
      })
    );
  }
}
