import { Injectable } from '@angular/core';
import {
  <PERSON>ttpRequest,
  HttpHandler,
  HttpEvent,
  HttpInterceptor
} from '@angular/common/http';
import { Observable } from 'rxjs';

// Note: This is a placeholder service that will be created later
import { AuthService } from '../services/auth.service';

/**
 * Interceptor that adds authorization token to outgoing HTTP requests
 */
@Injectable()
export class AuthInterceptor implements HttpInterceptor {

  constructor(private authService: AuthService) {}

  intercept(request: HttpRequest<unknown>, next: <PERSON>ttp<PERSON>and<PERSON>): Observable<HttpEvent<unknown>> {
    // Get the auth token from the service
    const authToken = this.authService.getToken();
    
    // Clone the request and replace the original headers with
    // cloned headers, updated with the authorization
    if (authToken) {
      request = request.clone({
        setHeaders: {
          Authorization: `Bearer ${authToken}`
        }
      });
    }
    
    // send cloned request with header to the next handler
    return next.handle(request);
  }
}
