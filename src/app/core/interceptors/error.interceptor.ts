import { Injectable } from '@angular/core';
import {
  HttpRequest,
  HttpHandler,
  HttpEvent,
  HttpInterceptor,
  HttpErrorResponse
} from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { Router } from '@angular/router';

// Note: This is a placeholder service that will be created later
import { AuthService } from '../services/auth.service';
import { NotificationService } from '../services/notification.service';

/**
 * Interceptor that handles HTTP errors globally
 */
@Injectable()
export class ErrorInterceptor implements HttpInterceptor {

  constructor(
    private authService: AuthService, 
    private router: Router,
    private notificationService: NotificationService
  ) {}

  intercept(request: HttpRequest<unknown>, next: HttpHandler): Observable<HttpEvent<unknown>> {
    return next.handle(request).pipe(
      catchError((error: HttpErrorResponse) => {
        let errorMessage = 'An unknown error occurred!';
        
        if (error.error instanceof ErrorEvent) {
          // Client-side error
          errorMessage = `Error: ${error.error.message}`;
        } else {
          // Server-side error
          if (error.status === 401) {
            // Handle unauthorized error (expired token, etc.)
            this.authService.logout();
            this.router.navigate(['/auth/login']);
            errorMessage = 'Your session has expired. Please log in again.';
          } else if (error.status === 403) {
            errorMessage = 'You do not have permission to perform this action.';
            this.router.navigate(['/dashboard']);
          } else {
            // Handle other server errors
            errorMessage = `Error Code: ${error.status}\\nMessage: ${error.message}`;
          }
        }
        
        // Show error notification
        this.notificationService.error(errorMessage);
        
        // Pass the error to the component
        return throwError(() => error);
      })
    );
  }
}
