import { inject } from '@angular/core';
import { CanActivateFn, Router } from '@angular/router';
import { AuthService } from '../services/auth.service';
import { map, take } from 'rxjs/operators';

/**
 * Guard that prevents authenticated users from accessing routes like login/signup
 * Redirects them to the dashboard if they're already logged in
 */
export const noAuthGuard: CanActivateFn = () => {
  const router = inject(Router);
  const authService = inject(AuthService);
  
  return authService.isAuthenticated$.pipe(
    take(1),
    map(isAuthenticated => {
      if (isAuthenticated) {
        // User is already authenticated, redirect to dashboard
        router.navigate(['/member/dashboard']);
        return false;
      }
      
      // User is not authenticated, allow access
      return true;
    })
  );
};
