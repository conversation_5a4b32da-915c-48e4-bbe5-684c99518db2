import { Injectable } from '@angular/core';
import { CanActivate, CanMatch, Route, UrlSegment, ActivatedRouteSnapshot, RouterStateSnapshot, Router, UrlTree } from '@angular/router';
import { Observable } from 'rxjs';
import { map, take } from 'rxjs/operators';

import { AuthService } from '../services/auth.service';
import { AuthUser } from '../models/user.model';

/**
 * Guard that prevents access to admin routes
 * Redirects to dashboard if user is not an admin
 */
@Injectable({
  providedIn: 'root'
})
export class AdminGuard implements CanActivate, CanMatch {

  constructor(private authService: AuthService, private router: Router) {}

  canActivate(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): Observable<boolean | UrlTree> | Promise<boolean | UrlTree> | boolean | UrlTree {
    return this.checkAdminRole();
  }

  canMatch(
    route: Route,
    segments: UrlSegment[]
  ): Observable<boolean | UrlTree> | Promise<boolean | UrlTree> | boolean | UrlTree {
    return this.checkAdminRole();
  }

  private checkAdminRole(): Observable<boolean | UrlTree> {
    return this.authService.currentUser$.pipe(
      take(1),
      map(user => {
        // Check if user exists and has admin role (support both 'admin' and 'ADMIN' values)
        if (user && ((user as AuthUser).role === 'admin' || (user as AuthUser).role === 'ADMIN')) {
          return true;
        }
        
        // Redirect to dashboard if not admin
        return this.router.createUrlTree(['/dashboard']);
      })
    );
  }
}
