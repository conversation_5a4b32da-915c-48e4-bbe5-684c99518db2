import { inject } from '@angular/core';
import { CanActivateFn, Router, ActivatedRouteSnapshot, RouterStateSnapshot } from '@angular/router';
import { AuthService } from '../services/auth.service';
import { map, take } from 'rxjs/operators';

/**
 * Guard that checks if the user has the required role(s) to access a route
 * Usage in routes:
 * {
 *   path: 'admin',
 *   canActivate: [roleGuard],
 *   data: { roles: ['admin'] }
 * }
 */
export const roleGuard: CanActivateFn = (
  route: ActivatedRouteSnapshot,
  state: RouterStateSnapshot
) => {
  const router = inject(Router);
  const authService = inject(AuthService);

  // Get required roles from route data
  const requiredRoles = route.data['roles'] as Array<string>;

  // If no roles specified, allow access
  if (!requiredRoles || requiredRoles.length === 0) {
    return true;
  }

  return authService.currentUser$.pipe(
    take(1),
    map(user => {
      // Check if user exists and has the required role
      if (!user) {
        router.navigate(['/auth/login'], { queryParams: { returnUrl: state.url } });
        return false;
      }

      // Check if user has any of the required roles
      const hasRequiredRole = requiredRoles.some(role =>
        user.role && user.role.toLowerCase() === role.toLowerCase()
      );

      if (!hasRequiredRole) {
        router.navigate(['/member/dashboard']);
        return false;
      }

      return true;
    })
  );
};

/**
 * Guard that checks if the user has the required membership tier to access a route
 * Usage in routes:
 * {
 *   path: 'vip-content',
 *   canActivate: [membershipTierGuard],
 *   data: { requiredTiers: ['premium', 'vip'] }
 * }
 */
export const membershipTierGuard: CanActivateFn = (
  route: ActivatedRouteSnapshot,
  state: RouterStateSnapshot
) => {
  const router = inject(Router);
  const authService = inject(AuthService);

  // Get required membership tiers from route data
  const requiredTiers = route.data['requiredTiers'] as Array<string>;

  // If no tiers specified, allow access
  if (!requiredTiers || requiredTiers.length === 0) {
    return true;
  }

  return authService.currentUser$.pipe(
    take(1),
    map(user => {
      // Check if user exists and has membership
      if (!user || !user.membershipTier) {
        router.navigate(['/auth/login'], { queryParams: { returnUrl: state.url } });
        return false;
      }

      // Check if user's membership tier is in the required tiers
      const hasRequiredTier = requiredTiers.some(tier =>
        user.membershipTier &&
        user.membershipTier.toLowerCase() === tier.toLowerCase()
      );

      if (!hasRequiredTier) {
        router.navigate(['/member/membership/upgrade'], {
          queryParams: { message: 'upgrade-required' }
        });
        return false;
      }

      return true;
    })
  );
};
