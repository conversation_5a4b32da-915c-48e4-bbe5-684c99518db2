/**
 * Models for payment processing with Stripe
 */

/**
 * Payment intent model returned from the backend
 * Matches the updated PaymentIntentResponseDto in the API documentation
 */
export interface PaymentIntent {
  id: string;
  clientSecret: string;
  amount: number;
  currency: string;
  status: PaymentIntentStatus;
}

/**
 * Payment intent status
 */
export type PaymentIntentStatus =
  | 'requires_payment_method'
  | 'requires_confirmation'
  | 'requires_action'
  | 'processing'
  | 'requires_capture'
  | 'canceled'
  | 'succeeded';

/**
 * Payment method model
 * Matches the PaymentMethodResponseDto in the API documentation
 */
export interface PaymentMethod {
  id: string;
  userId: string;
  type: string; // PaymentType enum
  stripePaymentMethodId: string;
  last4?: string;
  brand?: string;
  expiryMonth?: number;
  expiryYear?: number;
  holderName?: string;
  isDefault: boolean;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Payment method request
 * Matches the PaymentMethodCreateDto in the API documentation
 */
export interface SavePaymentMethodRequest {
  type: string; // PaymentType enum: 'CREDIT_CARD', etc.
  stripePaymentMethodId: string;
  isDefault?: boolean;
}

/**
 * Payment process request
 * Matches the updated DTO in the API documentation
 */
export interface ProcessPaymentRequest {
  paymentIntentId: string;
  paymentMethodId: string;
}

/**
 * Payment intent creation request
 * Matches the updated PaymentIntentCreateDto in the API documentation
 */
export interface CreatePaymentIntentRequest {
  planId: string;
  description?: string;
}

/**
 * Transaction model
 * Matches the updated TransactionResponseDto in the API documentation
 */
export interface Transaction {
  id: string;
  userId: string;
  amount: number;
  currency: string;
  status: 'PENDING' | 'COMPLETED' | 'FAILED' | 'REFUNDED';
  paymentMethod: string;
  paymentIntentId: string;
  description?: string;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Payment error model
 */
export interface PaymentError {
  type: 'card_error' | 'validation_error' | 'api_error' | 'server_error';
  code?: string;
  message: string;
  param?: string;
}
