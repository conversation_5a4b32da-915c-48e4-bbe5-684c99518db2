/**
 * Membership tier model
 */
export interface MembershipTier {
  id: string;
  name: string;
  price: number;
  currency?: string; // Added from frontend
  billingPeriod: 'MONTHLY' | 'YEARLY' | 'QUARTERLY';
  description: string;
  features: string[];
  benefits?: string[]; // Added from frontend
  entryAllocation: number;
  isPopular?: boolean; // Added from frontend
  duration: number;
  isActive: boolean;
}

/**
 * User subscription model (Active membership)
 */
export interface Subscription {
  id: string;
  userId: string;
  tierId: string;
  tierName: string;
  status: 'active' | 'canceled' | 'expired' | 'pending';
  startDate: Date;
  endDate: Date;
  nextBillingDate?: Date | null;
  paymentMethod?: string;
  autoRenew: boolean;
  entriesRemaining: number;
  entriesTotal: number;
  memberSince: Date;
}

/**
 * Membership history item
 */
export interface MembershipHistoryItem {
  id: string;
  tierName: string;
  startDate: Date | null;
  endDate: Date | null;
  status: 'active' | 'canceled' | 'expired';
  price: number;
  currency: string;
}

/**
 * Request model for subscribing to a plan
 */
export interface SubscriptionRequest {
  tierId: string;
  paymentMethodId?: string;
  couponCode?: string;
  autoRenew?: boolean;
}

/**
 * Response for available membership tiers
 */
export interface MembershipTiersResponse {
  success: boolean;
  data: MembershipTier[];
}

/**
 * Auto-renew update request
 */
export interface AutoRenewRequest {
  autoRenew: boolean;
}
