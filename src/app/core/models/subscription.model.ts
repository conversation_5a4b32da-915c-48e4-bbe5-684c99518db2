/**
 * Subscription-related models for the frontend
 */

export interface BillingAddress {
  line1: string;
  line2?: string;
  city: string;
  state?: string;
  postal_code: string;
  country: string;
}

export interface CreateSubscriptionRequest {
  priceId: string;
  paymentMethodId: string;
  billingAddress?: BillingAddress;
  trialPeriodDays?: number;
  autoRenew?: boolean;
}

export interface UpdateSubscriptionRequest {
  subscriptionId: string;
  newPriceId?: string;
  paymentMethodId?: string;
  billingAddress?: BillingAddress;
}

export interface SubscriptionDetails {
  id: string;
  customerId: string;
  status: SubscriptionStatus;
  currentPeriodStart: Date;
  currentPeriodEnd: Date;
  cancelAtPeriodEnd: boolean;
  canceledAt?: Date;
  trialStart?: Date;
  trialEnd?: Date;
  priceId: string;
  amount: number;
  currency: string;
  interval: string;
  paymentMethodId?: string;
  latestInvoiceId?: string;
  planName?: string;
  planDescription?: string;
}

export interface CustomerDetails {
  id: string;
  email: string;
  name?: string;
  defaultPaymentMethodId?: string;
  billingAddress?: BillingAddress;
}

export interface InvoiceDetails {
  id: string;
  subscriptionId: string;
  amount: number;
  currency: string;
  status: InvoiceStatus;
  created: Date;
  dueDate?: Date;
  paidAt?: Date;
  hostedInvoiceUrl?: string;
  invoicePdf?: string;
  description?: string;
}

export interface SubscriptionPreview {
  prorationAmount: number;
  nextInvoiceAmount: number;
  nextInvoiceDate: Date;
  immediateCharge?: number;
}

export interface SubscriptionPlan {
  id: string;
  name: string;
  description: string;
  amount: number;
  currency: string;
  interval: string;
  intervalCount: number;
  features: string[];
  isPopular?: boolean;
  stripePriceId: string;
  stripeProductId: string;
}

export enum SubscriptionStatus {
  INCOMPLETE = 'incomplete',
  INCOMPLETE_EXPIRED = 'incomplete_expired',
  TRIALING = 'trialing',
  ACTIVE = 'active',
  PAST_DUE = 'past_due',
  CANCELED = 'canceled',
  UNPAID = 'unpaid'
}

export enum InvoiceStatus {
  DRAFT = 'draft',
  OPEN = 'open',
  PAID = 'paid',
  UNCOLLECTIBLE = 'uncollectible',
  VOID = 'void'
}

export interface SubscriptionError {
  code: string;
  message: string;
  type: 'card_error' | 'validation_error' | 'api_error';
  param?: string;
}

export interface PaymentMethodWithBilling {
  id: string;
  type: string;
  brand?: string;
  last4?: string;
  expiryMonth?: number;
  expiryYear?: number;
  holderName?: string;
  isDefault: boolean;
  isBackup?: boolean;
  billingAddress?: BillingAddress;
  createdAt: Date;
}

export interface SubscriptionSettings {
  autoRenew: boolean;
  emailNotifications: {
    paymentSucceeded: boolean;
    paymentFailed: boolean;
    subscriptionCanceled: boolean;
    invoiceUpcoming: boolean;
    trialWillEnd: boolean;
  };
  gracePeriodDays: number;
  allowDowngrades: boolean;
  allowCancellation: boolean;
}

export interface BillingHistory {
  invoices: InvoiceDetails[];
  totalPages: number;
  currentPage: number;
  totalCount: number;
}

export interface SubscriptionMetrics {
  totalRevenue: number;
  monthlyRecurringRevenue: number;
  activeSubscriptions: number;
  churnRate: number;
  averageRevenuePerUser: number;
  lifetimeValue: number;
}

/**
 * Utility functions for subscription management
 */
export class SubscriptionUtils {
  /**
   * Check if subscription is active
   */
  static isActive(subscription: SubscriptionDetails): boolean {
    return subscription.status === SubscriptionStatus.ACTIVE || 
           subscription.status === SubscriptionStatus.TRIALING;
  }

  /**
   * Check if subscription is past due
   */
  static isPastDue(subscription: SubscriptionDetails): boolean {
    return subscription.status === SubscriptionStatus.PAST_DUE;
  }

  /**
   * Check if subscription is canceled
   */
  static isCanceled(subscription: SubscriptionDetails): boolean {
    return subscription.status === SubscriptionStatus.CANCELED;
  }

  /**
   * Check if subscription will cancel at period end
   */
  static willCancelAtPeriodEnd(subscription: SubscriptionDetails): boolean {
    return subscription.cancelAtPeriodEnd && this.isActive(subscription);
  }

  /**
   * Get days until next billing
   */
  static getDaysUntilNextBilling(subscription: SubscriptionDetails): number {
    const now = new Date();
    const nextBilling = new Date(subscription.currentPeriodEnd);
    const diffTime = nextBilling.getTime() - now.getTime();
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  }

  /**
   * Format subscription amount for display
   */
  static formatAmount(amount: number, currency: string): string {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency.toUpperCase()
    }).format(amount / 100);
  }

  /**
   * Get subscription status display text
   */
  static getStatusDisplayText(status: SubscriptionStatus): string {
    switch (status) {
      case SubscriptionStatus.ACTIVE:
        return 'Active';
      case SubscriptionStatus.TRIALING:
        return 'Trial';
      case SubscriptionStatus.PAST_DUE:
        return 'Past Due';
      case SubscriptionStatus.CANCELED:
        return 'Canceled';
      case SubscriptionStatus.INCOMPLETE:
        return 'Incomplete';
      case SubscriptionStatus.INCOMPLETE_EXPIRED:
        return 'Expired';
      case SubscriptionStatus.UNPAID:
        return 'Unpaid';
      default:
        return 'Unknown';
    }
  }

  /**
   * Get subscription status color class
   */
  static getStatusColorClass(status: SubscriptionStatus): string {
    switch (status) {
      case SubscriptionStatus.ACTIVE:
        return 'text-success';
      case SubscriptionStatus.TRIALING:
        return 'text-info';
      case SubscriptionStatus.PAST_DUE:
        return 'text-warning';
      case SubscriptionStatus.CANCELED:
      case SubscriptionStatus.INCOMPLETE_EXPIRED:
      case SubscriptionStatus.UNPAID:
        return 'text-danger';
      case SubscriptionStatus.INCOMPLETE:
        return 'text-secondary';
      default:
        return 'text-muted';
    }
  }

  /**
   * Validate billing address
   */
  static validateBillingAddress(address: BillingAddress): string[] {
    const errors: string[] = [];

    if (!address.line1?.trim()) {
      errors.push('Address line 1 is required');
    }

    if (!address.city?.trim()) {
      errors.push('City is required');
    }

    if (!address.postal_code?.trim()) {
      errors.push('Postal code is required');
    }

    if (!address.country?.trim()) {
      errors.push('Country is required');
    }

    // Validate postal code format based on country
    if (address.country === 'US' && address.postal_code) {
      const usZipRegex = /^\d{5}(-\d{4})?$/;
      if (!usZipRegex.test(address.postal_code)) {
        errors.push('Invalid US postal code format');
      }
    }

    return errors;
  }
}
