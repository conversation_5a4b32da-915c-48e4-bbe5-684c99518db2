/**
 * Giveaway model
 */
export interface Giveaway {
  id: string;
  title: string;
  slug?: string;
  description: string;
  startDate: Date;
  endDate: Date;
  drawDate: Date;
  status: GiveawayStatus;
  featuredImage?: string;
  imageUrl?: string;
  prizeValue: number;
  prizeDetails?: string;
  category?: string;
  tags?: string[];
  rules?: string;
  termsAndConditions?: string;
  minTier?: string;
  maxEntries: number;
  isActive: boolean;
  entryCount?: number;
  prizes?: Prize[];
  winners?: GiveawayWinner[];
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Giveaway status enum
 */
export enum GiveawayStatus {
  DRAFT = 'DRAFT',
  ACTIVE = 'ACTIVE',
  COMPLETED = 'COMPLETED',
  CANCELLED = 'CANCELLED'
}

/**
 * Prize model
 */
export interface Prize {
  id: string;
  giveawayId?: string;
  name: string;
  description?: string;
  value: number;
  currency?: string;
  quantity: number;
  images?: string[];
  specifications?: any;
  category?: string;
  sponsor?: string;
  sponsorLogoUrl?: string;
  createdAt?: Date;
  updatedAt?: Date;
}

/**
 * Giveaway entry model
 */
export interface Entry {
  id: string;
  userId: string;
  giveawayId: string;
  membershipId?: string;
  entryDate: Date;
  entryMethod?: string;
  referenceId?: string;
  quantity: number;
  isWinner?: boolean;
  user?: {
    id: string;
    email: string;
    firstName: string;
    lastName: string;
  };
}

/**
 * Giveaway winner model
 */
export interface GiveawayWinner {
  id: string;
  userId: string;
  giveawayId: string;
  prizeId?: string;
  entryId?: string;
  selectionDate: Date;
  status: WinnerStatus;
  claimDate?: Date;
  shippingDetails?: any;
  user?: {
    id: string;
    email: string;
    firstName: string;
    lastName: string;
    profileImage?: string;
  };
  prize?: {
    id: string;
    name: string;
    value: number;
    currency?: string;
    description?: string;
  };
}

/**
 * Winner status enum
 */
export enum WinnerStatus {
  SELECTED = 'SELECTED',
  NOTIFIED = 'NOTIFIED',
  CLAIMED = 'CLAIMED',
  FORFEITED = 'FORFEITED'
}

/**
 * Giveaway filter parameters
 */
export interface GiveawayFilterParams {
  page?: number;
  limit?: number;
  status?: GiveawayStatus;
  featured?: boolean;
  search?: string;
}

/**
 * Giveaway creation DTO
 */
export interface GiveawayCreateDto {
  title: string;
  description: string;
  startDate: Date | string;
  endDate: Date | string;
  drawDate: Date | string;
  status: GiveawayStatus;
  featuredImage?: string;
  prizeValue: number;
  prizeDetails?: string;
  imageUrl?: string;
  category?: string;
  tags?: string[];
  rules?: string;
  termsAndConditions?: string;
  minTier?: string;
  maxEntries: number;
  isActive: boolean;
}

/**
 * Prize creation DTO
 */
export interface PrizeCreateDto {
  name: string;
  description?: string;
  value: number;
  currency?: string;
  quantity: number;
  images?: string[];
  specifications?: any;
}

/**
 * Winner selection DTO
 */
export interface WinnerSelectionDto {
  giveawayId: string;
  numberOfWinners: number;
}

/**
 * Winner selection result
 */
export interface WinnerSelectionResultDto {
  drawResult: {
    giveawayId: string;
    drawCompleted: boolean;
    winners: {
      userId: string;
      entryId: string;
      prizeId: string;
      isAlternate: boolean;
    }[];
  };
  winners: GiveawayWinner[];
}
