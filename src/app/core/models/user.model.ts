/**
 * User model representing a registered user
 */
export interface User {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  role: 'USER' | 'ADMIN' | 'member' | 'admin';
  membershipTier?: string;
  membershipExpiry?: Date;
  createdAt: Date;
  updatedAt: Date;
  profileImageUrl?: string;
  isEmailVerified: boolean | boolean;
}

/**
 * Simplified user model for authentication state
 */
export interface AuthUser {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  role: 'USER' | 'ADMIN' | 'member' | 'admin';
  isVerified?: boolean;
  membershipTier?: string;
  membershipExpiry?: Date;
  profileImage?: string;
}
