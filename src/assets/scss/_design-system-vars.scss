/*
 * Winners Society Design System
 * Modern design principles inspired by award-winning applications of 2023-2024
 */

// ========================================
// 1. Color System
// ========================================

:root {
  // Primary Colors
  --primary: #3a36db;
  --primary-light: #6c63ff;
  --primary-dark: #2a2356;
  
  // Secondary Colors
  --secondary: #ff6b6b;
  --secondary-light: #ff8e8e;
  --secondary-dark: #e54b4b;
  
  // Neutral Colors
  --neutral-50: #f8fafc;
  --neutral-100: #f1f5f9;
  --neutral-200: #e2e8f0;
  --neutral-300: #cbd5e1;
  --neutral-400: #94a3b8;
  --neutral-500: #64748b;
  --neutral-600: #475569;
  --neutral-700: #334155;
  --neutral-800: #1e293b;
  --neutral-900: #0f172a;
  
  // Semantic Colors
  --success: #00c896;
  --warning: #ffb400;
  --danger: #ff5252;
  --info: #4fc3f7;
  
  // RGB Values for opacity variations
  --primary-rgb: 58, 54, 219;
  --primary-light-rgb: 108, 99, 255;
  --primary-dark-rgb: 42, 35, 86;
  --secondary-rgb: 255, 107, 107;
  --success-rgb: 0, 200, 150;
  --warning-rgb: 255, 180, 0;
  --danger-rgb: 255, 82, 82;
  --info-rgb: 79, 195, 247;
  
  // Background Colors
  --bg-light: var(--neutral-50);
  --bg-dark: var(--neutral-900);
  --bg-card: #ffffff;
  
  // Text Colors
  --text-primary: var(--neutral-900);
  --text-secondary: var(--neutral-600);
  --text-tertiary: var(--neutral-500);
  --text-light: #ffffff;
  
  // Border Colors
  --border-light: var(--neutral-200);
  --border-medium: var(--neutral-300);
  --border-dark: var(--neutral-400);
  
  // State Colors
  --hover-light: rgba(var(--primary-rgb), 0.05);
  --hover-medium: rgba(var(--primary-rgb), 0.1);
  --hover-dark: rgba(var(--primary-rgb), 0.2);
  --active-light: rgba(var(--primary-rgb), 0.1);
  --active-medium: rgba(var(--primary-rgb), 0.2);
  --active-dark: rgba(var(--primary-rgb), 0.3);
}

// ========================================
// 2. Typography System
// ========================================

:root {
  // Font Families
  --font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  --font-secondary: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  --font-mono: 'JetBrains Mono', 'SF Mono', 'Roboto Mono', Menlo, Monaco, Consolas, 'Liberation Mono', 'Courier New', monospace;
  
  // Font Sizes (8px grid system)
  --text-xs: 0.75rem;    // 12px
  --text-sm: 0.875rem;   // 14px
  --text-base: 1rem;     // 16px
  --text-md: 1.125rem;   // 18px
  --text-lg: 1.25rem;    // 20px
  --text-xl: 1.5rem;     // 24px
  --text-2xl: 1.75rem;   // 28px
  --text-3xl: 2rem;      // 32px
  --text-4xl: 2.5rem;    // 40px
  --text-5xl: 3rem;      // 48px
  
  // Line Heights
  --leading-none: 1;
  --leading-tight: 1.25;
  --leading-snug: 1.375;
  --leading-normal: 1.5;
  --leading-relaxed: 1.625;
  --leading-loose: 2;
  
  // Font Weights
  --font-thin: 100;
  --font-extralight: 200;
  --font-light: 300;
  --font-normal: 400;
  --font-medium: 500;
  --font-semibold: 600;
  --font-bold: 700;
  --font-extrabold: 800;
  --font-black: 900;
  
  // Letter Spacing
  --tracking-tighter: -0.05em;
  --tracking-tight: -0.025em;
  --tracking-normal: 0em;
  --tracking-wide: 0.025em;
  --tracking-wider: 0.05em;
  --tracking-widest: 0.1em;
}

// ========================================
// 3. Spacing System (8px grid)
// ========================================

:root {
  --space-0: 0;
  --space-1: 0.25rem;   // 4px
  --space-2: 0.5rem;    // 8px
  --space-3: 0.75rem;   // 12px
  --space-4: 1rem;      // 16px
  --space-5: 1.25rem;   // 20px
  --space-6: 1.5rem;    // 24px
  --space-8: 2rem;      // 32px
  --space-10: 2.5rem;   // 40px
  --space-12: 3rem;     // 48px
  --space-16: 4rem;     // 64px
  --space-20: 5rem;     // 80px
  --space-24: 6rem;     // 96px
  --space-32: 8rem;     // 128px
  --space-40: 10rem;    // 160px
  --space-48: 12rem;    // 192px
  --space-56: 14rem;    // 224px
  --space-64: 16rem;    // 256px
}

// ========================================
// 4. Border Radius
// ========================================

:root {
  --radius-none: 0;
  --radius-sm: 0.125rem;   // 2px
  --radius-md: 0.375rem;   // 6px
  --radius-lg: 0.5rem;     // 8px
  --radius-xl: 0.75rem;    // 12px
  --radius-2xl: 1rem;      // 16px
  --radius-3xl: 1.5rem;    // 24px
  --radius-full: 9999px;
}

// ========================================
// 5. Shadows
// ========================================

:root {
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  --shadow-inner: inset 0 2px 4px 0 rgba(0, 0, 0, 0.06);
  --shadow-outline: 0 0 0 3px rgba(var(--primary-rgb), 0.5);
  --shadow-none: none;
}

// ========================================
// 6. Animation & Transitions
// ========================================

:root {
  // Durations (all under 400ms)
  --duration-75: 75ms;
  --duration-100: 100ms;
  --duration-150: 150ms;
  --duration-200: 200ms;
  --duration-300: 300ms;
  --duration-400: 400ms;
  
  // Timing Functions
  --ease-linear: linear;
  --ease-in: cubic-bezier(0.4, 0, 1, 1);
  --ease-out: cubic-bezier(0, 0, 0.2, 1);
  --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
  
  // Reduced Motion
  --motion-safe: @media (prefers-reduced-motion: no-preference);
  --motion-reduce: @media (prefers-reduced-motion: reduce);
}

// ========================================
// 7. Z-Index Scale
// ========================================

:root {
  --z-0: 0;
  --z-10: 10;
  --z-20: 20;
  --z-30: 30;
  --z-40: 40;
  --z-50: 50;
  --z-auto: auto;
  
  // Special z-indices
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
}

// ========================================
// 8. Breakpoints
// ========================================

:root {
  --screen-xs: 480px;
  --screen-sm: 640px;
  --screen-md: 768px;
  --screen-lg: 1024px;
  --screen-xl: 1280px;
  --screen-2xl: 1536px;
}
