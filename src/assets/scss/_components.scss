/*
 * Winners Society Component Styles
 * Based on the design system
 */

// ========================================
// 1. Card Components
// ========================================

.card {
  background-color: var(--bg-card);
  border-radius: var(--radius-2xl);
  box-shadow: var(--shadow-md);
  overflow: hidden;
  transition: transform var(--duration-300) var(--ease-out),
              box-shadow var(--duration-300) var(--ease-out);

  &:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
  }

  .card-header {
    padding: var(--space-4) var(--space-6);
    border-bottom: 1px solid var(--border-light);
    display: flex;
    align-items: center;
    justify-content: space-between;

    .card-title {
      font-size: var(--text-lg);
      font-weight: var(--font-semibold);
      color: var(--text-primary);
      margin: 0;
    }
  }

  .card-body {
    padding: var(--space-6);
  }

  .card-footer {
    padding: var(--space-4) var(--space-6);
    border-top: 1px solid var(--border-light);
    background-color: var(--neutral-50);
  }

  // Card variants
  &.card-primary {
    border-top: 4px solid var(--primary);
  }

  &.card-secondary {
    border-top: 4px solid var(--secondary);
  }

  &.card-success {
    border-top: 4px solid var(--success);
  }

  &.card-warning {
    border-top: 4px solid var(--warning);
  }

  &.card-danger {
    border-top: 4px solid var(--danger);
  }

  &.card-info {
    border-top: 4px solid var(--info);
  }

  // Card sizes
  &.card-sm {
    .card-header {
      padding: var(--space-3) var(--space-4);
    }

    .card-body {
      padding: var(--space-4);
    }

    .card-footer {
      padding: var(--space-3) var(--space-4);
    }
  }

  &.card-lg {
    .card-header {
      padding: var(--space-6) var(--space-8);
    }

    .card-body {
      padding: var(--space-8);
    }

    .card-footer {
      padding: var(--space-6) var(--space-8);
    }
  }
}

// ========================================
// 2. Button Components
// ========================================

.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--space-2) var(--space-4);
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  line-height: var(--leading-normal);
  text-align: center;
  text-decoration: none;
  vertical-align: middle;
  cursor: pointer;
  user-select: none;
  border: 1px solid transparent;
  border-radius: var(--radius-lg);
  transition: color var(--duration-200) var(--ease-in-out),
              background-color var(--duration-200) var(--ease-in-out),
              border-color var(--duration-200) var(--ease-in-out),
              box-shadow var(--duration-200) var(--ease-in-out),
              transform var(--duration-200) var(--ease-in-out);

  &:focus {
    outline: none;
    box-shadow: var(--shadow-outline);
  }

  &:active {
    transform: translateY(1px);
  }

  // Button variants
  &.btn-primary {
    color: var(--text-light);
    background-color: var(--primary);
    border-color: var(--primary);

    &:hover {
      background-color: var(--primary-dark);
      border-color: var(--primary-dark);
    }
  }

  &.btn-secondary {
    color: var(--text-light);
    background-color: var(--secondary);
    border-color: var(--secondary);

    &:hover {
      background-color: var(--secondary-dark);
      border-color: var(--secondary-dark);
    }
  }

  &.btn-success {
    color: var(--text-light);
    background-color: var(--success);
    border-color: var(--success);

    &:hover {
      background-color: var(--success);
      filter: brightness(0.9);
      border-color: var(--success);
    }
  }

  &.btn-warning {
    color: var(--text-primary);
    background-color: var(--warning);
    border-color: var(--warning);

    &:hover {
      background-color: var(--warning);
      filter: brightness(0.9);
      border-color: var(--warning);
    }
  }

  &.btn-danger {
    color: var(--text-light);
    background-color: var(--danger);
    border-color: var(--danger);

    &:hover {
      background-color: var(--danger);
      filter: brightness(0.9);
      border-color: var(--danger);
    }
  }

  &.btn-outline-primary {
    color: var(--primary);
    background-color: transparent;
    border-color: var(--primary);

    &:hover {
      color: var(--text-light);
      background-color: var(--primary);
      border-color: var(--primary);
    }
  }

  &.btn-outline-secondary {
    color: var(--secondary);
    background-color: transparent;
    border-color: var(--secondary);

    &:hover {
      color: var(--text-light);
      background-color: var(--secondary);
      border-color: var(--secondary);
    }
  }

  // Button sizes
  &.btn-sm {
    padding: var(--space-1) var(--space-3);
    font-size: var(--text-xs);
    border-radius: var(--radius-md);
  }

  &.btn-lg {
    padding: var(--space-3) var(--space-6);
    font-size: var(--text-md);
    border-radius: var(--radius-xl);
  }

  // Icon button
  &.btn-icon {
    width: 36px;
    height: 36px;
    padding: 0;
    border-radius: var(--radius-full);

    &.btn-sm {
      width: 28px;
      height: 28px;
    }

    &.btn-lg {
      width: 48px;
      height: 48px;
    }

    i, .icon {
      font-size: 1rem;
    }
  }
}

// ========================================
// 3. Form Components
// ========================================

.form-control {
  display: block;
  width: 100%;
  padding: var(--space-2) var(--space-3);
  font-size: var(--text-base);
  font-weight: var(--font-normal);
  line-height: var(--leading-normal);
  color: var(--text-primary);
  background-color: var(--bg-card);
  background-clip: padding-box;
  border: 1px solid var(--border-medium);
  border-radius: var(--radius-lg);
  transition: border-color var(--duration-200) var(--ease-in-out),
              box-shadow var(--duration-200) var(--ease-in-out);

  &:focus {
    color: var(--text-primary);
    background-color: var(--bg-card);
    border-color: var(--primary-light);
    outline: 0;
    box-shadow: var(--shadow-outline);
  }

  &::placeholder {
    color: var(--text-tertiary);
    opacity: 1;
  }

  &:disabled,
  &[readonly] {
    background-color: var(--neutral-100);
    opacity: 1;
  }
}

.form-label {
  margin-bottom: var(--space-2);
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  color: var(--text-secondary);
}

.form-text {
  margin-top: var(--space-1);
  font-size: var(--text-xs);
  color: var(--text-tertiary);
}

.form-group {
  margin-bottom: var(--space-4);
}

// ========================================
// 4. Navigation Components
// ========================================

.nav {
  display: flex;
  flex-wrap: wrap;
  padding-left: 0;
  margin-bottom: 0;
  list-style: none;

  .nav-item {
    margin-bottom: 0;
  }

  .nav-link {
    display: block;
    padding: var(--space-2) var(--space-4);
    color: var(--text-secondary);
    text-decoration: none;
    transition: color var(--duration-200) var(--ease-in-out),
                background-color var(--duration-200) var(--ease-in-out);

    &:hover,
    &:focus {
      color: var(--primary);
    }

    &.active {
      color: var(--primary);
      font-weight: var(--font-medium);
    }

    &.disabled {
      color: var(--text-tertiary);
      pointer-events: none;
      cursor: default;
    }
  }
}

// Tabs
.nav-tabs {
  border-bottom: 1px solid var(--border-light);

  .nav-item {
    margin-bottom: -1px;
  }

  .nav-link {
    border: 1px solid transparent;
    border-top-left-radius: var(--radius-lg);
    border-top-right-radius: var(--radius-lg);

    &:hover,
    &:focus {
      border-color: var(--border-light) var(--border-light) var(--border-light);
    }

    &.active {
      color: var(--primary);
      background-color: var(--bg-card);
      border-color: var(--border-light) var(--border-light) var(--bg-card);
    }

    &.disabled {
      color: var(--text-tertiary);
      background-color: transparent;
      border-color: transparent;
    }
  }
}
