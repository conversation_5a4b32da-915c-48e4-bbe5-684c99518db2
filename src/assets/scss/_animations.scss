/*
 * Winners Society Animation Styles
 * All animations are under 400ms for optimal performance and user experience
 */

// ========================================
// 1. Fade Animations
// ========================================

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes fadeOut {
  from { opacity: 1; }
  to { opacity: 0; }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInLeft {
  from {
    opacity: 0;
    transform: translateX(-10px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translateX(10px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

// Animation Classes
.fade-in {
  animation: fadeIn var(--duration-300) var(--ease-out);
}

.fade-out {
  animation: fadeOut var(--duration-300) var(--ease-in);
}

.fade-in-up {
  animation: fadeInUp var(--duration-300) var(--ease-out);
}

.fade-in-down {
  animation: fadeInDown var(--duration-300) var(--ease-out);
}

.fade-in-left {
  animation: fadeInLeft var(--duration-300) var(--ease-out);
}

.fade-in-right {
  animation: fadeInRight var(--duration-300) var(--ease-out);
}

// ========================================
// 2. Scale Animations
// ========================================

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes scaleOut {
  from {
    opacity: 1;
    transform: scale(1);
  }
  to {
    opacity: 0;
    transform: scale(0.95);
  }
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

@keyframes heartbeat {
  0% { transform: scale(1); }
  14% { transform: scale(1.3); }
  28% { transform: scale(1); }
  42% { transform: scale(1.3); }
  70% { transform: scale(1); }
}

// Animation Classes
.scale-in {
  animation: scaleIn var(--duration-300) var(--ease-out);
}

.scale-out {
  animation: scaleOut var(--duration-300) var(--ease-in);
}

.pulse {
  animation: pulse var(--duration-400) var(--ease-in-out) infinite;
}

.heartbeat {
  animation: heartbeat 1.5s var(--ease-in-out) infinite;
}

// ========================================
// 3. Micro-interactions
// ========================================

// Hover Effects
.hover-lift {
  transition: transform var(--duration-300) var(--ease-out);
  
  &:hover {
    transform: translateY(-4px);
  }
}

.hover-scale {
  transition: transform var(--duration-300) var(--ease-out);
  
  &:hover {
    transform: scale(1.05);
  }
}

.hover-shadow {
  transition: box-shadow var(--duration-300) var(--ease-out);
  
  &:hover {
    box-shadow: var(--shadow-lg);
  }
}

// Button Interactions
.btn-interaction {
  position: relative;
  overflow: hidden;
  
  &::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 5px;
    height: 5px;
    background: rgba(255, 255, 255, 0.5);
    opacity: 0;
    border-radius: 100%;
    transform: scale(1, 1) translate(-50%, -50%);
    transform-origin: 50% 50%;
  }
  
  &:focus:not(:active)::after {
    animation: ripple var(--duration-400) var(--ease-out);
  }
}

@keyframes ripple {
  0% {
    transform: scale(0, 0);
    opacity: 0.5;
  }
  100% {
    transform: scale(20, 20);
    opacity: 0;
  }
}

// Input Focus Effect
.input-focus-effect {
  position: relative;
  
  &::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 2px;
    background-color: var(--primary);
    transition: width var(--duration-300) var(--ease-out);
  }
  
  &:focus-within::after {
    width: 100%;
  }
}

// ========================================
// 4. Loading Animations
// ========================================

@keyframes spinner {
  to { transform: rotate(360deg); }
}

.spinner {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 2px solid rgba(var(--primary-rgb), 0.3);
  border-radius: 50%;
  border-top-color: var(--primary);
  animation: spinner var(--duration-400) linear infinite;
}

@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: 200px 0;
  }
}

.skeleton {
  background: linear-gradient(90deg, var(--neutral-200) 25%, var(--neutral-100) 50%, var(--neutral-200) 75%);
  background-size: 200px 100%;
  animation: shimmer 1.5s infinite;
  border-radius: var(--radius-md);
}

// ========================================
// 5. Notification Animations
// ========================================

@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideOutRight {
  from {
    transform: translateX(0);
    opacity: 1;
  }
  to {
    transform: translateX(100%);
    opacity: 0;
  }
}

.notification-enter {
  animation: slideInRight var(--duration-300) var(--ease-out);
}

.notification-exit {
  animation: slideOutRight var(--duration-300) var(--ease-in);
}

// Badge Notification
@keyframes badgePulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.2); }
  100% { transform: scale(1); }
}

.badge-notification {
  animation: badgePulse 2s infinite;
}

// ========================================
// 6. Page Transitions
// ========================================

.page-enter {
  opacity: 0;
  transform: translateY(10px);
}

.page-enter-active {
  opacity: 1;
  transform: translateY(0);
  transition: opacity var(--duration-300) var(--ease-out),
              transform var(--duration-300) var(--ease-out);
}

.page-exit {
  opacity: 1;
}

.page-exit-active {
  opacity: 0;
  transition: opacity var(--duration-300) var(--ease-in);
}

// ========================================
// 7. Reduced Motion
// ========================================

@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
  
  .fade-in,
  .fade-out,
  .fade-in-up,
  .fade-in-down,
  .fade-in-left,
  .fade-in-right,
  .scale-in,
  .scale-out,
  .pulse,
  .heartbeat,
  .spinner,
  .skeleton,
  .notification-enter,
  .notification-exit,
  .badge-notification {
    animation: none !important;
  }
  
  .hover-lift:hover,
  .hover-scale:hover {
    transform: none !important;
  }
}
