/*
 * Winners Society Design System
 * Main entry point for the design system
 */

// Import design system files
@use '_index' as *;

// Additional styles for accessibility
.visually-hidden {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}

.focus-visible:focus {
  outline: 2px solid var(--primary);
  outline-offset: 2px;
}

// Skip to content link for keyboard users
.skip-to-content {
  position: absolute;
  top: -40px;
  left: 0;
  background: var(--primary);
  color: white;
  padding: 8px;
  z-index: 100;
  transition: top 0.3s;

  &:focus {
    top: 0;
  }
}

// High contrast mode adjustments
@media (forced-colors: active) {
  .btn,
  .card,
  .nav-link,
  .form-control {
    border: 1px solid;
  }
}

// Print styles
@media print {
  .no-print {
    display: none !important;
  }

  body {
    background-color: white !important;
    color: black !important;
  }

  .card,
  .container,
  .content-wrapper {
    box-shadow: none !important;
    border: 1px solid #ddd !important;
  }

  a[href]:after {
    content: " (" attr(href) ")";
  }
}
