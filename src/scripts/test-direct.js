/**
 * Simple direct test script for enrollment functionality
 *
 * This script tests the enrollment functionality directly using the Prisma client.
 */

import { PrismaClient } from '@prisma/client';

// Initialize Prisma client with connection from environment variable
const prisma = new PrismaClient({
  datasources: {
    db: {
      url: process.env['DATABASE_URL'] || 'postgresql://postgres@localhost:5432/winnerssociety?connect_timeout=10&sslmode=prefer&schema=public'
    }
  }
});

/**
 * Main test function
 */
async function testDirect() {
  try {
    console.log('Starting direct enrollment test...');

    // Create a test user if not exists
    const testUser = await getOrCreateTestUser();
    console.log(`Test user: ${testUser.id} (${testUser.email})`);

    // Create a membership tier if not exists
    const testTier = await getOrCreateTestTier();
    console.log(`Test tier: ${testTier.id} (${testTier.name})`);

    // Create a test giveaway with auto-enrollment enabled
    const testGiveaway = await createTestGiveaway([testTier.id]);
    console.log(`Test giveaway: ${testGiveaway.id} (${testGiveaway.title})`);

    // Create a membership for the test user
    const testMembership = await createTestMembership(testUser.id, testTier.id);
    console.log(`Test membership: ${testMembership.id}`);

    // Create an entry directly
    await createEntry(testUser.id, testGiveaway.id, testMembership.id, testTier.id, testTier.entryAllocation);
    console.log(`Created entry for user ${testUser.id} in giveaway ${testGiveaway.id}`);

    // Check if the entry was created
    const entry = await prisma.entry.findUnique({
      where: {
        userId_giveawayId: {
          userId: testUser.id,
          giveawayId: testGiveaway.id
        }
      }
    });

    if (entry) {
      console.log(`✅ SUCCESS: Entry was created with ${entry.quantity} entries!`);
    } else {
      console.log('❌ FAILURE: Entry was not created.');
    }

    // Clean up test data
    if (process.argv.includes('--cleanup')) {
      await cleanup(testUser.id, testGiveaway.id, testMembership.id);
      console.log('Test data cleaned up.');
    }

    console.log('Test completed.');
  } catch (error) {
    console.error('Error during test:', error);
  } finally {
    // Disconnect Prisma client
    await prisma.$disconnect();
    process.exit(0);
  }
}

/**
 * Get or create a test user
 */
async function getOrCreateTestUser() {
  const email = '<EMAIL>';

  // Check if user exists
  let user = await prisma.user.findUnique({
    where: { email }
  });

  if (!user) {
    // Create test user
    user = await prisma.user.create({
      data: {
        email,
        password: 'password123',
        firstName: 'Test',
        lastName: 'User',
        isVerified: true,
        isActive: true
      }
    });
  }

  return user;
}

/**
 * Get or create a test membership tier
 */
async function getOrCreateTestTier() {
  const name = 'Test Direct JS Tier';

  // Check if tier exists
  let tier = await prisma.membershipTier.findFirst({
    where: { name }
  });

  if (!tier) {
    // Create test tier
    tier = await prisma.membershipTier.create({
      data: {
        name,
        description: 'Test membership tier for direct JS enrollment',
        price: 9.99,
        billingPeriod: 'MONTHLY',
        entryAllocation: 5,
        duration: 30,
        features: ['Test feature 1', 'Test feature 2'],
        isActive: true
      }
    });
  }

  return tier;
}

/**
 * Create a test giveaway with auto-enrollment enabled
 */
async function createTestGiveaway(eligibleTierIds) {
  // Create start and end dates
  const startDate = new Date();
  const endDate = new Date();
  endDate.setDate(endDate.getDate() + 7);

  // Create test giveaway
  const giveaway = await prisma.giveaway.create({
    data: {
      title: 'Test Direct JS Enrollment Giveaway',
      description: 'A test giveaway for direct JS enrollment',
      startDate,
      endDate,
      status: 'ACTIVE',
      prizeValue: 100.00,
      prizeDetails: 'Test prize',
      rules: 'Test rules',
      maxEntries: 10,
      isActive: true,
      autoEnrollment: true,
      eligibleMembershipTierIds: eligibleTierIds
    }
  });

  return giveaway;
}

/**
 * Create a test membership for the user
 */
async function createTestMembership(userId, tierId) {
  // Create start and end dates
  const startDate = new Date();
  const endDate = new Date();
  endDate.setDate(endDate.getDate() + 30);

  // Create test membership
  const membership = await prisma.membership.create({
    data: {
      userId,
      membershipTierId: tierId,
      startDate,
      endDate,
      status: 'ACTIVE',
      autoRenew: true
    }
  });

  return membership;
}

/**
 * Create an entry directly
 */
async function createEntry(
  userId,
  giveawayId,
  membershipId,
  membershipTierId,
  quantity
) {
  // Check if entry already exists
  const existingEntry = await prisma.entry.findUnique({
    where: {
      userId_giveawayId: {
        userId,
        giveawayId
      }
    }
  });

  if (existingEntry) {
    // Update existing entry if needed
    if (quantity > existingEntry.quantity) {
      return await prisma.entry.update({
        where: { id: existingEntry.id },
        data: {
          quantity,
          membershipId,
          membershipTierId,
          lastUpdatedDate: new Date()
        }
      });
    }
    return existingEntry;
  } else {
    // Create new entry
    return await prisma.entry.create({
      data: {
        userId,
        giveawayId,
        membershipId,
        membershipTierId,
        quantity,
        source: 'MEMBERSHIP',
        entryMethod: 'MEMBERSHIP'
      }
    });
  }
}

/**
 * Clean up test data
 */
async function cleanup(userId, giveawayId, membershipId) {
  // Delete entry if exists
  await prisma.entry.deleteMany({
    where: {
      userId,
      giveawayId
    }
  });

  // Delete membership
  await prisma.membership.delete({
    where: { id: membershipId }
  });

  // Delete giveaway
  await prisma.giveaway.delete({
    where: { id: giveawayId }
  });
}

// Run the test
testDirect();
