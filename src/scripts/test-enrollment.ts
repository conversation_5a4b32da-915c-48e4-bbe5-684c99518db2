/**
 * Test script for automatic giveaway enrollment
 *
 * This script tests the automatic enrollment functionality by:
 * 1. Creating a new giveaway with auto-enrollment enabled
 * 2. Creating a new membership for a user
 * 3. Verifying that the user is automatically enrolled in the giveaway
 */

import { PrismaClient } from '@prisma/client';
import { initializeServices } from '../services/index.js';
import { initializeJobSystem } from '../jobs/index.js';

// Initialize Prisma client with connection from environment variable
const prisma = new PrismaClient({
  datasources: {
    db: {
      url: process.env['DATABASE_URL'] || 'postgresql://postgres@localhost:5432/winnerssociety?connect_timeout=10&sslmode=prefer&schema=public'
    }
  }
});

// Initialize services and job system
initializeServices();
initializeJobSystem();


/**
 * Main test function
 */
async function testEnrollment() {
  try {
    console.log('Starting automatic enrollment test...');

    // Create a test user if not exists
    const testUser = await getOrCreateTestUser();
    console.log(`Test user: ${testUser.id} (${testUser.email})`);

    // Create a membership tier if not exists
    const testTier = await getOrCreateTestTier();
    console.log(`Test tier: ${testTier.id} (${testTier.name})`);

    // Create a test giveaway with auto-enrollment enabled
    const testGiveaway = await createTestGiveaway([testTier.id]);
    console.log(`Test giveaway: ${testGiveaway.id} (${testGiveaway.title})`);

    // Create a membership for the test user
    const testMembership = await createTestMembership(testUser.id, testTier.id);
    console.log(`Test membership: ${testMembership.id}`);

    // Wait for enrollment to complete
    console.log('Waiting for automatic enrollment to complete...');
    await new Promise(resolve => setTimeout(resolve, 5000));

    // Check if the user was enrolled
    const entry = await prisma.entry.findUnique({
      where: {
        userId_giveawayId: {
          userId: testUser.id,
          giveawayId: testGiveaway.id
        }
      }
    });

    if (entry) {
      console.log(`✅ SUCCESS: User was automatically enrolled with ${entry.quantity} entries!`);
    } else {
      console.log('❌ FAILURE: User was not automatically enrolled.');
    }

    // Clean up test data
    if (process.argv.includes('--cleanup')) {
      await cleanup(testUser.id, testGiveaway.id, testMembership.id);
      console.log('Test data cleaned up.');
    }

    console.log('Test completed.');
  } catch (error) {
    console.error('Error during test:', error);
  } finally {
    // Disconnect Prisma client
    await prisma.$disconnect();
    process.exit(0);
  }
}

/**
 * Get or create a test user
 */
async function getOrCreateTestUser() {
  const email = '<EMAIL>';

  // Check if user exists
  let user = await prisma.user.findUnique({
    where: { email }
  });

  if (!user) {
    // Create test user
    user = await prisma.user.create({
      data: {
        email,
        password: 'password123',
        firstName: 'Test',
        lastName: 'User',
        isVerified: true,
        isActive: true
      }
    });
  }

  return user;
}

/**
 * Get or create a test membership tier
 */
async function getOrCreateTestTier() {
  const name = 'Test Tier';

  // Check if tier exists
  let tier = await prisma.membershipTier.findFirst({
    where: { name }
  });

  if (!tier) {
    // Create test tier
    tier = await prisma.membershipTier.create({
      data: {
        name,
        description: 'Test membership tier for automatic enrollment',
        price: 9.99,
        billingPeriod: 'MONTHLY',
        entryAllocation: 5,
        duration: 30,
        features: ['Test feature 1', 'Test feature 2'],
        isActive: true
      }
    });
  }

  return tier;
}

/**
 * Create a test giveaway with auto-enrollment enabled
 */
async function createTestGiveaway(eligibleTierIds: string[]) {
  // Create start and end dates
  const startDate = new Date();
  const endDate = new Date();
  endDate.setDate(endDate.getDate() + 7);

  // Create test giveaway
  const giveaway = await prisma.giveaway.create({
    data: {
      title: 'Test Auto-Enrollment Giveaway',
      description: 'A test giveaway for automatic enrollment',
      startDate,
      endDate,
      status: 'ACTIVE',
      prizeValue: 100.00,
      prizeDetails: 'Test prize',
      rules: 'Test rules',
      maxEntries: 10,
      isActive: true,
      autoEnrollment: true,
      eligibleMembershipTierIds: eligibleTierIds
    }
  });

  return giveaway;
}

/**
 * Create a test membership for the user
 */
async function createTestMembership(userId: string, tierId: string) {
  // Create start and end dates
  const startDate = new Date();
  const endDate = new Date();
  endDate.setDate(endDate.getDate() + 30);

  // Create test membership
  const membership = await prisma.membership.create({
    data: {
      userId,
      membershipTierId: tierId,
      startDate,
      endDate,
      status: 'ACTIVE',
      autoRenew: true
    }
  });

  return membership;
}

/**
 * Clean up test data
 */
async function cleanup(userId: string, giveawayId: string, membershipId: string) {
  // Delete entry if exists
  await prisma.entry.deleteMany({
    where: {
      userId,
      giveawayId
    }
  });

  // Delete membership
  await prisma.membership.delete({
    where: { id: membershipId }
  });

  // Delete giveaway
  await prisma.giveaway.delete({
    where: { id: giveawayId }
  });
}

// Run the test
testEnrollment();
