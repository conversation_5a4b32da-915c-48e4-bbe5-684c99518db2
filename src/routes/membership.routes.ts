import { Router } from 'express';
import { createMembershipController } from '../controllers/membership.controller.js';
import { MembershipService } from '../services/membership/membership-service.interface.js';
import { Request, Response, NextFunction } from 'express';

/**
 * Authentication middleware type
 */
type AuthMiddleware = (req: Request, res: Response, next: NextFunction) => Promise<void> | void;

/**
 * Create membership routes
 * 
 * @param membershipService - Membership service implementation
 * @param authMiddleware - Authentication middleware for protected routes
 * @returns Express router
 */
export function createMembershipRouter(
  membershipService: MembershipService,
  authMiddleware: AuthMiddleware
): Router {
  const router = Router();
  const membershipController = createMembershipController(membershipService);
  
  // Create all route handlers directly
  function createHandlers() {
    return {
      getCurrentMembership: async (req: Request, res: Response, next: NextFunction) => {
        try {
          await membershipController.getCurrentMembership(req, res);
        } catch (error) {
          next(error);
        }
      },
      subscribe: async (req: Request, res: Response, next: NextFunction) => {
        try {
          await membershipController.subscribe(req, res);
        } catch (error) {
          next(error);
        }
      },
      cancelMembership: async (req: Request, res: Response, next: NextFunction) => {
        try {
          await membershipController.cancelMembership(req, res);
        } catch (error) {
          next(error);
        }
      },
      updateAutoRenew: async (req: Request, res: Response, next: NextFunction) => {
        try {
          await membershipController.updateAutoRenew(req, res);
        } catch (error) {
          next(error);
        }
      },
      upgradeMembership: async (req: Request, res: Response, next: NextFunction) => {
        try {
          await membershipController.upgradeMembership(req, res);
        } catch (error) {
          next(error);
        }
      },
      getMembershipHistory: async (req: Request, res: Response, next: NextFunction) => {
        try {
          await membershipController.getMembershipHistory(req, res);
        } catch (error) {
          next(error);
        }
      },
      getAvailableMemberships: async (req: Request, res: Response, next: NextFunction) => {
        try {
          await membershipController.getAvailableMemberships(req, res);
        } catch (error) {
          next(error);
        }
      }
    };
  }
  
  const handlers = createHandlers();

  // All membership management routes require authentication
  router.use(authMiddleware);

  // Membership management routes
  router.get('/current', handlers.getCurrentMembership);
  router.post('/subscribe', handlers.subscribe);
  router.post('/cancel', handlers.cancelMembership);
  router.put('/auto-renew', handlers.updateAutoRenew);
  router.post('/upgrade', handlers.upgradeMembership);
  router.get('/history', handlers.getMembershipHistory);
  router.get('/available', handlers.getAvailableMemberships);

  return router;
} 