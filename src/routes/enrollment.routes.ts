/**
 * Enrollment Routes
 *
 * This file defines the routes for enrollment-related operations.
 * It includes routes for managing automatic enrollment and manual enrollment.
 */

import express from 'express';
import { createEnrollmentController } from '../controllers/enrollment.controller.js';
import { createServiceProvider } from '../services/index.js';
import { authenticateUser, authorizeRoles } from '../middleware/auth.middleware.js';

// Create router
const router = express.Router();

// Get services
const services = createServiceProvider();
const enrollmentService = services.getEnrollmentService();

// Create controller
const enrollmentController = createEnrollmentController(enrollmentService);

// Admin routes - require authentication and admin role
router.put(
  '/giveaways/:giveawayId/eligible-tiers',
  authenticateUser,
  authorizeRoles(['ADMIN']),
  (req, res) => enrollmentController.updateGiveawayEligibleTiers(req, res)
);

router.put(
  '/giveaways/:giveawayId/auto-enrollment',
  authenticateUser,
  authorizeRoles(['ADMIN']),
  (req, res) => enrollmentController.toggleAutoEnrollment(req, res)
);

router.post(
  '/giveaways/:giveawayId/manual-enrollment',
  authenticateUser,
  authorizeRoles(['ADMIN']),
  (req, res) => enrollmentController.manuallyEnrollUser(req, res)
);

router.post(
  '/giveaways/:giveawayId/retry-failed',
  authenticateUser,
  authorizeRoles(['ADMIN']),
  (req, res) => enrollmentController.retryFailedEnrollments(req, res)
);

router.get(
  '/giveaways/:giveawayId/failed-enrollments',
  authenticateUser,
  authorizeRoles(['ADMIN']),
  (req, res) => enrollmentController.getFailedEnrollments(req, res)
);

router.get(
  '/giveaways/:giveawayId/entry-statistics',
  authenticateUser,
  authorizeRoles(['ADMIN']),
  (req, res) => enrollmentController.getEntryStatistics(req, res)
);

export default router;
