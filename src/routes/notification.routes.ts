import { Router } from 'express';
import { createNotificationController } from '../controllers/notification.controller.js';
import { NotificationServiceInterface } from '../services/notification/notification-service.interface.js';
import { Request, Response, NextFunction } from 'express';

/**
 * Authentication middleware type
 */
type AuthMiddleware = (req: Request, res: Response, next: NextFunction) => Promise<void> | void;

/**
 * Create notification routes
 * 
 * @param notificationService - Notification service implementation
 * @param authMiddleware - Authentication middleware for protected routes
 * @returns Express router
 */
export function createNotificationRouter(
  notificationService: NotificationServiceInterface,
  authMiddleware: AuthMiddleware
): Router {
  const router = Router();
  const notificationController = createNotificationController(notificationService);
  
  // Create all route handlers directly
  function createHandlers() {
    return {
      getNotifications: async (req: Request, res: Response, next: NextFunction) => {
        try {
          await notificationController.getNotifications(req, res);
        } catch (error) {
          next(error);
        }
      },
      getNotificationDetails: async (req: Request, res: Response, next: NextFunction) => {
        try {
          await notificationController.getNotificationDetails(req, res);
        } catch (error) {
          next(error);
        }
      },
      markAsRead: async (req: Request, res: Response, next: NextFunction) => {
        try {
          await notificationController.markAsRead(req, res);
        } catch (error) {
          next(error);
        }
      },
      markAllAsRead: async (req: Request, res: Response, next: NextFunction) => {
        try {
          await notificationController.markAllAsRead(req, res);
        } catch (error) {
          next(error);
        }
      }
    };
  }
  
  const handlers = createHandlers();

  // All routes require authentication
  router.get('/', authMiddleware, handlers.getNotifications);
  router.get('/:id', authMiddleware, handlers.getNotificationDetails);
  router.patch('/:id/read', authMiddleware, handlers.markAsRead);
  router.patch('/read-all', authMiddleware, handlers.markAllAsRead);

  return router;
} 