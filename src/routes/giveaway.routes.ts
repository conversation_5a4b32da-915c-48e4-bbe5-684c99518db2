import { Router } from 'express';
import { createGiveawayController } from '../controllers/giveaway.controller.js';
import { GiveawayService } from '../services/giveaway/giveaway-service.interface.js';
import { Request, Response, NextFunction } from 'express';

/**
 * Authentication middleware type
 */
type AuthMiddleware = (req: Request, res: Response, next: NextFunction) => Promise<void> | void;

/**
 * Authorization middleware type (for admin-only endpoints)
 */
type AdminMiddleware = (req: Request, res: Response, next: NextFunction) => Promise<void> | void;

/**
 * Create giveaway routes
 *
 * @param giveawayService - Giveaway service implementation
 * @param authMiddleware - Authentication middleware for protected routes
 * @param adminMiddleware - Authorization middleware for admin-only routes
 * @returns Express router
 */
export function createGiveawayRouter(
  giveawayService: GiveawayService,
  authMiddleware: AuthMiddleware,
  _adminMiddleware: AdminMiddleware
): Router {
  const router = Router();
  const giveawayController = createGiveawayController(giveawayService);

  // Create all route handlers directly
  function createHandlers() {
    return {
      listGiveaways: async (req: Request, res: Response, next: NextFunction) => {
        try {
          await giveawayController.listGiveaways(req, res);
        } catch (error) {
          next(error);
        }
      },
      getGiveawayDetails: async (req: Request, res: Response, next: NextFunction) => {
        try {
          await giveawayController.getGiveawayDetails(req, res);
        } catch (error) {
          next(error);
        }
      },
      enterGiveaway: async (req: Request, res: Response, next: NextFunction) => {
        try {
          await giveawayController.enterGiveaway(req, res);
        } catch (error) {
          next(error);
        }
      }
    };
  }

  const handlers = createHandlers();

  // Public endpoints for browsing giveaways
  router.get('/', handlers.listGiveaways);
  router.get('/:id', handlers.getGiveawayDetails);

  // Routes requiring authentication
  router.post('/:id/enter', authMiddleware, handlers.enterGiveaway);

  // Admin-only routes have been moved to admin/giveaway.routes.ts

  return router;
}