import { Express } from 'express';
import { createAuthRouter } from './auth.routes.js';
import { createUserRouter } from './user.routes.js';
import { createMembershipRouter } from './membership.routes.js';
import { createMembershipTiersRouter } from './membership-tiers.routes.js';
import { createGiveawayRouter } from './giveaway.routes.js';
import { createPrizeRouter } from './prize.routes.js';
import { createWinnerRouter } from './winner.routes.js';
import { createPaymentRouter } from './payment.routes.js';
import { createContentRouter } from './content.routes.js';
import { createNotificationRouter } from './notification.routes.js';
import { createAdminDashboardRouter } from './admin/dashboard.routes.js';
import { createAdminUsersRouter } from './admin/users.routes.js';
import { createAdminGiveawayRouter } from './admin/giveaway.routes.js';
import enrollmentRouter from './enrollment.routes.js';

import { AuthService } from '../services/auth/auth-service.interface.js';
import { UserService } from '../services/user/user-service.interface.js';
import { MembershipService } from '../services/membership/membership-service.interface.js';
import { GiveawayService } from '../services/giveaway/giveaway-service.interface.js';
import { PrizeService } from '../services/prize/prize-service.interface.js';
import { WinnerService } from '../services/winner/winner-service.interface.js';
import { PaymentServiceInterface } from '../services/payment/payment-service.interface.js';
import { ContentServiceInterface } from '../services/content/content-service.interface.js';
import { NotificationServiceInterface } from '../services/notification/notification-service.interface.js';
import { AdminServiceInterface } from '../services/admin/admin-service.interface.js';
import { EnrollmentService } from '../services/enrollment.service.js';
import { Request, Response, NextFunction } from 'express';

// Define middleware types to match with route files
type AuthMiddleware = (req: Request, res: Response, next: NextFunction) => Promise<void> | void;
type AdminMiddleware = (req: Request, res: Response, next: NextFunction) => Promise<void> | void;

/**
 * Configure all application routes
 *
 * @param app - Express application
 * @param services - Object containing all service implementations
 * @param middlewares - Object containing all middleware functions
 */
export function setupRoutes(
  app: Express,
  services: {
    authService: AuthService;
    userService: UserService;
    membershipService: MembershipService;
    giveawayService: GiveawayService;
    prizeService: PrizeService;
    winnerService: WinnerService;
    paymentService: PaymentServiceInterface;
    contentService: ContentServiceInterface;
    notificationService: NotificationServiceInterface;
    adminService: AdminServiceInterface;
    enrollmentService: EnrollmentService;
  },
  middlewares: {
    authenticate: AuthMiddleware;
    requireAdmin: AdminMiddleware;
  }
) {
  // Create routers
  const authRouter = createAuthRouter(services.authService, middlewares.authenticate);
  const userRouter = createUserRouter(services.userService, middlewares.authenticate);
  const membershipRouter = createMembershipRouter(services.membershipService, middlewares.authenticate);
  const membershipTiersRouter = createMembershipTiersRouter(
    services.membershipService,
    middlewares.authenticate,
    middlewares.requireAdmin
  );
  const giveawayRouter = createGiveawayRouter(
    services.giveawayService,
    middlewares.authenticate,
    middlewares.requireAdmin
  );
  const prizeRouter = createPrizeRouter(
    services.prizeService,
    middlewares.authenticate,
    middlewares.requireAdmin
  );
  const winnerRouter = createWinnerRouter(
    services.winnerService,
    middlewares.authenticate,
    middlewares.requireAdmin
  );
  const paymentRouter = createPaymentRouter(
    services.paymentService,
    middlewares.authenticate,
    middlewares.requireAdmin
  );
  const contentRouter = createContentRouter(
    services.contentService,
    middlewares.authenticate,
    middlewares.requireAdmin
  );
  const notificationRouter = createNotificationRouter(
    services.notificationService,
    middlewares.authenticate
  );
  const adminDashboardRouter = createAdminDashboardRouter(
    services.adminService,
    middlewares.authenticate,
    middlewares.requireAdmin
  );
  const adminUsersRouter = createAdminUsersRouter(
    services.adminService,
    middlewares.authenticate,
    middlewares.requireAdmin
  );
  const adminGiveawayRouter = createAdminGiveawayRouter(
    services.giveawayService,
    middlewares.authenticate,
    middlewares.requireAdmin
  );

  // Register routers with app
  app.use('/api/auth', authRouter);
  app.use('/api/users', userRouter);
  app.use('/api/memberships', membershipRouter);
  app.use('/api/membership-tiers', membershipTiersRouter);
  app.use('/api/giveaways', giveawayRouter);
  app.use('/api/prizes', prizeRouter);
  app.use('/api/winners', winnerRouter);
  app.use('/api/payments', paymentRouter);
  app.use('/api/content', contentRouter);
  app.use('/api/notifications', notificationRouter);
  app.use('/api/admin/dashboard', adminDashboardRouter);
  app.use('/api/admin/users', adminUsersRouter);
  app.use('/api/admin/giveaways', adminGiveawayRouter);
  app.use('/api/enrollment', enrollmentRouter);
}

// Export individual router factory functions
export {
  createAuthRouter,
  createUserRouter,
  createMembershipRouter,
  createMembershipTiersRouter,
  createGiveawayRouter,
  createPrizeRouter,
  createWinnerRouter,
  createPaymentRouter,
  createContentRouter,
  createNotificationRouter,
  createAdminDashboardRouter,
  createAdminUsersRouter,
  createAdminGiveawayRouter
};