import { Router } from 'express';
import { createUserController } from '../controllers/user.controller.js';
import { UserService } from '../services/user/user-service.interface.js';
import { Request, Response, NextFunction } from 'express';
import multer from 'multer';

/**
 * Authentication middleware type
 */
type AuthMiddleware = (req: Request, res: Response, next: NextFunction) => Promise<void> | void;

/**
 * Configure multer storage for file uploads
 */
const storage = multer.memoryStorage();
const upload = multer({ 
  storage,
  limits: { fileSize: 5 * 1024 * 1024 }, // 5MB limit
  fileFilter: (_req, file, cb) => {
    // Accept only images
    if (file.mimetype.startsWith('image/')) {
      cb(null, true);
    } else {
      cb(new Error('Only image files are allowed'));
    }
  }
});

/**
 * Create user routes
 * 
 * @param userService - User service implementation
 * @param authMiddleware - Authentication middleware for protected routes
 * @returns Express router
 */
export function createUserRouter(
  userService: UserService,
  authMiddleware: AuthMiddleware
): Router {
  const router = Router();
  const userController = createUserController(userService);
  
  // Create all route handlers directly
  function createHandlers() {
    return {
      getProfile: async (req: Request, res: Response, next: NextFunction) => {
        try {
          await userController.getProfile(req, res);
        } catch (error) {
          next(error);
        }
      },
      getFullProfile: async (req: Request, res: Response, next: NextFunction) => {
        try {
          await userController.getFullProfile(req, res);
        } catch (error) {
          next(error);
        }
      },
      updateProfile: async (req: Request, res: Response, next: NextFunction) => {
        try {
          await userController.updateProfile(req, res);
        } catch (error) {
          next(error);
        }
      },
      changePassword: async (req: Request, res: Response, next: NextFunction) => {
        try {
          await userController.changePassword(req, res);
        } catch (error) {
          next(error);
        }
      },
      getPreferences: async (req: Request, res: Response, next: NextFunction) => {
        try {
          await userController.getPreferences(req, res);
        } catch (error) {
          next(error);
        }
      },
      updatePreferences: async (req: Request, res: Response, next: NextFunction) => {
        try {
          await userController.updatePreferences(req, res);
        } catch (error) {
          next(error);
        }
      },
      uploadProfileImage: async (req: Request, res: Response, next: NextFunction) => {
        try {
          await userController.uploadProfileImage(req, res);
        } catch (error) {
          next(error);
        }
      },
      getEntries: async (req: Request, res: Response, next: NextFunction) => {
        try {
          await userController.getEntries(req, res);
        } catch (error) {
          next(error);
        }
      },
      getWins: async (req: Request, res: Response, next: NextFunction) => {
        try {
          await userController.getWins(req, res);
        } catch (error) {
          next(error);
        }
      }
    };
  }
  
  const handlers = createHandlers();

  // All user routes require authentication
  router.use(authMiddleware);

  // Profile routes
  router.get('/profile', handlers.getProfile);
  router.get('/full-profile', handlers.getFullProfile); // Added for full user+profile info
  router.put('/profile', handlers.updateProfile);
  router.post('/profile/password', handlers.changePassword);
  router.get('/preferences', handlers.getPreferences);
  router.put('/preferences', handlers.updatePreferences);
  router.post('/profile/image', upload.single('image'), handlers.uploadProfileImage);

  // User data routes
  router.get('/entries', handlers.getEntries);
  router.get('/wins', handlers.getWins);

  return router;
}