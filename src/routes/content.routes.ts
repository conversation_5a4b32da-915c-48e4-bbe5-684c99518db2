import { Router } from 'express';
import { createContentController } from '../controllers/content.controller.js';
import { ContentServiceInterface } from '../services/content/content-service.interface.js';
import { Request, Response, NextFunction } from 'express';

/**
 * Authentication middleware type
 */
type AuthMiddleware = (req: Request, res: Response, next: NextFunction) => Promise<void> | void;

/**
 * Authorization middleware type (for admin-only endpoints)
 */
type AdminMiddleware = (req: Request, res: Response, next: NextFunction) => Promise<void> | void;

/**
 * Create content routes
 * 
 * @param contentService - Content service implementation
 * @param authMiddleware - Authentication middleware for protected routes
 * @param adminMiddleware - Authorization middleware for admin-only routes
 * @returns Express router
 */
export function createContentRouter(
  contentService: ContentServiceInterface,
  authMiddleware: AuthMiddleware,
  adminMiddleware: AdminMiddleware
): Router {
  const router = Router();
  const contentController = createContentController(contentService);
  
  // Create all route handlers directly
  function createHandlers() {
    return {
      getContentBySlug: async (req: Request, res: Response, next: NextFunction) => {
        try {
          await contentController.getContentBySlug(req, res);
        } catch (error) {
          next(error);
        }
      },
      listContentByCategory: async (req: Request, res: Response, next: NextFunction) => {
        try {
          await contentController.listContentByCategory(req, res);
        } catch (error) {
          next(error);
        }
      },
      createContent: async (req: Request, res: Response, next: NextFunction) => {
        try {
          await contentController.createContent(req, res);
        } catch (error) {
          next(error);
        }
      },
      updateContent: async (req: Request, res: Response, next: NextFunction) => {
        try {
          await contentController.updateContent(req, res);
        } catch (error) {
          next(error);
        }
      },
      deleteContent: async (req: Request, res: Response, next: NextFunction) => {
        try {
          await contentController.deleteContent(req, res);
        } catch (error) {
          next(error);
        }
      }
    };
  }
  
  const handlers = createHandlers();

  // Public routes (authentication optional but handled internally for access control)
  router.get('/slug/:slug', handlers.getContentBySlug);
  router.get('/category/:category', handlers.listContentByCategory);
  
  // Admin-only routes
  router.post('/', authMiddleware, adminMiddleware, handlers.createContent);
  router.put('/:id', authMiddleware, adminMiddleware, handlers.updateContent);
  router.delete('/:id', authMiddleware, adminMiddleware, handlers.deleteContent);

  return router;
} 