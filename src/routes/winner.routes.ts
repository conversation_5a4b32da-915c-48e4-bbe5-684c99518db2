import { Router } from 'express';
import { createWinnerController } from '../controllers/winner.controller.js';
import { WinnerService } from '../services/winner/winner-service.interface.js';
import { Request, Response, NextFunction } from 'express';

/**
 * Authentication middleware type
 */
type AuthMiddleware = (req: Request, res: Response, next: NextFunction) => Promise<void> | void;

/**
 * Authorization middleware type (for admin-only endpoints)
 */
type AdminMiddleware = (req: Request, res: Response, next: NextFunction) => Promise<void> | void;

/**
 * Create winner routes
 * 
 * @param winnerService - Winner service implementation
 * @param authMiddleware - Authentication middleware for protected routes
 * @param adminMiddleware - Authorization middleware for admin-only routes
 * @returns Express router
 */
export function createWinnerRouter(
  winnerService: WinnerService,
  authMiddleware: AuthMiddleware,
  adminMiddleware: AdminMiddleware
): Router {
  const router = Router();
  const winnerController = createWinnerController(winnerService);
  
  // Create all route handlers directly
  function createHandlers() {
    return {
      getWinners: async (req: Request, res: Response, next: NextFunction) => {
        try {
          await winnerController.getWinners(req, res);
        } catch (error) {
          next(error);
        }
      },
      getWinnerDetails: async (req: Request, res: Response, next: NextFunction) => {
        try {
          await winnerController.getWinnerDetails(req, res);
        } catch (error) {
          next(error);
        }
      },
      claimPrize: async (req: Request, res: Response, next: NextFunction) => {
        try {
          await winnerController.claimPrize(req, res);
        } catch (error) {
          next(error);
        }
      },
      updateWinnerStatus: async (req: Request, res: Response, next: NextFunction) => {
        try {
          await winnerController.updateWinnerStatus(req, res);
        } catch (error) {
          next(error);
        }
      }
    };
  }
  
  const handlers = createHandlers();

  // Routes for listing winners
  router.get('/giveaway/:id', handlers.getWinners);
  router.get('/:id', handlers.getWinnerDetails);

  // Routes requiring authentication
  router.post('/:id/claim', authMiddleware, handlers.claimPrize);

  // Admin-only routes
  router.put('/:id/status', authMiddleware, adminMiddleware, handlers.updateWinnerStatus);

  return router;
} 