import { Router } from 'express';
import { createPrizeController } from '../controllers/prize.controller.js';
import { PrizeService } from '../services/prize/prize-service.interface.js';
import { Request, Response, NextFunction } from 'express';

/**
 * Authentication middleware type
 */
type AuthMiddleware = (req: Request, res: Response, next: NextFunction) => Promise<void> | void;

/**
 * Authorization middleware type (for admin-only endpoints)
 */
type AdminMiddleware = (req: Request, res: Response, next: NextFunction) => Promise<void> | void;

/**
 * Create prize routes
 * 
 * @param prizeService - Prize service implementation
 * @param authMiddleware - Authentication middleware for protected routes
 * @param adminMiddleware - Authorization middleware for admin-only routes
 * @returns Express router
 */
export function createPrizeRouter(
  prizeService: PrizeService,
  authMiddleware: AuthMiddleware,
  adminMiddleware: AdminMiddleware
): Router {
  const router = Router();
  const prizeController = createPrizeController(prizeService);
  
  // Create all route handlers directly
  function createHandlers() {
    return {
      getPrizes: async (req: Request, res: Response, next: NextFunction) => {
        try {
          await prizeController.getPrizes(req, res);
        } catch (error) {
          next(error);
        }
      },
      getPrizeDetails: async (req: Request, res: Response, next: NextFunction) => {
        try {
          await prizeController.getPrizeDetails(req, res);
        } catch (error) {
          next(error);
        }
      },
      createPrize: async (req: Request, res: Response, next: NextFunction) => {
        try {
          await prizeController.createPrize(req, res);
        } catch (error) {
          next(error);
        }
      },
      updatePrize: async (req: Request, res: Response, next: NextFunction) => {
        try {
          await prizeController.updatePrize(req, res);
        } catch (error) {
          next(error);
        }
      },
      deletePrize: async (req: Request, res: Response, next: NextFunction) => {
        try {
          await prizeController.deletePrize(req, res);
        } catch (error) {
          next(error);
        }
      }
    };
  }
  
  const handlers = createHandlers();

  // Public endpoints for viewing prizes
  router.get('/giveaway/:id', handlers.getPrizes);
  router.get('/:id', handlers.getPrizeDetails);

  // Admin-only routes for managing prizes
  router.post('/giveaway/:giveawayId', authMiddleware, adminMiddleware, handlers.createPrize);
  router.put('/:id', authMiddleware, adminMiddleware, handlers.updatePrize);
  router.delete('/:id', authMiddleware, adminMiddleware, handlers.deletePrize);

  return router;
} 