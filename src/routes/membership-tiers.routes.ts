import { Router } from 'express';
import { createMembershipTiersController } from '../controllers/membership-tiers.controller.js';
import { MembershipService } from '../services/membership/membership-service.interface.js';
import { Request, Response, NextFunction } from 'express';

/**
 * Authentication middleware type
 */
type AuthMiddleware = (req: Request, res: Response, next: NextFunction) => Promise<void> | void;

/**
 * Authorization middleware type (for admin-only endpoints)
 */
type AdminMiddleware = (req: Request, res: Response, next: NextFunction) => Promise<void> | void;

/**
 * Create membership tiers routes
 * 
 * @param membershipService - Membership service implementation
 * @param authMiddleware - Authentication middleware for protected routes
 * @param adminMiddleware - Authorization middleware for admin-only routes
 * @returns Express router
 */
export function createMembershipTiersRouter(
  membershipService: MembershipService,
  authMiddleware: AuthMiddleware,
  adminMiddleware: AdminMiddleware
): Router {
  const router = Router();
  const membershipTiersController = createMembershipTiersController(membershipService);
  
  // Create all route handlers
  function createHandlers() {
    return {
      // Public handlers
      getPublicTiers: async (req: Request, res: Response, next: NextFunction) => {
        try {
          await membershipTiersController.getPublicTiers(req, res);
        } catch (error) {
          next(error);
        }
      },
      getPublicTierById: async (req: Request, res: Response, next: NextFunction) => {
        try {
          await membershipTiersController.getPublicTierById(req, res);
        } catch (error) {
          next(error);
        }
      },
      
      // Admin handlers
      getAllTiers: async (req: Request, res: Response, next: NextFunction) => {
        try {
          await membershipTiersController.getAllTiers(req, res);
        } catch (error) {
          next(error);
        }
      },
      getTierById: async (req: Request, res: Response, next: NextFunction) => {
        try {
          await membershipTiersController.getTierById(req, res);
        } catch (error) {
          next(error);
        }
      },
      createTier: async (req: Request, res: Response, next: NextFunction) => {
        try {
          await membershipTiersController.createTier(req, res);
        } catch (error) {
          next(error);
        }
      },
      updateTier: async (req: Request, res: Response, next: NextFunction) => {
        try {
          await membershipTiersController.updateTier(req, res);
        } catch (error) {
          next(error);
        }
      },
      seedTiers: async (req: Request, res: Response, next: NextFunction) => {
        try {
          await membershipTiersController.seedTiers(req, res);
        } catch (error) {
          next(error);
        }
      }
    };
  }
  
  const handlers = createHandlers();

  // Public endpoints for viewing membership tiers - no auth required
  // Change route to root ('/') for public tiers, so it can be mounted at a public path
  router.get('/', handlers.getPublicTiers);
  router.get('/:id', handlers.getPublicTierById);

  // Admin endpoints for managing tiers - require auth and admin role
  router.get('/admin', authMiddleware, adminMiddleware, handlers.getAllTiers);
  router.get('/admin/:id', authMiddleware, adminMiddleware, handlers.getTierById);
  router.post('/admin', authMiddleware, adminMiddleware, handlers.createTier);
  router.put('/admin/:id', authMiddleware, adminMiddleware, handlers.updateTier);
  router.post('/admin/seed', authMiddleware, adminMiddleware, handlers.seedTiers);

  return router;
} 