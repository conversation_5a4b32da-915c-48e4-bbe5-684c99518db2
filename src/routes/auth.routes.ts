import { Router } from 'express';
import { createAuthHandlers } from '../controllers/auth.controller.js';
import { AuthService } from '../services/auth/auth-service.interface.js';
import { Request, Response, NextFunction } from 'express';

type AuthMiddleware = (req: Request, res: Response, next: NextFunction) => Promise<void> | void;

/**
 * Create authentication routes
 *
 * @param authService - Authentication service implementation
 * @param authMiddleware - Authentication middleware for protected routes
 * @returns Express router
 */
export function createAuthRouter(
  authService: AuthService,
  authMiddleware?: AuthMiddleware
): Router {
  const router = Router();
  const authHandlers = createAuthHandlers(authService);

  // Public routes
  router.post('/register', authHandlers.register);
  router.post('/login', authHandlers.login);
  router.get('/verify-email/:token', authHandlers.verifyEmail); // Redirect endpoint for email links
  router.get('/api/verify-email/:token', authHandlers.verifyEmailAPI); // JSON API endpoint
  router.post('/forgot-password', authHandlers.forgotPassword);
  router.post('/reset-password', authHandlers.resetPassword);
  router.post('/refresh-token', authHandlers.refreshToken);
  router.post('/resend-verification-public', authHandlers.resendVerificationEmailByEmail);

  // If no middleware provided, skip protected routes
  if (!authMiddleware) {
    return router;
  }

  // Protected routes (require authentication)
  router.post('/logout', authMiddleware, authHandlers.logout);
  router.post('/resend-verification', authMiddleware, authHandlers.resendVerificationEmail);

  return router;
}