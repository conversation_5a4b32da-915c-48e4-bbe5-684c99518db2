import { Router } from 'express';
import { createPaymentController } from '../controllers/payment.controller.js';
import { PaymentServiceInterface } from '../services/payment/payment-service.interface.js';
import { Request, Response, NextFunction } from 'express';

/**
 * Authentication middleware type
 */
type AuthMiddleware = (req: Request, res: Response, next: NextFunction) => Promise<void> | void;

/**
 * Authorization middleware type (for admin-only endpoints)
 */
type AdminMiddleware = (req: Request, res: Response, next: NextFunction) => Promise<void> | void;

/**
 * Create payment routes
 * 
 * @param paymentService - Payment service implementation
 * @param authMiddleware - Authentication middleware for protected routes
 * @param adminMiddleware - Authorization middleware for admin-only routes
 * @returns Express router
 */
export function createPaymentRouter(
  paymentService: PaymentServiceInterface,
  authMiddleware: AuthMiddleware,
  adminMiddleware: AdminMiddleware
): Router {
  const router = Router();
  const paymentController = createPaymentController(paymentService);
  
  // Create all route handlers directly
  function createHandlers() {
    return {
      createPaymentIntent: async (req: Request, res: Response, next: NextFunction) => {
        try {
          await paymentController.createPaymentIntent(req, res);
        } catch (error) {
          next(error);
        }
      },
      processPayment: async (req: Request, res: Response, next: NextFunction) => {
        try {
          await paymentController.processPayment(req, res);
        } catch (error) {
          next(error);
        }
      },
      getTransactions: async (req: Request, res: Response, next: NextFunction) => {
        try {
          await paymentController.getTransactions(req, res);
        } catch (error) {
          next(error);
        }
      },
      getTransactionDetails: async (req: Request, res: Response, next: NextFunction) => {
        try {
          await paymentController.getTransactionDetails(req, res);
        } catch (error) {
          next(error);
        }
      },
      processRefund: async (req: Request, res: Response, next: NextFunction) => {
        try {
          await paymentController.processRefund(req, res);
        } catch (error) {
          next(error);
        }
      },
      handleWebhook: async (req: Request, res: Response, next: NextFunction) => {
        try {
          await paymentController.handleWebhook(req, res);
        } catch (error) {
          next(error);
        }
      },
      createSetupIntent: async (req: Request, res: Response, next: NextFunction) => {
        try {
          await paymentController.createSetupIntent(req, res);
        } catch (error) {
          next(error);
        }
      },
      createPaymentMethod: async (req: Request, res: Response, next: NextFunction) => {
        try {
          await paymentController.createPaymentMethod(req, res);
        } catch (error) {
          next(error);
        }
      },
      getPaymentMethods: async (req: Request, res: Response, next: NextFunction) => {
        try {
          await paymentController.getPaymentMethods(req, res);
        } catch (error) {
          next(error);
        }
      },
      updatePaymentMethod: async (req: Request, res: Response, next: NextFunction) => {
        try {
          await paymentController.updatePaymentMethod(req, res);
        } catch (error) {
          next(error);
        }
      },
      deletePaymentMethod: async (req: Request, res: Response, next: NextFunction) => {
        try {
          await paymentController.deletePaymentMethod(req, res);
        } catch (error) {
          next(error);
        }
      }
    };
  }
  
  const handlers = createHandlers();

  // Routes requiring authentication
  router.post('/intent', authMiddleware, handlers.createPaymentIntent);
  router.post('/payment-intent', authMiddleware, handlers.createPaymentIntent); // Alternative route for frontend compatibility
  router.post('/process', authMiddleware, handlers.processPayment);
  router.get('/transactions', authMiddleware, handlers.getTransactions);
  router.get('/transactions/:id', authMiddleware, handlers.getTransactionDetails);
  
  // Payment method routes
  router.post('/setup-intent', authMiddleware, handlers.createSetupIntent);
  router.post('/methods', authMiddleware, handlers.createPaymentMethod);
  router.get('/methods', authMiddleware, handlers.getPaymentMethods);
  router.put('/methods/:id', authMiddleware, handlers.updatePaymentMethod);
  router.delete('/methods/:id', authMiddleware, handlers.deletePaymentMethod);
  
  // Admin-only routes
  router.post('/refund', authMiddleware, adminMiddleware, handlers.processRefund);
  
  // Webhook route (no auth required as it's called by payment provider)
  router.post('/webhook', handlers.handleWebhook);

  return router;
}