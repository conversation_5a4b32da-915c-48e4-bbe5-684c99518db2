import { Router } from 'express';
import { createAdminDashboardController } from '../../controllers/admin/dashboard.controller.js';
import { AdminServiceInterface } from '../../services/admin/admin-service.interface.js';
import { Request, Response, NextFunction } from 'express';

/**
 * Authentication middleware type
 */
type AuthMiddleware = (req: Request, res: Response, next: NextFunction) => Promise<void> | void;

/**
 * Authorization middleware type (for admin-only endpoints)
 */
type AdminMiddleware = (req: Request, res: Response, next: NextFunction) => Promise<void> | void;

/**
 * Create admin dashboard routes
 * 
 * @param adminService - Admin service implementation
 * @param authMiddleware - Authentication middleware for protected routes
 * @param adminMiddleware - Authorization middleware for admin-only routes
 * @returns Express router
 */
export function createAdminDashboardRouter(
  adminService: AdminServiceInterface,
  authMiddleware: AuthMiddleware,
  adminMiddleware: AdminMiddleware
): Router {
  const router = Router();
  const adminDashboardController = createAdminDashboardController(adminService);
  
  // Create all route handlers directly
  function createHandlers() {
    return {
      getDashboardStats: async (req: Request, res: Response, next: NextFunction) => {
        try {
          await adminDashboardController.getDashboardStats(req, res);
        } catch (error) {
          next(error);
        }
      },
      getUserStats: async (req: Request, res: Response, next: NextFunction) => {
        try {
          await adminDashboardController.getUserStats(req, res);
        } catch (error) {
          next(error);
        }
      },
      getMembershipStats: async (req: Request, res: Response, next: NextFunction) => {
        try {
          await adminDashboardController.getMembershipStats(req, res);
        } catch (error) {
          next(error);
        }
      },
      getGiveawayStats: async (req: Request, res: Response, next: NextFunction) => {
        try {
          await adminDashboardController.getGiveawayStats(req, res);
        } catch (error) {
          next(error);
        }
      },
      getRevenueStats: async (req: Request, res: Response, next: NextFunction) => {
        try {
          await adminDashboardController.getRevenueStats(req, res);
        } catch (error) {
          next(error);
        }
      },
      generateReport: async (req: Request, res: Response, next: NextFunction) => {
        try {
          await adminDashboardController.generateReport(req, res);
        } catch (error) {
          next(error);
        }
      }
    };
  }
  
  const handlers = createHandlers();

  // All routes require authentication and admin privileges
  router.get('/stats', authMiddleware, adminMiddleware, handlers.getDashboardStats);
  router.get('/stats/users', authMiddleware, adminMiddleware, handlers.getUserStats);
  router.get('/stats/memberships', authMiddleware, adminMiddleware, handlers.getMembershipStats);
  router.get('/stats/giveaways', authMiddleware, adminMiddleware, handlers.getGiveawayStats);
  router.get('/stats/revenue', authMiddleware, adminMiddleware, handlers.getRevenueStats);
  router.post('/reports', authMiddleware, adminMiddleware, handlers.generateReport);

  return router;
} 