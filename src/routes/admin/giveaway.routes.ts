import { Router } from 'express';
import { createAdminGiveawayController } from '../../controllers/admin/giveaway.controller.js';
import { GiveawayService } from '../../services/giveaway/giveaway-service.interface.js';
import { Request, Response, NextFunction } from 'express';

/**
 * Authentication middleware type
 */
type AuthMiddleware = (req: Request, res: Response, next: NextFunction) => Promise<void> | void;

/**
 * Authorization middleware type (for admin-only endpoints)
 */
type AdminMiddleware = (req: Request, res: Response, next: NextFunction) => Promise<void> | void;

/**
 * Create admin giveaway routes
 *
 * @param giveawayService - Giveaway service implementation
 * @param authMiddleware - Authentication middleware for protected routes
 * @param adminMiddleware - Authorization middleware for admin-only routes
 * @returns Express router
 */
export function createAdminGiveawayRouter(
  giveawayService: GiveawayService,
  authMiddleware: AuthMiddleware,
  adminMiddleware: AdminMiddleware
): Router {
  const router = Router();
  const adminGiveawayController = createAdminGiveawayController(giveawayService);

  /**
   * Create route handlers with error handling
   */
  function createHandlers() {
    const handlers = createAdminGiveawayRouteHandlers(adminGiveawayController);

    return {
      listGiveaways: async (req: Request, res: Response, next: NextFunction) => {
        try {
          await handlers.listGiveaways(req, res, next);
        } catch (error) {
          next(error);
        }
      },
      getGiveawayById: async (req: Request, res: Response, next: NextFunction) => {
        try {
          await handlers.getGiveawayById(req, res, next);
        } catch (error) {
          next(error);
        }
      },
      createGiveaway: async (req: Request, res: Response, next: NextFunction) => {
        try {
          await handlers.createGiveaway(req, res, next);
        } catch (error) {
          next(error);
        }
      },
      updateGiveaway: async (req: Request, res: Response, next: NextFunction) => {
        try {
          await handlers.updateGiveaway(req, res, next);
        } catch (error) {
          next(error);
        }
      },
      deleteGiveaway: async (req: Request, res: Response, next: NextFunction) => {
        try {
          await handlers.deleteGiveaway(req, res, next);
        } catch (error) {
          next(error);
        }
      },
      getEntries: async (req: Request, res: Response, next: NextFunction) => {
        try {
          await handlers.getEntries(req, res, next);
        } catch (error) {
          next(error);
        }
      },
      getPrizes: async (req: Request, res: Response, next: NextFunction) => {
        try {
          await handlers.getPrizes(req, res, next);
        } catch (error) {
          next(error);
        }
      },
      conductDraw: async (req: Request, res: Response, next: NextFunction) => {
        try {
          await handlers.conductDraw(req, res, next);
        } catch (error) {
          next(error);
        }
      },
      publishGiveaway: async (req: Request, res: Response, next: NextFunction) => {
        try {
          await handlers.publishGiveaway(req, res, next);
        } catch (error) {
          next(error);
        }
      }
    };
  }

  const handlers = createHandlers();

  // All routes require authentication and admin privileges
  router.get('/', authMiddleware, adminMiddleware, handlers.listGiveaways);
  router.get('/:id', authMiddleware, adminMiddleware, handlers.getGiveawayById);
  router.post('/', authMiddleware, adminMiddleware, handlers.createGiveaway);
  router.put('/:id', authMiddleware, adminMiddleware, handlers.updateGiveaway);
  router.delete('/:id', authMiddleware, adminMiddleware, handlers.deleteGiveaway);
  router.get('/:id/entries', authMiddleware, adminMiddleware, handlers.getEntries);
  router.get('/:id/prizes', authMiddleware, adminMiddleware, handlers.getPrizes);
  router.post('/:id/draw', authMiddleware, adminMiddleware, handlers.conductDraw);
  router.post('/:id/publish', authMiddleware, adminMiddleware, handlers.publishGiveaway);

  return router;
}

/**
 * Import route handlers from controller
 */
import { createAdminGiveawayRouteHandlers } from '../../controllers/admin/giveaway.controller.js';
