import { Router } from 'express';
import { createAdminUsersController } from '../../controllers/admin/users.controller.js';
import { AdminServiceInterface } from '../../services/admin/admin-service.interface.js';
import { Request, Response, NextFunction } from 'express';

/**
 * Authentication middleware type
 */
type AuthMiddleware = (req: Request, res: Response, next: NextFunction) => Promise<void> | void;

/**
 * Authorization middleware type (for admin-only endpoints)
 */
type AdminMiddleware = (req: Request, res: Response, next: NextFunction) => Promise<void> | void;

/**
 * Create admin users routes
 * 
 * @param adminService - Admin service implementation
 * @param authMiddleware - Authentication middleware for protected routes
 * @param adminMiddleware - Authorization middleware for admin-only routes
 * @returns Express router
 */
export function createAdminUsersRouter(
  adminService: AdminServiceInterface,
  authMiddleware: AuthMiddleware,
  adminMiddleware: AdminMiddleware
): Router {
  const router = Router();
  const adminUsersController = createAdminUsersController(adminService);
  
  // Create all route handlers directly
  function createHandlers() {
    return {
      getUserById: async (req: Request, res: Response, next: NextFunction) => {
        try {
          await adminUsersController.getUserById(req, res);
        } catch (error) {
          next(error);
        }
      },
      updateUserRole: async (req: Request, res: Response, next: NextFunction) => {
        try {
          await adminUsersController.updateUserRole(req, res);
        } catch (error) {
          next(error);
        }
      },
      deleteUser: async (req: Request, res: Response, next: NextFunction) => {
        try {
          await adminUsersController.deleteUser(req, res);
        } catch (error) {
          next(error);
        }
      }
    };
  }
  
  const handlers = createHandlers();

  // All routes require authentication and admin privileges
  router.get('/:id', authMiddleware, adminMiddleware, handlers.getUserById);
  router.put('/:id/role', authMiddleware, adminMiddleware, handlers.updateUserRole);
  router.delete('/:id', authMiddleware, adminMiddleware, handlers.deleteUser);

  return router;
} 