// filepath: /Users/<USER>/Documents/Workspace/winners-society/src/dtos/entry.dto.ts
// Import necessary dependencies
import { BaseResponseDto, FilterParamsDto } from './base.dto.js';
import { EntryMethod } from '@prisma/client';

// Create an entry response DTO
export interface EntryResponseDto extends BaseResponseDto {
  giveawayId: string;
  userId: string;
  membershipId?: string;
  entryDate: Date;
  entryMethod: EntryMethod;
  referenceId?: string;
}

// Create an entry create DTO
export interface EntryCreateDto {
  giveawayId: string;
  entryMethod: EntryMethod;
  referenceId?: string;
}

// Create an entry with user response DTO
export interface EntryWithUserResponseDto extends EntryResponseDto {
  user: {
    id: string;
    email: string;
    firstName: string;
    lastName: string;
  };
}

// Create an entry filter params DTO
export interface EntryFilterParamsDto extends FilterParamsDto {
  giveawayId?: string;
  userId?: string;
  membershipId?: string;
  entryMethod?: EntryMethod;
  entryDateStart?: Date;
  entryDateEnd?: Date;
}
