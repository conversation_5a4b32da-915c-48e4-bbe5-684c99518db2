// filepath: /Users/<USER>/Documents/Workspace/winners-society/src/dtos/profile.dto.ts
// Import necessary dependencies
import { BaseResponseDto } from './base.dto.js';

// Create a profile response DTO
export interface ProfileResponseDto extends BaseResponseDto {
  bio?: string;
  avatar?: string;
  phone?: string;
  address?: string;
  city?: string;
  state?: string;
  zipCode?: string;
  country?: string;
  birthDate?: Date;
  userId: string;
}

// Create a profile create DTO
export interface ProfileCreateDto {
  bio?: string;
  avatar?: string;
  phone?: string;
  address?: string;
  city?: string;
  state?: string;
  zipCode?: string;
  country?: string;
  birthDate?: Date;
}

// Create a profile update DTO
export interface ProfileUpdateDto {
  bio?: string;
  avatar?: string;
  phone?: string;
  address?: string;
  city?: string;
  state?: string;
  zipCode?: string;
  country?: string;
  birthDate?: Date;
}
