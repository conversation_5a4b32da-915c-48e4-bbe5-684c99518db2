// Import necessary dependencies
import { PaymentStatus } from '@prisma/client';
import { BaseResponseDto, FilterParamsDto } from './base.dto.js';

// Create a transaction response DTO
export interface TransactionResponseDto extends BaseResponseDto {
  userId: string;
  membershipId?: string;
  amount: number;
  currency: string;
  status: PaymentStatus;
  paymentMethod: string;
  paymentIntentId?: string;
  description?: string;
  metadata?: Record<string, unknown>;
}

// Create a payment intent create DTO
export interface PaymentIntentCreateDto {
  planId: string;
  description?: string;
  metadata?: Record<string, unknown>;
}

// Create a payment intent response DTO
export interface PaymentIntentResponseDto {
  clientSecret: string;
  intentId: string;
  amount: number;
  currency: string;
}

/**
 * Payment Process DTO
 *
 * Used for processing payments with an existing payment intent.
 * Both paymentIntentId and paymentMethodId are required.
 * The payment intent must be created first using the payment-intent endpoint.
 */
export interface PaymentProcessDto {
  paymentIntentId: string;  // Required - ID of the previously created payment intent
  paymentMethodId: string;  // Required - ID of the payment method to use
}

// Create a refund create DTO
export interface RefundCreateDto {
  transactionId: string;
  amount?: number;
  reason?: string;
}

// Create a refund response DTO
export interface RefundResponseDto {
  id: string;
  transactionId: string;
  amount: number;
  status: string;
  createdAt: Date;
}

// Create a transaction filter params DTO
export interface TransactionFilterParamsDto extends FilterParamsDto {
  userId?: string;
  membershipId?: string;
  status?: PaymentStatus;
  paymentMethod?: string;
  amountMin?: number;
  amountMax?: number;
  createdAtStart?: Date;
  createdAtEnd?: Date;
}
