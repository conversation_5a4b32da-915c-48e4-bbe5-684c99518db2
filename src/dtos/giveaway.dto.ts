// filepath: /Users/<USER>/Documents/Workspace/winners-society/src/dtos/giveaway.dto.ts
// Import necessary dependencies
import { BaseResponseDto, FilterParamsDto } from './base.dto.js';
import { GiveawayStatus } from '@prisma/client';
import { PrizeResponseDto } from './prize.dto.js';
import { SelectionMethod } from './winner.dto.js';

/**
 * Giveaway type enumeration
 */
export enum GiveawayType {
  STANDARD = 'STANDARD',
  PREMIUM = 'PREMIUM',
  EXCLUSIVE = 'EXCLUSIVE',
  FLASH = 'FLASH'
}

/**
 * Giveaway entry data
 */
export interface GiveawayEntryDto {
  userId: string;
  giveawayId: string;
  quantity: number;
  referenceId?: string;
}

// Create a giveaway response DTO
export interface GiveawayResponseDto extends BaseResponseDto {
  title: string;
  description: string;
  startDate: Date;
  endDate: Date;
  drawDate: Date;  // Added from service interface
  status: GiveawayStatus;
  featuredImage?: string;
  prizeValue: number;
  prizeDetails: string;
  imageUrl?: string;
  category?: string;
  tags: string[];
  rules: string;
  termsAndConditions?: string;
  minTier?: string;
  requiredMembershipTierId?: string;  // Added from service interface
  maxEntries: number;
  entryLimit?: number;  // Added from service interface
  isActive: boolean;
  type?: GiveawayType;  // Added from service interface
  selectionMethod?: SelectionMethod;  // Added from service interface
  metadata?: Record<string, unknown>;  // Added from service interface
  winnerId?: string;
  entryCount?: number;
  prizes?: PrizeResponseDto[];
}

// Create a giveaway create DTO
export interface GiveawayCreateDto {
  title: string;
  description: string;
  startDate: Date;
  endDate: Date;
  drawDate: Date;  // Added from service interface
  status?: GiveawayStatus;
  featuredImage?: string;
  prizeValue: number;
  prizeDetails: string;
  imageUrl?: string;
  category?: string;
  tags?: string[];
  rules: string;
  termsAndConditions?: string;
  minTier?: string;
  requiredMembershipTierId?: string;  // Added from service interface
  maxEntries: number;
  entryLimit?: number;  // Added from service interface
  isActive?: boolean;
  type?: GiveawayType;  // Added from service interface
  selectionMethod?: SelectionMethod;  // Added from service interface
  metadata?: Record<string, unknown>;  // Added from service interface
}

// Create a giveaway update DTO
export interface GiveawayUpdateDto {
  title?: string;
  description?: string;
  startDate?: Date;
  endDate?: Date;
  drawDate?: Date;  // Added from service interface
  status?: GiveawayStatus;
  featuredImage?: string;
  prizeValue?: number;
  prizeDetails?: string;
  imageUrl?: string;
  category?: string;
  tags?: string[];
  rules?: string;
  termsAndConditions?: string;
  minTier?: string;
  requiredMembershipTierId?: string;  // Added from service interface
  maxEntries?: number;
  entryLimit?: number;  // Added from service interface
  isActive?: boolean;
  type?: GiveawayType;  // Added from service interface
  selectionMethod?: SelectionMethod;  // Added from service interface
  metadata?: Record<string, unknown>;  // Added from service interface
  winnerId?: string;
}

// Create a giveaway filter params DTO
export interface GiveawayFilterParamsDto extends FilterParamsDto {
  status?: GiveawayStatus;
  category?: string;
  tags?: string[];
  startDateStart?: Date;
  startDateEnd?: Date;
  endDateStart?: Date;
  endDateEnd?: Date;
  search?: string;
  type?: GiveawayType;  // Added from service interface
}

// Create a giveaway with details response DTO
export interface GiveawayWithDetailsResponseDto extends GiveawayResponseDto {
  prizes: PrizeResponseDto[];
  entryCount: number;
  userEntryCount?: number;
  userCanEnter?: boolean;
  remainingEntries?: number;
}
