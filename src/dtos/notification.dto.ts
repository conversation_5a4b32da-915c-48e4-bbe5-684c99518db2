// filepath: /Users/<USER>/Documents/Workspace/winners-society/src/dtos/notification.dto.ts
// Import necessary dependencies
import { BaseResponseDto, FilterParamsDto } from './base.dto.js';
import { NotificationType, NotificationStatus } from '@prisma/client';

// Create a notification response DTO
export interface NotificationResponseDto extends BaseResponseDto {
  userId: string;
  title: string;
  message: string;
  type: NotificationType;
  status: NotificationStatus;
  link?: string;
  metadata?: Record<string, any>;
}

// Create a notification create DTO
export interface NotificationCreateDto {
  userId: string;
  title: string;
  message: string;
  type: NotificationType;
  link?: string;
  metadata?: Record<string, any>;
}

// Create a notification update DTO
export interface NotificationUpdateDto {
  status?: NotificationStatus;
}

// Create a notification filter params DTO
export interface NotificationFilterParamsDto extends FilterParamsDto {
  userId?: string;
  type?: NotificationType;
  status?: NotificationStatus;
  createdAtStart?: Date;
  createdAtEnd?: Date;
}
