// filepath: /Users/<USER>/Documents/Workspace/winners-society/src/dtos/winner.dto.ts
// Import necessary dependencies
import { BaseResponseDto, FilterParamsDto } from './base.dto.js';
import { WinnerStatus } from '@prisma/client';

/**
 * Winner selection method enumeration
 */
export enum SelectionMethod {
  RANDOM = 'RANDOM',
  WEIGHTED = 'WEIGHTED'
}

// Create a shipping details DTO
export interface ShippingDetailsDto {
  fullName: string;
  addressLine1: string;
  addressLine2?: string;
  city: string;
  state: string;
  postalCode: string;
  country: string;
  phoneNumber?: string;
  specialInstructions?: string;
}

/**
 * Winner selection input data
 */
export interface WinnerSelectionDto {
  giveawayId: string;
  numberOfWinners: number;
  selectAlternates?: boolean;
  numberOfAlternates?: number;
  forceDraw?: boolean; // Allow drawing before end date in special cases
}

/**
 * Winner selection result
 */
export interface WinnerSelectionResultDto {
  giveawayId: string;
  winners: Array<{
    userId: string;
    entryId: string;
    prizeId: string;
    isAlternate: boolean;
  }>;
  drawDate: Date;
  drawBy: string;
}

// Create a winner response DTO
export interface WinnerResponseDto extends BaseResponseDto {
  giveawayId: string;
  prizeId: string;
  userId: string;
  entryId: string;
  selectionDate: Date;
  status: WinnerStatus;
  claimDate?: Date;
  shippingDetails?: ShippingDetailsDto;
  isAlternate?: boolean;
}

// Create a winner create DTO (for admin use)
export interface WinnerCreateDto {
  giveawayId: string;
  prizeId: string;
  userId: string;
  entryId: string;
  isAlternate?: boolean;
}

// Create a winner update DTO
export interface WinnerUpdateDto {
  status?: WinnerStatus;
  claimDate?: Date;
  shippingDetails?: ShippingDetailsDto;
}

// Create a prize claim DTO
export interface PrizeClaimDto {
  shippingDetails: ShippingDetailsDto;
}

// Create a winner with details response DTO
export interface WinnerWithDetailsResponseDto extends WinnerResponseDto {
  user: {
    id: string;
    email: string;
    firstName: string;
    lastName: string;
  };
  prize: {
    id: string;
    name: string;
    value: number;
    currency: string;
    images: string[];
  };
  giveaway: {
    id: string;
    title: string;
  };
}

// Create a winner filter params DTO
export interface WinnerFilterParamsDto extends FilterParamsDto {
  giveawayId?: string;
  prizeId?: string;
  userId?: string;
  status?: WinnerStatus;
  selectionDateStart?: Date;
  selectionDateEnd?: Date;
  claimDateStart?: Date;
  claimDateEnd?: Date;
  isAlternate?: boolean;
}
