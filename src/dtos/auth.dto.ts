// Import necessary dependencies
import { UserResponseDto } from './user.dto.js';

/**
 * Email verification DTO
 */
export interface EmailVerificationDto {
  token: string;
}

/**
 * User login DTO
 */
export interface UserLoginDto {
  email: string;
  password: string;
}

/**
 * Password reset request DTO
 */
export interface PasswordResetRequestDto {
  email: string;
}

/**
 * Password reset DTO
 */
export interface PasswordResetDto {
  token: string;
  newPassword: string;
  confirmPassword: string;
}

/**
 * Auth response DTO
 */
export interface AuthResponseDto {
  user: UserResponseDto;
  accessToken: string;
  refreshToken: string;
  expiresIn: number;
}

/**
 * Token refresh DTO
 */
export interface TokenRefreshDto {
  refreshToken: string;
}

/**
 * Token response DTO
 */
export interface TokenResponseDto {
  accessToken: string;
  refreshToken: string;
  expiresIn: number;
}

/**
 * Registration response DTO (no tokens - user must verify email first)
 */
export interface RegistrationResponseDto {
  user: UserResponseDto;
  message?: string;
}