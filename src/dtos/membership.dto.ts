// filepath: /Users/<USER>/Documents/Workspace/winners-society/src/dtos/membership.dto.ts
// Import necessary dependencies
import { BaseResponseDto, FilterParamsDto } from './base.dto.js';
import { UserResponseDto } from './user.dto.js';
import { MembershipStatus, BillingPeriod } from '@prisma/client';

// Define membership tier response DTO 
export interface MembershipTierResponseDto extends BaseResponseDto {
  name: string;
  description: string;
  price: number;
  currency: string;
  billingPeriod: BillingPeriod;
  entryAllocation: number;
  duration: number;
  features: any; // JSON array of included features
  benefits: string[];
  isActive: boolean;
  displayOrder: number;
}

// Define membership tier create DTO
export interface MembershipTierCreateDto {
  name: string;
  description: string;
  price: number;
  currency?: string;
  billingPeriod?: BillingPeriod;
  entryAllocation: number;
  duration: number;
  features?: any; // JSON array of included features
  benefits: string[];
  isActive?: boolean;
  displayOrder?: number;
}

// Define membership tier update DTO
export interface MembershipTierUpdateDto {
  name?: string;
  description?: string;
  price?: number;
  currency?: string;
  billingPeriod?: BillingPeriod;
  entryAllocation?: number;
  duration?: number;
  features?: any; // JSON array of included features
  benefits?: string[];
  isActive?: boolean;
  displayOrder?: number;
}

// Define membership history entry DTO
export interface MembershipHistoryEntryDto extends BaseResponseDto {
  membershipId: string;
  action: 'CREATED' | 'UPGRADED' | 'RENEWED' | 'CANCELLED' | 'EXPIRED';
  tierId: string;
  tierName: string;
  priceAtTime: number;
  notes?: string;
}

// Define membership response DTO 
export interface MembershipResponseDto extends BaseResponseDto {
  userId: string;
  user?: UserResponseDto;
  membershipTierId: string;
  membershipTier?: MembershipTierResponseDto;
  startDate: Date;
  endDate: Date;
  status: MembershipStatus;
  autoRenew: boolean;
  membershipHistory: any; // Track membership changes as JSON
  paymentMethodId?: string;
}

// Define membership subscribe DTO
export interface MembershipSubscribeDto {
  membershipTierId: string;
  autoRenew: boolean;
  paymentMethodId?: string;
}

// Define membership upgrade DTO
export interface MembershipUpgradeDto {
  newMembershipTierId: string;
  paymentMethodId?: string;
}

// Define membership cancel DTO
export interface MembershipCancelDto {
  reason?: string;
  feedback?: string;
}

// Define membership auto-renew DTO
export interface MembershipAutoRenewDto {
  autoRenew: boolean;
}

// Define membership filter params DTO
export interface MembershipFilterParamsDto extends FilterParamsDto {
  userId?: string;
  membershipTierId?: string;
  status?: MembershipStatus;
  autoRenew?: boolean;
  startDateFrom?: Date;
  startDateTo?: Date;
  endDateFrom?: Date;
  endDateTo?: Date;
}
