// filepath: /Users/<USER>/Documents/Workspace/winners-society/src/dtos/content.dto.ts
// Import necessary dependencies
import { BaseResponseDto, FilterParamsDto } from './base.dto.js';
import { ContentStatus, AccessLevel } from '@prisma/client';

// Create a content response DTO
export interface ContentResponseDto extends BaseResponseDto {
  title: string;
  slug: string;
  content: string;
  excerpt?: string;
  featuredImage?: string;
  authorId: string;
  author?: {
    id: string;
    firstName: string;
    lastName: string;
  };
  status: ContentStatus;
  publishDate?: Date;
  category?: string;
  tags: string[];
  accessLevel: AccessLevel;
}

// Create a content create DTO
export interface ContentCreateDto {
  title: string;
  slug: string;
  content: string;
  excerpt?: string;
  featuredImage?: string;
  status?: ContentStatus;
  publishDate?: Date;
  category?: string;
  tags?: string[];
  accessLevel?: AccessLevel;
}

// Create a content update DTO
export interface ContentUpdateDto {
  title?: string;
  slug?: string;
  content?: string;
  excerpt?: string;
  featuredImage?: string;
  status?: ContentStatus;
  publishDate?: Date;
  category?: string;
  tags?: string[];
  accessLevel?: AccessLevel;
}

// Create a content filter params DTO
export interface ContentFilterParamsDto extends FilterParamsDto {
  status?: ContentStatus;
  category?: string;
  tags?: string[];
  accessLevel?: AccessLevel;
  authorId?: string;
  publishDateStart?: Date;
  publishDateEnd?: Date;
  search?: string;
}
