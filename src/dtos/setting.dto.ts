// filepath: /Users/<USER>/Documents/Workspace/winners-society/src/dtos/setting.dto.ts
// Import necessary dependencies
import { BaseResponseDto, FilterParamsDto } from './base.dto.js';

// Create a setting response DTO
export interface SettingResponseDto extends BaseResponseDto {
  key: string;
  value: any;
  description?: string;
  isPublic: boolean;
}

// Create a setting create DTO
export interface SettingCreateDto {
  key: string;
  value: any;
  description?: string;
  isPublic?: boolean;
}

// Create a setting update DTO
export interface SettingUpdateDto {
  value?: any;
  description?: string;
  isPublic?: boolean;
}

// Create a setting filter params DTO
export interface SettingFilterParamsDto extends FilterParamsDto {
  key?: string;
  isPublic?: boolean;
}
