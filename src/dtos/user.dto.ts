// Import necessary dependencies
import { BaseResponseDto } from './base.dto.js';
import { Role } from '@prisma/client';
import { MembershipResponseDto } from './membership.dto.js';

// Create a user registration DTO
export interface UserRegistrationDto {
  email: string;
  password: string;
  firstName: string;
  lastName: string;
  addressLine1: string;
  city: string;
  state: string;
  postalCode: string;
  country: string;
  profileImage?: string;
}

// Create a user login DTO
export interface UserLoginDto {
  email: string;
  password: string;
}

// Create a user update DTO
export interface UserUpdateDto {
  firstName?: string;
  lastName?: string;
  profileImage?: string;
  preferences?: Record<string, any>;
}

// Create a password change DTO
export interface PasswordChangeDto {
  currentPassword: string;
  newPassword: string;
  confirmPassword: string;
}

// Create a password reset request DTO
export interface PasswordResetRequestDto {
  email: string;
}

// Create a password reset DTO
export interface PasswordResetDto {
  token: string;
  newPassword: string;
  confirmPassword: string;
}

// Create a user response DTO
export interface UserResponseDto extends BaseResponseDto {
  email: string;
  firstName: string;
  lastName: string;
  profileImage?: string;
  role: Role;
  isVerified: boolean;
  preferences?: Record<string, any>;
  lastLoginAt?: Date;
}

// Create a user with membership response DTO
export interface UserWithMembershipResponseDto extends UserResponseDto {
  membership?: MembershipResponseDto;
}

// Create a user filter params DTO
export interface UserFilterParamsDto {
  email?: string;
  role?: Role;
  isVerified?: boolean;
  createdAtStart?: Date;
  createdAtEnd?: Date;
}
