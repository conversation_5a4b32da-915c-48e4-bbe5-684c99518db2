// filepath: /Users/<USER>/Documents/Workspace/winners-society/src/dtos/payment-method.dto.ts
// Import necessary dependencies
import { BaseResponseDto, FilterParamsDto } from './base.dto.js';
import { PaymentType } from '@prisma/client';

// Create a payment method response DTO
export interface PaymentMethodResponseDto extends BaseResponseDto {
  userId: string;
  type: PaymentType;
  stripePaymentMethodId: string;
  last4?: string;
  brand?: string;
  expiryMonth?: number;
  expiryYear?: number;
  holderName?: string;
  isDefault: boolean;
}

// Create a payment method create DTO
export interface PaymentMethodCreateDto {
  type: PaymentType;
  stripePaymentMethodId: string; // Stripe payment method ID from client
  isDefault?: boolean;
}

// Create a payment method update DTO
export interface PaymentMethodUpdateDto {
  holderName?: string;
  isDefault?: boolean;
}

// Create a payment method filter params DTO
export interface PaymentMethodFilterParamsDto extends FilterParamsDto {
  userId?: string;
  type?: PaymentType;
  isDefault?: boolean;
}

// DTO for frontend to create a payment method with Stripe
export interface StripeDtoForClient {
  paymentMethodType: 'card' | 'bank_account' | 'ideal' | 'sepa_debit';
  clientSecret: string;
}
