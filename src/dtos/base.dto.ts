// Base DTO interfaces that will be extended by specific DTOs
// Include pagination, filtering, and sorting interfaces

// First, let's create a base response DTO interface
export interface BaseResponseDto {
  id: string;
  createdAt: Date;
  updatedAt: Date;
}

// Next, let's create a pagination params DTO
export interface PaginationParamsDto {
  page?: number;
  limit?: number;
  skip?: number;
  take?: number;
}

// Create a pagination meta DTO
export interface PaginationMetaDto {
  total: number;
  page: number;
  limit: number;
  hasNext: boolean;
  hasPrevious: boolean;
}

// Create a paginated response DTO
export interface PaginatedResponseDto<T> {
  data: T[];
  meta: PaginationMetaDto;
}

// Create a filter params base interface
export interface FilterParamsDto {
  [key: string]: string | number | boolean | Date | string[] | number[] | undefined;
}

// Create a sort params DTO
export interface SortParamsDto {
  field: string;
  direction: 'asc' | 'desc';
}

// Create a base query params DTO that combines pagination, filtering, and sorting
export interface BaseQueryParamsDto extends PaginationParamsDto {
  filters?: FilterParamsDto;
  sort?: SortParamsDto | SortParamsDto[];
}
