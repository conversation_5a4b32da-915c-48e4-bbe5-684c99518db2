/**
 * Admin DTOs
 * 
 * This file contains Data Transfer Objects for admin operations including
 * dashboard statistics, user management, transaction management, and reporting.
 */

import { FilterParamsDto } from './base.dto.js';
import { Role, PaymentStatus, MembershipStatus, GiveawayStatus, ContentStatus } from '@prisma/client';

/**
 * Dashboard statistics response DTO
 */
export interface DashboardStatsResponseDto {
  userStats: {
    totalUsers: number;
    activeUsers: number;
    newUsers: number;
    usersByRole: Record<Role, number>;
  };
  membershipStats: {
    totalMembers: number;
    activeMembers: number;
    membersByTier: Array<{ tier: string; count: number }>;
    membersByStatus: Record<MembershipStatus, number>;
  };
  giveawayStats: {
    totalGiveaways: number;
    activeGiveaways: number;
    completedGiveaways: number;
    giveawaysByStatus: Record<GiveawayStatus, number>;
    totalEntries: number;
    totalWinners: number;
  };
  transactionStats: {
    totalRevenue: number;
    currency: string;
    recentRevenue: number;
    transactionsByStatus: Record<PaymentStatus, number>;
    refundAmount: number;
  };
  contentStats: {
    totalContent: number;
    publishedContent: number;
    contentByStatus: Record<ContentStatus, number>;
    contentByAccessLevel: Record<string, number>;
  };
}

/**
 * Dashboard statistics params DTO
 */
export interface DashboardStatsParamsDto {
  dateRange?: 'day' | 'week' | 'month' | 'year' | 'all';
  startDate?: Date;
  endDate?: Date;
}

/**
 * User management filter params DTO
 */
export interface AdminUserFilterParamsDto extends FilterParamsDto {
  role?: Role;
  email?: string;
  membershipStatus?: MembershipStatus;
  hasActiveMembership?: boolean;
  isVerified?: boolean;
  createdAfter?: Date;
  createdBefore?: Date;
}

/**
 * User role update DTO
 */
export interface UserRoleUpdateDto {
  role: Role;
}

/**
 * Transaction management filter params DTO
 */
export interface AdminTransactionFilterParamsDto extends FilterParamsDto {
  userId?: string;
  status?: PaymentStatus;
  amountMin?: number;
  amountMax?: number;
  dateFrom?: Date;
  dateTo?: Date;
  paymentMethod?: string;
}

/**
 * Transaction refund DTO
 */
export interface TransactionRefundDto {
  amount?: number; // If not specified, full refund
  reason?: string;
  metadata?: Record<string, any>;
}

/**
 * Report type enum
 */
export enum ReportType {
  USER_ACTIVITY = 'USER_ACTIVITY',
  MEMBERSHIP_REVENUE = 'MEMBERSHIP_REVENUE',
  GIVEAWAY_PERFORMANCE = 'GIVEAWAY_PERFORMANCE',
  CONTENT_ENGAGEMENT = 'CONTENT_ENGAGEMENT',
  FINANCIAL_SUMMARY = 'FINANCIAL_SUMMARY'
}

/**
 * Report format enum
 */
export enum ReportFormat {
  CSV = 'CSV',
  JSON = 'JSON',
  PDF = 'PDF',
  EXCEL = 'EXCEL'
}

/**
 * Report generation params DTO
 */
export interface ReportGenerationParamsDto {
  type: ReportType;
  format: ReportFormat;
  startDate?: Date;
  endDate?: Date;
  filters?: Record<string, any>;
  includeFields?: string[];
  excludeFields?: string[];
}

/**
 * Report response DTO
 */
export interface ReportResponseDto {
  id: string;
  type: ReportType;
  format: ReportFormat;
  url: string;
  fileName: string;
  generatedAt: Date;
  expiresAt?: Date;
  metadata?: Record<string, any>;
}

/**
 * System settings update DTO
 */
export interface SystemSettingsUpdateDto {
  [key: string]: any;
}

/**
 * Admin notification creation DTO
 */
export interface AdminNotificationCreateDto {
  title: string;
  message: string;
  targetUserIds?: string[]; // If empty, send to all users
  targetRoles?: Role[];
  link?: string;
  metadata?: Record<string, any>;
} 