// filepath: /Users/<USER>/Documents/Workspace/winners-society/src/dtos/prize.dto.ts
// Import necessary dependencies
import { BaseResponseDto } from './base.dto.js';
import { WinnerResponseDto } from './winner.dto.js';

// Create a prize response DTO
export interface PrizeResponseDto extends BaseResponseDto {
  giveawayId: string;
  name: string;
  description: string;
  value: number;
  currency: string;
  quantity: number;
  images: string[];
  specifications?: Record<string, any>;
  winnerCount?: number;
}

// Create a prize create DTO
export interface PrizeCreateDto {
  giveawayId: string;
  name: string;
  description: string;
  value: number;
  currency?: string;
  quantity?: number;
  images: string[];
  specifications?: Record<string, any>;
}

// Create a prize update DTO
export interface PrizeUpdateDto {
  name?: string;
  description?: string;
  value?: number;
  currency?: string;
  quantity?: number;
  images?: string[];
  specifications?: Record<string, any>;
}

// Create a prize with winners response DTO
export interface PrizeWithWinnersResponseDto extends PrizeResponseDto {
  winners: WinnerResponseDto[];
}
