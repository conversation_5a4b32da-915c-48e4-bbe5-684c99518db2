export const environment = {
  production: false,
  apiUrl: 'http://localhost:3000/api',
  appName: 'Winners Society',
  stripe: {
    publishableKey: 'pk_test_51RPqboFd99NEHyJZR6wN5jWdmN9QYvm5ZbzdBMImK9ucTJHJx2MQ8Tkih9wovCIcWQofuZM65zlZmjGedtRneWnQ00gQlBYl9P', // Replace with your actual test key
    options: {
      locale: 'en', // Default locale
      apiVersion: '2025-03-31.basil', // Stripe API version (latest stable as of May 2025)
      appearance: {
        theme: 'stripe', // 'stripe', 'night', 'flat', or custom
        variables: {
          colorPrimary: '#0d6efd', // Bootstrap primary color
          colorBackground: '#ffffff',
          colorText: '#32325d',
          colorDanger: '#dc3545', // Bootstrap danger color
          fontFamily: 'Roboto, Open Sans, Segoe UI, sans-serif',
          borderRadius: '4px'
        }
      }
    }
  }
};
