/**
 * Giveaway Service Interface
 * 
 * This interface defines the operations specific to giveaway management,
 * including listing, creation, entry management, and winner selection.
 */

import { BaseService } from '../base/base-service.interface.js';
import { PaginatedResult, PaginationOptions } from '../../repositories/base/base-repository.interface.js';
import {
  GiveawayEntryDto,
  GiveawayCreateDto,
  GiveawayUpdateDto,
  GiveawayResponseDto
} from '../../dtos/giveaway.dto.js';
import { WinnerSelectionDto, WinnerSelectionResultDto } from '../../dtos/winner.dto.js';

/**
 * Giveaway Service Interface
 */
export interface GiveawayService extends BaseService<GiveawayResponseDto, GiveawayCreateDto, GiveawayUpdateDto> {
  /**
   * Get active giveaways
   * 
   * @param pagination - Optional pagination options
   * @returns Promise with active giveaways
   */
  getActiveGiveaways(pagination?: PaginationOptions): Promise<PaginatedResult<GiveawayResponseDto>>;

  /**
   * Get upcoming giveaways
   * 
   * @param pagination - Optional pagination options
   * @returns Promise with upcoming giveaways
   */
  getUpcomingGiveaways(pagination?: PaginationOptions): Promise<PaginatedResult<GiveawayResponseDto>>;

  /**
   * Get past giveaways
   * 
   * @param pagination - Optional pagination options
   * @returns Promise with past giveaways
   */
  getPastGiveaways(pagination?: PaginationOptions): Promise<PaginatedResult<GiveawayResponseDto>>;
  
  /**
   * Get giveaways by membership tier
   * 
   * @param membershipTierId - ID of the membership tier
   * @param pagination - Optional pagination options
   * @returns Promise with giveaways for the given tier
   */
  getGiveawaysByMembershipTier(membershipTierId: string, pagination?: PaginationOptions): Promise<PaginatedResult<GiveawayResponseDto>>;

  /**
   * Create a new giveaway
   * 
   * @param data - Giveaway creation data
   * @returns Promise with the created giveaway
   */
  createGiveaway(data: GiveawayCreateDto): Promise<GiveawayResponseDto>;

  /**
   * Update an existing giveaway
   * 
   * @param id - ID of the giveaway to update
   * @param data - Updated giveaway data
   * @returns Promise with the updated giveaway
   */
  updateGiveaway(id: string, data: GiveawayUpdateDto): Promise<GiveawayResponseDto>;

  /**
   * Cancel a giveaway
   * 
   * @param id - ID of the giveaway to cancel
   * @returns Promise with the cancelled giveaway
   */
  cancelGiveaway(id: string): Promise<GiveawayResponseDto>;

  /**
   * Enter a giveaway
   * 
   * @param entryData - Giveaway entry data
   * @returns Promise with boolean indicating success
   */
  enterGiveaway(entryData: GiveawayEntryDto): Promise<boolean>;

  /**
   * Check if a user has entered a giveaway
   * 
   * @param userId - ID of the user
   * @param giveawayId - ID of the giveaway
   * @returns Promise with number of entries or 0 if not entered
   */
  hasUserEnteredGiveaway(userId: string, giveawayId: string): Promise<number>;

  /**
   * Get total entry count for a giveaway
   * 
   * @param giveawayId - ID of the giveaway
   * @returns Promise with the total entry count
   */
  getEntryCount(giveawayId: string): Promise<number>;

  /**
   * Check if a user is eligible to enter a giveaway
   * 
   * @param userId - ID of the user
   * @param giveawayId - ID of the giveaway
   * @returns Promise with eligibility status and reason
   */
  checkEligibility(userId: string, giveawayId: string): Promise<{
    isEligible: boolean;
    reason?: string;
    entriesAvailable?: number;
  }>;

  /**
   * Conduct the draw for selecting winners
   * 
   * @param selectionData - Winner selection data
   * @returns Promise with the selection result
   */
  conductDraw(selectionData: WinnerSelectionDto): Promise<WinnerSelectionResultDto>;

  /**
   * Get winners for a giveaway
   * 
   * @param giveawayId - ID of the giveaway
   * @returns Promise with the winners
   */
  getWinners(giveawayId: string): Promise<Array<{
    userId: string;
    userName: string;
    prizeId: string;
    prizeName: string;
    isAlternate: boolean;
  }>>;

  /**
   * Get a user's entries for a giveaway
   * 
   * @param userId - ID of the user
   * @param giveawayId - ID of the giveaway
   * @returns Promise with the user's entries
   */
  getUserEntries(userId: string, giveawayId: string): Promise<Array<{
    id: string;
    createdAt: Date;
    entryCode?: string;
  }>>;

  /**
   * Publish a giveaway (change status from DRAFT to UPCOMING or ACTIVE)
   * 
   * @param id - ID of the giveaway to publish
   * @returns Promise with the published giveaway
   */
  publishGiveaway(id: string): Promise<GiveawayResponseDto>;
} 