/**
 * Giveaway Service Implementation
 *
 * This service handles giveaway management operations including listing, creation,
 * entry management, and winner selection following best practices for error handling and type safety.
 */

import {
  Giveaway,
  Entry,
  Prize,
  Winner,
  GiveawayStatus,
  WinnerStatus,
} from '@prisma/client';

import { GiveawayService } from './giveaway-service.interface.js';
import {
  GiveawayType,
  GiveawayCreateDto,
  GiveawayUpdateDto,
  GiveawayResponseDto,
  GiveawayEntryDto
} from '../../dtos/giveaway.dto.js';
import {
  WinnerSelectionDto,
  WinnerSelectionResultDto,
  SelectionMethod
} from '../../dtos/winner.dto.js';
import {
  FilterOptions,
  PaginatedResult,
  PaginationOptions
} from '../../repositories/base/base-repository.interface.js';
import { getServiceFactory } from '../index.js';

import {
  GiveawayRepository,
  PrizeRepository,
  EntryRepository,
  WinnerRepository
} from '../../repositories/index.js';

import {
  ValidationError,
  NotFoundError,
  AppError
} from '../../utils/errors.js';

import { MembershipService } from '../membership/membership-service.interface.js';

/**
 * Business logic error for giveaway rules violations
 */
class GiveawayBusinessError extends AppError {
  constructor(message: string) {
    super(message);
    this.name = 'GiveawayBusinessError';
  }
}

/**
 * Implementation of the Giveaway Service
 */
export class GiveawayServiceImpl implements GiveawayService {
  /**
   * Constructor for GiveawayServiceImpl
   */
  constructor(
    private readonly giveawayRepository: GiveawayRepository,
    private readonly prizeRepository: PrizeRepository,
    private readonly entryRepository: EntryRepository,
    private readonly winnerRepository: WinnerRepository,
    private readonly membershipService: MembershipService
  ) {}

  /**
   * Convert a Giveaway entity to a GiveawayResponseDto
   *
   * @private
   * @param giveaway - The giveaway entity to convert
   * @param prizes - Optional prizes to include
   * @param entryCount - Optional entry count to include
   * @returns GiveawayResponseDto
   */
  private toResponseDto(
    giveaway: Giveaway,
    prizes: Prize[] = [],
    entryCount = 0
  ): GiveawayResponseDto {
    // Create base object with required string fields
    const responseDto = {
      id: giveaway.id,
      title: giveaway.title,
      description: giveaway.description,
      startDate: giveaway.startDate,
      endDate: giveaway.endDate,
      drawDate: giveaway.drawDate || giveaway.endDate,
      status: giveaway.status,
      featuredImage: giveaway.featuredImage || '',
      prizeValue: Number(giveaway.prizeValue) || 0,
      prizeDetails: giveaway.prizeDetails || '',
      imageUrl: '',
      category: giveaway.category || '', // Use empty string as fallback
      tags: (giveaway.tags as string[]) || [],
      rules: giveaway.rules || '',
      termsAndConditions: giveaway.termsAndConditions || '',
      minTier: giveaway.minTier || null,
      requiredMembershipTierId: giveaway.minTier ? giveaway.minTier.toString() : '',
      maxEntries: giveaway.maxEntries || 0,
      entryLimit: giveaway.maxEntries || 0,
      isActive: giveaway.isActive,
      type: 'STANDARD', // Provide default value
      selectionMethod: 'RANDOM', // Provide default value
      metadata: {},
      winnerId: giveaway.winnerId || '',
      entryCount,
      prizes: prizes.map(prize => ({
        id: prize.id,
        name: prize.name,
        description: prize.description || '',
        imageUrl: '',
        value: Number(prize.value),
        quantity: prize.quantity,
        isDigital: false,
        giveawayId: prize.giveawayId,
        currency: prize.currency,
        images: prize.images as string[] || [],
        createdAt: prize.createdAt,
        updatedAt: prize.updatedAt
      })),
      createdAt: giveaway.createdAt,
      updatedAt: giveaway.updatedAt
    };

    // Use type assertion to tell TypeScript this matches the DTO
    return responseDto as GiveawayResponseDto;
  }

  /**
   * Get a giveaway by ID
   *
   * @param id - ID of the giveaway to retrieve
   * @returns Promise with giveaway or null if not found
   */
  async get(id: string): Promise<GiveawayResponseDto | null> {
    try {
      const giveaway = await this.giveawayRepository.findById(id);

      if (!giveaway) {
        return null;
      }

      // Get prizes for this giveaway
      const prizes = await this.prizeRepository.findByGiveawayId(id);

      // Get entry count
      const entryCount = await this.entryRepository.countByGiveaway(id);

      return this.toResponseDto(giveaway, prizes, entryCount);
    } catch (error) {
      if (error instanceof NotFoundError) {
        return null;
      }
      throw error;
    }
  }

  /**
   * List giveaways with optional filtering and pagination
   *
   * @param filter - Optional filter criteria
   * @param pagination - Optional pagination options
   * @returns Promise with array of giveaways
   */
  async list(
    filter?: FilterOptions,
    pagination?: PaginationOptions
  ): Promise<GiveawayResponseDto[]> {
    try {
      const giveaways = await this.giveawayRepository.findMany(filter, pagination);

      // Get all giveaway IDs
      const giveawayIds = giveaways.map(g => g.id);

      // Get prizes for these giveaways (if any)
      let prizesMap: Record<string, Prize[]> = {};
      if (giveawayIds.length > 0) {
        const allPrizes = await Promise.all(
          giveawayIds.map(id => this.prizeRepository.findByGiveawayId(id))
        );

        // Create a map of giveaway ID to prizes
        giveawayIds.forEach((id, index) => {
          prizesMap[id] = allPrizes[index] || [];
        });
      }

      // Get entry counts for these giveaways (if any)
      let entryCounts: Record<string, number> = {};
      if (giveawayIds.length > 0) {
        await Promise.all(
          giveawayIds.map(async id => {
            entryCounts[id] = await this.entryRepository.countByGiveaway(id);
          })
        );
      }

      // Convert giveaways to DTOs
      return giveaways.map(giveaway =>
        this.toResponseDto(
          giveaway,
          prizesMap[giveaway.id] || [],
          entryCounts[giveaway.id] || 0
        )
      );
    } catch (error) {
      throw error;
    }
  }

  /**
   * List giveaways with pagination metadata
   *
   * @param filter - Optional filter criteria
   * @param pagination - Pagination options
   * @returns Promise with paginated result
   */
  async listPaginated(
    filter?: FilterOptions,
    pagination?: PaginationOptions
  ): Promise<PaginatedResult<GiveawayResponseDto>> {
    try {
      const result = await this.giveawayRepository.findManyPaginated(filter, pagination);

      // Get all giveaway IDs
      const giveawayIds = result.data.map(g => g.id);

      // Get prizes for these giveaways (if any)
      let prizesMap: Record<string, Prize[]> = {};
      if (giveawayIds.length > 0) {
        const allPrizes = await Promise.all(
          giveawayIds.map(id => this.prizeRepository.findByGiveawayId(id))
        );

        // Create a map of giveaway ID to prizes
        giveawayIds.forEach((id, index) => {
          prizesMap[id] = allPrizes[index] || [];
        });
      }

      // Get entry counts for these giveaways (if any)
      let entryCounts: Record<string, number> = {};
      if (giveawayIds.length > 0) {
        await Promise.all(
          giveawayIds.map(async id => {
            entryCounts[id] = await this.entryRepository.countByGiveaway(id);
          })
        );
      }

      // Convert giveaways to DTOs
      const data = result.data.map(giveaway =>
        this.toResponseDto(
          giveaway,
          prizesMap[giveaway.id] || [],
          entryCounts[giveaway.id] || 0
        )
      );

      return {
        data,
        meta: result.meta
      };
    } catch (error) {
      throw error;
    }
  }

  /**
   * Count giveaways that match the filter criteria
   *
   * @param filter - Optional filter criteria
   * @returns Promise with the count
   */
  async count(filter?: FilterOptions): Promise<number> {
    try {
      return await this.giveawayRepository.count(filter);
    } catch (error) {
      throw error;
    }
  }

  /**
   * Create a new giveaway
   *
   * @param data - The data to create the giveaway with
   * @returns Promise with the created giveaway
   */
  async create(data: GiveawayCreateDto): Promise<GiveawayResponseDto> {
    return this.createGiveaway(data);
  }

  /**
   * Create multiple giveaways
   *
   * @param dataArray - Array of giveaway data to create
   * @returns Promise with array of created giveaways
   */
  async createMany(dataArray: GiveawayCreateDto[]): Promise<GiveawayResponseDto[]> {
    try {
      const results: GiveawayResponseDto[] = [];

      for (const data of dataArray) {
        const giveaway = await this.createGiveaway(data);
        results.push(giveaway);
      }

      return results;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Update an existing giveaway
   *
   * @param id - The ID of the giveaway to update
   * @param data - The data to update the giveaway with
   * @returns Promise with the updated giveaway
   */
  async update(id: string, data: GiveawayUpdateDto): Promise<GiveawayResponseDto> {
    return this.updateGiveaway(id, data);
  }

  /**
   * Delete a giveaway
   *
   * @param id - The ID of the giveaway to delete
   * @returns Promise with the deleted giveaway
   */
  async delete(id: string): Promise<GiveawayResponseDto> {
    try {
      const giveaway = await this.giveawayRepository.findById(id);

      if (!giveaway) {
        throw new NotFoundError(`Giveaway with ID ${id} not found`);
      }

      // Check if there are entries
      const entryCount = await this.entryRepository.countByGiveaway(id);

      if (entryCount > 0) {
        throw new GiveawayBusinessError(
          `Cannot delete giveaway with ID ${id} because it has ${entryCount} entries`
        );
      }

      // Get prizes for the response
      const prizes = await this.prizeRepository.findByGiveawayId(id);

      // Delete the giveaway
      const deletedGiveaway = await this.giveawayRepository.delete(id);

      return this.toResponseDto(deletedGiveaway, prizes, entryCount);
    } catch (error) {
      if (error instanceof NotFoundError || error instanceof GiveawayBusinessError) {
        throw error;
      }
      throw error;
    }
  }

  /**
   * Check if a giveaway exists
   *
   * @param id - The ID of the giveaway to check
   * @returns Promise with boolean indicating existence
   */
  async exists(id: string): Promise<boolean> {
    try {
      return await this.giveawayRepository.exists(id);
    } catch (error) {
      throw error;
    }
  }

  /**
   * Get active giveaways
   *
   * @param pagination - Optional pagination options
   * @returns Promise with active giveaways
   */
  async getActiveGiveaways(
    pagination?: PaginationOptions
  ): Promise<PaginatedResult<GiveawayResponseDto>> {
    try {
      // First, get active giveaways from repository
      const activeGiveaways = await this.giveawayRepository.findActive();

      // Apply pagination
      const page = pagination?.page || 1;
      const limit = pagination?.limit || 10;
      const skip = (page - 1) * limit;

      const paginatedGiveaways = activeGiveaways.slice(skip, skip + limit);

      // Convert to response DTOs with prizes and entry counts
      const giveawayPromises = paginatedGiveaways.map(async (giveaway) => {
        const prizes = await this.prizeRepository.findByGiveawayId(giveaway.id);
        const entryCount = await this.entryRepository.countByGiveaway(giveaway.id);
        return this.toResponseDto(giveaway, prizes, entryCount);
      });

      const giveaways = await Promise.all(giveawayPromises);

      // Return paginated result
      return {
        data: giveaways,
        meta: {
          total: activeGiveaways.length,
          page,
          limit,
          hasMore: skip + limit < activeGiveaways.length
        }
      };
    } catch (error) {
      throw error;
    }
  }

  /**
   * Get upcoming giveaways
   *
   * @param pagination - Optional pagination options
   * @returns Promise with upcoming giveaways
   */
  async getUpcomingGiveaways(
    pagination?: PaginationOptions
  ): Promise<PaginatedResult<GiveawayResponseDto>> {
    try {
      // Optional parameters
      const limit = pagination?.limit;

      // Get upcoming giveaways
      const upcomingGiveaways = await this.giveawayRepository.findUpcoming(limit);

      // Apply pagination
      const page = pagination?.page || 1;
      const pageLimit = pagination?.limit || 10;
      const skip = (page - 1) * pageLimit;

      const paginatedGiveaways = upcomingGiveaways.slice(skip, skip + pageLimit);

      // Convert to response DTOs with prizes and entry counts
      const giveawayPromises = paginatedGiveaways.map(async (giveaway) => {
        const prizes = await this.prizeRepository.findByGiveawayId(giveaway.id);
        const entryCount = await this.entryRepository.countByGiveaway(giveaway.id);
        return this.toResponseDto(giveaway, prizes, entryCount);
      });

      const giveaways = await Promise.all(giveawayPromises);

      // Return paginated result
      return {
        data: giveaways,
        meta: {
          total: upcomingGiveaways.length,
          page,
          limit: pageLimit,
          hasMore: skip + pageLimit < upcomingGiveaways.length
        }
      };
    } catch (error) {
      throw error;
    }
  }

  /**
   * Get past giveaways
   *
   * @param pagination - Optional pagination options
   * @returns Promise with past giveaways
   */
  async getPastGiveaways(
    pagination?: PaginationOptions
  ): Promise<PaginatedResult<GiveawayResponseDto>> {
    try {
      // Optional parameters
      const limit = pagination?.limit;

      // Get past giveaways
      const pastGiveaways = await this.giveawayRepository.findPast(limit);

      // Apply pagination
      const page = pagination?.page || 1;
      const pageLimit = pagination?.limit || 10;
      const skip = (page - 1) * pageLimit;

      const paginatedGiveaways = pastGiveaways.slice(skip, skip + pageLimit);

      // Convert to response DTOs with prizes and entry counts
      const giveawayPromises = paginatedGiveaways.map(async (giveaway) => {
        const prizes = await this.prizeRepository.findByGiveawayId(giveaway.id);
        const entryCount = await this.entryRepository.countByGiveaway(giveaway.id);
        return this.toResponseDto(giveaway, prizes, entryCount);
      });

      const giveaways = await Promise.all(giveawayPromises);

      // Return paginated result
      return {
        data: giveaways,
        meta: {
          total: pastGiveaways.length,
          page,
          limit: pageLimit,
          hasMore: skip + pageLimit < pastGiveaways.length
        }
      };
    } catch (error) {
      throw error;
    }
  }

  /**
   * Get giveaways by membership tier
   *
   * @param membershipTierId - ID of the membership tier
   * @param pagination - Optional pagination options
   * @returns Promise with giveaways for the given tier
   */
  async getGiveawaysByMembershipTier(
    membershipTierId: string,
    pagination?: PaginationOptions
  ): Promise<PaginatedResult<GiveawayResponseDto>> {
    try {
      if (!membershipTierId) {
        throw new ValidationError('Membership tier ID is required');
      }

      // Find giveaways that have this tier as required tier or are open to all
      const filter: FilterOptions = {
        where: {
          OR: [
            { requiredMembershipTierId: membershipTierId },
            { requiredMembershipTierId: null }
          ],
          isActive: true
        }
      };

      // Get paginated results using the generic method
      return this.listPaginated(filter, pagination);
    } catch (error) {
      throw error;
    }
  }

  /**
   * Create a new giveaway
   *
   * @param data - Giveaway creation data
   * @returns Promise with the created giveaway
   */
  async createGiveaway(data: GiveawayCreateDto): Promise<GiveawayResponseDto> {
    try {
      // Validate required fields
      if (!data.title || !data.description || !data.startDate || !data.endDate || !data.drawDate) {
        throw new ValidationError('Title, description, start date, end date, and draw date are required');
      }

      // Validate dates
      const now = new Date();
      const startDate = new Date(data.startDate);
      const endDate = new Date(data.endDate);
      const drawDate = new Date(data.drawDate);

      // Ensure start date is in the future or today
      if (startDate < now && startDate.toDateString() !== now.toDateString()) {
        throw new ValidationError('Start date must be in the future or today');
      }

      // Ensure end date is after start date
      if (endDate <= startDate) {
        throw new ValidationError('End date must be after start date');
      }

      // Ensure draw date is on or after end date
      if (drawDate < endDate) {
        throw new ValidationError('Draw date must be on or after end date');
      }

      // Determine status based on dates if not provided
      if (!data.status) {
        if (startDate > now) {
          data.status = GiveawayStatus.DRAFT;
        } else if (startDate <= now && endDate >= now) {
          data.status = GiveawayStatus.ACTIVE;
        } else {
          data.status = GiveawayStatus.DRAFT;
        }
      }

      // Set default values for optional fields
      const giveawayData = {
        ...data,
        drawDate,
        isActive: data.isActive !== undefined ? data.isActive : true,
        maxEntries: data.maxEntries || 1,
        type: data.type || GiveawayType.STANDARD,
        selectionMethod: data.selectionMethod || SelectionMethod.RANDOM
      };

      // Create the giveaway in the database
      const giveaway = await this.giveawayRepository.create(giveawayData as unknown as Giveaway);

      // If the giveaway is created with an ACTIVE status, trigger automatic enrollment
      if (giveaway.status === GiveawayStatus.ACTIVE) {
        try {
          const enrollmentService = getServiceFactory().createEnrollmentService();
          await enrollmentService.handleGiveawayActivation(giveaway.id);
          console.log(`Triggered automatic enrollment for newly created giveaway ${giveaway.id}`);
        } catch (enrollmentError) {
          // Log the error but don't fail the giveaway creation
          console.error('Error triggering automatic enrollment:', enrollmentError);
        }
      }

      // Return the created giveaway with empty prizes array and 0 entries
      return this.toResponseDto(giveaway, [], 0);
    } catch (error) {
      // Handle specific errors
      if (error instanceof ValidationError) {
        throw error;
      }

      // Re-throw any other errors
      throw error;
    }
  }

  /**
   * Update an existing giveaway
   *
   * @param id - ID of the giveaway to update
   * @param data - Updated giveaway data
   * @returns Promise with the updated giveaway
   */
  async updateGiveaway(id: string, data: GiveawayUpdateDto): Promise<GiveawayResponseDto> {
    try {
      // Find the giveaway
      const existingGiveaway = await this.giveawayRepository.findById(id);

      if (!existingGiveaway) {
        throw new NotFoundError(`Giveaway with ID ${id} not found`);
      }

      // Validate dates if provided
      if (data.startDate || data.endDate || data.drawDate) {
        const startDate = data.startDate ? new Date(data.startDate) : existingGiveaway.startDate;
        const endDate = data.endDate ? new Date(data.endDate) : existingGiveaway.endDate;
        const drawDate = data.drawDate ? new Date(data.drawDate) : (existingGiveaway.drawDate || existingGiveaway.endDate);

        // Ensure end date is after start date
        if (endDate <= startDate) {
          throw new ValidationError('End date must be after start date');
        }

        // Ensure draw date is on or after end date
        if (drawDate < endDate) {
          throw new ValidationError('Draw date must be on or after end date');
        }
      }

      // Do not allow certain updates if giveaway has entries
      const entryCount = await this.entryRepository.countByGiveaway(id);

      if (entryCount > 0) {
        // Check if trying to update critical fields
        if (data.requiredMembershipTierId && data.requiredMembershipTierId !== (existingGiveaway.minTier ? existingGiveaway.minTier.toString() : undefined)) {
          throw new GiveawayBusinessError('Cannot change required membership tier for a giveaway with entries');
        }
      }

      // Update the giveaway
      const updatedGiveaway = await this.giveawayRepository.update(id, data as unknown as Partial<Giveaway>);

      // If the giveaway status is being changed to ACTIVE, trigger automatic enrollment
      if (data.status === GiveawayStatus.ACTIVE && existingGiveaway.status !== GiveawayStatus.ACTIVE) {
        try {
          const enrollmentService = getServiceFactory().createEnrollmentService();
          await enrollmentService.handleGiveawayActivation(id);
          console.log(`Triggered automatic enrollment for giveaway ${id}`);
        } catch (enrollmentError) {
          // Log the error but don't fail the giveaway update
          console.error('Error triggering automatic enrollment:', enrollmentError);
        }
      }

      // Get prizes for the response
      const prizes = await this.prizeRepository.findByGiveawayId(id);

      return this.toResponseDto(updatedGiveaway, prizes, entryCount);
    } catch (error) {
      // Handle specific errors
      if (error instanceof NotFoundError || error instanceof ValidationError || error instanceof GiveawayBusinessError) {
        throw error;
      }

      // Re-throw any other errors
      throw error;
    }
  }

  /**
   * Cancel a giveaway
   *
   * @param id - ID of the giveaway to cancel
   * @returns Promise with the cancelled giveaway
   */
  async cancelGiveaway(id: string): Promise<GiveawayResponseDto> {
    try {
      // Find the giveaway
      const giveaway = await this.giveawayRepository.findById(id);

      if (!giveaway) {
        throw new NotFoundError(`Giveaway with ID ${id} not found`);
      }

      // Check if the giveaway is already cancelled
      if (giveaway.status === GiveawayStatus.CANCELLED) {
        throw new GiveawayBusinessError(`Giveaway with ID ${id} is already cancelled`);
      }

      // Check if the giveaway is completed
      if (giveaway.status === GiveawayStatus.COMPLETED) {
        throw new GiveawayBusinessError(`Cannot cancel a completed giveaway`);
      }

      // Update the giveaway status to CANCELLED
      const updatedGiveaway = await this.giveawayRepository.update(id, {
        status: GiveawayStatus.CANCELLED,
        isActive: false
      } as Partial<Giveaway>);

      // Get prizes and entry count for the response
      const prizes = await this.prizeRepository.findByGiveawayId(id);
      const entryCount = await this.entryRepository.countByGiveaway(id);

      return this.toResponseDto(updatedGiveaway, prizes, entryCount);
    } catch (error) {
      // Handle specific errors
      if (error instanceof NotFoundError || error instanceof GiveawayBusinessError) {
        throw error;
      }

      // Re-throw any other errors
      throw error;
    }
  }

  /**
   * Publish a giveaway (change status from DRAFT to UPCOMING or ACTIVE)
   *
   * @param id - ID of the giveaway to publish
   * @returns Promise with the published giveaway
   */
  async publishGiveaway(id: string): Promise<GiveawayResponseDto> {
    try {
      // Find the giveaway
      const giveaway = await this.giveawayRepository.findById(id);

      if (!giveaway) {
        throw new NotFoundError(`Giveaway with ID ${id} not found`);
      }

      // Check if the giveaway is already published
      if (giveaway.status !== GiveawayStatus.DRAFT) {
        throw new GiveawayBusinessError(`Giveaway with ID ${id} is already published`);
      }

      // Determine status based on dates
      const now = new Date();
      const startDate = giveaway.startDate;
      let newStatus: GiveawayStatus;

      if (startDate > now) {
        newStatus = GiveawayStatus.DRAFT;
      } else {
        newStatus = GiveawayStatus.ACTIVE;
      }

      // Update the giveaway status
      const updatedGiveaway = await this.giveawayRepository.update(id, {
        status: newStatus,
        isActive: true
      } as Partial<Giveaway>);

      // If the giveaway is now active, trigger automatic enrollment
      if (newStatus === GiveawayStatus.ACTIVE) {
        try {
          const enrollmentService = getServiceFactory().createEnrollmentService();
          await enrollmentService.handleGiveawayActivation(id);
          console.log(`Triggered automatic enrollment for giveaway ${id}`);
        } catch (enrollmentError) {
          // Log the error but don't fail the giveaway activation
          console.error('Error triggering automatic enrollment:', enrollmentError);
        }
      }

      // Get prizes and entry count for the response
      const prizes = await this.prizeRepository.findByGiveawayId(id);
      const entryCount = await this.entryRepository.countByGiveaway(id);

      return this.toResponseDto(updatedGiveaway, prizes, entryCount);
    } catch (error) {
      // Handle specific errors
      if (error instanceof NotFoundError || error instanceof GiveawayBusinessError) {
        throw error;
      }

      // Re-throw any other errors
      throw error;
    }
  }

  /**
   * Enter a giveaway
   *
   * @param entryData - Giveaway entry data
   * @returns Promise with boolean indicating success
   */
  async enterGiveaway(entryData: GiveawayEntryDto): Promise<boolean> {
    try {
      // Validate required fields
      if (!entryData.userId || !entryData.giveawayId) {
        throw new ValidationError('User ID and giveaway ID are required');
      }

      if (entryData.quantity <= 0) {
        throw new ValidationError('Entry quantity must be positive');
      }

      // Check eligibility first
      const eligibility = await this.checkEligibility(entryData.userId, entryData.giveawayId);

      if (!eligibility.isEligible) {
        throw new GiveawayBusinessError(eligibility.reason || 'User is not eligible to enter this giveaway');
      }

      // Check if user has enough available entries
      if (eligibility.entriesAvailable !== undefined &&
          entryData.quantity > eligibility.entriesAvailable) {
        throw new GiveawayBusinessError(`User only has ${eligibility.entriesAvailable} entries available`);
      }

      // Get the giveaway to check its status
      const giveaway = await this.giveawayRepository.findById(entryData.giveawayId);

      if (!giveaway) {
        throw new NotFoundError(`Giveaway with ID ${entryData.giveawayId} not found`);
      }

      // Check if giveaway is active
      if (giveaway.status !== GiveawayStatus.ACTIVE) {
        throw new GiveawayBusinessError(`Giveaway is not active (current status: ${giveaway.status})`);
      }

      // Check if giveaway has reached its entry limit
      if (giveaway.maxEntries) {
        const totalEntries = await this.entryRepository.countByGiveaway(entryData.giveawayId);
        if (totalEntries + entryData.quantity > giveaway.maxEntries) {
          throw new GiveawayBusinessError('Giveaway has reached its entry limit');
        }
      }

      // Create entry in the database
      await this.entryRepository.create({
        userId: entryData.userId,
        giveawayId: entryData.giveawayId,
        quantity: entryData.quantity,
        referenceId: entryData.referenceId
      } as unknown as Entry);

      return true;
    } catch (error) {
      if (error instanceof ValidationError ||
          error instanceof NotFoundError ||
          error instanceof GiveawayBusinessError) {
        throw error;
      }
      throw error;
    }
  }

  /**
   * Check if a user has entered a giveaway
   *
   * @param userId - ID of the user
   * @param giveawayId - ID of the giveaway
   * @returns Promise with number of entries or 0 if not entered
   */
  async hasUserEnteredGiveaway(userId: string, giveawayId: string): Promise<number> {
    try {
      if (!userId || !giveawayId) {
        throw new ValidationError('User ID and giveaway ID are required');
      }

      // Find entries by user and giveaway
      const entries = await this.entryRepository.findByGiveawayAndUser(giveawayId, userId);

      // Calculate total entries
      const totalEntries = entries.reduce((sum, entry) => sum + ((entry as any).quantity || 1), 0);

      return totalEntries;
    } catch (error) {
      if (error instanceof ValidationError) {
        throw error;
      }
      throw error;
    }
  }

  /**
   * Get total entry count for a giveaway
   *
   * @param giveawayId - ID of the giveaway
   * @returns Promise with the total entry count
   */
  async getEntryCount(giveawayId: string): Promise<number> {
    try {
      if (!giveawayId) {
        throw new ValidationError('Giveaway ID is required');
      }

      return await this.entryRepository.countByGiveaway(giveawayId);
    } catch (error) {
      if (error instanceof ValidationError) {
        throw error;
      }
      throw error;
    }
  }

  /**
   * Check if a user is eligible to enter a giveaway
   *
   * @param userId - ID of the user
   * @param giveawayId - ID of the giveaway
   * @returns Promise with eligibility status and reason
   */
  async checkEligibility(userId: string, giveawayId: string): Promise<{
    isEligible: boolean;
    reason?: string;
    entriesAvailable?: number;
  }> {
    try {
      if (!userId || !giveawayId) {
        throw new ValidationError('User ID and giveaway ID are required');
      }

      // Get the giveaway
      const giveaway = await this.giveawayRepository.findById(giveawayId);

      if (!giveaway) {
        return {
          isEligible: false,
          reason: `Giveaway with ID ${giveawayId} not found`
        };
      }

      // Check if giveaway is active
      if (giveaway.status !== GiveawayStatus.ACTIVE) {
        return {
          isEligible: false,
          reason: `Giveaway is not active (current status: ${giveaway.status})`
        };
      }

      // Check if giveaway dates are valid
      const now = new Date();
      if (giveaway.startDate > now) {
        return {
          isEligible: false,
          reason: `Giveaway has not started yet`
        };
      }

      if (giveaway.endDate < now) {
        return {
          isEligible: false,
          reason: `Giveaway has already ended`
        };
      }

      // Check membership tier requirement
      if (giveaway.minTier) {
        const hasMembership = await this.membershipService.hasActiveMembership(userId);

        if (!hasMembership) {
          return {
            isEligible: false,
            reason: `This giveaway requires an active membership`
          };
        }

        // Check if user's tier level is sufficient
        const userTierLevel = await this.membershipService.getUserTierLevel(userId);
        const requiredTierLevel = typeof giveaway.minTier === 'string' ? parseInt(giveaway.minTier, 10) : (giveaway.minTier || 0);

        if (userTierLevel < requiredTierLevel) {
          return {
            isEligible: false,
            reason: `This giveaway requires membership level ${requiredTierLevel} or higher`
          };
        }
      }

      // Check if user has reached the maximum entries for this giveaway
      const existingEntries = await this.hasUserEnteredGiveaway(userId, giveawayId);

      if (giveaway.maxEntries && existingEntries >= giveaway.maxEntries) {
        return {
          isEligible: false,
          reason: `You have reached the maximum entries allowed (${giveaway.maxEntries})`
        };
      }

      // Check if user has available entries based on membership tier
      const availableEntries = await this.membershipService.getAvailableEntries(userId);

      if (availableEntries <= 0) {
        return {
          isEligible: false,
          reason: `You have no entries available in your membership tier`
        };
      }

      // Calculate remaining entries
      const remainingEntries = giveaway.maxEntries
        ? Math.min(availableEntries, giveaway.maxEntries - existingEntries)
        : availableEntries;

      return {
        isEligible: true,
        entriesAvailable: remainingEntries
      };
    } catch (error) {
      if (error instanceof ValidationError) {
        throw error;
      }
      throw error;
    }
  }

  /**
   * Get a user's entries for a giveaway
   *
   * @param userId - ID of the user
   * @param giveawayId - ID of the giveaway
   * @returns Promise with the user's entries
   */
  async getUserEntries(userId: string, giveawayId: string): Promise<Array<{
    id: string;
    createdAt: Date;
    referenceId?: string;
  }>> {
    try {
      if (!userId || !giveawayId) {
        throw new ValidationError('User ID and giveaway ID are required');
      }

      // Find entries by user and giveaway
      const entries = await this.entryRepository.findByGiveawayAndUser(giveawayId, userId);

      // Map to the expected format with optional referenceId
      return entries.map(entry => {
        const result: {
          id: string;
          createdAt: Date;
          referenceId?: string;
        } = {
          id: entry.id,
          createdAt: entry.createdAt
        };

        if (entry.referenceId) {
          result.referenceId = entry.referenceId;
        }

        return result;
      });
    } catch (error) {
      if (error instanceof ValidationError) {
        throw error;
      }
      throw error;
    }
  }

  /**
   * Conduct the draw for selecting winners
   *
   * @param selectionData - Winner selection data
   * @returns Promise with the selection result
   */
  async conductDraw(selectionData: WinnerSelectionDto): Promise<WinnerSelectionResultDto> {
    try {
      // Validate required fields
      if (!selectionData.giveawayId || !selectionData.numberOfWinners) {
        throw new ValidationError('Giveaway ID and number of winners are required');
      }

      if (selectionData.numberOfWinners <= 0) {
        throw new ValidationError('Number of winners must be positive');
      }

      // Get the giveaway
      const giveaway = await this.giveawayRepository.findById(selectionData.giveawayId);

      if (!giveaway) {
        throw new NotFoundError(`Giveaway with ID ${selectionData.giveawayId} not found`);
      }

      // Check if giveaway is ready for drawing
      const now = new Date();

      if (!selectionData.forceDraw && giveaway.endDate > now) {
        throw new GiveawayBusinessError(`Giveaway has not ended yet`);
      }

      // Check if we have enough prizes
      const prizes = await this.prizeRepository.findByGiveawayId(selectionData.giveawayId);

      const totalPrizes = prizes.reduce((sum, prize) => sum + prize.quantity, 0);

      const totalWinners = selectionData.numberOfWinners +
        (selectionData.selectAlternates ? (selectionData.numberOfAlternates || 0) : 0);

      if (totalPrizes < totalWinners) {
        throw new GiveawayBusinessError(`Not enough prizes available for ${totalWinners} winners`);
      }

      // Get all entries for this giveaway
      const giveawayEntries = await this.entryRepository.findByGiveawayAndUser(
        selectionData.giveawayId,
        "" // Use empty string instead of undefined for no user filter
      );

      if (giveawayEntries.length === 0) {
        throw new GiveawayBusinessError(`No entries found for this giveaway`);
      }

      // Expand entries based on quantity
      let allEntries: { entryId: string; userId: string }[] = [];

      giveawayEntries.forEach(entry => {
        for (let i = 0; i < entry.quantity; i++) {
          allEntries.push({
            entryId: entry.id,
            userId: entry.userId
          });
        }
      });

      // Always use random selection method
      // Simple random selection using Fisher-Yates shuffle
      for (let i = allEntries.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        if (allEntries[i] && allEntries[j]) {
          const temp = allEntries[i] as { entryId: string; userId: string };
          allEntries[i] = allEntries[j] as { entryId: string; userId: string };
          allEntries[j] = temp;
        }
      }

      // Select winners from shuffled array
      let selectedWinners: { userId: string; entryId: string; isAlternate: boolean }[] = [];
      for (let i = 0; i < totalWinners && i < allEntries.length; i++) {
        const entry = allEntries[i];
        if (entry) {
          selectedWinners.push({
            userId: entry.userId,
            entryId: entry.entryId,
            isAlternate: i >= selectionData.numberOfWinners
          });
        }
      }

      // Assign prizes to winners
      let prizeIndex = 0;
      const winnersWithPrizes = selectedWinners.map(winner => {
        // Make sure we have a valid prize by default
        const defaultPrize = prizes[0];
        const prize = prizes[prizeIndex % prizes.length] || defaultPrize;
        prizeIndex++;

        return {
          ...winner,
          prizeId: prize?.id || 'prize-1' // Use optional chaining with fallback
        };
      });

      // Save winners to database
      await Promise.all(winnersWithPrizes.map(winner =>
        this.winnerRepository.create({
          userId: winner.userId,
          giveawayId: selectionData.giveawayId,
          prizeId: winner.prizeId,
          entryId: winner.entryId,
          status: WinnerStatus.SELECTED,
          selectionDate: new Date(),
          // Add default values for required properties
          claimDate: null,
          shippingDetails: {}
        } as unknown as Winner) // Need to use unknown assertion due to type mismatch
      ));

      // Update giveaway status
      await this.giveawayRepository.update(selectionData.giveawayId, {
        status: GiveawayStatus.COMPLETED
      } as Partial<Giveaway>);

      // Build and return result
      return {
        giveawayId: selectionData.giveawayId,
        winners: winnersWithPrizes,
        drawDate: new Date(),
        drawBy: 'system' // Could be replaced with admin user ID if available
      };
    } catch (error) {
      if (error instanceof ValidationError ||
          error instanceof NotFoundError ||
          error instanceof GiveawayBusinessError) {
        throw error;
      }
      throw error;
    }
  }

  /**
   * Get winners for a giveaway
   *
   * @param giveawayId - ID of the giveaway
   * @returns Promise with the winners
   */
  async getWinners(giveawayId: string): Promise<Array<{
    userId: string;
    userName: string;
    prizeId: string;
    prizeName: string;
    isAlternate: boolean;
  }>> {
    try {
      if (!giveawayId) {
        throw new ValidationError('Giveaway ID is required');
      }

      // Get winners for this giveaway
      const winners = await this.winnerRepository.findByGiveaway(giveawayId);

      if (winners.length === 0) {
        return [];
      }

      // Get prize details
      const prizes = await this.prizeRepository.findByGiveawayId(giveawayId);
      const prizeMap = new Map(prizes.map(prize => [prize.id, prize]));

      // For simplicity, we'll use a mock user service here
      // In a real implementation, this would call the user service to get user details
      const mockUserMap: Record<string, { firstName: string; lastName: string }> = {};

      // Transform winners to the expected format
      return winners.map(winner => {
        const prize = prizeMap.get(winner.prizeId);
        const user = mockUserMap[winner.userId];
        const userName = user
          ? `${user.firstName} ${user.lastName}`
          : `User ${winner.userId}`;

        // Return with isAlternate hardcoded as the property does not exist on Winner type
        return {
          userId: winner.userId,
          userName,
          prizeId: winner.prizeId,
          prizeName: prize ? prize.name : `Prize ${winner.prizeId}`,
          isAlternate: false // Set a default value since property doesn't exist in Winner type
        };
      });
    } catch (error) {
      if (error instanceof ValidationError) {
        throw error;
      }
      throw error;
    }
  }
}