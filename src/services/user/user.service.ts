/**
 * User Service Implementation
 * 
 * This service handles user profile management, preferences, and user-specific operations.
 * It implements the UserService interface and serves as the primary service for user data management.
 */

import { User, Role } from '@prisma/client';
import { 
  UserRegistrationDto, 
  UserUpdateDto,
  UserResponseDto, 
  PasswordChangeDto
} from '../../dtos/user.dto.js';
import { UserService } from './user-service.interface.js';
import { UserRepository } from '../../repositories/user/user-repository.interface.js';
import { FilterOptions, PaginatedResult, PaginationOptions } from '../../repositories/base/base-repository.interface.js';
import { ValidationError, NotFoundError, AuthenticationError } from '../../utils/errors.js';
import { hashPassword, validatePasswordMatch, validatePasswordComplexity } from '../../utils/password.utils.js';
import { mapUserToDto } from '../../utils/user.utils.js';

/**
 * Implementation of the User Service
 */
export class UserServiceImpl implements UserService {
  /**
   * Constructor for UserServiceImpl
   * 
   * @param userRepository - Repository for user data access
   * @param authService - Service for authentication operations
   * @param fileService - Optional service for file uploads
   */
  constructor(
    private readonly userRepository: UserRepository,
    private readonly fileService?: any
  ) {}

  /**
   * Get a user by ID
   * 
   * @param id - ID of the user to retrieve
   * @returns Promise with user or null if not found
   */
  async get(id: string): Promise<User | null> {
    return this.userRepository.findById(id);
  }

  /**
   * List users with optional filtering and pagination
   * 
   * @param filter - Optional filter criteria
   * @param pagination - Optional pagination options
   * @returns Promise with array of users
   */
  async list(filter?: FilterOptions, pagination?: PaginationOptions): Promise<User[]> {
    return this.userRepository.findMany(filter, pagination);
  }

  /**
   * List users with pagination metadata
   * 
   * @param filter - Optional filter criteria
   * @param pagination - Pagination options
   * @returns Promise with paginated result
   */
  async listPaginated(filter?: FilterOptions, pagination?: PaginationOptions): Promise<PaginatedResult<User>> {
    return this.userRepository.findManyPaginated(filter, pagination);
  }

  /**
   * Count users that match the filter criteria
   * 
   * @param filter - Optional filter criteria
   * @returns Promise with the count
   */
  async count(filter?: FilterOptions): Promise<number> {
    return this.userRepository.count(filter);
  }

  /**
   * Create a new user
   * 
   * @param data - User registration data
   * @returns Promise with the created user
   */
  async create(data: UserRegistrationDto): Promise<User> {
    // Check if user with email already exists
    const existingUser = await this.userRepository.findByEmail(data.email);
    if (existingUser) {
      throw new ValidationError('User with this email already exists');
    }

    // Validate and hash the password using the utility functions
    validatePasswordComplexity(data.password);
    const hashedPassword = await hashPassword(data.password);

    // Create user data object
    const userData = {
      email: data.email,
      password: hashedPassword,
      firstName: data.firstName,
      lastName: data.lastName,
      profileImage: data.profileImage || null,
      isVerified: false,
      role: Role.USER
    };

    // Create the user in the database
    return this.userRepository.create(userData);
  }

  /**
   * Create multiple users
   * 
   * @param dataArray - Array of user data to create
   * @returns Promise with array of created users
   */
  async createMany(dataArray: UserRegistrationDto[]): Promise<User[]> {
    // Process each registration data
    const processedData = await Promise.all(
      dataArray.map(async (data) => {
        // Validate password for each user
        validatePasswordComplexity(data.password);
        
        // Hash the password using the utility function
        const hashedPassword = await hashPassword(data.password);

        // Return processed user data
        return {
          email: data.email,
          password: hashedPassword,
          firstName: data.firstName,
          lastName: data.lastName,
          profileImage: data.profileImage || null,
          isVerified: false,
          role: Role.USER
        };
      })
    );

    // Create users in the database
    return this.userRepository.createMany(processedData);
  }

  /**
   * Update an existing user
   * 
   * @param id - ID of the user to update
   * @param data - The data to update the user with
   * @returns Promise with the updated user
   */
  async update(id: string, data: UserUpdateDto): Promise<User> {
    // Check if user exists
    const userExists = await this.userRepository.exists(id);
    if (!userExists) {
      throw new NotFoundError('User not found');
    }

    // Update the user
    return this.userRepository.update(id, data);
  }

  /**
   * Delete a user
   * 
   * @param id - ID of the user to delete
   * @returns Promise with the deleted user or void
   */
  async delete(id: string): Promise<User | void> {
    // Check if user exists
    const userExists = await this.userRepository.exists(id);
    if (!userExists) {
      throw new NotFoundError('User not found');
    }

    // Delete the user
    return this.userRepository.delete(id);
  }

  /**
   * Check if a user exists
   * 
   * @param id - ID of the user to check
   * @returns Promise with boolean indicating existence
   */
  async exists(id: string): Promise<boolean> {
    return this.userRepository.exists(id);
  }

  /**
   * Get a user's profile
   * 
   * @param userId - ID of the user
   * @returns Promise with the user profile
   */
  async getProfile(userId: string): Promise<UserResponseDto> {
    // Find the user
    const user = await this.userRepository.findById(userId);
    if (!user) {
      throw new NotFoundError('User not found');
    }

    // Map user to response DTO using the utility function
    return mapUserToDto(user);
  }

  /**
   * Update a user's profile
   * 
   * @param userId - ID of the user
   * @param profileData - Updated profile data
   * @returns Promise with the updated user
   */
  async updateProfile(userId: string, profileData: UserUpdateDto): Promise<UserResponseDto> {
    // Check if user exists
    const user = await this.userRepository.findById(userId);
    if (!user) {
      throw new NotFoundError('User not found');
    }

    // Update the user profile
    const updatedUser = await this.userRepository.update(userId, profileData);

    // Map updated user to response DTO using the utility function
    return mapUserToDto(updatedUser);
  }

  /**
   * Change a user's password
   * 
   * @param userId - ID of the user
   * @param passwordData - Current and new password
   * @returns Promise with the updated user
   */
  async changePassword(userId: string, passwordData: PasswordChangeDto): Promise<UserResponseDto> {
    // Find the user
    const user = await this.userRepository.findById(userId);
    if (!user) {
      throw new NotFoundError('User not found');
    }

    // Verify current password using the utility function
    const passwordMatch = await validatePasswordMatch(
      passwordData.currentPassword,
      user.password
    );
    if (!passwordMatch) {
      throw new AuthenticationError('Current password is incorrect');
    }

    // Verify that new passwords match
    if (passwordData.newPassword !== passwordData.confirmPassword) {
      throw new ValidationError('New passwords do not match');
    }

    // Validate password complexity using the utility function
    validatePasswordComplexity(passwordData.newPassword);

    // Hash the new password using the utility function
    const hashedPassword = await hashPassword(passwordData.newPassword);

    // Update the user's password
    const updatedUser = await this.userRepository.updatePassword(userId, hashedPassword);

    // Map updated user to response DTO using the utility function
    return mapUserToDto(updatedUser);
  }

  /**
   * Get user preferences
   * 
   * @param userId - ID of the user
   * @returns Promise with the user preferences
   */
  async getUserPreferences(userId: string): Promise<Record<string, any>> {
    // Find the user
    const user = await this.userRepository.findById(userId);
    if (!user) {
      throw new NotFoundError('User not found');
    }

    // Access preferences from the user record
    // Cast user to any since preferences may not be in the standard User model
    const userWithPreferences = user as unknown as User & { preferences?: Record<string, any> };
    return userWithPreferences.preferences || {};
  }

  /**
   * Update user preferences
   * 
   * @param userId - ID of the user
   * @param preferences - Updated preferences
   * @returns Promise with the updated preferences
   */
  async updateUserPreferences(userId: string, preferences: Record<string, any>): Promise<Record<string, any>> {
    // Check if user exists
    const userExists = await this.userRepository.exists(userId);
    if (!userExists) {
      throw new NotFoundError('User not found');
    }

    // Use type assertion since preferences isn't in the standard User model
    // but is likely implemented as a JSON field in the database
    const updateData = { preferences } as unknown as Partial<User>;
    await this.userRepository.update(userId, updateData);

    return preferences;
  }

  /**
   * Get user's entries for all giveaways
   * 
   * @param userId - ID of the user
   * @returns Promise with the user entries
   */
  async getUserEntries(userId: string): Promise<any[]> {
    // Check if user exists
    const userExists = await this.userRepository.exists(userId);
    if (!userExists) {
      throw new NotFoundError('User not found');
    }

    // Return entries (this would typically call an entry repository)
    return [];
  }

  /**
   * Get user's wins
   * 
   * @param userId - ID of the user
   * @returns Promise with the user wins
   */
  async getUserWins(userId: string): Promise<any[]> {
    // Check if user exists
    const userExists = await this.userRepository.exists(userId);
    if (!userExists) {
      throw new NotFoundError('User not found');
    }

    // Return wins (this would typically call a winner repository)
    return [];
  }

  /**
   * Upload profile image
   * 
   * @param userId - ID of the user
   * @param imageFile - Image file data
   * @returns Promise with the updated user
   */
  async uploadProfileImage(userId: string, imageFile: any): Promise<UserResponseDto> {
    // Check if user exists
    const user = await this.userRepository.findById(userId);
    if (!user) {
      throw new NotFoundError('User not found');
    }

    // Check if file service is available
    if (!this.fileService) {
      throw new ValidationError('File upload service is not available');
    }

    try {
      // Upload the file
      const uploadResult = await this.fileService.uploadFile(imageFile, {
        folder: 'profile-images',
        allowedTypes: ['image/jpeg', 'image/png', 'image/gif'],
        maxSize: 5 * 1024 * 1024 // 5MB
      });

      // Update user with new profile image URL
      const updatedUser = await this.userRepository.update(userId, {
        profileImage: uploadResult.url
      });

      // Map updated user to response DTO using the utility function
      return mapUserToDto(updatedUser);
    } catch (error) {
      if (error instanceof Error) {
        throw new ValidationError(`Failed to upload profile image: ${error.message}`);
      }
      throw new ValidationError('Failed to upload profile image');
    }
  }

  /**
   * Check if email exists
   * 
   * @param email - Email to check
   * @returns Promise with boolean indicating if email exists
   */
  async emailExists(email: string): Promise<boolean> {
    const user = await this.userRepository.findByEmail(email);
    return !!user;
  }
} 