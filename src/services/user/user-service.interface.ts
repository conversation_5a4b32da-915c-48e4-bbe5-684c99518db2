import { BaseService } from '../base/base-service.interface.js';
import { User } from '@prisma/client';
import { 
  UserRegistrationDto, 
  UserUpdateDto, 
  PasswordChangeDto,
  UserResponseDto
} from '../../dtos/user.dto.js';

/**
 * User Service Interface
 * 
 * This interface defines the operations specific to user management.
 */
export interface UserService extends BaseService<User, UserRegistrationDto, UserUpdateDto> {
  /**
   * Get a user's profile
   * 
   * @param userId - ID of the user
   * @returns Promise with the user profile
   */
  getProfile(userId: string): Promise<UserResponseDto>;

  /**
   * Update a user's profile
   * 
   * @param userId - ID of the user
   * @param profileData - Updated profile data
   * @returns Promise with the updated user
   */
  updateProfile(userId: string, profileData: UserUpdateDto): Promise<UserResponseDto>;

  /**
   * Change a user's password
   * 
   * @param userId - ID of the user
   * @param passwordData - Current and new password
   * @returns Promise with the updated user
   */
  changePassword(userId: string, passwordData: PasswordChangeDto): Promise<UserResponseDto>;

  /**
   * Get user preferences
   * 
   * @param userId - ID of the user
   * @returns Promise with the user preferences
   */
  getUserPreferences(userId: string): Promise<Record<string, any>>;

  /**
   * Update user preferences
   * 
   * @param userId - ID of the user
   * @param preferences - Updated preferences
   * @returns Promise with the updated preferences
   */
  updateUserPreferences(userId: string, preferences: Record<string, any>): Promise<Record<string, any>>;

  /**
   * Get user's entries for all giveaways
   * 
   * @param userId - ID of the user
   * @param giveawayId - Optional ID to filter entries by giveaway
   * @returns Promise with the user entries
   */
  getUserEntries(userId: string, giveawayId?: string): Promise<any[]>;

  /**
   * Get user's wins
   * 
   * @param userId - ID of the user
   * @returns Promise with the user wins
   */
  getUserWins(userId: string): Promise<any[]>;

  /**
   * Upload profile image
   * 
   * @param userId - ID of the user
   * @param imageFile - Image file data
   * @returns Promise with the updated user
   */
  uploadProfileImage(userId: string, imageFile: any): Promise<UserResponseDto>;

  /**
   * Check if email exists
   * 
   * @param email - Email to check
   * @returns Promise with boolean indicating if email exists
   */
  emailExists(email: string): Promise<boolean>;

} 