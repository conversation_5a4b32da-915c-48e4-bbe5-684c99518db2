/**
 * Subscription Service Interface
 *
 * Handles Stripe subscription management for recurring membership payments
 */

export interface BillingAddress {
  line1: string;
  line2?: string;
  city: string;
  state?: string;
  postal_code: string;
  country: string;
}

export interface CreateSubscriptionOptions {
  userId: string;
  priceId: string;
  paymentMethodId: string;
  billingAddress?: BillingAddress;
  trialPeriodDays?: number;
  metadata?: Record<string, string>;
}

export interface UpdateSubscriptionOptions {
  subscriptionId: string;
  newPriceId?: string;
  paymentMethodId?: string;
  billingAddress?: BillingAddress;
  metadata?: Record<string, string>;
}

export interface SubscriptionDetails {
  id: string;
  customerId: string;
  status: string;
  currentPeriodStart: Date;
  currentPeriodEnd: Date;
  cancelAtPeriodEnd: boolean;
  canceledAt?: Date | undefined;
  trialStart?: Date | undefined;
  trialEnd?: Date | undefined;
  priceId: string;
  amount: number;
  currency: string;
  interval: string;
  paymentMethodId?: string;
  latestInvoiceId?: string;
  metadata?: Record<string, string>;
}

export interface CustomerDetails {
  id: string;
  email: string;
  name?: string | null | undefined;
  defaultPaymentMethodId?: string;
  billingAddress?: BillingAddress;
  taxIds?: Array<{
    type: string;
    value: string;
  }>;
}

export interface InvoiceDetails {
  id: string;
  subscriptionId: string;
  amount: number;
  currency: string;
  status: string;
  created: Date;
  dueDate?: Date | undefined;
  paidAt?: Date | undefined;
  hostedInvoiceUrl?: string | null | undefined;
  invoicePdf?: string | null | undefined;
}

export interface SubscriptionServiceInterface {
  /**
   * Create a new subscription for a user
   */
  createSubscription(options: CreateSubscriptionOptions): Promise<SubscriptionDetails>;

  /**
   * Get subscription details by ID
   */
  getSubscription(subscriptionId: string): Promise<SubscriptionDetails>;

  /**
   * Update an existing subscription
   */
  updateSubscription(options: UpdateSubscriptionOptions): Promise<SubscriptionDetails>;

  /**
   * Cancel a subscription
   */
  cancelSubscription(subscriptionId: string, cancelAtPeriodEnd?: boolean): Promise<SubscriptionDetails>;

  /**
   * Resume a canceled subscription (if not yet ended)
   */
  resumeSubscription(subscriptionId: string): Promise<SubscriptionDetails>;

  /**
   * Get or create a Stripe customer for a user
   */
  getOrCreateCustomer(userId: string): Promise<CustomerDetails>;

  /**
   * Update customer information
   */
  updateCustomer(customerId: string, updates: Partial<CustomerDetails>): Promise<CustomerDetails>;

  /**
   * Get customer's subscriptions
   */
  getCustomerSubscriptions(customerId: string): Promise<SubscriptionDetails[]>;

  /**
   * Get subscription invoices
   */
  getSubscriptionInvoices(subscriptionId: string): Promise<InvoiceDetails[]>;

  /**
   * Preview subscription changes (for upgrades/downgrades)
   */
  previewSubscriptionChange(subscriptionId: string, newPriceId: string): Promise<{
    prorationAmount: number;
    nextInvoiceAmount: number;
    nextInvoiceDate: Date;
  }>;

  /**
   * Handle subscription webhook events
   */
  handleWebhookEvent(event: any): Promise<void>;

  /**
   * Sync subscription status from Stripe
   */
  syncSubscriptionStatus(subscriptionId: string): Promise<SubscriptionDetails>;

  /**
   * Get upcoming invoice for a subscription
   */
  getUpcomingInvoice(subscriptionId: string): Promise<InvoiceDetails>;

  /**
   * Update subscription payment method
   */
  updateSubscriptionPaymentMethod(subscriptionId: string, paymentMethodId: string): Promise<SubscriptionDetails>;

  /**
   * Add backup payment method to customer
   */
  addBackupPaymentMethod(customerId: string, paymentMethodId: string): Promise<void>;

  /**
   * Remove backup payment method
   */
  removeBackupPaymentMethod(customerId: string, paymentMethodId: string): Promise<void>;

  /**
   * Retry failed payment with backup method
   */
  retryFailedPayment(invoiceId: string, paymentMethodId?: string): Promise<InvoiceDetails>;
}

/**
 * Subscription webhook event types
 */
export enum SubscriptionWebhookEvents {
  SUBSCRIPTION_CREATED = 'customer.subscription.created',
  SUBSCRIPTION_UPDATED = 'customer.subscription.updated',
  SUBSCRIPTION_DELETED = 'customer.subscription.deleted',
  INVOICE_PAYMENT_SUCCEEDED = 'invoice.payment_succeeded',
  INVOICE_PAYMENT_FAILED = 'invoice.payment_failed',
  INVOICE_UPCOMING = 'invoice.upcoming',
  CUSTOMER_SUBSCRIPTION_TRIAL_WILL_END = 'customer.subscription.trial_will_end',
  PAYMENT_METHOD_ATTACHED = 'payment_method.attached',
  PAYMENT_METHOD_DETACHED = 'payment_method.detached'
}

/**
 * Subscription status enum
 */
export enum SubscriptionStatus {
  INCOMPLETE = 'incomplete',
  INCOMPLETE_EXPIRED = 'incomplete_expired',
  TRIALING = 'trialing',
  ACTIVE = 'active',
  PAST_DUE = 'past_due',
  CANCELED = 'canceled',
  UNPAID = 'unpaid'
}

/**
 * Invoice status enum
 */
export enum InvoiceStatus {
  DRAFT = 'draft',
  OPEN = 'open',
  PAID = 'paid',
  UNCOLLECTIBLE = 'uncollectible',
  VOID = 'void'
}
