/**
 * Stripe Subscription Service Implementation
 *
 * Handles Stripe subscription management for recurring membership payments
 */

import <PERSON><PERSON> from 'stripe';
import { PrismaClient } from '@prisma/client';
import {
  SubscriptionServiceInterface,
  CreateSubscriptionOptions,
  UpdateSubscriptionOptions,
  SubscriptionDetails,
  CustomerDetails,
  InvoiceDetails,
  BillingAddress
} from './subscription-service.interface.js';
import {
  NotFoundError,
  BusinessLogicError
} from '../../utils/errors.js';

export class StripeSubscriptionService implements SubscriptionServiceInterface {
  private stripe: Stripe;
  private prisma: PrismaClient;

  constructor() {
    this.stripe = new Stripe(process.env['STRIPE_SECRET_KEY']!, {
      apiVersion: '2025-04-30.basil'
    });
    this.prisma = new PrismaClient();
  }

  /**
   * Create a new subscription for a user
   */
  async createSubscription(options: CreateSubscriptionOptions): Promise<SubscriptionDetails> {
    try {
      // Get or create Stripe customer
      const customer = await this.getOrCreateCustomer(options.userId);

      // Attach payment method to customer
      await this.stripe.paymentMethods.attach(options.paymentMethodId, {
        customer: customer.id
      });

      // Set as default payment method
      await this.stripe.customers.update(customer.id, {
        invoice_settings: {
          default_payment_method: options.paymentMethodId
        }
      });

      // Create subscription
      const subscriptionParams: Stripe.SubscriptionCreateParams = {
        customer: customer.id,
        items: [{
          price: options.priceId
        }],
        payment_behavior: 'default_incomplete',
        payment_settings: {
          save_default_payment_method: 'on_subscription',
          payment_method_types: ['card']
        },
        expand: ['latest_invoice.payment_intent'],
        metadata: {
          userId: options.userId,
          ...options.metadata
        }
      };

      // Only add trial_period_days if it's defined
      if (options.trialPeriodDays !== undefined) {
        subscriptionParams.trial_period_days = options.trialPeriodDays;
      }

      const subscription = await this.stripe.subscriptions.create(subscriptionParams);

      // Update billing address if provided
      if (options.billingAddress) {
        await this.updateCustomerBillingAddress(customer.id, options.billingAddress);
      }

      // Update local database
      await this.updateLocalMembership(options.userId, subscription);

      return this.mapStripeSubscriptionToDetails(subscription);
    } catch (error) {
      console.error('Error creating subscription:', error);
      throw new BusinessLogicError('Failed to create subscription');
    }
  }

  /**
   * Get subscription details by ID
   */
  async getSubscription(subscriptionId: string): Promise<SubscriptionDetails> {
    try {
      const subscription = await this.stripe.subscriptions.retrieve(subscriptionId, {
        expand: ['latest_invoice', 'customer']
      });

      return this.mapStripeSubscriptionToDetails(subscription);
    } catch (error) {
      console.error('Error retrieving subscription:', error);
      throw new NotFoundError('Subscription not found');
    }
  }

  /**
   * Update an existing subscription
   */
  async updateSubscription(options: UpdateSubscriptionOptions): Promise<SubscriptionDetails> {
    try {
      const updateData: Stripe.SubscriptionUpdateParams = {};

      if (options.newPriceId) {
        // Get current subscription to update the price
        const currentSub = await this.stripe.subscriptions.retrieve(options.subscriptionId);
        if (currentSub.items.data[0]) {
          updateData.items = [{
            id: currentSub.items.data[0].id,
            price: options.newPriceId
          }];
          updateData.proration_behavior = 'create_prorations';
        }
      }

      if (options.paymentMethodId) {
        updateData.default_payment_method = options.paymentMethodId;
      }

      if (options.metadata) {
        updateData.metadata = options.metadata;
      }

      const subscription = await this.stripe.subscriptions.update(
        options.subscriptionId,
        updateData
      );

      // Update billing address if provided
      if (options.billingAddress && subscription.customer) {
        await this.updateCustomerBillingAddress(
          subscription.customer as string,
          options.billingAddress
        );
      }

      return this.mapStripeSubscriptionToDetails(subscription);
    } catch (error) {
      console.error('Error updating subscription:', error);
      throw new BusinessLogicError('Failed to update subscription');
    }
  }

  /**
   * Cancel a subscription
   */
  async cancelSubscription(subscriptionId: string, cancelAtPeriodEnd = true): Promise<SubscriptionDetails> {
    try {
      const subscription = await this.stripe.subscriptions.update(subscriptionId, {
        cancel_at_period_end: cancelAtPeriodEnd,
        metadata: {
          canceledAt: new Date().toISOString()
        }
      });

      // Update local database
      await this.updateLocalMembershipCancellation(subscriptionId, cancelAtPeriodEnd);

      return this.mapStripeSubscriptionToDetails(subscription);
    } catch (error) {
      console.error('Error canceling subscription:', error);
      throw new BusinessLogicError('Failed to cancel subscription');
    }
  }

  /**
   * Resume a canceled subscription
   */
  async resumeSubscription(subscriptionId: string): Promise<SubscriptionDetails> {
    try {
      const subscription = await this.stripe.subscriptions.update(subscriptionId, {
        cancel_at_period_end: false
      });

      // Update local database
      await this.updateLocalMembershipCancellation(subscriptionId, false);

      return this.mapStripeSubscriptionToDetails(subscription);
    } catch (error) {
      console.error('Error resuming subscription:', error);
      throw new BusinessLogicError('Failed to resume subscription');
    }
  }

  /**
   * Get or create a Stripe customer for a user
   */
  async getOrCreateCustomer(userId: string): Promise<CustomerDetails> {
    try {
      // Check if user already has a Stripe customer ID
      const user = await this.prisma.user.findUnique({
        where: { id: userId }
      });

      if (!user) {
        throw new NotFoundError('User not found');
      }

      if (user.stripeCustomerId) {
        // Retrieve existing customer
        const customer = await this.stripe.customers.retrieve(user.stripeCustomerId);
        return this.mapStripeCustomerToDetails(customer as Stripe.Customer);
      }

      // Create new customer
      const customer = await this.stripe.customers.create({
        email: user.email,
        name: `${user.firstName} ${user.lastName}`,
        metadata: {
          userId: user.id
        }
      });

      // Update user with Stripe customer ID
      await this.prisma.user.update({
        where: { id: userId },
        data: { stripeCustomerId: customer.id }
      });

      return this.mapStripeCustomerToDetails(customer);
    } catch (error) {
      console.error('Error getting/creating customer:', error);
      throw new BusinessLogicError('Failed to create customer');
    }
  }

  /**
   * Update customer information
   */
  async updateCustomer(customerId: string, updates: Partial<CustomerDetails>): Promise<CustomerDetails> {
    try {
      const updateData: Stripe.CustomerUpdateParams = {};

      if (updates.email) updateData.email = updates.email;
      if (updates.name) updateData.name = updates.name;
      if (updates.billingAddress) {
        const address: Stripe.AddressParam = {
          line1: updates.billingAddress.line1,
          city: updates.billingAddress.city,
          postal_code: updates.billingAddress.postal_code,
          country: updates.billingAddress.country
        };

        // Only add optional fields if they exist
        if (updates.billingAddress.line2) {
          address.line2 = updates.billingAddress.line2;
        }
        if (updates.billingAddress.state) {
          address.state = updates.billingAddress.state;
        }

        updateData.address = address;
      }

      const customer = await this.stripe.customers.update(customerId, updateData);
      return this.mapStripeCustomerToDetails(customer);
    } catch (error) {
      console.error('Error updating customer:', error);
      throw new BusinessLogicError('Failed to update customer');
    }
  }

  /**
   * Get customer's subscriptions
   */
  async getCustomerSubscriptions(customerId: string): Promise<SubscriptionDetails[]> {
    try {
      const subscriptions = await this.stripe.subscriptions.list({
        customer: customerId,
        expand: ['data.latest_invoice']
      });

      return subscriptions.data.map(sub => this.mapStripeSubscriptionToDetails(sub));
    } catch (error) {
      console.error('Error retrieving customer subscriptions:', error);
      throw new BusinessLogicError('Failed to retrieve subscriptions');
    }
  }

  /**
   * Get subscription invoices
   */
  async getSubscriptionInvoices(subscriptionId: string): Promise<InvoiceDetails[]> {
    try {
      const invoices = await this.stripe.invoices.list({
        subscription: subscriptionId,
        limit: 100
      });

      return invoices.data.map(invoice => this.mapStripeInvoiceToDetails(invoice));
    } catch (error) {
      console.error('Error retrieving subscription invoices:', error);
      throw new BusinessLogicError('Failed to retrieve invoices');
    }
  }

  // Additional helper methods will be added in the next part...

  /**
   * Map Stripe subscription to our SubscriptionDetails interface
   */
  private mapStripeSubscriptionToDetails(subscription: Stripe.Subscription): SubscriptionDetails {
    return {
      id: subscription.id,
      customerId: subscription.customer as string,
      status: subscription.status,
      currentPeriodStart: new Date((subscription as any).current_period_start * 1000),
      currentPeriodEnd: new Date((subscription as any).current_period_end * 1000),
      cancelAtPeriodEnd: subscription.cancel_at_period_end,
      canceledAt: subscription.canceled_at ? new Date(subscription.canceled_at * 1000) : undefined,
      trialStart: subscription.trial_start ? new Date(subscription.trial_start * 1000) : undefined,
      trialEnd: subscription.trial_end ? new Date(subscription.trial_end * 1000) : undefined,
      priceId: subscription.items.data[0]?.price.id || '',
      amount: subscription.items.data[0]?.price.unit_amount || 0,
      currency: subscription.items.data[0]?.price.currency || 'usd',
      interval: subscription.items.data[0]?.price.recurring?.interval || 'month',
      paymentMethodId: subscription.default_payment_method as string,
      latestInvoiceId: subscription.latest_invoice as string,
      metadata: subscription.metadata
    };
  }

  /**
   * Map Stripe customer to our CustomerDetails interface
   */
  private mapStripeCustomerToDetails(customer: Stripe.Customer): CustomerDetails {
    let billingAddress: BillingAddress | undefined = undefined;

    if (customer.address && customer.address.line1 && customer.address.city &&
        customer.address.postal_code && customer.address.country) {
      billingAddress = {
        line1: customer.address.line1,
        city: customer.address.city,
        postal_code: customer.address.postal_code,
        country: customer.address.country
      };

      // Add optional fields if they exist
      if (customer.address.line2) {
        billingAddress.line2 = customer.address.line2;
      }
      if (customer.address.state) {
        billingAddress.state = customer.address.state;
      }
    }

    const result: CustomerDetails = {
      id: customer.id,
      email: customer.email || '',
      name: customer.name,
      defaultPaymentMethodId: customer.invoice_settings?.default_payment_method as string
    };

    if (billingAddress) {
      result.billingAddress = billingAddress;
    }

    return result;
  }

  /**
   * Map Stripe invoice to our InvoiceDetails interface
   */
  private mapStripeInvoiceToDetails(invoice: Stripe.Invoice): InvoiceDetails {
    return {
      id: invoice.id || '',
      subscriptionId: ((invoice as any).subscription as string) || '',
      amount: invoice.amount_paid,
      currency: invoice.currency,
      status: invoice.status || 'draft',
      created: new Date(invoice.created * 1000),
      dueDate: invoice.due_date ? new Date(invoice.due_date * 1000) : undefined,
      paidAt: invoice.status_transitions?.paid_at ? new Date(invoice.status_transitions.paid_at * 1000) : undefined,
      hostedInvoiceUrl: invoice.hosted_invoice_url,
      invoicePdf: invoice.invoice_pdf
    };
  }

  // Placeholder methods - will be implemented in the next part
  async previewSubscriptionChange(_subscriptionId: string, _newPriceId: string): Promise<any> {
    throw new Error('Method not implemented');
  }

  async handleWebhookEvent(_event: any): Promise<void> {
    throw new Error('Method not implemented');
  }

  async syncSubscriptionStatus(_subscriptionId: string): Promise<SubscriptionDetails> {
    throw new Error('Method not implemented');
  }

  async getUpcomingInvoice(_subscriptionId: string): Promise<InvoiceDetails> {
    throw new Error('Method not implemented');
  }

  async updateSubscriptionPaymentMethod(_subscriptionId: string, _paymentMethodId: string): Promise<SubscriptionDetails> {
    throw new Error('Method not implemented');
  }

  async addBackupPaymentMethod(_customerId: string, _paymentMethodId: string): Promise<void> {
    throw new Error('Method not implemented');
  }

  async removeBackupPaymentMethod(_customerId: string, _paymentMethodId: string): Promise<void> {
    throw new Error('Method not implemented');
  }

  async retryFailedPayment(_invoiceId: string, _paymentMethodId?: string): Promise<InvoiceDetails> {
    throw new Error('Method not implemented');
  }

  private async updateLocalMembership(_userId: string, _subscription: Stripe.Subscription): Promise<void> {
    // Implementation will be added
  }

  private async updateLocalMembershipCancellation(_subscriptionId: string, _cancelAtPeriodEnd: boolean): Promise<void> {
    // Implementation will be added
  }

  private async updateCustomerBillingAddress(_customerId: string, _billingAddress: BillingAddress): Promise<void> {
    // Implementation will be added
  }
}
