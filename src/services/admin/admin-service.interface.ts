/**
 * Admin Service Interface
 * 
 * This interface defines the operations for admin-specific functionality,
 * including dashboard statistics, user management, transaction management,
 * and report generation.
 */

import { PaginatedResult, PaginationOptions } from '../../repositories/base/base-repository.interface.js';
import { 
  DashboardStatsResponseDto,
  DashboardStatsParamsDto,
  AdminUserFilterParamsDto,
  UserRoleUpdateDto,
  AdminTransactionFilterParamsDto,
  TransactionRefundDto,
  ReportGenerationParamsDto,
  ReportResponseDto,
  SystemSettingsUpdateDto,
  AdminNotificationCreateDto
} from '../../dtos/admin.dto.js';
import { UserResponseDto } from '../../dtos/user.dto.js';
import { TransactionResponseDto } from '../../dtos/transaction.dto.js';
import { SettingResponseDto } from '../../dtos/setting.dto.js';

/**
 * Admin service interface for admin operations
 */
export interface AdminServiceInterface {
  /**
   * Get dashboard statistics
   * 
   * @param params - Optional parameters to filter statistics
   * @returns Promise with dashboard statistics
   */
  getDashboardStats(params?: DashboardStatsParamsDto): Promise<DashboardStatsResponseDto>;
  
  /**
   * Get users with optional filtering and pagination
   * 
   * @param filter - Optional filter criteria for users
   * @param pagination - Optional pagination options
   * @returns Promise with paginated result of users
   */
  getUsers(
    filter?: AdminUserFilterParamsDto,
    pagination?: PaginationOptions
  ): Promise<PaginatedResult<UserResponseDto>>;
  
  /**
   * Update a user's role
   * 
   * @param userId - ID of the user to update
   * @param data - Role update data
   * @param adminId - ID of the admin making the change
   * @returns Promise with the updated user
   */
  updateUserRole(
    userId: string,
    data: UserRoleUpdateDto,
    adminId: string
  ): Promise<UserResponseDto>;
  
  /**
   * Delete a user account
   * 
   * @param userId - ID of the user to delete
   * @param adminId - ID of the admin making the change
   * @returns Promise with the deleted user
   */
  deleteUser(userId: string, adminId: string): Promise<UserResponseDto>;
  
  /**
   * Get transactions with optional filtering and pagination
   * 
   * @param filter - Optional filter criteria for transactions
   * @param pagination - Optional pagination options
   * @returns Promise with paginated result of transactions
   */
  getTransactions(
    filter?: AdminTransactionFilterParamsDto,
    pagination?: PaginationOptions
  ): Promise<PaginatedResult<TransactionResponseDto>>;
  
  /**
   * Process a refund for a transaction
   * 
   * @param transactionId - ID of the transaction to refund
   * @param data - Refund data
   * @param adminId - ID of the admin processing the refund
   * @returns Promise with the updated transaction
   */
  processRefund(
    transactionId: string,
    data: TransactionRefundDto,
    adminId: string
  ): Promise<TransactionResponseDto>;
  
  /**
   * Generate a report
   * 
   * @param params - Report generation parameters
   * @param adminId - ID of the admin generating the report
   * @returns Promise with the report response
   */
  generateReport(
    params: ReportGenerationParamsDto,
    adminId: string
  ): Promise<ReportResponseDto>;
  
  /**
   * Get system settings
   * 
   * @returns Promise with array of system settings
   */
  getSystemSettings(): Promise<SettingResponseDto[]>;
  
  /**
   * Update system settings
   * 
   * @param settings - Settings to update
   * @param adminId - ID of the admin making the change
   * @returns Promise with array of updated settings
   */
  updateSystemSettings(
    settings: SystemSettingsUpdateDto,
    adminId: string
  ): Promise<SettingResponseDto[]>;
  
  /**
   * Send admin notification
   * 
   * @param data - Notification data
   * @param adminId - ID of the admin sending the notification
   * @returns Promise with number of notifications sent
   */
  sendAdminNotification(
    data: AdminNotificationCreateDto,
    adminId: string
  ): Promise<number>;
  
  /**
   * Check if user has admin privileges
   * 
   * @param userId - ID of the user to check
   * @returns Promise with boolean indicating admin status
   */
  isAdmin(userId: string): Promise<boolean>;
} 