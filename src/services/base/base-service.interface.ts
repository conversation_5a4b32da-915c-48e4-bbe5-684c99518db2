/**
 * Base Service Interface
 * 
 * This interface defines the standard operations that all services should implement.
 * It uses generic type parameters for flexibility across different entity types.
 */

import { FilterOptions, PaginatedResult, PaginationOptions } from '../../repositories/base/base-repository.interface.js';

/**
 * Base service interface for CRUD operations
 * 
 * @template T - Entity type
 * @template D - DTO type for creation
 * @template U - DTO type for updates (defaults to D)
 * @template ID - ID type (defaults to string)
 */
export interface BaseService<T, D, U = D, ID = string> {
  /**
   * Get an entity by ID
   * 
   * @param id - ID of the entity to retrieve
   * @returns Promise with entity or null if not found
   */
  get(id: ID): Promise<T | null>;

  /**
   * List entities with optional filtering and pagination
   * 
   * @param filter - Optional filter criteria
   * @param pagination - Optional pagination options
   * @returns Promise with array of entities
   */
  list(filter?: FilterOptions, pagination?: PaginationOptions): Promise<T[]>;

  /**
   * List entities with pagination metadata
   * 
   * @param filter - Optional filter criteria
   * @param pagination - Pagination options
   * @returns Promise with paginated result
   */
  listPaginated(filter?: FilterOptions, pagination?: PaginationOptions): Promise<PaginatedResult<T>>;

  /**
   * Count entities that match the filter criteria
   * 
   * @param filter - Optional filter criteria
   * @returns Promise with the count
   */
  count(filter?: FilterOptions): Promise<number>;

  /**
   * Create a new entity
   * 
   * @param data - The data to create the entity with
   * @returns Promise with the created entity
   */
  create(data: D): Promise<T>;

  /**
   * Create multiple entities
   * 
   * @param dataArray - Array of entity data to create
   * @returns Promise with array of created entities
   */
  createMany(dataArray: D[]): Promise<T[]>;

  /**
   * Update an existing entity
   * 
   * @param id - The ID of the entity to update
   * @param data - The data to update the entity with
   * @returns Promise with the updated entity
   */
  update(id: ID, data: U): Promise<T>;

  /**
   * Delete an entity
   * 
   * @param id - The ID of the entity to delete
   * @returns Promise with the deleted entity or void
   */
  delete(id: ID): Promise<T | void>;

  /**
   * Check if an entity exists
   * 
   * @param id - The ID of the entity to check
   * @returns Promise with boolean indicating existence
   */
  exists(id: ID): Promise<boolean>;
}
