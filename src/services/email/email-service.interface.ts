/**
 * Email Service Interface
 * 
 * Defines methods for sending emails through various providers.
 */

export interface EmailService {
  /**
   * Send a plain text or HTML email
   * 
   * @param to - Recipient email address
   * @param subject - Email subject
   * @param content - Email content (HTML or plain text)
   * @param isHtml - Whether the content is HTML (default: true)
   * @returns Promise indicating success
   */
  sendEmail(
    to: string,
    subject: string,
    content: string,
    isHtml?: boolean
  ): Promise<void>;

  /**
   * Send an email using a template
   * 
   * @param to - Recipient email address
   * @param subject - Email subject
   * @param templateId - Template identifier
   * @param templateData - Data to populate the template
   * @returns Promise indicating success
   */
  sendTemplateEmail(
    to: string,
    subject: string,
    templateId: string,
    templateData: Record<string, any>
  ): Promise<void>;

  /**
   * Send email verification email
   * 
   * @param to - Recipient email address
   * @param firstName - User's first name
   * @param verificationLink - Verification link URL
   * @returns Promise indicating success
   */
  sendVerificationEmail(
    to: string,
    firstName: string,
    verificationLink: string
  ): Promise<void>;

  /**
   * Send password reset email
   * 
   * @param to - Recipient email address
   * @param firstName - User's first name
   * @param resetLink - Password reset link URL
   * @returns Promise indicating success
   */
  sendPasswordResetEmail(
    to: string,
    firstName: string,
    resetLink: string
  ): Promise<void>;
}
