/**
 * SendGrid Email Service Implementation
 *
 * This service handles email sending through SendGrid API.
 */

import sgMail from '@sendgrid/mail';
import { EmailService } from './email-service.interface.js';

/**
 * SendGrid email service implementation
 */
export class SendGridEmailService implements EmailService {
  private readonly fromEmail: string;
  private readonly fromName: string;

  constructor() {
    // Initialize SendGrid with API key from environment
    const apiKey = process.env['SENDGRID_API_KEY'];
    if (!apiKey) {
      throw new Error('SENDGRID_API_KEY environment variable is required');
    }

    sgMail.setApiKey(apiKey);

    // Set default from email and name
    this.fromEmail = process.env['SENDGRID_FROM_EMAIL'] || '<EMAIL>';
    this.fromName = process.env['SENDGRID_FROM_NAME'] || 'Winners Society';
  }

  /**
   * Send a plain text or HTML email
   *
   * @param to - Recipient email address
   * @param subject - Email subject
   * @param content - Email content (HTML or plain text)
   * @param isHtml - Whether the content is HTML (default: true)
   * @returns Promise indicating success
   */
  async sendEmail(
    to: string,
    subject: string,
    content: string,
    isHtml: boolean = true
  ): Promise<void> {
    try {
      const msg = {
        to,
        from: {
          email: this.fromEmail,
          name: this.fromName
        },
        subject,
        ...(isHtml ? { html: content } : { text: content })
      };

      await sgMail.send(msg);
      console.log(`Email sent successfully to ${to}`);
    } catch (error) {
      console.error('Error sending email:', error);
      throw new Error(`Failed to send email: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Send an email using a template
   *
   * @param to - Recipient email address
   * @param subject - Email subject
   * @param templateId - Template identifier
   * @param templateData - Data to populate the template
   * @returns Promise indicating success
   */
  async sendTemplateEmail(
    to: string,
    subject: string,
    templateId: string,
    templateData: Record<string, any>
  ): Promise<void> {
    try {
      const msg = {
        to,
        from: {
          email: this.fromEmail,
          name: this.fromName
        },
        subject,
        templateId,
        dynamicTemplateData: templateData
      };

      await sgMail.send(msg);
      console.log(`Template email sent successfully to ${to} using template ${templateId}`);
    } catch (error) {
      console.error('Error sending template email:', error);
      throw new Error(`Failed to send template email: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Send email verification email
   *
   * @param to - Recipient email address
   * @param firstName - User's first name
   * @param verificationLink - Verification link URL
   * @returns Promise indicating success
   */
  async sendVerificationEmail(
    to: string,
    firstName: string,
    verificationLink: string
  ): Promise<void> {
    const subject = 'Verify Your Email Address - Winners Society';
    const htmlContent = this.generateVerificationEmailTemplate(firstName, verificationLink);

    await this.sendEmail(to, subject, htmlContent, true);
  }

  /**
   * Send password reset email
   *
   * @param to - Recipient email address
   * @param firstName - User's first name
   * @param resetLink - Password reset link URL
   * @returns Promise indicating success
   */
  async sendPasswordResetEmail(
    to: string,
    firstName: string,
    resetLink: string
  ): Promise<void> {
    const subject = 'Reset Your Password - Winners Society';
    const htmlContent = this.generatePasswordResetEmailTemplate(firstName, resetLink);

    await this.sendEmail(to, subject, htmlContent, true);
  }

  /**
   * Generate HTML template for email verification
   *
   * @param firstName - User's first name
   * @param verificationLink - Verification link URL
   * @returns HTML email content
   */
  private generateVerificationEmailTemplate(firstName: string, verificationLink: string): string {
    return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Verify Your Email</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background-color: #4f46e5; color: white; padding: 20px; text-align: center; border-radius: 8px 8px 0 0; }
        .content { background-color: #f9fafb; padding: 30px; border-radius: 0 0 8px 8px; }
        .button {
            display: inline-block;
            background-color: #4f46e5 !important;
            color: #ffffff !important;
            padding: 12px 24px;
            text-decoration: none !important;
            border-radius: 6px;
            margin: 20px 0;
            font-weight: bold;
            font-size: 16px;
            border: none;
            mso-padding-alt: 0;
            text-align: center;
        }
        .button:hover { background-color: #4338ca !important; }
        .footer { text-align: center; margin-top: 20px; font-size: 14px; color: #666; }
    </style>
</head>
<body>
    <div class="header">
        <h1>Welcome to Winners Society!</h1>
    </div>
    <div class="content">
        <h2>Hi ${firstName},</h2>
        <p>Thank you for joining Winners Society! To complete your registration and start participating in our exclusive giveaways, please verify your email address.</p>

        <p>Click the button below to verify your email:</p>

        <a href="${verificationLink}" class="button" style="display: inline-block; background-color: #4f46e5; color: #ffffff; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin: 20px 0; font-weight: bold; font-size: 16px; text-align: center;">Verify Email Address</a>

        <p>If the button doesn't work, you can copy and paste this link into your browser:</p>
        <p style="word-break: break-all; color: #4f46e5;">${verificationLink}</p>

        <p><strong>Important:</strong> This verification link will expire in 24 hours for security reasons.</p>

        <p>If you didn't create an account with Winners Society, please ignore this email.</p>

        <p>Best regards,<br>The Winners Society Team</p>
    </div>
    <div class="footer">
        <p>© 2024 Winners Society. All rights reserved.</p>
    </div>
</body>
</html>`;
  }

  /**
   * Generate HTML template for password reset
   *
   * @param firstName - User's first name
   * @param resetLink - Password reset link URL
   * @returns HTML email content
   */
  private generatePasswordResetEmailTemplate(firstName: string, resetLink: string): string {
    return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reset Your Password</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background-color: #dc2626; color: white; padding: 20px; text-align: center; border-radius: 8px 8px 0 0; }
        .content { background-color: #f9fafb; padding: 30px; border-radius: 0 0 8px 8px; }
        .button {
            display: inline-block;
            background-color: #dc2626 !important;
            color: #ffffff !important;
            padding: 12px 24px;
            text-decoration: none !important;
            border-radius: 6px;
            margin: 20px 0;
            font-weight: bold;
            font-size: 16px;
            border: none;
            mso-padding-alt: 0;
            text-align: center;
        }
        .button:hover { background-color: #b91c1c !important; }
        .footer { text-align: center; margin-top: 20px; font-size: 14px; color: #666; }
    </style>
</head>
<body>
    <div class="header">
        <h1>Password Reset Request</h1>
    </div>
    <div class="content">
        <h2>Hi ${firstName},</h2>
        <p>We received a request to reset your password for your Winners Society account.</p>

        <p>Click the button below to reset your password:</p>

        <a href="${resetLink}" class="button" style="display: inline-block; background-color: #dc2626; color: #ffffff; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin: 20px 0; font-weight: bold; font-size: 16px; text-align: center;">Reset Password</a>

        <p>If the button doesn't work, you can copy and paste this link into your browser:</p>
        <p style="word-break: break-all; color: #dc2626;">${resetLink}</p>

        <p><strong>Important:</strong> This reset link will expire in 1 hour for security reasons.</p>

        <p>If you didn't request a password reset, please ignore this email. Your password will remain unchanged.</p>

        <p>Best regards,<br>The Winners Society Team</p>
    </div>
    <div class="footer">
        <p>© 2024 Winners Society. All rights reserved.</p>
    </div>
</body>
</html>`;
  }
}
