/**
 * Notification Service Interface
 * 
 * This interface defines the operations for managing user notifications,
 * including creating notifications, retrieving user notifications, 
 * marking notifications as read, and sending email notifications.
 */

import { BaseService } from '../base/base-service.interface.js';
import {
  NotificationResponseDto,
  NotificationCreateDto,
  NotificationUpdateDto,
  NotificationFilterParamsDto
} from '../../dtos/notification.dto.js';
import { PaginatedResult, PaginationOptions } from '../../repositories/base/base-repository.interface.js';

/**
 * Notification service interface for notification operations
 * @extends BaseService<NotificationResponseDto, NotificationCreateDto, NotificationUpdateDto>
 */
export interface NotificationServiceInterface extends BaseService<
  NotificationResponseDto,
  NotificationCreateDto,
  NotificationUpdateDto
> {
  /**
   * Get notifications for a specific user
   * 
   * @param userId - The ID of the user
   * @param pagination - Optional pagination options
   * @returns Promise with paginated result of user notifications
   */
  getUserNotifications(
    userId: string,
    pagination?: PaginationOptions
  ): Promise<PaginatedResult<NotificationResponseDto>>;

  /**
   * Get unread notifications for a specific user
   * 
   * @param userId - The ID of the user
   * @param pagination - Optional pagination options
   * @returns Promise with paginated result of unread user notifications
   */
  getUnreadNotifications(
    userId: string,
    pagination?: PaginationOptions
  ): Promise<PaginatedResult<NotificationResponseDto>>;
  
  /**
   * Mark a notification as read
   * 
   * @param notificationId - The ID of the notification to mark as read
   * @param userId - The ID of the user (for authorization check)
   * @returns Promise with the updated notification
   */
  markAsRead(notificationId: string, userId: string): Promise<NotificationResponseDto>;
  
  /**
   * Mark all notifications as read for a user
   * 
   * @param userId - The ID of the user
   * @returns Promise with the number of notifications updated
   */
  markAllAsRead(userId: string): Promise<number>;
  
  /**
   * Filter notifications based on criteria
   * 
   * @param filterParams - Filter parameters
   * @param pagination - Optional pagination options
   * @returns Promise with paginated result of filtered notifications
   */
  filterNotifications(
    filterParams: NotificationFilterParamsDto,
    pagination?: PaginationOptions
  ): Promise<PaginatedResult<NotificationResponseDto>>;
  
  /**
   * Send a notification to a user
   * 
   * @param data - Notification data to create
   * @returns Promise with the created notification
   */
  sendNotification(data: NotificationCreateDto): Promise<NotificationResponseDto>;
  
  /**
   * Send notifications to multiple users
   * 
   * @param userIds - Array of user IDs to send notification to
   * @param notificationData - Notification data (without userId)
   * @returns Promise with array of created notifications
   */
  sendNotificationToMany(
    userIds: string[],
    notificationData: Omit<NotificationCreateDto, 'userId'>
  ): Promise<NotificationResponseDto[]>;
  
  /**
   * Send a system notification to all users
   * 
   * @param title - Notification title
   * @param message - Notification message
   * @param link - Optional link
   * @param metadata - Optional metadata
   * @returns Promise with the number of notifications created
   */
  sendSystemNotification(
    title: string,
    message: string,
    link?: string,
    metadata?: Record<string, any>
  ): Promise<number>;
  
  /**
   * Send an email notification
   * 
   * @param userId - The ID of the user to send email to
   * @param subject - Email subject
   * @param message - Email message
   * @param templateId - Optional email template ID
   * @param templateData - Optional data for email template
   * @returns Promise with boolean indicating success
   */
  sendEmailNotification(
    userId: string,
    subject: string,
    message: string,
    templateId?: string,
    templateData?: Record<string, any>
  ): Promise<boolean>;
  
  /**
   * Delete notifications older than the specified date
   * 
   * @param date - The cutoff date
   * @returns Promise with the number of notifications deleted
   */
  deleteOlderThan(date: Date): Promise<number>;
} 