/**
 * Notification Service Implementation
 * 
 * This service manages notification operations including creation, retrieval,
 * marking as read, and sending email notifications.
 */

import { Notification, NotificationType, NotificationStatus } from '@prisma/client';
import { NotificationServiceInterface } from './notification-service.interface.js';
import {
  NotificationResponseDto,
  NotificationCreateDto,
  NotificationUpdateDto,
  NotificationFilterParamsDto
} from '../../dtos/notification.dto.js';
import {
  FilterOptions,
  PaginatedResult,
  PaginationOptions
} from '../../repositories/base/base-repository.interface.js';
import { 
  NotificationRepository,
  UserRepository
} from '../../repositories/index.js';
import { 
  ValidationError, 
  NotFoundError, 
  AuthorizationError,
  BusinessLogicError,
  AppError 
} from '../../utils/errors.js';

/**
 * Implementation of the Notification Service
 */
export class NotificationService implements NotificationServiceInterface {
  /**
   * Constructor for NotificationService
   */
  constructor(
    private readonly notificationRepository: NotificationRepository,
    private readonly userRepository: UserRepository,
    private readonly emailService?: any // Will be replaced with actual email service
  ) {}

  /**
   * Convert a Notification entity to a NotificationResponseDto
   * 
   * @private
   * @param notification - The notification entity to convert
   * @returns NotificationResponseDto
   */
  private toResponseDto(notification: Notification): NotificationResponseDto {
    const dto: NotificationResponseDto = {
      id: notification.id,
      userId: notification.userId,
      title: notification.title,
      message: notification.message,
      type: notification.type,
      status: notification.status,
      createdAt: notification.createdAt,
      updatedAt: notification.updatedAt
    };
    
    // Add optional fields only if they exist
    if (notification.link) {
      dto.link = notification.link;
    }
    
    if (notification.metadata) {
      dto.metadata = notification.metadata as Record<string, any>;
    }
    
    return dto;
  }

  /**
   * Get a notification by ID
   * 
   * @param id - ID of the notification to retrieve
   * @returns Promise with notification or null if not found
   */
  async get(id: string): Promise<NotificationResponseDto | null> {
    try {
      const notification = await this.notificationRepository.findById(id);
      return notification ? this.toResponseDto(notification) : null;
    } catch (error) {
      this.handleError(error, 'Failed to get notification');
      return null;
    }
  }

  /**
   * List notifications with optional filtering and pagination
   * 
   * @param filter - Optional filter criteria
   * @param pagination - Optional pagination options
   * @returns Promise with array of notifications
   */
  async list(
    filter?: FilterOptions,
    pagination?: PaginationOptions
  ): Promise<NotificationResponseDto[]> {
    try {
      const notifications = await this.notificationRepository.findMany(filter, pagination);
      return notifications.map(notification => this.toResponseDto(notification));
    } catch (error) {
      this.handleError(error, 'Failed to list notifications');
      return [];
    }
  }

  /**
   * List notifications with pagination metadata
   * 
   * @param filter - Optional filter criteria
   * @param pagination - Pagination options
   * @returns Promise with paginated result of notifications
   */
  async listPaginated(
    filter?: FilterOptions,
    pagination?: PaginationOptions
  ): Promise<PaginatedResult<NotificationResponseDto>> {
    try {
      const result = await this.notificationRepository.findManyPaginated(filter, pagination);
      
      return {
        data: result.data.map(notification => this.toResponseDto(notification)),
        meta: result.meta
      };
    } catch (error) {
      this.handleError(error, 'Failed to list paginated notifications');
      throw error;
    }
  }

  /**
   * Count notifications that match the filter criteria
   * 
   * @param filter - Optional filter criteria
   * @returns Promise with the count
   */
  async count(filter?: FilterOptions): Promise<number> {
    try {
      return await this.notificationRepository.count(filter);
    } catch (error) {
      this.handleError(error, 'Failed to count notifications');
      throw error;
    }
  }

  /**
   * Create a new notification
   * 
   * @param data - Notification data to create
   * @returns Promise with the created notification
   */
  async create(data: NotificationCreateDto): Promise<NotificationResponseDto> {
    try {
      // Validate required fields
      if (!data.userId) {
        throw new ValidationError('User ID is required');
      }
      
      if (!data.title) {
        throw new ValidationError('Title is required');
      }
      
      if (!data.message) {
        throw new ValidationError('Message is required');
      }
      
      // Check if user exists
      const userExists = await this.userRepository.exists(data.userId);
      if (!userExists) {
        throw new NotFoundError(`User with ID ${data.userId} not found`);
      }
      
      // Create notification
      const notification = await this.notificationRepository.create({
        ...data,
        status: NotificationStatus.UNREAD
      });
      
      return this.toResponseDto(notification);
    } catch (error) {
      this.handleError(error, 'Failed to create notification');
      throw error;
    }
  }

  /**
   * Create multiple notifications
   * 
   * @param dataArray - Array of notification data to create
   * @returns Promise with array of created notifications
   */
  async createMany(dataArray: NotificationCreateDto[]): Promise<NotificationResponseDto[]> {
    try {
      // Validate all items have required fields
      for (const data of dataArray) {
        if (!data.userId || !data.title || !data.message) {
          throw new ValidationError('All notifications must have userId, title, and message');
        }
      }
      
      // Set status to UNREAD for all notifications
      const notificationsWithStatus = dataArray.map(data => ({
        ...data,
        status: NotificationStatus.UNREAD
      }));
      
      const createdNotifications = await this.notificationRepository.createMany(notificationsWithStatus);
      return createdNotifications.map(notification => this.toResponseDto(notification));
    } catch (error) {
      this.handleError(error, 'Failed to create multiple notifications');
      throw error;
    }
  }

  /**
   * Update a notification
   * 
   * @param id - ID of the notification to update
   * @param data - Data to update the notification with
   * @returns Promise with the updated notification
   */
  async update(id: string, data: NotificationUpdateDto): Promise<NotificationResponseDto> {
    try {
      // Verify notification exists
      const existingNotification = await this.notificationRepository.findById(id);
      if (!existingNotification) {
        throw new NotFoundError(`Notification with ID ${id} not found`);
      }
      
      const updatedNotification = await this.notificationRepository.update(id, data);
      return this.toResponseDto(updatedNotification);
    } catch (error) {
      this.handleError(error, 'Failed to update notification');
      throw error;
    }
  }

  /**
   * Delete a notification
   * 
   * @param id - ID of the notification to delete
   * @returns Promise with the deleted notification
   */
  async delete(id: string): Promise<NotificationResponseDto> {
    try {
      // Verify notification exists
      const existingNotification = await this.notificationRepository.findById(id);
      if (!existingNotification) {
        throw new NotFoundError(`Notification with ID ${id} not found`);
      }
      
      const deletedNotification = await this.notificationRepository.delete(id);
      return this.toResponseDto(deletedNotification);
    } catch (error) {
      this.handleError(error, 'Failed to delete notification');
      throw error;
    }
  }

  /**
   * Check if a notification exists
   * 
   * @param id - ID of the notification to check
   * @returns Promise with boolean indicating existence
   */
  async exists(id: string): Promise<boolean> {
    try {
      return await this.notificationRepository.exists(id);
    } catch (error) {
      this.handleError(error, 'Failed to check notification existence');
      throw error;
    }
  }

  /**
   * Get notifications for a specific user
   * 
   * @param userId - The ID of the user
   * @param pagination - Optional pagination options
   * @returns Promise with paginated result of user notifications
   */
  async getUserNotifications(
    userId: string,
    pagination?: PaginationOptions
  ): Promise<PaginatedResult<NotificationResponseDto>> {
    try {
      if (!userId) {
        throw new ValidationError('User ID is required');
      }
      
      // Check if user exists
      const userExists = await this.userRepository.exists(userId);
      if (!userExists) {
        throw new NotFoundError(`User with ID ${userId} not found`);
      }
      
      // Get notifications with pagination
      const filter: FilterOptions = { userId };
      const result = await this.notificationRepository.findManyPaginated(filter, pagination);
      
      return {
        data: result.data.map(notification => this.toResponseDto(notification)),
        meta: result.meta
      };
    } catch (error) {
      this.handleError(error, 'Failed to get user notifications');
      throw error;
    }
  }

  /**
   * Get unread notifications for a specific user
   * 
   * @param userId - The ID of the user
   * @param pagination - Optional pagination options
   * @returns Promise with paginated result of unread user notifications
   */
  async getUnreadNotifications(
    userId: string,
    pagination?: PaginationOptions
  ): Promise<PaginatedResult<NotificationResponseDto>> {
    try {
      if (!userId) {
        throw new ValidationError('User ID is required');
      }
      
      // Check if user exists
      const userExists = await this.userRepository.exists(userId);
      if (!userExists) {
        throw new NotFoundError(`User with ID ${userId} not found`);
      }
      
      // Get unread notifications with pagination
      const filter: FilterOptions = {
        userId,
        status: NotificationStatus.UNREAD
      };
      
      const result = await this.notificationRepository.findManyPaginated(filter, pagination);
      
      return {
        data: result.data.map(notification => this.toResponseDto(notification)),
        meta: result.meta
      };
    } catch (error) {
      this.handleError(error, 'Failed to get unread notifications');
      throw error;
    }
  }

  /**
   * Mark a notification as read
   * 
   * @param notificationId - The ID of the notification to mark as read
   * @param userId - The ID of the user (for authorization check)
   * @returns Promise with the updated notification
   */
  async markAsRead(notificationId: string, userId: string): Promise<NotificationResponseDto> {
    try {
      if (!notificationId) {
        throw new ValidationError('Notification ID is required');
      }
      
      if (!userId) {
        throw new ValidationError('User ID is required');
      }
      
      // Get notification to check ownership
      const notification = await this.notificationRepository.findById(notificationId);
      if (!notification) {
        throw new NotFoundError(`Notification with ID ${notificationId} not found`);
      }
      
      // Check if notification belongs to the user
      if (notification.userId !== userId) {
        throw new AuthorizationError('User is not authorized to mark this notification as read');
      }
      
      // If already read, return as is
      if (notification.status === NotificationStatus.READ) {
        return this.toResponseDto(notification);
      }
      
      // Mark as read
      const updatedNotification = await this.notificationRepository.markAsRead(notificationId);
      return this.toResponseDto(updatedNotification);
    } catch (error) {
      this.handleError(error, 'Failed to mark notification as read');
      throw error;
    }
  }

  /**
   * Mark all notifications as read for a user
   * 
   * @param userId - The ID of the user
   * @returns Promise with the number of notifications updated
   */
  async markAllAsRead(userId: string): Promise<number> {
    try {
      if (!userId) {
        throw new ValidationError('User ID is required');
      }
      
      // Check if user exists
      const userExists = await this.userRepository.exists(userId);
      if (!userExists) {
        throw new NotFoundError(`User with ID ${userId} not found`);
      }
      
      // Mark all as read
      return await this.notificationRepository.markAllAsRead(userId);
    } catch (error) {
      this.handleError(error, 'Failed to mark all notifications as read');
      throw error;
    }
  }

  /**
   * Filter notifications based on criteria
   * 
   * @param filterParams - Filter parameters
   * @param pagination - Optional pagination options
   * @returns Promise with paginated result of filtered notifications
   */
  async filterNotifications(
    filterParams: NotificationFilterParamsDto,
    pagination?: PaginationOptions
  ): Promise<PaginatedResult<NotificationResponseDto>> {
    try {
      // Build filter from params
      const filter: FilterOptions = {};
      
      if (filterParams.userId) {
        filter['userId'] = filterParams.userId;
      }
      
      if (filterParams.type) {
        filter['type'] = filterParams.type;
      }
      
      if (filterParams.status) {
        filter['status'] = filterParams.status;
      }
      
      // Date range filters
      if (filterParams.createdAtStart || filterParams.createdAtEnd) {
        filter['createdAt'] = {};
        
        if (filterParams.createdAtStart) {
          filter['createdAt'].gte = filterParams.createdAtStart;
        }
        
        if (filterParams.createdAtEnd) {
          filter['createdAt'].lte = filterParams.createdAtEnd;
        }
      }
      
      // Get filtered notifications
      const result = await this.notificationRepository.findManyPaginated(filter, pagination);
      
      return {
        data: result.data.map(notification => this.toResponseDto(notification)),
        meta: result.meta
      };
    } catch (error) {
      this.handleError(error, 'Failed to filter notifications');
      throw error;
    }
  }

  /**
   * Send a notification to a user
   * 
   * @param data - Notification data to create
   * @returns Promise with the created notification
   */
  async sendNotification(data: NotificationCreateDto): Promise<NotificationResponseDto> {
    // Simply uses the create method
    return this.create(data);
  }

  /**
   * Send notifications to multiple users
   * 
   * @param userIds - Array of user IDs to send notification to
   * @param notificationData - Notification data (without userId)
   * @returns Promise with array of created notifications
   */
  async sendNotificationToMany(
    userIds: string[],
    notificationData: Omit<NotificationCreateDto, 'userId'>
  ): Promise<NotificationResponseDto[]> {
    try {
      if (!userIds || userIds.length === 0) {
        throw new ValidationError('At least one user ID is required');
      }
      
      if (!notificationData.title || !notificationData.message) {
        throw new ValidationError('Notification title and message are required');
      }
      
      // Create notification data for each user
      const notificationsToCreate = userIds.map(userId => ({
        userId,
        ...notificationData,
        status: NotificationStatus.UNREAD
      }));
      
      // Create the notifications
      const createdNotifications = await this.notificationRepository.createMany(notificationsToCreate);
      return createdNotifications.map(notification => this.toResponseDto(notification));
    } catch (error) {
      this.handleError(error, 'Failed to send notifications to multiple users');
      throw error;
    }
  }

  /**
   * Send a system notification to all users
   * 
   * @param title - Notification title
   * @param message - Notification message
   * @param link - Optional link
   * @param metadata - Optional metadata
   * @returns Promise with the number of notifications created
   */
  async sendSystemNotification(
    title: string,
    message: string,
    link?: string,
    metadata?: Record<string, any>
  ): Promise<number> {
    try {
      if (!title || !message) {
        throw new ValidationError('Notification title and message are required');
      }
      
      // Get all active users
      // In a real implementation, you might want to filter users by status or other criteria
      const users = await this.userRepository.findMany();
      
      if (users.length === 0) {
        return 0; // No users to notify
      }
      
      // Create notification data
      const notificationData: Omit<NotificationCreateDto, 'userId'> = {
        title,
        message,
        type: NotificationType.SYSTEM
      };
      
      // Add optional fields only if they exist
      if (link !== undefined) {
        notificationData.link = link;
      }
      
      if (metadata !== undefined) {
        notificationData.metadata = metadata;
      }
      
      // Get user IDs
      const userIds = users.map(user => user.id);
      
      // Send notifications
      const notifications = await this.sendNotificationToMany(userIds, notificationData);
      
      return notifications.length;
    } catch (error) {
      this.handleError(error, 'Failed to send system notification');
      throw error;
    }
  }

  /**
   * Send an email notification
   * 
   * @param userId - The ID of the user to send email to
   * @param subject - Email subject
   * @param message - Email message
   * @param templateId - Optional email template ID
   * @param templateData - Optional data for email template
   * @returns Promise with boolean indicating success
   */
  async sendEmailNotification(
    userId: string,
    subject: string,
    message: string,
    templateId?: string,
    templateData?: Record<string, any>
  ): Promise<boolean> {
    try {
      if (!userId) {
        throw new ValidationError('User ID is required');
      }
      
      if (!subject || !message) {
        throw new ValidationError('Email subject and message are required');
      }
      
      // Check if user exists and get email
      const user = await this.userRepository.findById(userId);
      if (!user) {
        throw new NotFoundError(`User with ID ${userId} not found`);
      }
      
      if (!user.email) {
        throw new ValidationError(`User with ID ${userId} does not have an email address`);
      }
      
      // Check if email service is available
      if (!this.emailService) {
        throw new BusinessLogicError('Email service is not configured');
      }
      
      // Send email (implementation depends on the actual email service)
      if (templateId) {
        // Send templated email
        await this.emailService.sendTemplateEmail(
          user.email,
          subject,
          templateId,
          templateData || {}
        );
      } else {
        // Send plain email
        await this.emailService.sendEmail(
          user.email,
          subject,
          message
        );
      }
      
      // If no error was thrown, consider the email sent successfully
      return true;
    } catch (error) {
      // Log the error but don't throw it
      console.error('Failed to send email notification:', error);
      return false;
    }
  }

  /**
   * Delete notifications older than the specified date
   * 
   * @param date - The cutoff date
   * @returns Promise with the number of notifications deleted
   */
  async deleteOlderThan(date: Date): Promise<number> {
    try {
      if (!date) {
        throw new ValidationError('Date is required');
      }
      
      return await this.notificationRepository.deleteOlderThan(date);
    } catch (error) {
      this.handleError(error, 'Failed to delete old notifications');
      throw error;
    }
  }

  /**
   * Handle errors from service operations
   * 
   * @private
   * @param error - The caught error
   * @param message - A message describing the operation that failed
   * @throws The appropriate error type
   */
  private handleError(error: unknown, message: string): never {
    // Log the error for debugging
    console.error(`${message}:`, error);
    
    // Re-throw domain errors
    if (
      error instanceof NotFoundError ||
      error instanceof ValidationError ||
      error instanceof AuthorizationError ||
      error instanceof BusinessLogicError ||
      error instanceof AppError
    ) {
      throw error;
    }
    
    // For unknown errors, throw a generic business logic error
    throw new BusinessLogicError(message);
  }
} 