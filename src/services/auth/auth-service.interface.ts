/**
 * Authentication Service Interface
 *
 * Defines methods for user authentication, token management, and security operations.
 */

import { User } from '@prisma/client';
import {
  UserLoginDto,
  PasswordResetRequestDto,
  PasswordResetDto,
  TokenRefreshDto,
  AuthResponseDto,
  TokenResponseDto,
  EmailVerificationDto,
  RegistrationResponseDto
} from '../../dtos/auth.dto.js';
import { UserRegistrationDto } from '../../dtos/user.dto.js';

/**
 * Auth service interface
 */
export interface AuthService {
  /**
   * Register a new user
   *
   * @param userData - User registration data
   * @returns Promise with registration response containing user (no tokens - user must verify email first)
   */
  register(userData: UserRegistrationDto): Promise<RegistrationResponseDto>;

  /**
   * Login a user with email and password
   *
   * @param credentials - Login credentials
   * @returns Promise with authentication response containing user and tokens
   */
  login(credentials: UserLoginDto): Promise<AuthResponseDto>;

  /**
   * Request a password reset
   *
   * @param data - Password reset request data with email
   * @returns Promise indicating the reset email was sent
   */
  requestPasswordReset(data: PasswordResetRequestDto): Promise<void>;

  /**
   * Confirm a password reset with token
   *
   * @param data - Password reset confirmation data
   * @returns Promise indicating success
   */
  confirmPasswordReset(data: PasswordResetDto): Promise<void>;

  /**
   * Verify a user's email address
   *
   * @param data - Email verification data with token
   * @returns Promise with verification result
   */
  verifyEmail(data: EmailVerificationDto): Promise<{ email: string; isVerified: boolean }>;

  /**
   * Refresh authentication tokens using a refresh token
   *
   * @param data - Token refresh data
   * @returns Promise with new authentication tokens
   */
  refreshToken(data: TokenRefreshDto): Promise<TokenResponseDto>;

  /**
   * Logout a user (invalidate their tokens)
   *
   * @param userId - ID of the user to logout
   * @param refreshToken - Current refresh token to invalidate
   * @returns Promise indicating success
   */
  logout(userId: string, refreshToken: string): Promise<void>;

  /**
   * Resend email verification
   *
   * @param userId - ID of the user to send verification email to
   * @returns Promise indicating the verification email was sent
   */
  resendVerificationEmail(userId: string): Promise<void>;

  /**
   * Resend email verification by email (public endpoint)
   *
   * @param email - Email address to send verification email to
   * @returns Promise indicating the verification email was sent
   */
  resendVerificationEmailByEmail(email: string): Promise<void>;

  /**
   * Validate a JWT token
   *
   * @param token - JWT token to validate
   * @returns Promise with the decoded token payload or null if invalid
   */
  validateToken(token: string): Promise<any | null>;

  /**
   * Generate authentication tokens for a user
   *
   * @param user - The user to generate tokens for
   * @returns Promise with token response
   */
  generateTokensForUser(user: User): Promise<TokenResponseDto>;
}
