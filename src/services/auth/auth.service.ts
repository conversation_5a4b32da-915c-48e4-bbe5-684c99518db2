/**
 * Authentication Service Implementation
 *
 * This service handles user authentication, token management, and security operations
 * including password validation, JWT token generation, and email verification.
 */

import jwt from 'jsonwebtoken';
import crypto from 'crypto';
import { User, Role, VerificationTokenType } from '@prisma/client';
import {
  UserLoginDto,
  PasswordResetRequestDto,
  PasswordResetDto,
  TokenRefreshDto,
  AuthResponseDto,
  TokenResponseDto,
  EmailVerificationDto,
  RegistrationResponseDto
} from '../../dtos/auth.dto.js';
import { UserRegistrationDto } from '../../dtos/user.dto.js';
import { AuthService } from './auth-service.interface.js';
import { PrismaClient } from '@prisma/client';
import { EmailService } from '../email/email-service.interface.js';
import { VerificationTokenService } from '../verification/verification-token.service.js';


/**
 * Authentication Service Implementation
 */
export class AuthServiceImpl implements AuthService {
  #prisma: PrismaClient;
  private readonly tokenSecret: string;
  private readonly refreshTokens: Map<string, { userId: string, expiresAt: number }>;
  private readonly emailService: EmailService;
  private readonly verificationTokenService: VerificationTokenService;

  /**
   * Create a new auth service
   *
   * @param prisma - Prisma client
   * @param emailService - Email service for sending verification emails
   */
  constructor(prisma: PrismaClient, emailService: EmailService) {
    this.#prisma = prisma;
    this.emailService = emailService;
    this.verificationTokenService = new VerificationTokenService(prisma);
    this.tokenSecret = process.env['JWT_SECRET'] || 'development-secret-key';
    this.refreshTokens = new Map();
  }

  /**
   * Register a new user
   *
   * @param userData - User registration data
   * @returns Promise with registration response containing user (no tokens - user must verify email first)
   */
  async register(userData: UserRegistrationDto): Promise<RegistrationResponseDto> {
    console.log('Using Prisma instance:', this.#prisma ? 'initialized' : 'not initialized');

    try {
      // Check if user with email already exists
      const existingUser = await this.#prisma.user.findUnique({
        where: { email: userData.email }
      });

      if (existingUser) {
        throw new Error('User with this email already exists');
      }

      // Create new user in database with email verification disabled by default
      const newUser = await this.#prisma.user.create({
        data: {
          email: userData.email,
          password: crypto.createHash('sha256').update(userData.password).digest('hex'), // Simple hash for demo
          firstName: userData.firstName,
          lastName: userData.lastName,
          profileImage: userData.profileImage || null, // Convert undefined to null for Prisma
          role: Role.USER,
          isVerified: false // User must verify email
        }
      });

      // Create profile with address fields
      await this.#prisma.profile.create({
        data: {
          userId: newUser.id,
          address: userData.addressLine1,
          city: userData.city,
          state: userData.state,
          zipCode: userData.postalCode,
          country: userData.country
        }
      });

      // Generate verification token and send email
      try {
        const verificationToken = await this.verificationTokenService.generateToken(
          newUser.id,
          VerificationTokenType.EMAIL_VERIFICATION,
          24 // 24 hours expiration
        );

        // Create verification link pointing to frontend
        const frontendUrl = process.env['FRONTEND_URL'] || 'http://localhost:4200';
        const verificationLink = `${frontendUrl}/auth/verify-email?token=${verificationToken}`;

        // Send verification email
        await this.emailService.sendVerificationEmail(
          newUser.email,
          newUser.firstName,
          verificationLink
        );

        console.log(`Verification email sent to: ${newUser.email}`);
      } catch (emailError) {
        console.error('Error sending verification email:', emailError);
        // Don't fail registration if email sending fails
        // User can request resend later
      }

      // Return user data WITHOUT tokens (user must verify email first)
      return {
        user: {
          id: newUser.id,
          email: newUser.email,
          firstName: newUser.firstName,
          lastName: newUser.lastName,
          role: newUser.role,
          isVerified: newUser.isVerified,
          createdAt: newUser.createdAt,
          updatedAt: newUser.updatedAt
        },
        message: 'Registration successful! Please check your email to verify your account before logging in.'
      };
    } catch (error) {
      console.error('Error registering user:', error);
      throw error;
    }
  }

  /**
   * Login a user with email and password
   *
   * @param credentials - Login credentials
   * @returns Promise with authentication response containing user and tokens
   */
  async login(credentials: UserLoginDto): Promise<AuthResponseDto> {
    console.log('Using Prisma instance:', this.#prisma ? 'initialized' : 'not initialized');

    try {
      // Find user by email
      const user = await this.#prisma.user.findUnique({
        where: { email: credentials.email }
      });

      // If user not found or password doesn't match, throw error
      if (!user) {
        throw new Error('Invalid email or password');
      }

      // Simple password check - in production, use proper password hashing comparison
      const hashedPassword = crypto.createHash('sha256').update(credentials.password).digest('hex');
      if (user.password !== hashedPassword) {
        throw new Error('Invalid email or password');
      }

      // Generate tokens for the user
      const tokens = await this.generateTokensForUser(user);

      // Return user data and tokens
      return {
        user: {
          id: user.id,
          email: user.email,
          firstName: user.firstName,
          lastName: user.lastName,
          role: user.role,
          isVerified: user.isVerified,
          createdAt: user.createdAt,
          updatedAt: user.updatedAt
        },
        accessToken: tokens.accessToken,
        refreshToken: tokens.refreshToken,
        expiresIn: tokens.expiresIn
      };
    } catch (error) {
      console.error('Error during login:', error);
      throw error;
    }
  }

  /**
   * Request a password reset
   *
   * @param data - Password reset request data with email
   * @returns Promise indicating the reset email was sent
   */
  async requestPasswordReset(data: PasswordResetRequestDto): Promise<void> {
    const { email } = data;

    try {
      // Check if user exists
      const user = await this.#prisma.user.findUnique({
        where: { email }
      });

      if (!user) {
        // Don't reveal if email exists or not (security best practice)
        console.log(`Password reset requested for non-existent email: ${email}`);
        return;
      }

      // Generate password reset token
      const resetToken = await this.verificationTokenService.generateToken(
        user.id,
        VerificationTokenType.PASSWORD_RESET,
        1 // 1 hour expiration
      );

      // Create reset link pointing to frontend
      const frontendUrl = process.env['FRONTEND_URL'] || 'http://localhost:4200';
      const resetLink = `${frontendUrl}/auth/reset-password?token=${resetToken}`;

      // Send password reset email
      try {
        await this.emailService.sendPasswordResetEmail(
          user.email,
          user.firstName,
          resetLink
        );

        console.log(`Password reset email sent to: ${email}`);
      } catch (emailError) {
        console.error('Error sending password reset email:', emailError);
        // Don't expose email sending errors to client for security
      }
    } catch (error) {
      console.error('Error requesting password reset:', error);
      // Don't expose error details to client for security
    }
  }

  /**
   * Confirm a password reset with token
   *
   * @param data - Password reset confirmation data
   * @returns Promise indicating success
   */
  async confirmPasswordReset(data: PasswordResetDto): Promise<void> {
    const { token, newPassword } = data;

    try {
      // Validate and consume the password reset token
      const userId = await this.verificationTokenService.validateAndConsumeToken(
        token,
        VerificationTokenType.PASSWORD_RESET
      );

      if (!userId) {
        throw new Error('Invalid or expired password reset token');
      }

      // Hash the new password
      const hashedPassword = crypto.createHash('sha256').update(newPassword).digest('hex');

      // Update user's password
      await this.#prisma.user.update({
        where: { id: userId },
        data: { password: hashedPassword }
      });

      console.log(`Password reset confirmed for user: ${userId}`);
    } catch (error) {
      console.error('Error confirming password reset:', error);
      throw new Error('Invalid or expired password reset token');
    }
  }

  /**
   * Verify a user's email address
   *
   * @param data - Email verification data with token
   * @returns Promise with verification result
   */
  async verifyEmail(data: EmailVerificationDto): Promise<{ email: string; isVerified: boolean }> {
    const { token } = data;

    try {
      // Validate and consume the verification token
      const userId = await this.verificationTokenService.validateAndConsumeToken(
        token,
        VerificationTokenType.EMAIL_VERIFICATION
      );

      if (!userId) {
        throw new Error('Invalid or expired verification token');
      }

      // Check if user exists and get current verification status
      const existingUser = await this.#prisma.user.findUnique({
        where: { id: userId }
      });

      if (!existingUser) {
        throw new Error('User not found for verification token');
      }

      if (existingUser.isVerified) {
        throw new Error('Email address is already verified');
      }

      // Update user's verified status
      const user = await this.#prisma.user.update({
        where: { id: userId },
        data: { isVerified: true }
      });

      console.log(`Email verified for user: ${user.email}`);

      return {
        email: user.email,
        isVerified: true
      };
    } catch (error) {
      console.error('Error verifying email:', error);

      // Re-throw with more specific error messages
      if (error instanceof Error) {
        if (error.message.includes('already verified')) {
          throw new Error('Email address is already verified');
        } else if (error.message.includes('not found')) {
          throw new Error('User not found for verification token');
        } else if (error.message.includes('Invalid or expired')) {
          throw new Error('Verification token has expired or is invalid');
        }
      }

      throw new Error('Invalid or expired verification token');
    }
  }

  /**
   * Refresh authentication tokens using a refresh token
   *
   * @param data - Token refresh data
   * @returns Promise with new authentication tokens
   */
  async refreshToken(data: TokenRefreshDto): Promise<TokenResponseDto> {
    const { refreshToken } = data;

    // Verify refresh token exists in our stored tokens
    const storedToken = this.refreshTokens.get(refreshToken);
    if (!storedToken) {
      throw new Error('Invalid refresh token');
    }

    // Check if token is expired
    if (storedToken.expiresAt < Date.now()) {
      this.refreshTokens.delete(refreshToken);
      throw new Error('Refresh token expired');
    }

    try {
      // Get user from database
      const user = await this.#prisma.user.findUnique({
        where: { id: storedToken.userId }
      });

      if (!user) {
        throw new Error('User not found');
      }

      // Invalidate the old refresh token
      this.refreshTokens.delete(refreshToken);

      // Generate new tokens
      return this.generateTokensForUser(user);
    } catch (error) {
      console.error('Error refreshing token:', error);
      throw error;
    }
  }

  /**
   * Logout a user (invalidate their tokens)
   *
   * @param userId - ID of the user to logout
   * @param refreshToken - Current refresh token to invalidate
   * @returns Promise indicating success
   */
  async logout(_userId: string, refreshToken: string): Promise<void> {
    // In a real implementation, we would invalidate the refresh token

    this.refreshTokens.delete(refreshToken);
    return Promise.resolve();
  }

  /**
   * Resend email verification
   *
   * @param userId - ID of the user to send verification email to
   * @returns Promise indicating the verification email was sent
   */
  async resendVerificationEmail(userId: string): Promise<void> {
    try {
      // Check if user exists
      const user = await this.#prisma.user.findUnique({
        where: { id: userId }
      });

      if (!user) {
        throw new Error('User not found');
      }

      if (user.isVerified) {
        throw new Error('User is already verified');
      }

      // Generate new verification token
      const verificationToken = await this.verificationTokenService.generateToken(
        user.id,
        VerificationTokenType.EMAIL_VERIFICATION,
        24 // 24 hours expiration
      );

      // Create verification link pointing to frontend
      const frontendUrl = process.env['FRONTEND_URL'] || 'http://localhost:4200';
      const verificationLink = `${frontendUrl}/auth/verify-email?token=${verificationToken}`;

      // Send verification email
      await this.emailService.sendVerificationEmail(
        user.email,
        user.firstName,
        verificationLink
      );

      console.log(`Verification email resent to: ${user.email}`);
    } catch (error) {
      console.error('Error resending verification email:', error);
      throw error;
    }
  }

  /**
   * Resend email verification by email (public endpoint)
   *
   * @param email - Email address to send verification email to
   * @returns Promise indicating the verification email was sent
   */
  async resendVerificationEmailByEmail(email: string): Promise<void> {
    try {
      // Check if user exists
      const user = await this.#prisma.user.findUnique({
        where: { email }
      });

      if (!user) {
        // Don't reveal if email exists or not (security best practice)
        console.log(`Verification email requested for non-existent email: ${email}`);
        return;
      }

      if (user.isVerified) {
        console.log(`Verification email requested for already verified user: ${email}`);
        return;
      }

      // Generate new verification token
      const verificationToken = await this.verificationTokenService.generateToken(
        user.id,
        VerificationTokenType.EMAIL_VERIFICATION,
        24 // 24 hours expiration
      );

      // Create verification link pointing to frontend
      const frontendUrl = process.env['FRONTEND_URL'] || 'http://localhost:4200';
      const verificationLink = `${frontendUrl}/auth/verify-email?token=${verificationToken}`;

      // Send verification email
      await this.emailService.sendVerificationEmail(
        user.email,
        user.firstName,
        verificationLink
      );

      console.log(`Verification email resent to: ${user.email}`);
    } catch (error) {
      console.error('Error resending verification email by email:', error);
      // Don't expose error details to client for security
    }
  }

  /**
   * Validate a JWT token
   *
   * @param token - JWT token to validate
   * @returns Promise with the decoded token payload or null if invalid
   */
  async validateToken(token: string): Promise<any | null> {
    try {
      return jwt.verify(token, this.tokenSecret);
    } catch (error) {
      return null;
    }
  }

  /**
   * Generate authentication tokens for a user
   *
   * @param user - The user to generate tokens for
   * @returns Promise with token response
   */
  async generateTokensForUser(user: User): Promise<TokenResponseDto> {
    // Set token expiration time (e.g., 1 hour for access token)
    const expiresIn = 3600; // seconds

    // Create the JWT payload
    const payload = {
      userId: user.id,
      email: user.email,
      role: user.role
    };

    // Generate the access token
    const accessToken = jwt.sign(payload, this.tokenSecret, { expiresIn });

    // Generate refresh token (longer lived)
    const refreshToken = this.generateRefreshToken(user.id);

    // Return the tokens
    return {
      accessToken,
      refreshToken,
      expiresIn
    };
  }

  /**
   * Generate a refresh token for a user
   *
   * @param userId - ID of the user
   * @returns Refresh token
   */
  private generateRefreshToken(userId: string): string {
    const token = crypto.randomBytes(40).toString('hex');

    // Store refresh token with expiration (e.g., 7 days)
    const expiresAt = Date.now() + 7 * 24 * 60 * 60 * 1000;
    this.refreshTokens.set(token, { userId, expiresAt });

    return token;
  }
}
