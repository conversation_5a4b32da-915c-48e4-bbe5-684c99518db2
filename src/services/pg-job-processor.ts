/**
 * PostgreSQL-based Job Processor
 *
 * This file provides a simple job processing system that uses PostgreSQL
 * instead of Redis/Bull for job queue management.
 */

import { PrismaClient, Prisma } from '@prisma/client';
import logger from '../utils/logger.js';

// Initialize Prisma client with connection from environment variable
const prisma = new PrismaClient({
  datasources: {
    db: {
      url: process.env['DATABASE_URL'] || 'postgresql://postgres@localhost:5432/winnerssociety?connect_timeout=10&sslmode=prefer&schema=public'
    }
  }
});

// Type definitions for our custom fields
type EntryWithCustomFields = Prisma.EntryUncheckedCreateInput & {
  source?: string;
  entryMethod?: string;
};

type EntryUpdateWithCustomFields = Prisma.EntryUncheckedUpdateInput & {
  source?: string;
};

// Helper function to create a giveaway entry
async function createEntry(data: EntryWithCustomFields) {
  // Extract custom fields
  const { source, entryMethod, ...standardFields } = data;

  // Create the entry with standard fields
  const entry = await prisma.entry.create({
    data: standardFields
  });

  return entry;
}

// Helper function to update a giveaway entry
async function updateEntry(id: string, data: EntryUpdateWithCustomFields) {
  // Extract custom fields
  const { source, ...standardFields } = data;

  // Update the entry with standard fields
  const entry = await prisma.entry.update({
    where: { id },
    data: standardFields
  });

  return entry;
}

/**
 * Job types
 */
export enum JobType {
  PROCESS_GIVEAWAY_ACTIVATION = 'process-giveaway-activation',
  PROCESS_NEW_MEMBERSHIP = 'process-new-membership',
  PROCESS_MEMBERSHIP_UPGRADE = 'process-membership-upgrade',
  RETRY_FAILED_ENROLLMENTS = 'retry-failed-enrollments'
}

/**
 * Process a giveaway activation
 * Enrolls all eligible users in a newly activated giveaway
 *
 * @param giveawayId - ID of the giveaway
 */
export async function processGiveawayActivation(giveawayId: string): Promise<void> {
  try {
    logger.info(`Processing giveaway activation for giveaway ${giveawayId}`);

    // Get the giveaway
    const giveaway = await prisma.giveaway.findUnique({
      where: { id: giveawayId }
    });

    if (!giveaway) {
      throw new Error(`Giveaway with ID ${giveawayId} not found`);
    }

    if (!giveaway.autoEnrollment) {
      logger.info(`Skipping automatic enrollment for giveaway ${giveawayId} as it's disabled`);
      return;
    }

    // Get eligible membership tiers
    const eligibleTierIds = giveaway.eligibleMembershipTierIds as string[];

    if (!eligibleTierIds.length) {
      logger.info(`No eligible membership tiers defined for giveaway ${giveawayId}`);
      return;
    }

    // Find all eligible users with active memberships in the eligible tiers
    const eligibleUsers = await prisma.user.findMany({
      where: {
        isVerified: true,
        isActive: true,
        memberships: {
          some: {
            status: 'ACTIVE',
            membershipTierId: {
              in: eligibleTierIds
            }
          }
        }
      },
      include: {
        memberships: {
          where: {
            status: 'ACTIVE',
            membershipTierId: {
              in: eligibleTierIds
            }
          },
          include: {
            membershipTier: true
          }
        }
      }
    });

    logger.info(`Found ${eligibleUsers.length} eligible users for giveaway ${giveawayId}`);

    // Create an audit log entry for this batch enrollment
    let auditLogId = '';
    await prisma.$transaction(async (tx) => {
      const auditLog = await tx.enrollmentAuditLog.create({
        data: {
          giveawayId,
          userId: null, // No specific user for batch enrollment
          adminId: null, // No admin for automatic enrollment
          action: 'AUTO_ENROLLMENT',
          affectedUserCount: eligibleUsers.length,
          status: 'SUCCESS',
          details: {
            eligibleTierIds,
            processType: 'DIRECT_POSTGRESQL'
          }
        }
      });
      auditLogId = auditLog.id;
    });

    let successCount = 0;
    let failureCount = 0;

    // Process each eligible user
    for (const user of eligibleUsers) {
      try {
        // Skip users without active memberships
        if (!user.memberships.length) continue;

        // Get the user's highest tier membership
        const membership = user.memberships.reduce((highest, current) => {
          if (!highest) return current;
          const highestEntryAllocation = highest.membershipTier.entryAllocation;
          const currentEntryAllocation = current.membershipTier.entryAllocation;
          return currentEntryAllocation > highestEntryAllocation ? current : highest;
        }, null as any);

        if (!membership) continue;

        // Check if user already has an entry for this giveaway
        const existingEntry = await prisma.entry.findUnique({
          where: {
            userId_giveawayId: {
              userId: user.id,
              giveawayId
            }
          }
        });

        if (existingEntry) {
          // If entry exists, update it if the new tier has more entries
          if (membership.membershipTier.entryAllocation > existingEntry.quantity) {
            await updateEntry(existingEntry.id, {
              quantity: membership.membershipTier.entryAllocation,
              source: 'MEMBERSHIP_UPDATED',
              membershipId: membership.id,
              membershipTierId: membership.membershipTierId,
              lastUpdatedDate: new Date()
            });

            logger.debug(`Updated entry for user ${user.id} in giveaway ${giveawayId}`);
          }
        } else {
          // Create a new entry
          await createEntry({
            userId: user.id,
            giveawayId,
            membershipId: membership.id,
            membershipTierId: membership.membershipTierId,
            quantity: membership.membershipTier.entryAllocation,
            source: 'MEMBERSHIP',
            entryMethod: 'MEMBERSHIP'
          });

          logger.debug(`Created entry for user ${user.id} in giveaway ${giveawayId}`);
        }

        successCount++;
      } catch (error) {
        failureCount++;

        // Log the error
        logger.error(`Failed to enroll user ${user.id} in giveaway ${giveawayId}:`, error);

        // Create a failed enrollment record
        await prisma.$transaction(async (tx) => {
          await tx.failedEnrollment.create({
            data: {
              giveawayId,
              userId: user.id,
              membershipTierId: user.memberships[0]?.membershipTierId || null,
              jobId: null, // Set to null for direct processing
              errorMessage: error instanceof Error ? error.message : 'Unknown error',
              errorCode: error instanceof Error ? error.name : 'UNKNOWN',
              status: 'PENDING'
            }
          });
        });
      }
    }

    // Update the audit log with final counts
    if (auditLogId) {
      await prisma.$transaction(async (tx) => {
        await tx.enrollmentAuditLog.update({
          where: { id: auditLogId },
          data: {
            status: failureCount > 0 ? 'PARTIAL_SUCCESS' : 'SUCCESS',
            details: {
              successCount,
              failureCount,
              totalProcessed: successCount + failureCount
            }
          }
        });
      });
    }

    logger.info(`Completed giveaway activation for giveaway ${giveawayId}`);
    logger.info(`Successfully enrolled ${successCount} users, failed to enroll ${failureCount} users`);
  } catch (error) {
    logger.error(`Error processing giveaway activation:`, error);

    // Create an audit log entry for the failed job
    await prisma.$transaction(async (tx) => {
      await tx.enrollmentAuditLog.create({
        data: {
          giveawayId,
          userId: null,
          adminId: null,
          action: 'AUTO_ENROLLMENT',
          affectedUserCount: 0,
          status: 'FAILURE',
          details: {
            error: error instanceof Error ? error.message : 'Unknown error'
          }
        }
      });
    });

    throw error;
  }
}

/**
 * Process a new membership
 * Enrolls a new member in all eligible active giveaways
 *
 * @param userId - ID of the user
 * @param membershipId - ID of the membership
 * @param membershipTierId - ID of the membership tier
 */
export async function processNewMembership(
  userId: string,
  membershipId: string,
  membershipTierId: string
): Promise<void> {
  try {
    logger.info(`Processing new membership for user ${userId}`);

    // Get the user
    const user = await prisma.user.findUnique({
      where: { id: userId },
      include: {
        memberships: {
          where: {
            id: membershipId,
            status: 'ACTIVE'
          }
        }
      }
    });

    if (!user) {
      throw new Error(`User with ID ${userId} not found`);
    }

    if (!user.isVerified) {
      logger.info(`Skipping enrollment for user ${userId} as email is not verified`);
      return;
    }

    if (!user.memberships.length) {
      throw new Error(`Active membership with ID ${membershipId} not found for user ${userId}`);
    }

    // Get the membership tier
    const membershipTier = await prisma.membershipTier.findUnique({
      where: { id: membershipTierId }
    });

    if (!membershipTier) {
      throw new Error(`Membership tier with ID ${membershipTierId} not found`);
    }

    // Find all active giveaways with auto-enrollment enabled
    const activeGiveaways = await prisma.giveaway.findMany({
      where: {
        status: 'ACTIVE',
        startDate: { lte: new Date() },
        endDate: { gte: new Date() },
        eligibleMembershipTierIds: {
          has: membershipTierId
        }
      }
    });

    // Filter giveaways with auto-enrollment enabled
    const autoEnrollGiveaways = activeGiveaways.filter(giveaway => giveaway.autoEnrollment === true);

    logger.info(`Found ${autoEnrollGiveaways.length} eligible giveaways for user ${userId}`);

    // Create an audit log entry
    let auditLogId = '';
    await prisma.$transaction(async (tx) => {
      const auditLog = await tx.enrollmentAuditLog.create({
        data: {
          userId,
          giveawayId: autoEnrollGiveaways[0]?.id || '00000000-0000-0000-0000-000000000000', // Use first giveaway or placeholder
          adminId: null,
          action: 'AUTO_ENROLLMENT',
          affectedUserCount: 1,
          status: 'SUCCESS',
          details: {
            membershipId,
            membershipTierId,
            processType: 'DIRECT_POSTGRESQL'
          }
        }
      });
      auditLogId = auditLog.id;
    });

    let successCount = 0;
    let failureCount = 0;

    // Process each giveaway
    for (const giveaway of autoEnrollGiveaways) {
      try {
        // Check if user already has an entry for this giveaway
        const existingEntry = await prisma.entry.findUnique({
          where: {
            userId_giveawayId: {
              userId,
              giveawayId: giveaway.id
            }
          }
        });

        if (existingEntry) {
          // If entry exists, update it if the new tier has more entries
          if (membershipTier.entryAllocation > existingEntry.quantity) {
            await updateEntry(existingEntry.id, {
              quantity: membershipTier.entryAllocation,
              source: 'MEMBERSHIP_UPDATED',
              membershipId,
              membershipTierId,
              lastUpdatedDate: new Date()
            });

            logger.debug(`Updated entry for user ${userId} in giveaway ${giveaway.id}`);
          }
        } else {
          // Create a new entry
          await createEntry({
            userId,
            giveawayId: giveaway.id,
            membershipId,
            membershipTierId,
            quantity: membershipTier.entryAllocation,
            source: 'MEMBERSHIP',
            entryMethod: 'MEMBERSHIP'
          });

          logger.debug(`Created entry for user ${userId} in giveaway ${giveaway.id}`);
        }

        successCount++;
      } catch (error) {
        failureCount++;

        // Log the error
        logger.error(`Failed to enroll user ${userId} in giveaway ${giveaway.id}:`, error);

        // Create a failed enrollment record
        await prisma.$transaction(async (tx) => {
          await tx.failedEnrollment.create({
            data: {
              giveawayId: giveaway.id,
              userId,
              membershipTierId,
              jobId: null, // Set to null for direct processing
              errorMessage: error instanceof Error ? error.message : 'Unknown error',
              errorCode: error instanceof Error ? error.name : 'UNKNOWN',
              status: 'PENDING'
            }
          });
        });
      }
    }

    // Update the audit log with final counts
    if (auditLogId) {
      await prisma.$transaction(async (tx) => {
        await tx.enrollmentAuditLog.update({
          where: { id: auditLogId },
          data: {
            status: failureCount > 0 ? 'PARTIAL_SUCCESS' : 'SUCCESS',
            details: {
              successCount,
              failureCount,
              totalProcessed: successCount + failureCount
            }
          }
        });
      });
    }

    logger.info(`Completed new membership processing for user ${userId}`);
    logger.info(`Successfully enrolled in ${successCount} giveaways, failed to enroll in ${failureCount} giveaways`);
  } catch (error) {
    logger.error(`Error processing new membership:`, error);
    throw error;
  }
}

/**
 * Process a membership upgrade
 * Updates entries for a user who upgraded their membership
 *
 * @param userId - ID of the user
 * @param membershipId - ID of the membership
 * @param membershipTierId - ID of the new membership tier
 * @param previousTierId - ID of the previous membership tier
 */
/**
 * Process a job based on its type
 *
 * @param jobType - Type of job to process
 * @param data - Job data
 */
export async function processJob(jobType: string, data: any): Promise<void> {
  logger.info(`Processing job of type ${jobType}`);

  switch (jobType) {
    case JobType.PROCESS_GIVEAWAY_ACTIVATION:
      if (!data.giveawayId) {
        throw new Error('giveawayId is required for giveaway activation');
      }
      await processGiveawayActivation(data.giveawayId);
      break;

    case JobType.PROCESS_NEW_MEMBERSHIP:
      if (!data.userId || !data.membershipId || !data.membershipTierId) {
        throw new Error('userId, membershipId, and membershipTierId are required for new membership');
      }
      await processNewMembership(data.userId, data.membershipId, data.membershipTierId);
      break;

    case JobType.PROCESS_MEMBERSHIP_UPGRADE:
      if (!data.userId || !data.membershipId || !data.membershipTierId || !data.metadata?.previousTierId) {
        throw new Error('userId, membershipId, membershipTierId, and previousTierId are required for membership upgrade');
      }
      await processMembershipUpgrade(
        data.userId,
        data.membershipId,
        data.membershipTierId,
        data.metadata.previousTierId
      );
      break;

    case JobType.RETRY_FAILED_ENROLLMENTS:
      if (!data.failedEnrollmentIds || !Array.isArray(data.failedEnrollmentIds)) {
        throw new Error('failedEnrollmentIds array is required for retry failed enrollments');
      }
      // This would need to be implemented
      logger.warn('Retry failed enrollments not implemented in direct PostgreSQL processor');
      break;

    default:
      throw new Error(`Unknown job type: ${jobType}`);
  }

  logger.info(`Completed job of type ${jobType}`);
}

export async function processMembershipUpgrade(
  userId: string,
  membershipId: string,
  membershipTierId: string,
  previousTierId: string
): Promise<void> {
  try {
    logger.info(`Processing membership upgrade for user ${userId}`);

    // Get the user
    const user = await prisma.user.findUnique({
      where: { id: userId }
    });

    if (!user) {
      throw new Error(`User with ID ${userId} not found`);
    }

    // Get the membership tiers
    const [previousTier, newTier] = await Promise.all([
      prisma.membershipTier.findUnique({ where: { id: previousTierId } }),
      prisma.membershipTier.findUnique({ where: { id: membershipTierId } })
    ]);

    if (!previousTier) {
      throw new Error(`Previous membership tier with ID ${previousTierId} not found`);
    }

    if (!newTier) {
      throw new Error(`New membership tier with ID ${membershipTierId} not found`);
    }

    // Only proceed if the new tier has more entries than the previous tier
    if (newTier.entryAllocation <= previousTier.entryAllocation) {
      logger.info(`Skipping upgrade for user ${userId} as new tier doesn't provide more entries`);
      return;
    }

    // Find all active giveaways where the user has entries
    const userEntries = await prisma.entry.findMany({
      where: {
        userId,
        giveaway: {
          status: 'ACTIVE',
          endDate: { gte: new Date() }
        }
      },
      include: {
        giveaway: true
      }
    });

    logger.info(`Found ${userEntries.length} active entries for user ${userId}`);

    // Create an audit log entry
    const auditLog = await prisma.enrollmentAuditLog.create({
      data: {
        userId,
        giveawayId: userEntries[0]?.giveawayId || '00000000-0000-0000-0000-000000000000', // Use first giveaway or placeholder
        action: 'MEMBERSHIP_UPGRADE',
        affectedUserCount: 1,
        status: 'SUCCESS',
        details: {
          membershipId,
          membershipTierId,
          previousTierId,
          previousEntryAllocation: previousTier.entryAllocation,
          newEntryAllocation: newTier.entryAllocation
        }
      }
    });

    let successCount = 0;
    let failureCount = 0;

    // Process each entry
    for (const entry of userEntries) {
      try {
        // Check if the giveaway allows this tier
        const giveaway = entry.giveaway;
        const eligibleTierIds = giveaway.eligibleMembershipTierIds as string[];

        if (!eligibleTierIds.includes(membershipTierId)) {
          logger.debug(`Skipping giveaway ${giveaway.id} as new tier is not eligible`);
          continue;
        }

        // Update the entry with the new tier's allocation
        await prisma.entry.update({
          where: { id: entry.id },
          data: {
            quantity: newTier.entryAllocation,
            source: 'MEMBERSHIP_UPGRADED',
            membershipId,
            membershipTierId,
            lastUpdatedDate: new Date()
          }
        });

        logger.debug(`Updated entry for user ${userId} in giveaway ${giveaway.id}`);
        successCount++;
      } catch (error) {
        failureCount++;

        // Log the error
        logger.error(`Failed to update entry for user ${userId} in giveaway ${entry.giveawayId}:`, error);

        // Create a failed enrollment record
        await prisma.failedEnrollment.create({
          data: {
            giveawayId: entry.giveawayId,
            userId,
            membershipTierId,
            errorMessage: error instanceof Error ? error.message : 'Unknown error',
            errorCode: error instanceof Error ? error.name : 'UNKNOWN',
            status: 'PENDING'
          }
        });
      }
    }

    // Update the audit log with final counts
    await prisma.enrollmentAuditLog.update({
      where: { id: auditLog.id },
      data: {
        status: failureCount > 0 ? 'PARTIAL_SUCCESS' : 'SUCCESS',
        details: {
          successCount,
          failureCount,
          totalProcessed: successCount + failureCount
        }
      }
    });

    logger.info(`Completed membership upgrade for user ${userId}`);
    logger.info(`Successfully updated ${successCount} entries, failed to update ${failureCount} entries`);
  } catch (error) {
    logger.error(`Error processing membership upgrade:`, error);
    throw error;
  }
}