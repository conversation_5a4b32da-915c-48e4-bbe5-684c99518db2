/**
 * Verification Token Service
 * 
 * This service handles creation, validation, and management of verification tokens
 * for email verification and password reset functionality.
 */

import crypto from 'crypto';
import { PrismaClient, VerificationTokenType } from '@prisma/client';

/**
 * Verification token service implementation
 */
export class VerificationTokenService {
  private readonly prisma: PrismaClient;

  constructor(prisma: PrismaClient) {
    this.prisma = prisma;
  }

  /**
   * Generate a secure verification token
   * 
   * @param userId - User ID to associate with the token
   * @param type - Type of verification token
   * @param expirationHours - Hours until token expires (default: 24 for email, 1 for password reset)
   * @returns Promise with the generated token string
   */
  async generateToken(
    userId: string,
    type: VerificationTokenType,
    expirationHours?: number
  ): Promise<string> {
    // Set default expiration based on token type
    const defaultHours = type === VerificationTokenType.EMAIL_VERIFICATION ? 24 : 1;
    const hours = expirationHours || defaultHours;

    // Generate a secure random token
    const token = crypto.randomBytes(32).toString('hex');

    // Calculate expiration date
    const expiresAt = new Date();
    expiresAt.setHours(expiresAt.getHours() + hours);

    // Invalidate any existing tokens of the same type for this user
    await this.invalidateExistingTokens(userId, type);

    // Create new token in database
    await this.prisma.verificationToken.create({
      data: {
        userId,
        token,
        type,
        expiresAt,
        isUsed: false
      }
    });

    return token;
  }

  /**
   * Validate and consume a verification token
   * 
   * @param token - Token string to validate
   * @param type - Expected token type
   * @returns Promise with user ID if valid, null if invalid
   */
  async validateAndConsumeToken(
    token: string,
    type: VerificationTokenType
  ): Promise<string | null> {
    try {
      // Find the token in database
      const verificationToken = await this.prisma.verificationToken.findFirst({
        where: {
          token,
          type,
          isUsed: false,
          expiresAt: {
            gt: new Date() // Token must not be expired
          }
        }
      });

      if (!verificationToken) {
        return null; // Token not found, expired, or already used
      }

      // Mark token as used
      await this.prisma.verificationToken.update({
        where: {
          id: verificationToken.id
        },
        data: {
          isUsed: true
        }
      });

      return verificationToken.userId;
    } catch (error) {
      console.error('Error validating verification token:', error);
      return null;
    }
  }

  /**
   * Check if a token exists and is valid (without consuming it)
   * 
   * @param token - Token string to check
   * @param type - Expected token type
   * @returns Promise with boolean indicating validity
   */
  async isTokenValid(
    token: string,
    type: VerificationTokenType
  ): Promise<boolean> {
    try {
      const verificationToken = await this.prisma.verificationToken.findFirst({
        where: {
          token,
          type,
          isUsed: false,
          expiresAt: {
            gt: new Date()
          }
        }
      });

      return !!verificationToken;
    } catch (error) {
      console.error('Error checking token validity:', error);
      return false;
    }
  }

  /**
   * Invalidate all existing tokens of a specific type for a user
   * 
   * @param userId - User ID
   * @param type - Token type to invalidate
   * @returns Promise indicating completion
   */
  async invalidateExistingTokens(
    userId: string,
    type: VerificationTokenType
  ): Promise<void> {
    try {
      await this.prisma.verificationToken.updateMany({
        where: {
          userId,
          type,
          isUsed: false
        },
        data: {
          isUsed: true
        }
      });
    } catch (error) {
      console.error('Error invalidating existing tokens:', error);
      throw error;
    }
  }

  /**
   * Clean up expired tokens from the database
   * 
   * @returns Promise with the number of tokens deleted
   */
  async cleanupExpiredTokens(): Promise<number> {
    try {
      const result = await this.prisma.verificationToken.deleteMany({
        where: {
          OR: [
            {
              expiresAt: {
                lt: new Date()
              }
            },
            {
              isUsed: true,
              createdAt: {
                lt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) // Delete used tokens older than 7 days
              }
            }
          ]
        }
      });

      console.log(`Cleaned up ${result.count} expired verification tokens`);
      return result.count;
    } catch (error) {
      console.error('Error cleaning up expired tokens:', error);
      throw error;
    }
  }

  /**
   * Get all active tokens for a user (for debugging/admin purposes)
   * 
   * @param userId - User ID
   * @returns Promise with array of active tokens
   */
  async getUserActiveTokens(userId: string): Promise<any[]> {
    try {
      return await this.prisma.verificationToken.findMany({
        where: {
          userId,
          isUsed: false,
          expiresAt: {
            gt: new Date()
          }
        },
        select: {
          id: true,
          type: true,
          expiresAt: true,
          createdAt: true
        }
      });
    } catch (error) {
      console.error('Error getting user active tokens:', error);
      return [];
    }
  }
}
