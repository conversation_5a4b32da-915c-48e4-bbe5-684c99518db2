/**
 * Membership Service Implementation
 *
 * This service handles membership subscription management, tier access, and related operations.
 * It implements the MembershipService interface and follows best practices for error handling and type safety.
 */

import {
  MembershipService,
  MembershipTierDto,
  MembershipSubscriptionDto,
  MembershipRenewalDto,
  MembershipUpgradeDto,
  MembershipHistoryEntryDto
} from './membership-service.interface.js';
import { UserWithMembershipResponseDto } from '../../dtos/user.dto.js';
import { FilterOptions, PaginatedResult, PaginationOptions } from '../../repositories/base/base-repository.interface.js';
import { MembershipRepository } from '../../repositories/membership/membership-repository.interface.js';
import { MembershipTierRepository } from '../../repositories/membership-tier/membership-tier-repository.interface.js';
import { UserRepository } from '../../repositories/user/user-repository.interface.js';
import { ValidationError, NotFoundError } from '../../utils/errors.js';
import { Membership, MembershipStatus } from '@prisma/client';
import { MembershipResponseDto } from '../../dtos/membership.dto.js';
import { getServiceFactory } from '../index.js';

// Define missing enums and types
enum MembershipAction {
  SUBSCRIBE = 'SUBSCRIBE',
  UPGRADE = 'UPGRADE',
  DOWNGRADE = 'DOWNGRADE',
  CANCEL = 'CANCEL',
  RENEW = 'RENEW'
}

// Custom error for business logic violations
class BusinessLogicError extends Error {
  constructor(message: string) {
    super(message);
    this.name = 'BusinessLogicError';
  }
}

// Mock Payment Service
interface PaymentServiceMock {
  processPayment(data: {
    userId: string;
    amount: number;
    description: string;
    paymentMethodId: string;
    metadata?: Record<string, any>;
  }): Promise<{ transactionId: string; success: boolean }>;
}

// Error handler utility
function handleDatabaseError(error: any, entityName: string): Error {
  console.error(`Database error with ${entityName}:`, error);
  if (error.code === 'P2002') {
    return new ValidationError(`${entityName} with this identifier already exists`);
  }
  if (error.code === 'P2025') {
    return new NotFoundError(`${entityName} not found`);
  }
  return error;
}

/**
 * Implementation of the Membership Service
 */
export class MembershipServiceImpl implements MembershipService {
  private paymentService: PaymentServiceMock;

  /**
   * Constructor for MembershipServiceImpl
   */
  constructor(
    private readonly membershipRepository: MembershipRepository,
    private readonly membershipTierRepository: MembershipTierRepository,
    private readonly userRepository: UserRepository
  ) {
    // Initialize mock payment service
    this.paymentService = {
      processPayment: async (data) => {
        console.log('Mock payment processed:', data);
        return {
          transactionId: `mock-transaction-${Date.now()}`,
          success: true
        };
      }
    };
  }

  /**
   * Get available membership tiers
   *
   * @param includeInactive - Whether to include inactive tiers
   * @returns Promise with array of available tiers
   */
  async getAvailableTiers(includeInactive = false): Promise<MembershipTierDto[]> {
    try {
      // We'll revert to the simplest approach that works
      // Get all tiers and filter manually
      const tiers = await this.membershipTierRepository.findMany();

      // Filter in memory to avoid query issues
      const filteredTiers = includeInactive
        ? tiers
        : tiers.filter(tier => tier.isActive === true);

      // Return with proper mapping
      return filteredTiers.map((tier: any) => ({
        id: tier.id,
        name: tier.name,
        description: tier.description,
        price: Number(tier.price),  // Convert Decimal to Number
        billingPeriod: tier.billingPeriod as string,
        features: Array.isArray(tier.features) ? tier.features :
                 typeof tier.features === 'object' ? Object.values(tier.features) : [],
        entryAllocation: tier.entryAllocation,
        duration: tier.duration,
        isActive: tier.isActive
      }));
    } catch (error) {
      console.error('Error fetching tiers:', error);
      throw handleDatabaseError(error, 'MembershipTier');
    }
  }

  /**
   * Get a membership tier by ID
   *
   * @param tierId - ID of the tier
   * @returns Promise with the tier details
   */
  async getTierById(tierId: string): Promise<MembershipTierDto | null> {
    if (!tierId) {
      throw new ValidationError('Tier ID is required');
    }

    try {
      const tier = await this.membershipTierRepository.findById(tierId);

      if (!tier) {
        return null;
      }

      return {
        id: tier.id,
        name: tier.name,
        description: tier.description,
        price: tier.price,
        billingPeriod: tier.billingPeriod,
        features: tier.features as string[],
        entryAllocation: tier.entryAllocation,
        duration: tier.duration,
        isActive: tier.isActive
      };
    } catch (error) {
      throw handleDatabaseError(error, 'MembershipTier');
    }
  }

  /**
   * Subscribe a user to a membership tier
   *
   * @param subscriptionData - Subscription data
   * @returns Promise with the subscription result
   */
  async subscribe(subscriptionData: MembershipSubscriptionDto): Promise<Membership> {
    // Validate required fields
    if (!subscriptionData.tierId || !subscriptionData.userId) {
      throw new ValidationError('User ID and tier ID are required');
    }

    try {
      // Check if user exists
      const user = await this.userRepository.findById(subscriptionData.userId);
      if (!user) {
        throw new NotFoundError('User not found');
      }

      // Check if tier exists and is active
      const tier = await this.membershipTierRepository.findById(subscriptionData.tierId);
      if (!tier) {
        throw new NotFoundError('Membership tier not found');
      }

      if (!tier.isActive) {
        throw new BusinessLogicError('This membership tier is currently unavailable');
      }

      // Check if user already has an active membership
      const existingMemberships = await this.membershipRepository.findActiveByUserId(subscriptionData.userId);
      // Only throw an error if there are actual memberships in the array
      if (existingMemberships && Array.isArray(existingMemberships) && existingMemberships.length > 0) {
        throw new BusinessLogicError('User already has an active membership');
      }

      // Process payment if required
      let transactionId: string | undefined;
      if (Number(tier.price) > 0) {
        if (!subscriptionData.paymentMethodId) {
          throw new ValidationError('Payment method is required for paid memberships');
        }

        // Apply coupon if provided
        let finalPrice = Number(tier.price);
        if (subscriptionData.couponCode) {
          const couponResult = await this.applyCoupon(
            subscriptionData.tierId,
            subscriptionData.couponCode
          );

          finalPrice = couponResult.finalPrice;
        }

        // Process payment with mock service
        const paymentResult = await this.paymentService.processPayment({
          userId: subscriptionData.userId,
          amount: Number(finalPrice),
          description: `Membership: ${tier.name}`,
          paymentMethodId: subscriptionData.paymentMethodId,
          metadata: {
            membershipTierId: tier.id,
            action: 'subscription'
          }
        });

        transactionId = paymentResult.transactionId;
      }

      // Create membership record
      const startDate = new Date();
      const endDate = this.calculateEndDate(startDate, tier.billingPeriod);

      const membership = await this.membershipRepository.create({
        userId: subscriptionData.userId,
        membershipTierId: subscriptionData.tierId, // Fixed property name
        startDate,
        endDate,
        autoRenew: subscriptionData.autoRenew ?? true, // Provide default value
        status: MembershipStatus.ACTIVE, // Using proper enum
        paymentMethodId: subscriptionData.paymentMethodId || null
      });

      // Create membership history entry
      const historyEntry: {
        userId: string;
        tierId: string;
        action: MembershipAction;
        fromDate: Date;
        toDate: Date;
        amount: number;
        transactionId?: string;
      } = {
        userId: subscriptionData.userId,
        tierId: subscriptionData.tierId,
        action: MembershipAction.SUBSCRIBE,
        fromDate: startDate,
        toDate: endDate,
        amount: Number(tier.price)
      };

      // Only add transactionId if defined
      if (transactionId) {
        historyEntry.transactionId = transactionId;
      }

      await this.createMembershipHistoryEntry(historyEntry);

      // Trigger automatic enrollment in eligible giveaways
      try {
        const enrollmentService = getServiceFactory().createEnrollmentService();
        await enrollmentService.handleNewMembership(
          subscriptionData.userId,
          membership.id,
          subscriptionData.tierId
        );
        console.log(`Triggered automatic enrollment for user ${subscriptionData.userId}`);
      } catch (enrollmentError) {
        // Log the error but don't fail the membership creation
        console.error('Error triggering automatic enrollment:', enrollmentError);
      }

      return membership;
    } catch (error) {
      if (error instanceof ValidationError ||
          error instanceof NotFoundError ||
          error instanceof BusinessLogicError) {
        throw error;
      }
      throw handleDatabaseError(error, 'Membership');
    }
  }

  /**
   * Cancel a user's membership
   *
   * @param userId - ID of the user
   * @param cancelImmediately - Whether to cancel immediately or at end of billing period
   * @returns Promise indicating success
   */
  async cancelMembership(userId: string, cancelImmediately = false): Promise<void> {
    if (!userId) {
      throw new ValidationError('User ID is required');
    }

    try {
      // Find active membership
      const memberships = await this.membershipRepository.findActiveByUserId(userId);
      // Check if the array has any elements
      if (!Array.isArray(memberships) || memberships.length === 0) {
        throw new NotFoundError('No active membership found for this user');
      }

      // Get the first membership from the array
      const activeMembership = memberships[0];

      if (!activeMembership) {
        throw new NotFoundError('No active membership details found');
      }

      if (cancelImmediately) {
        // Update membership to inactive
        await this.membershipRepository.update(activeMembership.id, {
          status: MembershipStatus.CANCELLED, // Using proper enum
          endDate: new Date(),
          autoRenew: false
        });
      } else {
        // Just turn off auto-renew
        await this.membershipRepository.update(activeMembership.id, {
          autoRenew: false
        });
      }

      // Create history entry
      const tier = await this.membershipTierRepository.findById(activeMembership.membershipTierId);
      if (!tier) {
        throw new NotFoundError('Membership tier not found');
      }

      await this.createMembershipHistoryEntry({
        userId,
        tierId: activeMembership.membershipTierId,
        action: MembershipAction.CANCEL,
        fromDate: activeMembership.startDate,
        toDate: cancelImmediately ? new Date() : activeMembership.endDate,
        amount: 0
      });
    } catch (error) {
      if (error instanceof ValidationError || error instanceof NotFoundError) {
        throw error;
      }
      throw handleDatabaseError(error, 'Membership');
    }
  }

  // Implementation of other required methods...

  /**
   * Helper method to calculate end date based on billing period
   */
  private calculateEndDate(startDate: Date, billingPeriod: string): Date {
    const endDate = new Date(startDate);

    switch (billingPeriod) {
      case 'MONTHLY':
        endDate.setMonth(endDate.getMonth() + 1);
        break;
      case 'QUARTERLY':
        endDate.setMonth(endDate.getMonth() + 3);
        break;
      case 'ANNUALLY':
        endDate.setFullYear(endDate.getFullYear() + 1);
        break;
      default:
        endDate.setMonth(endDate.getMonth() + 1); // Default to monthly
    }

    return endDate;
  }

  /**
   * Helper method to create membership history entry
   */
  private async createMembershipHistoryEntry(data: {
    userId: string;
    tierId: string;
    action: MembershipAction;
    fromDate: Date;
    toDate?: Date;
    amount: number;
    transactionId?: string;
  }): Promise<void> {
    try {
      const tier = await this.membershipTierRepository.findById(data.tierId);
      if (!tier) {
        throw new NotFoundError('Membership tier not found');
      }

      // Mock creating a history entry since the repository doesn't have this method
      console.log('Creating membership history entry:', {
        userId: data.userId,
        tierId: data.tierId,
        tierName: tier.name,
        action: data.action,
        fromDate: data.fromDate,
        toDate: data.toDate,
        amount: data.amount,
        transactionId: data.transactionId
      });

      // If repository had the method, we would use it like this:
      /*
      await this.membershipRepository.createHistoryEntry({
        userId: data.userId,
        tierId: data.tierId,
        tierName: tier.name,
        action: data.action,
        fromDate: data.fromDate,
        toDate: data.toDate,
        amount: data.amount,
        transactionId: data.transactionId
      });
      */
    } catch (error) {
      throw handleDatabaseError(error, 'MembershipHistory');
    }
  }

  // Implement remaining required methods from the interface...

  // These are placeholder implementations that should be completed with actual logic

  async get(id: string): Promise<any> {
    try {
      return await this.membershipRepository.findById(id);
    } catch (error) {
      throw handleDatabaseError(error, 'Membership');
    }
  }

  async list(filter?: FilterOptions, pagination?: PaginationOptions): Promise<any[]> {
    try {
      return await this.membershipRepository.findMany(filter, pagination);
    } catch (error) {
      throw handleDatabaseError(error, 'Membership');
    }
  }

  async listPaginated(filter?: FilterOptions, pagination?: PaginationOptions): Promise<PaginatedResult<any>> {
    try {
      return await this.membershipRepository.findManyPaginated(filter, pagination);
    } catch (error) {
      throw handleDatabaseError(error, 'Membership');
    }
  }

  async count(filter?: FilterOptions): Promise<number> {
    try {
      return await this.membershipRepository.count(filter);
    } catch (error) {
      throw handleDatabaseError(error, 'Membership');
    }
  }

  async create(data: MembershipSubscriptionDto): Promise<any> {
    return this.subscribe(data);
  }

  async createMany(dataArray: MembershipSubscriptionDto[]): Promise<any[]> {
    const results = [];
    for (const data of dataArray) {
      results.push(await this.subscribe(data));
    }
    return results;
  }

  async update(id: string, data: MembershipRenewalDto): Promise<any> {
    try {
      const membership = await this.membershipRepository.findById(id);
      if (!membership) {
        throw new NotFoundError('Membership not found');
      }

      return await this.membershipRepository.update(id, {
        autoRenew: data.autoRenew,
        // Add any other updatable fields here
      });
    } catch (error) {
      if (error instanceof NotFoundError) {
        throw error;
      }
      throw handleDatabaseError(error, 'Membership');
    }
  }

  async delete(id: string): Promise<any> {
    try {
      const membership = await this.membershipRepository.findById(id);
      if (!membership) {
        throw new NotFoundError('Membership not found');
      }

      return await this.membershipRepository.delete(id);
    } catch (error) {
      if (error instanceof NotFoundError) {
        throw error;
      }
      throw handleDatabaseError(error, 'Membership');
    }
  }

  async exists(id: string): Promise<boolean> {
    try {
      return await this.membershipRepository.exists(id);
    } catch (error) {
      throw handleDatabaseError(error, 'Membership');
    }
  }

  async updateAutoRenew(userId: string, renewalSettings: MembershipRenewalDto): Promise<MembershipRenewalDto> {
    if (!userId) {
      throw new ValidationError('User ID is required');
    }

    try {
      const memberships = await this.membershipRepository.findActiveByUserId(userId);
      // Check if the array has any elements
      if (!Array.isArray(memberships) || memberships.length === 0) {
        throw new NotFoundError('No active membership found for this user');
      }

      // Get the first membership from the array
      const activeMembership = memberships[0];

      if (!activeMembership) {
        throw new NotFoundError('No active membership details found');
      }

      await this.membershipRepository.update(activeMembership.id, {
        autoRenew: renewalSettings.autoRenew
      });

      return renewalSettings;
    } catch (error) {
      if (error instanceof ValidationError || error instanceof NotFoundError) {
        throw error;
      }
      throw handleDatabaseError(error, 'Membership');
    }
  }

  async upgradeMembership(upgradeData: MembershipUpgradeDto): Promise<any> {
    if (!upgradeData.userId || !upgradeData.currentTierId || !upgradeData.newTierId) {
      throw new ValidationError('User ID, current tier ID, and new tier ID are required');
    }

    try {
      // Check if user exists
      const user = await this.userRepository.findById(upgradeData.userId);
      if (!user) {
        throw new NotFoundError('User not found');
      }

      // Check if tiers exist and are active
      const [currentTier, newTier] = await Promise.all([
        this.membershipTierRepository.findById(upgradeData.currentTierId),
        this.membershipTierRepository.findById(upgradeData.newTierId)
      ]);

      if (!currentTier) {
        throw new NotFoundError('Current membership tier not found');
      }

      if (!newTier) {
        throw new NotFoundError('New membership tier not found');
      }

      if (!newTier.isActive) {
        throw new BusinessLogicError('The new membership tier is currently unavailable');
      }

      // Verify that the new tier is an upgrade (higher price)
      if (Number(newTier.price) <= Number(currentTier.price)) {
        throw new BusinessLogicError('New tier must have a higher price than current tier');
      }

      // Find user's active membership
      const memberships = await this.membershipRepository.findActiveByUserId(upgradeData.userId);
      if (!Array.isArray(memberships) || memberships.length === 0) {
        throw new NotFoundError('No active membership found for this user');
      }

      const activeMembership = memberships[0];
      if (!activeMembership) {
        throw new NotFoundError('No active membership details found');
      }

      // Verify that the current tier matches
      if (activeMembership.membershipTierId !== upgradeData.currentTierId) {
        throw new BusinessLogicError('Current tier does not match user\'s active membership');
      }

      // Calculate prorated amount if needed
      let upgradeAmount = Number(newTier.price);
      if (upgradeData.applyProration) {
        const remainingDays = this.calculateRemainingDays(activeMembership.endDate);
        const totalDays = this.calculateTotalDays(activeMembership.startDate, activeMembership.endDate);
        const remainingRatio = remainingDays / totalDays;

        const currentTierRemainingValue = Number(currentTier.price) * remainingRatio;
        upgradeAmount = Number(newTier.price) - currentTierRemainingValue;

        if (upgradeAmount < 0) upgradeAmount = 0;
      }

      // Process payment if required
      let transactionId: string | undefined;
      if (upgradeAmount > 0) {
        if (!upgradeData.paymentMethodId) {
          throw new ValidationError('Payment method is required for paid upgrades');
        }

        // Process payment with mock service
        const paymentResult = await this.paymentService.processPayment({
          userId: upgradeData.userId,
          amount: upgradeAmount,
          description: `Membership Upgrade: ${currentTier.name} to ${newTier.name}`,
          paymentMethodId: upgradeData.paymentMethodId,
          metadata: {
            previousTierId: currentTier.id,
            newTierId: newTier.id,
            action: 'upgrade'
          }
        });

        transactionId = paymentResult.transactionId;
      }

      // Update membership record
      const startDate = new Date(); // Start date for the new tier
      const endDate = this.calculateEndDate(startDate, newTier.billingPeriod);

      const updatedMembership = await this.membershipRepository.update(activeMembership.id, {
        membershipTierId: upgradeData.newTierId,
        startDate,
        endDate,
        // Keep other fields the same
        status: MembershipStatus.ACTIVE,
        autoRenew: activeMembership.autoRenew,
        paymentMethodId: upgradeData.paymentMethodId || activeMembership.paymentMethodId
      });

      // Create membership history entry
      const historyEntry: {
        userId: string;
        tierId: string;
        action: MembershipAction;
        fromDate: Date;
        toDate: Date;
        amount: number;
        transactionId?: string;
      } = {
        userId: upgradeData.userId,
        tierId: upgradeData.newTierId,
        action: MembershipAction.UPGRADE,
        fromDate: startDate,
        toDate: endDate,
        amount: upgradeAmount
      };

      if (transactionId) {
        historyEntry.transactionId = transactionId;
      }

      await this.createMembershipHistoryEntry(historyEntry);

      // Trigger automatic enrollment update in eligible giveaways
      try {
        const enrollmentService = getServiceFactory().createEnrollmentService();
        await enrollmentService.handleMembershipUpgrade(
          upgradeData.userId,
          updatedMembership.id,
          upgradeData.newTierId,
          upgradeData.currentTierId
        );
        console.log(`Triggered automatic enrollment update for user ${upgradeData.userId}`);
      } catch (enrollmentError) {
        // Log the error but don't fail the membership upgrade
        console.error('Error triggering automatic enrollment update:', enrollmentError);
      }

      return updatedMembership;
    } catch (error) {
      if (error instanceof ValidationError ||
          error instanceof NotFoundError ||
          error instanceof BusinessLogicError) {
        throw error;
      }
      throw handleDatabaseError(error, 'Membership');
    }
  }

  /**
   * Calculate the number of days remaining until the end date
   */
  private calculateRemainingDays(endDate: Date): number {
    const now = new Date();
    const end = new Date(endDate);
    const diffTime = end.getTime() - now.getTime();
    return Math.max(0, Math.ceil(diffTime / (1000 * 60 * 60 * 24)));
  }

  /**
   * Calculate the total number of days in a membership period
   */
  private calculateTotalDays(startDate: Date, endDate: Date): number {
    const start = new Date(startDate);
    const end = new Date(endDate);
    const diffTime = end.getTime() - start.getTime();
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  }

  async downgradeMembership(userId: string, newTierId: string, endOfPeriod?: boolean): Promise<any> {
    // Mock implementation
    console.log('Downgrade membership:', { userId, newTierId, endOfPeriod });
    return { success: true, message: 'Membership downgraded (mock)' };
  }

  async getMembershipHistory(_userId: string): Promise<MembershipHistoryEntryDto[]> {
    // Mock implementation
    return [
      {
        action: 'SUBSCRIBE',
        date: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
        tierId: 'tier-1',
        tierName: 'Basic',
        tierPrice: 9.99,
        transactionId: 'mock-transaction-123'
      }
    ];
  }

  async getUserWithMembership(userId: string): Promise<UserWithMembershipResponseDto> {
    try {
      const user = await this.userRepository.findById(userId);
      if (!user) {
        throw new NotFoundError('User not found');
      }

      // Get the user's active membership
      const activeMemberships = await this.membershipRepository.findActiveByUserId(userId);
      const activeMembership = activeMemberships && activeMemberships.length > 0 ? activeMemberships[0] : null;

      // If no active membership, return user without membership
      if (!activeMembership) {
        return {
          id: user.id,
          email: user.email,
          firstName: user.firstName,
          lastName: user.lastName,
          role: user.role,
          isVerified: user.isVerified,
          createdAt: user.createdAt,
          updatedAt: user.updatedAt
        };
      }

      // Get membership tier details
      const membershipWithTier = await this.membershipRepository.findWithTierDetails(activeMembership.id);

      // Get tier information separately if needed
      let tierInfo = undefined;
      if (membershipWithTier) {
        const tier = await this.membershipTierRepository.findById(membershipWithTier.membershipTierId);
        if (tier) {
          tierInfo = {
            id: tier.id,
            name: tier.name,
            description: tier.description,
            price: Number(tier.price),
            currency: tier.currency,
            billingPeriod: tier.billingPeriod,
            entryAllocation: tier.entryAllocation,
            duration: tier.duration,
            features: tier.features,
            benefits: tier.benefits,
            isActive: tier.isActive,
            displayOrder: tier.displayOrder,
            createdAt: tier.createdAt,
            updatedAt: tier.updatedAt
          };
        }
      }

      // Convert to MembershipResponseDto
      const membershipDto: MembershipResponseDto = {
        id: activeMembership.id,
        userId: activeMembership.userId,
        membershipTierId: activeMembership.membershipTierId,
        startDate: activeMembership.startDate,
        endDate: activeMembership.endDate,
        status: activeMembership.status,
        autoRenew: activeMembership.autoRenew,
        membershipHistory: activeMembership.membershipHistory,
        createdAt: activeMembership.createdAt,
        updatedAt: activeMembership.updatedAt
      };

      // Only add optional fields if they exist
      if (activeMembership.paymentMethodId) {
        membershipDto.paymentMethodId = activeMembership.paymentMethodId;
      }

      // Only add the membership tier if it exists
      if (tierInfo) {
        membershipDto.membershipTier = tierInfo;
      }

      // Build the response object with user and membership
      return {
        id: user.id,
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
        role: user.role,
        isVerified: user.isVerified,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt,
        membership: membershipDto
      };
    } catch (error) {
      if (error instanceof NotFoundError) {
        throw error;
      }
      throw handleDatabaseError(error, 'User');
    }
  }

  async hasActiveMembership(userId: string): Promise<boolean> {
    try {
      const memberships = await this.membershipRepository.findActiveByUserId(userId);
      // Check if the array has any elements
      return Array.isArray(memberships) && memberships.length > 0;
    } catch (error) {
      throw handleDatabaseError(error, 'Membership');
    }
  }

  async getUserTierLevel(userId: string): Promise<number> {
    try {
      const memberships = await this.membershipRepository.findActiveByUserId(userId);
      // Check if the array has any elements
      if (!Array.isArray(memberships) || memberships.length === 0) {
        return 0; // No membership = tier level 0
      }

      // Get the first membership from the array
      const activeMembership = memberships[0];

      if (!activeMembership) {
        return 0; // No active membership details = tier level 0
      }

      // Instead of tier level, return the entry allocation as an indicator of tier value
      const tier = await this.membershipTierRepository.findById(activeMembership.membershipTierId);
      return tier ? tier.entryAllocation : 0;
    } catch (error) {
      throw handleDatabaseError(error, 'Membership');
    }
  }

  async getAvailableEntries(userId: string): Promise<number> {
    try {
      const tierLevel = await this.getUserTierLevel(userId);
      // Mock calculation - in a real implementation, this would be based on tier
      return tierLevel * 5;
    } catch (error) {
      throw handleDatabaseError(error, 'Membership');
    }
  }

  async processMembershipRenewal(membershipId: string): Promise<any> {
    // Mock implementation
    console.log('Processing renewal for membership:', membershipId);
    return { success: true, message: 'Membership renewed (mock)' };
  }

  async applyCoupon(tierId: string, couponCode: string): Promise<{
    originalPrice: number;
    discountAmount: number;
    finalPrice: number;
    couponApplied: boolean;
  }> {
    // Mock implementation
    const tier = await this.membershipTierRepository.findById(tierId);
    if (!tier) {
      throw new NotFoundError('Membership tier not found');
    }

    const originalPrice = Number(tier.price);
    const discountAmount = couponCode === 'WELCOME' ? originalPrice * 0.1 : 0;

    return {
      originalPrice,
      discountAmount,
      finalPrice: originalPrice - discountAmount,
      couponApplied: discountAmount > 0
    };
  }

  /**
   * Create a new membership tier
   *
   * @param tierData - Tier data
   * @returns Promise with the created tier
   */
  async createTier(tierData: Partial<MembershipTierDto>): Promise<MembershipTierDto> {
    try {
      // Validate required fields
      if (!tierData.name || !tierData.description || tierData.price === undefined || !tierData.billingPeriod) {
        throw new ValidationError('Name, description, price, and billing period are required');
      }

      // Set defaults for optional fields
      const completeData = {
        ...tierData,
        entryAllocation: tierData.entryAllocation || 0,
        duration: tierData.duration || 30, // Default to 30 days if not specified
        features: tierData.features || [],
        isActive: tierData.isActive !== undefined ? tierData.isActive : true
      };

      // Create tier in repository with type assertion to handle enum conversion
      const newTier = await this.membershipTierRepository.create(completeData as any);

      return {
        id: newTier.id,
        name: newTier.name,
        description: newTier.description,
        price: Number(newTier.price),
        billingPeriod: newTier.billingPeriod as string,
        features: newTier.features as string[],
        entryAllocation: newTier.entryAllocation,
        duration: newTier.duration,
        isActive: newTier.isActive
      };
    } catch (error) {
      throw handleDatabaseError(error, 'MembershipTier');
    }
  }

  /**
   * Update a membership tier
   *
   * @param tierId - ID of the tier to update
   * @param tierData - Updated tier data
   * @returns Promise with the updated tier
   */
  async updateTier(tierId: string, tierData: Partial<MembershipTierDto>): Promise<MembershipTierDto> {
    try {
      // Check if tier exists
      const existingTier = await this.membershipTierRepository.findById(tierId);
      if (!existingTier) {
        throw new NotFoundError(`Membership tier with ID ${tierId} not found`);
      }

      // Update tier in repository with type assertion to handle enum conversion
      const updatedTier = await this.membershipTierRepository.update(tierId, tierData as any);

      return {
        id: updatedTier.id,
        name: updatedTier.name,
        description: updatedTier.description,
        price: Number(updatedTier.price),
        billingPeriod: updatedTier.billingPeriod as string,
        features: updatedTier.features as string[],
        entryAllocation: updatedTier.entryAllocation,
        duration: updatedTier.duration,
        isActive: updatedTier.isActive
      };
    } catch (error) {
      throw handleDatabaseError(error, 'MembershipTier');
    }
  }
}