/**
 * Membership Service Interface
 * 
 * This interface defines the operations specific to membership management,
 * including tier management, subscriptions, cancellations, and upgrades.
 */

import { BaseService } from '../base/base-service.interface.js';
import { UserWithMembershipResponseDto } from '../../dtos/user.dto.js';

/**
 * Membership tier data
 */
export interface MembershipTierDto {
  id: string;
  name: string;
  description: string;
  price: number | any; // Support both number and Prisma Decimal
  billingPeriod: string; // Support any billing period value
  features: string[] | any; // Support both string[] and Prisma Json
  entryAllocation: number;
  duration: number;
  isActive: boolean;
}

/**
 * Membership subscription data
 */
export interface MembershipSubscriptionDto {
  userId: string;
  tierId: string;
  autoRenew?: boolean;
  paymentMethodId?: string;
  couponCode?: string;
}

/**
 * Membership auto-renewal settings
 */
export interface MembershipRenewalDto {
  autoRenew: boolean;
}

/**
 * Membership upgrade data
 */
export interface MembershipUpgradeDto {
  userId: string;
  currentTierId: string;
  newTierId: string;
  paymentMethodId?: string;
  applyProration?: boolean;
}

/**
 * Membership history entry
 */
export interface MembershipHistoryEntryDto {
  action: string;
  date: Date;
  tierId: string;
  tierName: string;
  tierPrice: number;
  transactionId?: string;
}

/**
 * Membership Service Interface
 */
export interface MembershipService extends BaseService<any, MembershipSubscriptionDto, MembershipRenewalDto> {
  /**
   * Get available membership tiers
   * 
   * @param includeInactive - Whether to include inactive tiers
   * @returns Promise with array of available tiers
   */
  getAvailableTiers(includeInactive?: boolean): Promise<MembershipTierDto[]>;

  /**
   * Get a membership tier by ID
   * 
   * @param tierId - ID of the tier
   * @returns Promise with the tier details
   */
  getTierById(tierId: string): Promise<MembershipTierDto | null>;

  /**
   * Create a new membership tier
   * 
   * @param tierData - Tier data
   * @returns Promise with the created tier
   */
  createTier(tierData: Partial<MembershipTierDto>): Promise<MembershipTierDto>;

  /**
   * Update a membership tier
   * 
   * @param tierId - ID of the tier to update
   * @param tierData - Updated tier data
   * @returns Promise with the updated tier
   */
  updateTier(tierId: string, tierData: Partial<MembershipTierDto>): Promise<MembershipTierDto>;

  /**
   * Subscribe a user to a membership tier
   * 
   * @param subscriptionData - Subscription data
   * @returns Promise with the subscription result
   */
  subscribe(subscriptionData: MembershipSubscriptionDto): Promise<any>;

  /**
   * Cancel a user's membership
   * 
   * @param userId - ID of the user
   * @param cancelImmediately - Whether to cancel immediately or at end of billing period
   * @returns Promise indicating success
   */
  cancelMembership(userId: string, cancelImmediately?: boolean): Promise<void>;

  /**
   * Update membership auto-renew settings
   * 
   * @param userId - ID of the user
   * @param renewalSettings - New renewal settings
   * @returns Promise with the updated settings
   */
  updateAutoRenew(userId: string, renewalSettings: MembershipRenewalDto): Promise<MembershipRenewalDto>;

  /**
   * Upgrade a user's membership to a higher tier
   * 
   * @param upgradeData - Upgrade data including userId and new tier
   * @returns Promise with the upgrade result
   */
  upgradeMembership(upgradeData: MembershipUpgradeDto): Promise<any>;

  /**
   * Downgrade a user's membership to a lower tier
   * 
   * @param userId - ID of the user
   * @param newTierId - ID of the new tier
   * @param endOfPeriod - Whether to downgrade at end of billing period
   * @returns Promise with the downgrade result
   */
  downgradeMembership(userId: string, newTierId: string, endOfPeriod?: boolean): Promise<any>;

  /**
   * Get membership history for a user
   * 
   * @param userId - ID of the user
   * @returns Promise with the membership history
   */
  getMembershipHistory(userId: string): Promise<MembershipHistoryEntryDto[]>;

  /**
   * Get user with current membership
   * 
   * @param userId - ID of the user
   * @returns Promise with user data including membership
   */
  getUserWithMembership(userId: string): Promise<UserWithMembershipResponseDto>;

  /**
   * Check if user has an active membership
   * 
   * @param userId - ID of the user
   * @returns Promise with boolean indicating if user has active membership
   */
  hasActiveMembership(userId: string): Promise<boolean>;

  /**
   * Get a user's tier level
   * 
   * @param userId - ID of the user
   * @returns Promise with the user's tier level or 0 if no membership
   */
  getUserTierLevel(userId: string): Promise<number>;

  /**
   * Get available entries for a user
   * 
   * @param userId - ID of the user
   * @returns Promise with number of available entries
   */
  getAvailableEntries(userId: string): Promise<number>;
} 