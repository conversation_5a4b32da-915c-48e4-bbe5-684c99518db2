/**
 * Service Testing Utilities
 *
 * This file provides utilities for testing service implementations, including:
 * - Mock implementations of service interfaces
 * - Mock implementations of repository dependencies
 * - Factory functions for test data generation
 * - Setup and teardown functions for service tests
 * - Test helpers for validating enum usage
 * - Type-safe mock data generators
 * - Error handling test case generators
 */

import {
  BaseService,
  // Only import services that are actually used
  UserService,
  MembershipService,
  GiveawayService,
  PrizeService,
  WinnerService
} from '../index.js';

import {
  UserRepository,
  MembershipRepository,
  MembershipTierRepository,
  GiveawayRepository,
  PrizeRepository,
  EntryRepository,
  WinnerRepository,
  TransactionRepository,
  ContentRepository,
  NotificationRepository,
  SettingRepository,
  BaseRepository
} from '../../repositories/index.js';

import {
  FilterOptions,
  PaginatedResult,
  PaginationOptions
} from '../../repositories/base/base-repository.interface.js';

import {
  ValidationError,
  NotFoundError,
  ConflictError,
  BusinessLogicError,
  AppError
} from '../../utils/errors.js';

import {
  DuplicateError,
  ForeignKeyError,
  RepositoryError
} from '../../repositories/errors.js';

import {
  Role,
  MembershipStatus,
  GiveawayStatus,
  PaymentStatus,
  ContentStatus,
  AccessLevel,
  NotificationType,
  WinnerStatus,
  MembershipTier,
  Giveaway,
  Prize
  // Only import types that are actually used
} from '@prisma/client';

// Mock data types for type safety in tests
export type MockUser = {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  password: string;
  role: Role;
  profileImage: string | null;
  isVerified: boolean;
  isActive: boolean;
  stripeCustomerId: string | null;
  createdAt: Date;
  updatedAt: Date;
};

export type MockMembership = {
  id: string;
  userId: string;
  membershipTierId: string;
  status: MembershipStatus;
  startDate: Date;
  endDate: Date;
  autoRenew: boolean;
  createdAt: Date;
  updatedAt: Date;
  membershipHistory: any;  // To match the Prisma model
  paymentMethodId: string | null; // To match the Prisma model
  stripeCustomerId: string | null;
  stripeSubscriptionId: string | null;
  currentPeriodEnd: Date | null;
  cancelAtPeriodEnd: boolean;
};

export type MockMembershipTier = {
  id: string;
  name: string;
  description: string;
  price: number; // Will be converted to Decimal when needed
  currency: string;
  duration: number;
  benefits: string[];
  createdAt: Date;
  updatedAt: Date;
  isActive: boolean;
  billingPeriod: string; // Will be cast to BillingPeriod enum
  entryAllocation: number;
  features: any[];
  displayOrder: number;
};

export type MockGiveaway = {
  id: string;
  title: string;
  description: string;
  startDate: Date;
  endDate: Date;
  status: GiveawayStatus;
  createdAt: Date;
  updatedAt: Date;
  // Additional fields needed by the Giveaway interface
  isActive: boolean;
  drawDate: Date;
  featuredImage: string | null;
  prizeValue: number; // Will be converted to Decimal
  currency: string;
  entryRequirements: string | null;
  eligibility: string | null;
  termsUrl: string | null;
  maxEntryCount: number;
  entryCount: number;
  category: string | null;
  ownerId: string;
  winnerId: string | null;
  // Additional fields that might be in the Giveaway interface
  prizeDetails: string | null;
  imageUrl: string | null;
  tags: string[];
  rules: string | null;
  privacyPolicy: string | null;
  entryInstructions: string | null;
  customFields: any;
  // More required fields
  termsAndConditions: string | null;
  minTier: string | null;
  maxEntries: number;
};

export type MockPrize = {
  id: string;
  giveawayId: string;
  name: string;
  description: string | null;
  value: number; // Will be converted to Decimal when needed
  currency: string;
  quantity: number;
  images: string[]; // Array of image URLs
  specifications: any; // JSON Value for specifications
  createdAt: Date;
  updatedAt: Date;
};

export type MockEntry = {
  id: string;
  userId: string;
  giveawayId: string;
  entries: number;
  createdAt: Date;
  updatedAt: Date;
};

export type MockWinner = {
  id: string;
  userId: string;
  giveawayId: string;
  prizeId: string;
  entryId: string;
  status: WinnerStatus;
  selectionDate: Date;
  claimDate: Date | null;
  shippingDetails: any;  // Using 'any' to match JsonValue
  claimed: boolean;      // Legacy property
  claimedAt: Date | null; // Make this non-optional but nullable
  createdAt: Date;
  updatedAt: Date;
};

export type MockTransaction = {
  id: string;
  userId: string;
  amount: number;
  currency: string;
  status: PaymentStatus;
  description: string;
  paymentMethod: string;
  membershipId?: string;
  paymentIntentId?: string;
  createdAt: Date;
  updatedAt: Date;
};

export type MockContent = {
  id: string;
  title: string;
  slug: string;
  content: string;
  authorId: string;
  status: ContentStatus;
  accessLevel: AccessLevel;
  createdAt: Date;
  updatedAt: Date;
};

export type MockNotification = {
  id: string;
  userId: string;
  title: string;
  message: string;
  type: NotificationType;
  isRead: boolean;
  link?: string;
  createdAt: Date;
  updatedAt: Date;
};

// -----------------------------------------------------------------------------
// Generic Mock Base Repository
// -----------------------------------------------------------------------------

/**
 * Creates a mock base repository implementation
 *
 * @template T - Entity type
 * @template ID - ID type
 * @param mockData - Initial mock data
 * @returns Mock repository implementation
 */
export function createMockBaseRepository<T extends { id: ID }, ID = string>(mockData: T[] = []): BaseRepository<T, ID> {
  // Clone the mock data to avoid reference issues
  const data = [...mockData];

  return {
    findById: jest.fn(async (id: ID): Promise<T | null> => {
      const entity = data.find(item => item.id === id);
      return entity ? { ...entity } : null;
    }),

    findByIds: jest.fn(async (ids: ID[]): Promise<T[]> => {
      return data
        .filter(item => ids.includes(item.id))
        .map(item => ({ ...item }));
    }),

    findMany: jest.fn(async (filter?: FilterOptions, pagination?: PaginationOptions): Promise<T[]> => {
      const filtered = filter ? data.filter(item => applyFilter(item, filter)) : data;

      const { skip, take } = getPaginationParams(pagination);
      return filtered.slice(skip, skip + take).map(item => ({ ...item }));
    }),

    findManyPaginated: jest.fn(async (filter?: FilterOptions, pagination?: PaginationOptions): Promise<PaginatedResult<T>> => {
      const filtered = filter ? data.filter(item => applyFilter(item, filter)) : data;
      const { skip, take, page, limit } = getPaginationParams(pagination);

      return {
        data: filtered.slice(skip, skip + take).map(item => ({ ...item })),
        meta: {
          total: filtered.length,
          page,
          limit,
          hasMore: skip + take < filtered.length
        }
      };
    }),

    count: jest.fn(async (filter?: FilterOptions): Promise<number> => {
      return filter ? data.filter(item => applyFilter(item, filter)).length : data.length;
    }),

    create: jest.fn(async (newData: Partial<T>): Promise<T> => {
      // Generate ID if not provided
      const id = newData.id ?? String(Date.now()) as unknown as ID;
      const createdEntity = { id, ...newData } as T;
      data.push(createdEntity);
      return { ...createdEntity };
    }),

    createMany: jest.fn(async (newData: Partial<T>[]): Promise<T[]> => {
      const created: T[] = [];

      for (const item of newData) {
        const id = item.id ?? String(Date.now() + Math.random() * 1000) as unknown as ID;
        const createdEntity = { id, ...item } as T;
        data.push(createdEntity);
        created.push({ ...createdEntity });
      }

      return created;
    }),

    update: jest.fn(async (id: ID, updateData: Partial<T>): Promise<T> => {
      const index = data.findIndex(item => item.id === id);

      if (index === -1) {
        throw new NotFoundError(`Entity with ID ${String(id)} not found`);
      }

      const updated = { ...data[index], ...updateData } as T;
      data[index] = updated;
      return { ...updated } as T;
    }),

    updateMany: jest.fn(async (filter: FilterOptions, updateData: Partial<T>): Promise<number> => {
      let count = 0;

      for (let i = 0; i < data.length; i++) {
        if (applyFilter(data[i], filter)) {
          data[i] = { ...data[i], ...updateData } as T;
          count++;
        }
      }

      return count;
    }),

    delete: jest.fn(async (id: ID): Promise<T> => {
      const index = data.findIndex(item => item.id === id);

      if (index === -1) {
        throw new NotFoundError(`Entity with ID ${String(id)} not found`);
      }

      const deleted = data[index];
      data.splice(index, 1);
      return { ...deleted } as T;
    }),

    deleteMany: jest.fn(async (filter: FilterOptions): Promise<number> => {
      const initialLength = data.length;
      const indices = data
        .map((item, index) => applyFilter(item, filter) ? index : -1)
        .filter(index => index !== -1)
        .sort((a, b) => b - a); // Sort descending to avoid index shifting

      for (const index of indices) {
        data.splice(index, 1);
      }

      return initialLength - data.length;
    }),

    exists: jest.fn(async (id: ID): Promise<boolean> => {
      return data.some(item => item.id === id);
    }),

    existsWhere: jest.fn(async (filter: FilterOptions): Promise<boolean> => {
      return data.some(item => applyFilter(item, filter));
    }),

    findOne: jest.fn(async (filter: FilterOptions): Promise<T | null> => {
      const entity = data.find(item => applyFilter(item, filter));
      return entity ? { ...entity } : null;
    })
  };
}

// Helper function to calculate pagination parameters
function getPaginationParams(options?: PaginationOptions): {
  skip: number;
  take: number;
  page: number;
  limit: number;
} {
  const page = options?.page || 1;
  const limit = options?.limit || 10;
  const skip = options?.skip !== undefined ? options.skip : (page - 1) * limit;
  const take = options?.take !== undefined ? options.take : limit;

  return { skip, take, page, limit };
}

// Simple filter application function (for basic testing)
function applyFilter<T>(item: T, filter: FilterOptions): boolean {
  for (const [key, value] of Object.entries(filter)) {
    if (typeof value === 'object' && value !== null) {
      // Handle operators like 'gt', 'lt', etc.
      const operators = value as Record<string, any>;
      for (const [op, opValue] of Object.entries(operators)) {
        const itemValue = (item as any)[key];
        switch (op) {
          case 'gt': if (!(itemValue > opValue)) return false; break;
          case 'gte': if (!(itemValue >= opValue)) return false; break;
          case 'lt': if (!(itemValue < opValue)) return false; break;
          case 'lte': if (!(itemValue <= opValue)) return false; break;
          case 'in': if (!(opValue as any[]).includes(itemValue)) return false; break;
          case 'contains': if (!String(itemValue).includes(String(opValue))) return false; break;
        }
      }
    } else if ((item as any)[key] !== value) {
      return false;
    }
  }
  return true;
}

// -----------------------------------------------------------------------------
// Specific Repository Mocks
// -----------------------------------------------------------------------------

/**
 * Creates a mock user repository
 *
 * @param mockUsers - Initial mock user data
 * @returns Mock user repository
 */
export function createMockUserRepository(mockUsers: MockUser[] = []): UserRepository {
  const baseRepo = createMockBaseRepository<MockUser>(mockUsers);

  return {
    ...baseRepo,
    findByEmail: jest.fn(async (email: string): Promise<MockUser | null> => {
      const user = mockUsers.find(u => u.email === email);
      return user ? { ...user } : null;
    }),
    updatePassword: jest.fn(async (id: string, passwordHash: string): Promise<MockUser> => {
      const index = mockUsers.findIndex(user => user.id === id);

      if (index === -1) {
        throw new NotFoundError(`User with ID ${id} not found`);
      }

      const updatedUser = {
        ...mockUsers[index],
        password: passwordHash
      } as MockUser;

      mockUsers[index] = updatedUser;
      return { ...updatedUser };
    }),
    updateVerificationStatus: jest.fn(async (id: string, isVerified: boolean): Promise<MockUser> => {
      const index = mockUsers.findIndex(user => user.id === id);

      if (index === -1) {
        throw new NotFoundError(`User with ID ${id} not found`);
      }

      const updatedUser = {
        ...mockUsers[index],
        isVerified
      } as MockUser;

      mockUsers[index] = updatedUser;
      return { ...updatedUser };
    }),

    verifyEmail: jest.fn(async (id: string): Promise<MockUser> => {
      const index = mockUsers.findIndex(user => user.id === id);

      if (index === -1) {
        throw new NotFoundError(`User with ID ${id} not found`);
      }

      const updatedUser = {
        ...mockUsers[index],
        isVerified: true
      } as MockUser;

      mockUsers[index] = updatedUser;
      return { ...updatedUser };
    }),

    findWithMemberships: jest.fn(async (userId: string): Promise<MockUser | null> => {
      const user = mockUsers.find(u => u.id === userId);
      return user ? { ...user } : null;
      // Note: In a real implementation, this would include membership data
      // For testing purposes, we're just returning the user object
    })
  };
}

/**
 * Creates a mock membership repository
 *
 * @param mockMemberships - Initial mock membership data
 * @returns Mock membership repository
 */
export function createMockMembershipRepository(mockMemberships: MockMembership[] = []): MembershipRepository {
  const baseRepo = createMockBaseRepository<MockMembership>(mockMemberships);

  return {
    ...baseRepo,
    findByUserId: jest.fn(async (userId: string): Promise<MockMembership[]> => {
      return mockMemberships
        .filter(membership => membership.userId === userId);
    }),
    findActiveByUserId: jest.fn(async (userId: string): Promise<MockMembership[]> => {
      return mockMemberships
        .filter(m => m.userId === userId && m.status === MembershipStatus.ACTIVE);
    }),
    updateMembershipHistory: jest.fn(async (membershipId: string, historyData: any): Promise<MockMembership> => {
      const index = mockMemberships.findIndex(m => m.id === membershipId);

      if (index === -1) {
        throw new NotFoundError(`Membership with ID ${membershipId} not found`);
      }

      // Since we checked the index is valid, we know the membership exists
      // Use a non-null assertion to tell TypeScript this is safe
      const currentMembership = mockMemberships[index]!;
      const currentHistory = currentMembership.membershipHistory || [];
      const updatedHistory = Array.isArray(currentHistory)
        ? [...currentHistory, { ...historyData, timestamp: new Date() }]
        : [{ ...historyData, timestamp: new Date() }];

      // Create an updated membership with all required properties
      const updatedMembership: MockMembership = {
        id: currentMembership.id,
        userId: currentMembership.userId,
        membershipTierId: currentMembership.membershipTierId,
        status: currentMembership.status,
        startDate: currentMembership.startDate,
        endDate: currentMembership.endDate,
        autoRenew: currentMembership.autoRenew,
        createdAt: currentMembership.createdAt,
        updatedAt: new Date(),
        membershipHistory: updatedHistory,
        paymentMethodId: currentMembership.paymentMethodId,
        stripeCustomerId: currentMembership.stripeCustomerId ?? null,
        stripeSubscriptionId: currentMembership.stripeSubscriptionId ?? null,
        currentPeriodEnd: currentMembership.currentPeriodEnd ?? null,
        cancelAtPeriodEnd: currentMembership.cancelAtPeriodEnd ?? false
      };

      // Update the membership in the array
      mockMemberships[index] = updatedMembership;

      // Return the updated membership
      return updatedMembership;
    }),
    findWithTierDetails: jest.fn(async (membershipId: string): Promise<MockMembership | null> => {
      const membership = mockMemberships.find(m => m.id === membershipId);
      // Note: In a real implementation, this would include tier details
      // For testing purposes, we just return the membership object
      return membership ? membership : null;
    }),
    findExpiringSoon: jest.fn(async (daysThreshold: number = 7): Promise<MockMembership[]> => {
      const now = new Date();
      const thresholdDate = new Date(now);
      thresholdDate.setDate(now.getDate() + daysThreshold);

      return mockMemberships
        .filter(m => {
          const endDate = new Date(m.endDate);
          return endDate <= thresholdDate && endDate >= now;
        });
    })
  };
}

/**
 * Creates a mock winner repository
 *
 * @param mockWinners - Initial mock winners
 * @returns Mock winner repository
 */
export function createMockWinnerRepository(mockWinners: MockWinner[] = []): WinnerRepository {
  const baseRepo = createMockBaseRepository<MockWinner>(mockWinners);

  return {
    ...baseRepo,

    // Winner-specific repository methods
    findByUser: jest.fn(async (userId: string): Promise<MockWinner[]> => {
      return mockWinners
        .filter(winner => winner.userId === userId);
    }),

    findByGiveaway: jest.fn(async (giveawayId: string): Promise<MockWinner[]> => {
      return mockWinners
        .filter(winner => winner.giveawayId === giveawayId);
    }),

    findPending: jest.fn(async (giveawayId?: string): Promise<MockWinner[]> => {
      return mockWinners
        .filter(winner =>
          (winner.status === WinnerStatus.SELECTED || winner.status === WinnerStatus.NOTIFIED) &&
          (giveawayId ? winner.giveawayId === giveawayId : true)
        );
    }),

    findWithDetails: jest.fn(async (winnerId?: string): Promise<MockWinner[]> => {
      return mockWinners
        .filter(winner => winnerId ? winner.id === winnerId : true);
    }),

    updateStatus: jest.fn(async (winnerId: string, status: string): Promise<MockWinner> => {
      const winnerIndex = mockWinners.findIndex(w => w.id === winnerId);

      if (winnerIndex === -1) {
        throw new NotFoundError(`Winner with ID ${winnerId} not found`);
      }

      // Access the winner directly from the array with non-null assertion (!)
      // since we've verified it exists with the index check
      const winnerToUpdate = mockWinners[winnerIndex]!;

      // Create a new winner object with the updated status
      const updatedWinner = createMockWinner({
        ...winnerToUpdate,
        status: status as WinnerStatus,
        updatedAt: new Date()
      });

      // Update the array
      mockWinners[winnerIndex] = updatedWinner;

      return updatedWinner;
    })
  };
}

/**
 * Creates a mock membership tier repository
 *
 * @param mockTiers - Initial mock membership tiers
 * @returns Mock membership tier repository
 */
/**
 * Creates a mock membership tier with full properties
 */
export function createMockMembershipTier(overrides: Partial<MockMembershipTier> = {}): MockMembershipTier {
  const now = new Date();
  return {
    id: overrides.id || `tier-${Math.random().toString(36).substring(2, 10)}`,
    name: overrides.name || 'Test Tier',
    description: overrides.description || 'Test tier description',
    price: overrides.price || 9.99,
    currency: overrides.currency || 'USD',
    duration: overrides.duration || 30,
    benefits: overrides.benefits || ['Benefit 1', 'Benefit 2'],
    createdAt: overrides.createdAt || now,
    updatedAt: overrides.updatedAt || now,
    isActive: overrides.isActive !== undefined ? overrides.isActive : true,
    billingPeriod: overrides.billingPeriod || 'MONTHLY',
    entryAllocation: overrides.entryAllocation || 10,
    features: overrides.features || [],
    displayOrder: overrides.displayOrder || 0
  };
}

export function createMockMembershipTierRepository(mockTiers: MockMembershipTier[] = []): MembershipTierRepository {
  // Convert mock membership tiers to the format expected by the repository
  const convertTierToPrisma = (tier: MockMembershipTier) => ({
    ...tier,
    // Convert number to mock Decimal for Prisma compatibility
    price: {
      toNumber: () => tier.price,
      toString: () => tier.price.toString(),
      equals: (other: any) => tier.price === (typeof other === 'number' ? other : other.toNumber())
    }
  }) as unknown as MembershipTier;

  return {
    // Base repository methods
    findById: jest.fn(async (id: string) => {
      const tier = mockTiers.find(t => t.id === id);
      return tier ? convertTierToPrisma(tier) : null;
    }),

    findByIds: jest.fn(async (ids: string[]) => {
      const tiers = mockTiers.filter(t => ids.includes(t.id));
      return tiers.map(convertTierToPrisma);
    }),

    findMany: jest.fn(async (_filter?: any, _pagination?: any) => {
      return mockTiers.map(convertTierToPrisma);
    }),

    findManyPaginated: jest.fn(async (_filter?: any, pagination?: any) => {
      const { page = 1, limit = 10 } = pagination || {};
      const start = (page - 1) * limit;
      const end = start + limit;
      const items = mockTiers.slice(start, end);

      return {
        data: items.map(convertTierToPrisma),
        meta: {
          total: mockTiers.length,
          page,
          limit,
          hasMore: end < mockTiers.length
        }
      };
    }),

    count: jest.fn(async (_filter?: any) => mockTiers.length),

    create: jest.fn(async (data: any) => {
      const newTier = createMockMembershipTier({
        ...data,
        id: data.id || `tier-${Date.now()}`
      });
      mockTiers.push(newTier);
      return convertTierToPrisma(newTier);
    }),

    createMany: jest.fn(async (data: any[]) => {
      const created = data.map(item => {
        const newTier = createMockMembershipTier({
          ...item,
          id: item.id || `tier-${Date.now()}-${Math.random().toString(36).substring(2, 7)}`
        });
        mockTiers.push(newTier);
        return convertTierToPrisma(newTier);
      });

      return created;
    }),

    update: jest.fn(async (id: string, data: any) => {
      const index = mockTiers.findIndex(t => t.id === id);
      if (index === -1) {
        throw new NotFoundError(`Tier with ID ${id} not found`);
      }

      const updated = {
        ...mockTiers[index],
        ...data,
        updatedAt: new Date()
      };

      mockTiers[index] = updated;
      return convertTierToPrisma(updated);
    }),

    updateMany: jest.fn(async (_filter: any, data: any) => {
      // Simplified implementation that updates all tiers
      mockTiers.forEach((tier, i) => {
        mockTiers[i] = {
          ...tier,
          ...data,
          updatedAt: new Date()
        };
      });

      return mockTiers.length;
    }),

    delete: jest.fn(async (id: string) => {
      const index = mockTiers.findIndex(t => t.id === id);
      if (index === -1) {
        throw new NotFoundError(`Tier with ID ${id} not found`);
      }

      // Since we checked the index is valid, we can use non-null assertion
      const deleted = mockTiers[index]!;
      mockTiers.splice(index, 1);
      return convertTierToPrisma(deleted);
    }),

    deleteMany: jest.fn(async (_filter: any) => {
      const count = mockTiers.length;
      mockTiers.length = 0;
      return count;
    }),

    exists: jest.fn(async (id: string) => {
      return mockTiers.some(t => t.id === id);
    }),

    existsWhere: jest.fn(async (_filter: any) => {
      return mockTiers.length > 0;
    }),

    findOne: jest.fn(async (_filter: any) => {
      if (mockTiers.length === 0) {
        return null;
      }
      return convertTierToPrisma(mockTiers[0]!);
    }),

    // MembershipTier-specific methods
    findActive: jest.fn(async () => {
      const activeTiers = mockTiers.filter(t => t.isActive);
      return activeTiers.map(convertTierToPrisma);
    }),

    findByDisplayOrder: jest.fn(async () => {
      const sortedTiers = [...mockTiers].sort((a, b) => a.displayOrder - b.displayOrder);
      return sortedTiers.map(convertTierToPrisma);
    }),

    findWithFeatures: jest.fn(async (tierIds?: string[]) => {
      const filteredTiers = tierIds
        ? mockTiers.filter(tier => tierIds.includes(tier.id))
        : mockTiers;

      return filteredTiers.map(convertTierToPrisma);
    }),

    findByActiveStatus: jest.fn().mockResolvedValue([])
  };
}

// Other repository mocks follow the same pattern. Add more as needed for your tests.

// -----------------------------------------------------------------------------
// Base Service Mocks
// -----------------------------------------------------------------------------

/**
 * Creates a mock base service implementation
 *
 * @template T - Response DTO type
 * @template D - Create DTO type
 * @template U - Update DTO type
 * @template ID - ID type
 * @param mockData - Initial mock data
 * @returns Mock base service implementation
 */
export function createMockBaseService<T, D, U = D, ID = string>(mockData: T[] = []): BaseService<T, D, U, ID> {
  const data = [...mockData];

  return {
    get: jest.fn(async (id: ID): Promise<T | null> => {
      const entity = data.find((item: any) => item.id === id);
      return entity ? { ...entity } : null;
    }),

    list: jest.fn(async (_filter?: FilterOptions, _pagination?: PaginationOptions): Promise<T[]> => {
      // Simple implementation for tests
      return [...data];
    }),

    listPaginated: jest.fn(async (_filter?: FilterOptions, pagination?: PaginationOptions): Promise<PaginatedResult<T>> => {
      const { page, limit } = getPaginationParams(pagination);

      return {
        data: [...data],
        meta: {
          total: data.length,
          page,
          limit,
          hasMore: false
        }
      };
    }),

    count: jest.fn(async (_filter?: FilterOptions): Promise<number> => {
      return data.length;
    }),

    create: jest.fn(async (createData: D): Promise<T> => {
      const newEntity = {
        id: String(Date.now()) as any,
        ...createData,
        createdAt: new Date(),
        updatedAt: new Date()
      } as unknown as T;

      data.push(newEntity);
      return { ...newEntity };
    }),

    createMany: jest.fn(async (createDataArray: D[]): Promise<T[]> => {
      const newEntities = createDataArray.map(item => ({
        id: String(Date.now() + Math.random() * 1000) as any,
        ...item,
        createdAt: new Date(),
        updatedAt: new Date()
      } as unknown as T));

      data.push(...newEntities);
      return newEntities.map(entity => ({ ...entity }));
    }),

    update: jest.fn(async (id: ID, updateData: U): Promise<T> => {
      const index = data.findIndex((item: any) => item.id === id);

      if (index === -1) {
        throw new NotFoundError(`Entity with ID ${String(id)} not found`);
      }

      const updated = {
        ...data[index],
        ...updateData,
        updatedAt: new Date()
      } as T;

      data[index] = updated;
      return { ...updated };
    }),

    delete: jest.fn(async (id: ID): Promise<T> => {
      const index = data.findIndex((item: any) => item.id === id);

      if (index === -1) {
        throw new NotFoundError(`Entity with ID ${String(id)} not found`);
      }

      const deleted = data[index] as T;
      data.splice(index, 1);
      return { ...deleted };
    }),

    exists: jest.fn(async (id: ID): Promise<boolean> => {
      return data.some((item: any) => item.id === id);
    })
  };
}

// -----------------------------------------------------------------------------
// Mock Data Generators
// -----------------------------------------------------------------------------

/**
 * Creates a mock user
 *
 * @param overrides - Properties to override defaults
 * @returns Mock user
 */
export function createMockUser(overrides: Partial<MockUser> = {}): MockUser {
  const id = overrides.id || `user-${Date.now()}-${Math.floor(Math.random() * 1000)}`;

  return {
    id,
    email: overrides.email || `user-${id}@example.com`,
    firstName: overrides.firstName || 'Test',
    lastName: overrides.lastName || 'User',
    password: overrides.password || 'hashed_password',
    role: overrides.role || Role.USER,
    profileImage: overrides.profileImage ?? null,
    isVerified: overrides.isVerified ?? true,
    isActive: overrides.isActive ?? true,
    stripeCustomerId: overrides.stripeCustomerId ?? null,
    createdAt: overrides.createdAt || new Date(),
    updatedAt: overrides.updatedAt || new Date()
  };
}

/**
 * Creates multiple mock users
 *
 * @param count - Number of users to create
 * @param baseOverrides - Base properties to override for all users
 * @returns Array of mock users
 */
export function createMockUsers(count: number, baseOverrides: Partial<MockUser> = {}): MockUser[] {
  return Array.from({ length: count }, (_, i) =>
    createMockUser({
      ...baseOverrides,
      id: `user-${Date.now()}-${i}`,
      email: `user-${i}@example.com`
    })
  );
}

/**
 * Creates a mock membership
 *
 * @param overrides - Properties to override defaults
 * @returns Mock membership
 */
export function createMockMembership(overrides: Partial<MockMembership> = {}): MockMembership {
  const id = overrides.id || `membership-${Date.now()}-${Math.floor(Math.random() * 1000)}`;
  const now = new Date();
  const endDate = new Date();
  endDate.setMonth(endDate.getMonth() + 1); // Default 1 month membership

  return {
    id,
    userId: overrides.userId || `user-${Date.now()}`,
    membershipTierId: overrides.membershipTierId || `tier-${Date.now()}`,
    status: overrides.status || MembershipStatus.ACTIVE,
    startDate: overrides.startDate || now,
    endDate: overrides.endDate || endDate,
    autoRenew: overrides.autoRenew ?? true,
    createdAt: overrides.createdAt || now,
    updatedAt: overrides.updatedAt || now,
    membershipHistory: overrides.membershipHistory || [],
    paymentMethodId: overrides.paymentMethodId || null,
    stripeCustomerId: overrides.stripeCustomerId ?? null,
    stripeSubscriptionId: overrides.stripeSubscriptionId ?? null,
    currentPeriodEnd: overrides.currentPeriodEnd ?? null,
    cancelAtPeriodEnd: overrides.cancelAtPeriodEnd ?? false
  };
}

// Add more mock generators as needed for your tests

// -----------------------------------------------------------------------------
// Test Setup and Teardown Utilities
// -----------------------------------------------------------------------------

/**
 * Test context type
 */
export interface TestContext {
  repositories: {
    user: UserRepository;
    membership: MembershipRepository;
    membershipTier: MembershipTierRepository;
    giveaway: GiveawayRepository;
    prize: PrizeRepository;
    entry: EntryRepository;
    winner: WinnerRepository;
    transaction: TransactionRepository;
    content: ContentRepository;
    notification: NotificationRepository;
    setting: SettingRepository;
    [key: string]: BaseRepository<any, string>;
  };
  services: {
    user?: UserService;
    membership?: MembershipService;
    giveaway?: GiveawayService;
    prize?: PrizeService;
    winner?: WinnerService;
    [key: string]: any;
  };
  mockData: {
    users: MockUser[];
    memberships: MockMembership[];
    giveaways: MockGiveaway[];
    prizes: MockPrize[];
    winners: MockWinner[];
    // Add more mock data arrays as needed
    [key: string]: any[];
  };
  cleanup: () => void;
}

/**
 * Setup function for service tests
 *
 * @param options - Setup options
 * @returns Test context with repositories, mock data, and services
 */
export function setupServiceTest(options: {
  users?: MockUser[];
  memberships?: MockMembership[];
  giveaways?: MockGiveaway[];
  prizes?: MockPrize[];
  winners?: MockWinner[];
  setupServices?: boolean; // Whether to automatically create service implementations
  // Add more options as needed
} = {}): TestContext {
  // Initialize mock data
  const mockUsers = options.users || [];
  const mockMemberships = options.memberships || [];
  const mockGiveaways = options.giveaways || [];
  const mockPrizes = options.prizes || [];
  const mockWinners = options.winners || [];

  // Create repositories
  const userRepository = createMockUserRepository(mockUsers);
  const membershipRepository = createMockMembershipRepository(mockMemberships);
  const membershipTierRepository = createMockMembershipTierRepository([]);
  const giveawayRepository = createMockGiveawayRepository(mockGiveaways);
  const prizeRepository = createMockPrizeRepository(mockPrizes);
  const entryRepository = createMockBaseRepository<any, string>([]) as EntryRepository;
  const winnerRepository = createMockWinnerRepository(mockWinners);
  const transactionRepository = createMockBaseRepository<any, string>([]) as TransactionRepository;
  const contentRepository = createMockBaseRepository<any, string>([]) as ContentRepository;
  const notificationRepository = createMockBaseRepository<any, string>([]) as NotificationRepository;
  const settingRepository = createMockBaseRepository<any, string>([]) as SettingRepository;

  // Create services if requested
  const services: TestContext['services'] = {};

  if (options.setupServices) {
    services.winner = createMockWinnerService();
    // Add other services as needed
  }

  // Function to reset all mocks and spies
  const resetAllMocks = () => {
    jest.clearAllMocks();

    // Reset data in repositories
    mockUsers.length = 0;
    mockUsers.push(...(options.users || []));

    mockMemberships.length = 0;
    mockMemberships.push(...(options.memberships || []));

    mockGiveaways.length = 0;
    mockGiveaways.push(...(options.giveaways || []));

    mockPrizes.length = 0;
    mockPrizes.push(...(options.prizes || []));

    mockWinners.length = 0;
    mockWinners.push(...(options.winners || []));

    // Reset any other data arrays as needed
  };

  // Return the test context
  return {
    repositories: {
      user: userRepository,
      membership: membershipRepository,
      membershipTier: membershipTierRepository,
      giveaway: giveawayRepository,
      prize: prizeRepository,
      entry: entryRepository,
      winner: winnerRepository,
      transaction: transactionRepository,
      content: contentRepository,
      notification: notificationRepository,
      setting: settingRepository
    },
    services,
    mockData: {
      users: mockUsers,
      memberships: mockMemberships,
      giveaways: mockGiveaways,
      prizes: mockPrizes,
      winners: mockWinners
    },
    cleanup: resetAllMocks
  };
}

// -----------------------------------------------------------------------------
// Enum Validation Helpers
// -----------------------------------------------------------------------------

/**
 * Validates that a value is a valid enum value
 *
 * @param enumType - The enum to validate against
 * @param value - The value to validate
 * @returns True if value is a valid enum value
 */
export function isValidEnum<T extends Record<string, string | number>>(
  enumType: T,
  value: any
): value is T[keyof T] {
  return Object.values(enumType).includes(value);
}

/**
 * Tests that a function correctly handles invalid enum values
 *
 * @param fn - Function to test
 * @param validEnum - Valid enum to test against
 * @param invalidValue - Invalid value to pass
 */
export function testEnumValidation<T extends Record<string, string | number>>(
  fn: (value: any) => any,
  validEnum: T,
  invalidValue: any
): void {
  expect(() => fn(invalidValue)).toThrow();

  // Test with all valid values
  Object.values(validEnum).forEach(validValue => {
    expect(() => fn(validValue)).not.toThrow();
  });
}

// -----------------------------------------------------------------------------
// Error Handling Test Utilities
// -----------------------------------------------------------------------------

/**
 * Creates test cases for error handling
 *
 * @param serviceMethod - Method to test
 * @param repositoryMethod - Repository method that will throw
 * @param error - Error to throw
 * @param args - Arguments to pass to service method
 */
export function createErrorHandlingTest(
  serviceMethod: (...args: any[]) => Promise<any>,
  repositoryMethod: jest.SpyInstance,
  error: Error,
  args: any[] = []
): () => Promise<void> {
  return async () => {
    // Setup repository to throw an error
    repositoryMethod.mockRejectedValueOnce(error);

    // Test that service properly handles the error
    await expect(serviceMethod(...args)).rejects.toThrow();
  };
}

/**
 * Creates a suite of error handling tests for common error types
 *
 * @param serviceMethod - Service method to test
 * @param repositoryMethod - Repository method that will throw
 * @param args - Arguments to pass to service method
 */
export function testStandardErrorHandling(
  serviceMethod: (...args: any[]) => Promise<any>,
  repositoryMethod: jest.SpyInstance,
  args: any[] = []
): void {
  it('handles NotFoundError', async () => {
    const error = new NotFoundError('Resource not found');
    await createErrorHandlingTest(serviceMethod, repositoryMethod, error, args)();
  });

  it('handles ValidationError', async () => {
    const error = new ValidationError('Validation failed');
    await createErrorHandlingTest(serviceMethod, repositoryMethod, error, args)();
  });

  it('handles BusinessLogicError', async () => {
    const error = new BusinessLogicError('Business rule violation');
    await createErrorHandlingTest(serviceMethod, repositoryMethod, error, args)();
  });

  it('handles unexpected errors', async () => {
    const error = new Error('Unexpected error');
    await createErrorHandlingTest(serviceMethod, repositoryMethod, error, args)();
  });
}

// -----------------------------------------------------------------------------
// Transaction Simulation
// -----------------------------------------------------------------------------

/**
 * Simulates a transaction for testing
 *
 * @param fn - Function to execute in transaction
 * @returns Result of the function
 */
export async function simulateTransaction<T>(fn: () => Promise<T>): Promise<T> {
  let result: T;
  let error: Error | undefined;

  try {
    result = await fn();
  } catch (e) {
    error = e as Error;
    // Simulate transaction rollback on error
  }

  if (error) {
    throw error;
  }

  return result!;
}

/**
 * Creates a transactional mock repository
 *
 * @template T - Entity type
 * @template ID - ID type
 * @param repository - Base repository to enhance with transactions
 * @returns Repository with transaction support
 */
export function createTransactionalRepository<T, ID = string>(
  repository: BaseRepository<T, ID>
): BaseRepository<T, ID> & { withTransaction: (fn: () => Promise<any>) => Promise<any> } {
  return {
    ...repository,
    withTransaction: (fn: () => Promise<any>) => simulateTransaction(fn)
  };
}

// -----------------------------------------------------------------------------
// Enhanced Error Testing Utilities
// -----------------------------------------------------------------------------

/**
 * Creates a mock error of a specific type for testing
 *
 * @param ErrorClass - The error class to instantiate
 * @param message - Error message
 * @param metadata - Additional data for the error
 * @returns A configured error instance
 */
export function createMockError<T extends new (...args: any[]) => Error>(
  ErrorClass: T,
  message: string = 'Test error',
  metadata: Record<string, any> = {}
): InstanceType<T> {
  const error = new ErrorClass(message) as InstanceType<T>;

  // Add any additional metadata properties
  Object.entries(metadata).forEach(([key, value]) => {
    (error as any)[key] = value;
  });

  return error;
}

/**
 * Tests how a service handles repository errors
 *
 * @param serviceMethod - The service method to test
 * @param repositoryMethod - The repository method that will throw (jest spy)
 * @param errorType - Type of error to simulate
 * @param expectedErrorType - Expected error type after service handling
 * @param args - Arguments to pass to the service method
 */
export function testRepositoryErrorHandling<T extends Error>(
  serviceMethod: (...args: any[]) => Promise<any>,
  repositoryMethod: jest.SpyInstance,
  errorType: new (...args: any[]) => T,
  expectedErrorType: new (...args: any[]) => Error,
  args: any[] = []
): () => Promise<void> {
  return async () => {
    // Create the repository error
    const repoError = new errorType('Test repository error');

    // Make the repository method throw the error
    repositoryMethod.mockRejectedValueOnce(repoError);

    // Expect the service to transform it to the expected error type
    await expect(serviceMethod(...args)).rejects.toThrow(expectedErrorType);
  };
}

/**
 * Run a comprehensive suite of error handling tests for a service method
 *
 * @param serviceMethod - The service method to test
 * @param repositoryMethod - The repository method that will be called (jest spy)
 * @param args - Arguments to pass to the service method
 */
export function testComprehensiveErrorHandling(
  serviceMethod: (...args: any[]) => Promise<any>,
  repositoryMethod: jest.SpyInstance,
  args: any[] = []
): void {
  describe('error handling', () => {
    test('handles NotFoundError', testRepositoryErrorHandling(
      serviceMethod,
      repositoryMethod,
      NotFoundError,
      NotFoundError,
      args
    ));

    test('handles DuplicateError', testRepositoryErrorHandling(
      serviceMethod,
      repositoryMethod,
      DuplicateError,
      ConflictError,
      args
    ));

    test('handles ValidationError', testRepositoryErrorHandling(
      serviceMethod,
      repositoryMethod,
      ValidationError,
      ValidationError,
      args
    ));

    test('handles ForeignKeyError', testRepositoryErrorHandling(
      serviceMethod,
      repositoryMethod,
      ForeignKeyError,
      ValidationError,
      args
    ));

    test('handles unknown repository errors', testRepositoryErrorHandling(
      serviceMethod,
      repositoryMethod,
      RepositoryError,
      AppError,
      args
    ));
  });
}

// -----------------------------------------------------------------------------
// Enhanced Factory Functions for Test Data
// -----------------------------------------------------------------------------

/**
 * Create a mock prize with specified overrides
 *
 * @param overrides - Partial properties to override defaults
 * @returns A mock prize object
 */
export function createMockPrize(overrides: Partial<MockPrize> = {}): MockPrize {
  const now = new Date();

  return {
    id: overrides.id || `prize-${Math.random().toString(36).substring(2, 10)}`,
    giveawayId: overrides.giveawayId || `giveaway-${Math.random().toString(36).substring(2, 10)}`,
    name: overrides.name || 'Test Prize',
    description: overrides.description || 'A test prize for unit testing',
    value: overrides.value !== undefined ? overrides.value : 100,
    currency: overrides.currency || 'USD',
    quantity: overrides.quantity !== undefined ? overrides.quantity : 1,
    images: overrides.images || ['https://example.com/prize.jpg'],
    specifications: overrides.specifications || null,
    createdAt: overrides.createdAt || now,
    updatedAt: overrides.updatedAt || now
  };
}

/**
 * Create multiple mock prizes
 *
 * @param count - Number of prizes to create
 * @param baseOverrides - Base overrides to apply to all prizes
 * @returns Array of mock prizes
 */
export function createMockPrizes(count: number, baseOverrides: Partial<MockPrize> = {}): MockPrize[] {
  return Array.from({ length: count }, (_, i) =>
    createMockPrize({
      ...baseOverrides,
      id: `prize-${i + 1}`,
      name: `Test Prize ${i + 1}`
    })
  );
}

/**
 * Create a mock winner with specified overrides
 *
 * @param overrides - Partial properties to override defaults
 * @returns A mock winner object
 */
export function createMockWinner(overrides: Partial<MockWinner> = {}): MockWinner {
  const now = new Date();

  return {
    id: overrides.id || `winner-${Math.random().toString(36).substring(2, 10)}`,
    userId: overrides.userId || `user-${Math.random().toString(36).substring(2, 10)}`,
    giveawayId: overrides.giveawayId || `giveaway-${Math.random().toString(36).substring(2, 10)}`,
    prizeId: overrides.prizeId || `prize-${Math.random().toString(36).substring(2, 10)}`,
    entryId: overrides.entryId || `entry-${Math.random().toString(36).substring(2, 10)}`,
    status: overrides.status || WinnerStatus.SELECTED,
    selectionDate: overrides.selectionDate || now,
    claimDate: overrides.claimDate !== undefined ? overrides.claimDate : null,
    shippingDetails: overrides.shippingDetails || {},
    claimed: overrides.claimed !== undefined ? overrides.claimed : false,
    claimedAt: overrides.claimedAt !== undefined ? overrides.claimedAt : null,
    createdAt: overrides.createdAt || now,
    updatedAt: overrides.updatedAt || now
  };
}

/**
 * Create multiple mock winners
 *
 * @param count - Number of winners to create
 * @param baseOverrides - Base overrides to apply to all winners
 * @returns Array of mock winners
 */
export function createMockWinners(count: number, baseOverrides: Partial<MockWinner> = {}): MockWinner[] {
  return Array.from({ length: count }, (_, i) =>
    createMockWinner({
      ...baseOverrides,
      id: `winner-${i + 1}`
    })
  );
}

/**
 * Create a mock giveaway with specified overrides
 *
 * @param overrides - Partial properties to override defaults
 * @returns A mock giveaway object
 */
export function createMockGiveaway(overrides: Partial<MockGiveaway> = {}): MockGiveaway {
  const now = new Date();
  const startDate = new Date(now);
  startDate.setDate(startDate.getDate() - 1);

  const endDate = new Date(now);
  endDate.setDate(endDate.getDate() + 7);

  const drawDate = new Date(endDate);
  drawDate.setDate(drawDate.getDate() + 1);

  return {
    id: overrides.id || `giveaway-${Math.random().toString(36).substring(2, 10)}`,
    title: overrides.title || 'Test Giveaway',
    description: overrides.description || 'A test giveaway for unit testing',
    startDate: overrides.startDate || startDate,
    endDate: overrides.endDate || endDate,
    status: overrides.status || GiveawayStatus.ACTIVE,
    createdAt: overrides.createdAt || now,
    updatedAt: overrides.updatedAt || now,
    isActive: overrides.isActive !== undefined ? overrides.isActive : true,
    drawDate: overrides.drawDate || drawDate,
    featuredImage: overrides.featuredImage || null,
    prizeValue: overrides.prizeValue || 100,
    currency: overrides.currency || 'USD',
    entryRequirements: overrides.entryRequirements || null,
    eligibility: overrides.eligibility || null,
    termsUrl: overrides.termsUrl || null,
    maxEntryCount: overrides.maxEntryCount || 1000,
    entryCount: overrides.entryCount || 0,
    category: overrides.category || null,
    ownerId: overrides.ownerId || 'owner-123',
    winnerId: overrides.winnerId || null,
    // Additional fields needed by the Giveaway interface
    prizeDetails: overrides.prizeDetails || null,
    imageUrl: overrides.imageUrl || null,
    tags: overrides.tags || [],
    rules: overrides.rules || null,
    privacyPolicy: overrides.privacyPolicy || null,
    entryInstructions: overrides.entryInstructions || null,
    customFields: overrides.customFields || {},
    // Additional fields that might be needed
    termsAndConditions: overrides.termsAndConditions || null,
    minTier: overrides.minTier || null,
    maxEntries: overrides.maxEntries || 100
  };
}

/**
 * Create multiple mock giveaways
 *
 * @param count - Number of giveaways to create
 * @param baseOverrides - Base overrides to apply to all giveaways
 * @returns Array of mock giveaways
 */
export function createMockGiveaways(count: number, baseOverrides: Partial<MockGiveaway> = {}): MockGiveaway[] {
  return Array.from({ length: count }, (_, i) =>
    createMockGiveaway({
      ...baseOverrides,
      id: `giveaway-${i + 1}`,
      title: `Test Giveaway ${i + 1}`
    })
  );
}

// -----------------------------------------------------------------------------
// Mock Service Implementations
// -----------------------------------------------------------------------------

/**
 * Creates a mock winner service for testing
 *
 * @param mockWinners - Initial mock winners
 * @param mockUsers - Initial mock users
 * @param mockGiveaways - Initial mock giveaways
 * @param mockPrizes - Initial mock prizes
 * @returns Mock winner service implementation
 */
// No need to import DTOs that aren't being used

export function createMockWinnerService(): WinnerService {
  // Create a mock implementation of WinnerService with all required methods
  return {
    // Base service methods
    get: jest.fn(),
    list: jest.fn(),
    listPaginated: jest.fn(),
    count: jest.fn(),
    create: jest.fn(),
    createMany: jest.fn(),
    update: jest.fn(),
    delete: jest.fn(),
    exists: jest.fn(),

    // Winner-specific methods
    getWinnersByGiveaway: jest.fn(),
    getWinnersByUser: jest.fn(),
    getWinnersByGiveawayPaginated: jest.fn(),
    getWinnerDetails: jest.fn(),
    claimPrize: jest.fn(),
    checkClaimEligibility: jest.fn(),
    updateWinnerStatus: jest.fn(),
    getPendingWinners: jest.fn(),
    filterWinners: jest.fn(),
    createWinnersFromSelection: jest.fn(),
    notifyWinners: jest.fn(),
    processExpiredClaims: jest.fn(),
    reselectWinner: jest.fn()
  };
}

/**
 * Creates a mock prize repository for testing
 *
 * @param mockPrizes - Initial mock prizes
 * @returns Mock prize repository implementation
 */
export function createMockPrizeRepository(mockPrizes: MockPrize[] = []): PrizeRepository {
  // Create the repository with casting to PrizeRepository type since we're mocking
  const repo = {
    // Base repository methods
    findById: jest.fn(async (id: string) => {
      const prize = mockPrizes.find(p => p.id === id);
      return prize ? { ...prize, value: { toNumber: () => prize.value } } as unknown as Prize : null;
    }),

    findByIds: jest.fn(async (ids: string[]) => {
      const prizes = mockPrizes.filter(p => ids.includes(p.id));
      return prizes.map(p => ({ ...p, value: { toNumber: () => p.value } })) as unknown as Prize[];
    }),

    findMany: jest.fn(async (_filter?: any, _pagination?: any) => {
      return mockPrizes.map(p => ({ ...p, value: { toNumber: () => p.value } })) as unknown as Prize[];
    }),

    findManyPaginated: jest.fn(async (_filter?: any, pagination?: any) => {
      const { page = 1, limit = 10 } = pagination || {};
      const start = (page - 1) * limit;
      const end = start + limit;
      const items = mockPrizes.slice(start, end);

      return {
        data: items.map(p => ({ ...p, value: { toNumber: () => p.value } })) as unknown as Prize[],
        meta: {
          total: mockPrizes.length,
          page,
          limit,
          hasMore: end < mockPrizes.length
        }
      };
    }),

    count: jest.fn(async (_filter?: any) => mockPrizes.length),

    create: jest.fn(async (data: any) => {
      const newPrize = createMockPrize({
        ...data,
        id: data.id || `prize-${Date.now()}`
      });
      mockPrizes.push(newPrize);
      return { ...newPrize, value: { toNumber: () => newPrize.value } } as unknown as Prize;
    }),

    createMany: jest.fn(async (data: any[]) => {
      const created = data.map(item => {
        const newPrize = createMockPrize({
          ...item,
          id: item.id || `prize-${Date.now()}-${Math.random().toString(36).substring(2, 7)}`
        });
        mockPrizes.push(newPrize);
        return { ...newPrize, value: { toNumber: () => newPrize.value } } as unknown as Prize;
      });

      return created;
    }),

    update: jest.fn(async (id: string, data: any) => {
      const index = mockPrizes.findIndex(p => p.id === id);
      if (index === -1) {
        throw new NotFoundError(`Prize with ID ${id} not found`);
      }

      const updated = {
        ...mockPrizes[index],
        ...data,
        updatedAt: new Date()
      };

      mockPrizes[index] = updated;
      return { ...updated, value: { toNumber: () => updated.value } } as unknown as Prize;
    }),

    updateMany: jest.fn(async (_filter: any, data: any) => {
      // Simplified implementation that updates all prizes
      mockPrizes.forEach((prize, i) => {
        mockPrizes[i] = {
          ...prize,
          ...data,
          updatedAt: new Date()
        };
      });

      return mockPrizes.length;
    }),

    delete: jest.fn(async (id: string) => {
      const index = mockPrizes.findIndex(p => p.id === id);
      if (index === -1) {
        throw new NotFoundError(`Prize with ID ${id} not found`);
      }

      // Since we checked index is valid, we can safely assert the prize exists
      const deleted = mockPrizes[index]!;
      mockPrizes.splice(index, 1);
      return { ...deleted, value: { toNumber: () => deleted.value } } as unknown as Prize;
    }),

    deleteMany: jest.fn(async (_filter: any) => {
      const count = mockPrizes.length;
      mockPrizes.length = 0;
      return count;
    }),

    exists: jest.fn(async (id: string) => {
      return mockPrizes.some(p => p.id === id);
    }),

    existsWhere: jest.fn(async (_filter: any) => {
      return mockPrizes.length > 0;
    }),

    findOne: jest.fn(async (_filter: any) => {
      if (mockPrizes.length === 0) {
        return null;
      }
      const prize = mockPrizes[0]!;
      return { ...prize, value: { toNumber: () => prize.value } } as unknown as Prize;
    }),

    // Prize-specific methods
    findByGiveawayId: jest.fn(async (giveawayId: string) => {
      const prizes = mockPrizes.filter(p => p.giveawayId === giveawayId);
      return prizes.map(p => ({ ...p, value: { toNumber: () => p.value } })) as unknown as Prize[];
    }),

    findAvailable: jest.fn(async (giveawayId?: string) => {
      let prizes = mockPrizes.filter(p => p.quantity > 0);

      if (giveawayId) {
        prizes = prizes.filter(p => p.giveawayId === giveawayId);
      }

      return prizes.map(p => ({ ...p, value: { toNumber: () => p.value } })) as unknown as Prize[];
    }),

    updateQuantity: jest.fn(async (prizeId: string, quantityChange: number) => {
      const prize = mockPrizes.find(p => p.id === prizeId);
      if (!prize) {
        throw new NotFoundError(`Prize with ID ${prizeId} not found`);
      }

      const newQuantity = prize.quantity + quantityChange;

      if (newQuantity < 0) {
        throw new Error(`Prize ${prizeId} quantity cannot go below zero`);
      }

      const updatedPrize = {
        ...prize,
        quantity: newQuantity,
        updatedAt: new Date()
      };

      const prizeIndex = mockPrizes.findIndex(p => p.id === prizeId);
      mockPrizes[prizeIndex] = updatedPrize;

      return { ...updatedPrize, value: { toNumber: () => updatedPrize.value } } as unknown as Prize;
    })
  };

  // Return the fully implemented repository with proper typing
  return repo;
}

/**
 * Creates a mock giveaway repository for testing
 *
 * @param mockGiveaways - Initial mock giveaways
 * @returns Mock giveaway repository implementation
 */
export function createMockGiveawayRepository(mockGiveaways: MockGiveaway[] = []): GiveawayRepository {
  // Convert a mock giveaway to the Giveaway type expected by Prisma
  const convertGiveawayToPrisma = (g: MockGiveaway) => ({
    ...g,
    // Convert number to Decimal for Prisma compatibility
    prizeValue: {
      toNumber: () => g.prizeValue,
      toString: () => g.prizeValue.toString(),
      equals: (other: any) => g.prizeValue === (typeof other === 'number' ? other : other.toNumber())
    }
  }) as unknown as Giveaway;

  // Create the repository with fully implemented methods
  const repo = {
    // Base repository methods
    findById: jest.fn(async (id: string) => {
      const giveaway = mockGiveaways.find(g => g.id === id);
      return giveaway ? convertGiveawayToPrisma(giveaway) : null;
    }),

    findByIds: jest.fn(async (ids: string[]) => {
      const giveaways = mockGiveaways.filter(g => ids.includes(g.id));
      return giveaways.map(convertGiveawayToPrisma);
    }),

    findMany: jest.fn(async (_filter?: any, _pagination?: any) => {
      // Simple implementation for testing
      return mockGiveaways.map(convertGiveawayToPrisma);
    }),

    findManyPaginated: jest.fn(async (_filter?: any, pagination?: any) => {
      const { page = 1, limit = 10 } = pagination || {};
      const start = (page - 1) * limit;
      const end = start + limit;
      const items = mockGiveaways.slice(start, end);

      return {
        data: items.map(convertGiveawayToPrisma),
        meta: {
          total: mockGiveaways.length,
          page,
          limit,
          hasMore: end < mockGiveaways.length
        }
      };
    }),

    count: jest.fn(async (_filter?: any) => mockGiveaways.length),

    create: jest.fn(async (data: any) => {
      const newGiveaway = createMockGiveaway({
        ...data,
        id: data.id || `giveaway-${Date.now()}`
      });
      mockGiveaways.push(newGiveaway);
      return convertGiveawayToPrisma(newGiveaway);
    }),

    createMany: jest.fn(async (data: any[]) => {
      const created = data.map(item => {
        const newGiveaway = createMockGiveaway({
          ...item,
          id: item.id || `giveaway-${Date.now()}-${Math.random().toString(36).substring(2, 7)}`
        });
        mockGiveaways.push(newGiveaway);
        return convertGiveawayToPrisma(newGiveaway);
      });

      return created;
    }),

    update: jest.fn(async (id: string, data: any) => {
      const index = mockGiveaways.findIndex(g => g.id === id);
      if (index === -1) {
        throw new NotFoundError(`Giveaway with ID ${id} not found`);
      }

      const updated = {
        ...mockGiveaways[index],
        ...data,
        updatedAt: new Date()
      };

      mockGiveaways[index] = updated;
      return convertGiveawayToPrisma(updated);
    }),

    updateMany: jest.fn(async (_filter: any, data: any) => {
      // Simplified implementation that updates all giveaways
      mockGiveaways.forEach((giveaway, i) => {
        mockGiveaways[i] = {
          ...giveaway,
          ...data,
          updatedAt: new Date()
        };
      });

      return mockGiveaways.length;
    }),

    delete: jest.fn(async (id: string) => {
      const index = mockGiveaways.findIndex(g => g.id === id);
      if (index === -1) {
        throw new NotFoundError(`Giveaway with ID ${id} not found`);
      }

      const deleted = mockGiveaways[index]!;
      mockGiveaways.splice(index, 1);
      return convertGiveawayToPrisma(deleted);
    }),

    deleteMany: jest.fn(async (_filter: any) => {
      const count = mockGiveaways.length;
      mockGiveaways.length = 0;
      return count;
    }),

    exists: jest.fn(async (id: string) => {
      return mockGiveaways.some(g => g.id === id);
    }),

    existsWhere: jest.fn(async (_filter: any) => {
      return mockGiveaways.length > 0;
    }),

    findOne: jest.fn(async (_filter: any) => {
      if (mockGiveaways.length === 0) {
        return null;
      }
      const giveaway = mockGiveaways[0]!;
      return convertGiveawayToPrisma(giveaway);
    }),

    // Giveaway-specific methods
    findActive: jest.fn(async () => {
      const now = new Date();
      const active = mockGiveaways.filter(g =>
        g.status === GiveawayStatus.ACTIVE &&
        g.startDate <= now &&
        g.endDate >= now
      );
      return active.map(convertGiveawayToPrisma);
    }),

    findUpcoming: jest.fn(async (limit?: number) => {
      const now = new Date();
      const upcoming = mockGiveaways.filter(g => g.startDate > now);

      if (limit && limit > 0) {
        return upcoming.slice(0, limit).map(convertGiveawayToPrisma);
      }

      return upcoming.map(convertGiveawayToPrisma);
    }),

    findPast: jest.fn(async (limit?: number) => {
      const now = new Date();
      const past = mockGiveaways.filter(g => g.endDate < now);

      if (limit && limit > 0) {
        return past.slice(0, limit).map(convertGiveawayToPrisma);
      }

      return past.map(convertGiveawayToPrisma);
    }),

    findByCategory: jest.fn(async (categoryParam: string) => {
      // Just return giveaways that match the category if it exists
      const filtered = mockGiveaways.filter(g => g.category === categoryParam);
      return filtered.map(convertGiveawayToPrisma);
    }),

    findWithPrizes: jest.fn(async (giveawayId?: string) => {
      const filtered = giveawayId
        ? mockGiveaways.filter(g => g.id === giveawayId)
        : mockGiveaways;

      return filtered.map(g => ({
        ...convertGiveawayToPrisma(g),
        prizes: [] // In a real implementation, this would include related prizes
      })) as unknown as (Giveaway & { prizes: any[] })[];
    }),

    findWithEntryCount: jest.fn(async (giveawayId?: string) => {
      const filtered = giveawayId
        ? mockGiveaways.filter(g => g.id === giveawayId)
        : mockGiveaways;

      return filtered.map(g => ({
        ...convertGiveawayToPrisma(g),
        entryCount: g.entryCount || 0
      })) as unknown as Array<Giveaway & { entryCount: number }>;
    })
  };

  // Return the repository with proper typing
  return repo;
}

/**
 * Validates a model object against a schema of expected properties and types
 *
 * @param model - The object to validate
 * @param schema - Schema with expected property types
 * @returns Boolean indicating if the model is valid
 */
export function validateModelSchema<T>(
  model: T,
  schema: Record<keyof T, 'string' | 'number' | 'boolean' | 'date' | 'array' | 'object' | RegExp>
): boolean {
  for (const [key, expectedType] of Object.entries(schema)) {
    const value = model[key as keyof T];

    // Handle undefined/null for optional properties
    if (value === undefined || value === null) {
      continue;
    }

    if (expectedType === 'string' && typeof value !== 'string') {
      return false;
    }

    if (expectedType === 'number' && typeof value !== 'number') {
      return false;
    }

    if (expectedType === 'boolean' && typeof value !== 'boolean') {
      return false;
    }

    if (expectedType === 'date' && !(value instanceof Date)) {
      return false;
    }

    if (expectedType === 'array' && !Array.isArray(value)) {
      return false;
    }

    if (expectedType === 'object' && (typeof value !== 'object' || Array.isArray(value) || value instanceof Date)) {
      return false;
    }

    if (expectedType instanceof RegExp && (typeof value !== 'string' || !expectedType.test(value))) {
      return false;
    }
  }

  return true;
}

/**
 * Creates a test suite for validating a domain model
 *
 * @param modelName - Name of the model being tested
 * @param createModelFn - Function that creates a model instance
 * @param schema - Schema with expected property types
 */
export function testModelValidation<T>(
  modelName: string,
  createModelFn: () => T,
  schema: Record<keyof T, 'string' | 'number' | 'boolean' | 'date' | 'array' | 'object' | RegExp>
): void {
  describe(`${modelName} schema validation`, () => {
    let model: T;

    beforeEach(() => {
      model = createModelFn();
    });

    test(`${modelName} should match schema`, () => {
      expect(validateModelSchema(model, schema)).toBe(true);
    });

    // Generate tests for each property in the schema
    Object.entries(schema).forEach(([propName, expectedType]) => {
      test(`${propName} should be of type ${expectedType}`, () => {
        const prop = model[propName as keyof T];

        if (prop === undefined || prop === null) {
          // If property is optional, test passes
          return;
        }

        if (expectedType === 'string') {
          expect(typeof prop).toBe('string');
        } else if (expectedType === 'number') {
          expect(typeof prop).toBe('number');
        } else if (expectedType === 'boolean') {
          expect(typeof prop).toBe('boolean');
        } else if (expectedType === 'date') {
          expect(prop instanceof Date).toBe(true);
        } else if (expectedType === 'array') {
          expect(Array.isArray(prop)).toBe(true);
        } else if (expectedType === 'object') {
          expect(typeof prop).toBe('object');
          expect(Array.isArray(prop)).toBe(false);
          expect(prop instanceof Date).toBe(false);
        } else if (expectedType instanceof RegExp) {
          expect(typeof prop).toBe('string');
          expect(expectedType.test(prop as string)).toBe(true);
        }
      });
    });
  });
}