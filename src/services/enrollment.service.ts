/**
 * Enrollment Service
 *
 * This service handles the business logic for automatic enrollment in giveaways.
 * It provides methods for enrolling users and managing enrollment processes.
 */

import { PrismaClient, Giveaway, Entry, FailedEnrollment } from '@prisma/client';
import logger from '../utils/logger.js';
import * as pgJobProcessor from './pg-job-processor.js';

// Initialize Prisma client
const prisma = new PrismaClient();

/**
 * Enrollment Service class
 */
export class EnrollmentService {
  /**
   * Handle giveaway activation
   * This triggers automatic enrollment for all eligible users
   *
   * @param giveawayId - ID of the giveaway being activated
   * @returns Success message
   */
  async handleGiveawayActivation(giveawayId: string): Promise<string> {
    try {
      // Verify the giveaway exists and is active
      const giveaway = await prisma.giveaway.findUnique({
        where: { id: giveawayId }
      });

      if (!giveaway) {
        throw new Error(`Giveaway with ID ${giveawayId} not found`);
      }

      if (giveaway.status !== 'ACTIVE') {
        throw new Error(`Giveaway with ID ${giveawayId} is not active`);
      }

      // Check if auto-enrollment is enabled (using the eligibleMembershipTierIds as a proxy)
      const eligibleTierIds = giveaway.eligibleMembershipTierIds as string[];
      if (!eligibleTierIds || eligibleTierIds.length === 0) {
        logger.info(`No eligible tiers defined for giveaway ${giveawayId}`);
        return 'NO_ELIGIBLE_TIERS';
      }

      // Process the giveaway activation directly
      await pgJobProcessor.processGiveawayActivation(giveawayId);

      logger.info(`Processed giveaway activation for giveaway ${giveawayId}`);
      return 'ENROLLMENT_PROCESSED';
    } catch (error) {
      logger.error(`Failed to handle giveaway activation for giveaway ${giveawayId}:`, error);
      throw error;
    }
  }

  /**
   * Handle new membership
   * This enrolls a new member in all eligible active giveaways
   *
   * @param userId - ID of the user with new membership
   * @param membershipId - ID of the new membership
   * @param membershipTierId - ID of the membership tier
   * @returns Success message
   */
  async handleNewMembership(userId: string, membershipId: string, membershipTierId: string): Promise<string> {
    try {
      // Verify the user and membership exist
      const user = await prisma.user.findUnique({
        where: { id: userId }
      });

      if (!user) {
        throw new Error(`User with ID ${userId} not found`);
      }

      const membership = await prisma.membership.findUnique({
        where: { id: membershipId }
      });

      if (!membership) {
        throw new Error(`Membership with ID ${membershipId} not found`);
      }

      if (membership.status !== 'ACTIVE') {
        throw new Error(`Membership with ID ${membershipId} is not active`);
      }

      // Process the new membership directly
      await pgJobProcessor.processNewMembership(userId, membershipId, membershipTierId);

      logger.info(`Processed new membership for user ${userId}`);
      return 'ENROLLMENT_PROCESSED';
    } catch (error) {
      logger.error(`Failed to handle new membership for user ${userId}:`, error);
      throw error;
    }
  }

  /**
   * Handle membership upgrade
   * This updates entries for a user who upgraded their membership
   *
   * @param userId - ID of the user upgrading membership
   * @param membershipId - ID of the upgraded membership
   * @param membershipTierId - ID of the new membership tier
   * @param previousTierId - ID of the previous membership tier
   * @returns Success message
   */
  async handleMembershipUpgrade(
    userId: string,
    membershipId: string,
    membershipTierId: string,
    previousTierId: string
  ): Promise<string> {
    try {
      // Verify the user and membership exist
      const user = await prisma.user.findUnique({
        where: { id: userId }
      });

      if (!user) {
        throw new Error(`User with ID ${userId} not found`);
      }

      const membership = await prisma.membership.findUnique({
        where: { id: membershipId }
      });

      if (!membership) {
        throw new Error(`Membership with ID ${membershipId} not found`);
      }

      if (membership.status !== 'ACTIVE') {
        throw new Error(`Membership with ID ${membershipId} is not active`);
      }

      // Process the membership upgrade directly
      await pgJobProcessor.processMembershipUpgrade(
        userId,
        membershipId,
        membershipTierId,
        previousTierId
      );

      logger.info(`Processed membership upgrade for user ${userId}`);
      return 'ENROLLMENT_PROCESSED';
    } catch (error) {
      logger.error(`Failed to handle membership upgrade for user ${userId}:`, error);
      throw error;
    }
  }

  /**
   * Manually enroll a user in a giveaway
   *
   * @param giveawayId - ID of the giveaway
   * @param userId - ID of the user to enroll
   * @param membershipTierId - ID of the membership tier to use for entry allocation
   * @param numberOfEntries - Optional custom number of entries
   * @param updateStrategy - Strategy for handling existing entries
   * @param adminId - ID of the admin performing the manual enrollment
   * @param notes - Optional notes about the manual enrollment
   * @returns The created or updated entry
   */
  async manuallyEnrollUser(
    giveawayId: string,
    userId: string,
    membershipTierId: string,
    numberOfEntries?: number,
    updateStrategy: 'REPLACE_OR_CREATE' | 'ADD_TO_EXISTING' | 'ONLY_IF_NOT_EXISTS' = 'REPLACE_OR_CREATE',
    adminId?: string,
    notes?: string
  ): Promise<Entry> {
    try {
      // Verify the giveaway and user exist
      const [giveaway, user, membershipTier] = await Promise.all([
        prisma.giveaway.findUnique({ where: { id: giveawayId } }),
        prisma.user.findUnique({ where: { id: userId } }),
        prisma.membershipTier.findUnique({ where: { id: membershipTierId } })
      ]);

      if (!giveaway) {
        throw new Error(`Giveaway with ID ${giveawayId} not found`);
      }

      if (!user) {
        throw new Error(`User with ID ${userId} not found`);
      }

      if (!membershipTier) {
        throw new Error(`Membership tier with ID ${membershipTierId} not found`);
      }

      // Determine the number of entries
      const entryQuantity = numberOfEntries || membershipTier.entryAllocation;

      // Check if user already has an entry for this giveaway
      const existingEntry = await prisma.entry.findUnique({
        where: {
          userId_giveawayId: {
            userId,
            giveawayId
          }
        }
      });

      let entry: Entry;

      // Handle based on update strategy
      if (existingEntry) {
        switch (updateStrategy) {
          case 'REPLACE_OR_CREATE':
            // Replace the existing entry
            entry = await prisma.entry.update({
              where: { id: existingEntry.id },
              data: {
                quantity: entryQuantity,
                source: 'MANUAL',
                membershipTierId,
                createdByUserId: adminId || null,
                notes: notes || null,
                lastUpdatedDate: new Date()
              }
            });
            break;

          case 'ADD_TO_EXISTING':
            // Add to the existing entry quantity
            entry = await prisma.entry.update({
              where: { id: existingEntry.id },
              data: {
                quantity: existingEntry.quantity + entryQuantity,
                source: 'MANUAL',
                membershipTierId,
                createdByUserId: adminId || null,
                notes: notes || null,
                lastUpdatedDate: new Date()
              }
            });
            break;

          case 'ONLY_IF_NOT_EXISTS':
            // Don't update, just return the existing entry
            entry = existingEntry;
            break;

          default:
            throw new Error(`Invalid update strategy: ${updateStrategy}`);
        }
      } else {
        // Create a new entry
        entry = await prisma.entry.create({
          data: {
            userId,
            giveawayId,
            membershipTierId,
            quantity: entryQuantity,
            source: 'MANUAL',
            entryMethod: 'MANUAL',
            createdByUserId: adminId || null,
            notes: notes || null
          }
        });
      }

      // Create an audit log entry
      await prisma.enrollmentAuditLog.create({
        data: {
          giveawayId,
          userId,
          adminId: adminId || null,
          action: 'MANUAL_ENROLLMENT',
          status: 'SUCCESS',
          details: {
            entryId: entry.id,
            membershipTierId,
            numberOfEntries: entryQuantity,
            updateStrategy,
            notes: notes || null
          }
        }
      });

      return entry;
    } catch (error) {
      logger.error(`Failed to manually enroll user ${userId} in giveaway ${giveawayId}:`, error);

      // Create an audit log entry for the failed manual enrollment
      if (adminId) {
        await prisma.enrollmentAuditLog.create({
          data: {
            giveawayId,
            userId,
            adminId,
            action: 'MANUAL_ENROLLMENT',
            status: 'FAILURE',
            details: {
              membershipTierId,
              numberOfEntries: numberOfEntries,
              updateStrategy,
              notes: notes || null,
              error: error instanceof Error ? error.message : 'Unknown error'
            }
          }
        });
      }

      throw error;
    }
  }

  /**
   * Retry failed enrollments
   *
   * @param giveawayId - ID of the giveaway
   * @param failedEnrollmentIds - Array of failed enrollment IDs to retry
   * @param adminId - ID of the admin triggering the retry
   * @returns The created job ID
   */
  async retryFailedEnrollments(
    giveawayId: string,
    failedEnrollmentIds: string[],
    adminId?: string
  ): Promise<string> {
    try {
      // Verify the giveaway exists
      const giveaway = await prisma.giveaway.findUnique({
        where: { id: giveawayId }
      });

      if (!giveaway) {
        throw new Error(`Giveaway with ID ${giveawayId} not found`);
      }

      // Verify the failed enrollments exist
      const failedEnrollments = await prisma.failedEnrollment.findMany({
        where: {
          id: { in: failedEnrollmentIds },
          giveawayId,
          status: 'PENDING'
        }
      });

      if (failedEnrollments.length === 0) {
        throw new Error('No pending failed enrollments found with the provided IDs');
      }

      // Process the retry directly using PostgreSQL
      await pgJobProcessor.processJob('RETRY_FAILED_ENROLLMENTS', { giveawayId, failedEnrollmentIds });

      // Generate a mock job ID
      const jobId = `pg-${Date.now()}-${Math.floor(Math.random() * 1000)}`;

      // Create an audit log entry
      await prisma.enrollmentAuditLog.create({
        data: {
          giveawayId,
          adminId: adminId || null,
          action: 'RETRY_ENROLLMENT',
          affectedUserCount: failedEnrollments.length,
          status: 'SUCCESS',
          details: {
            jobId: jobId,
            failedEnrollmentIds,
            initiatedBy: adminId ? 'ADMIN' : 'SYSTEM'
          }
        }
      });

      logger.info(`Processed retry failed enrollments job ${jobId} for giveaway ${giveawayId}`);
      return jobId;
    } catch (error) {
      logger.error(`Failed to retry failed enrollments for giveaway ${giveawayId}:`, error);

      // Create an audit log entry for the failed retry
      if (adminId) {
        await prisma.enrollmentAuditLog.create({
          data: {
            giveawayId,
            adminId: adminId || null,
            action: 'RETRY_ENROLLMENT',
            status: 'FAILURE',
            details: {
              failedEnrollmentIds,
              error: error instanceof Error ? error.message : 'Unknown error'
            }
          }
        });
      }

      throw error;
    }
  }

  /**
   * Get failed enrollments for a giveaway
   *
   * @param giveawayId - ID of the giveaway
   * @param status - Optional status filter
   * @param page - Page number for pagination
   * @param limit - Number of items per page
   * @returns Paginated list of failed enrollments
   */
  async getFailedEnrollments(
    giveawayId: string,
    status?: string,
    page: number = 1,
    limit: number = 10
  ): Promise<{ failedEnrollments: FailedEnrollment[]; total: number; page: number; limit: number }> {
    try {
      // Calculate pagination
      const skip = (page - 1) * limit;

      // Build the where clause
      const where: any = { giveawayId };
      if (status) {
        where.status = status;
      }

      // Get failed enrollments with pagination
      const [failedEnrollments, total] = await Promise.all([
        prisma.failedEnrollment.findMany({
          where,
          skip,
          take: limit,
          orderBy: { attemptDate: 'desc' },
          include: {
            user: {
              select: {
                id: true,
                email: true,
                firstName: true,
                lastName: true
              }
            }
          }
        }),
        prisma.failedEnrollment.count({ where })
      ]);

      return {
        failedEnrollments,
        total,
        page,
        limit
      };
    } catch (error) {
      logger.error(`Failed to get failed enrollments for giveaway ${giveawayId}:`, error);
      throw error;
    }
  }

  /**
   * Get enrollment audit logs
   *
   * @param giveawayId - Optional giveaway ID filter
   * @param userId - Optional user ID filter
   * @param action - Optional action filter
   * @param startDate - Optional start date filter
   * @param endDate - Optional end date filter
   * @param page - Page number for pagination
   * @param limit - Number of items per page
   * @returns Paginated list of audit logs
   */
  async getEnrollmentAuditLogs(
    giveawayId?: string,
    userId?: string,
    action?: string,
    startDate?: Date,
    endDate?: Date,
    page: number = 1,
    limit: number = 10
  ): Promise<{ auditLogs: any[]; total: number; page: number; limit: number }> {
    try {
      // Calculate pagination
      const skip = (page - 1) * limit;

      // Build the where clause
      const where: any = {};
      if (giveawayId) {
        where.giveawayId = giveawayId;
      }
      if (userId) {
        where.userId = userId;
      }
      if (action) {
        where.action = action;
      }
      if (startDate || endDate) {
        where.timestamp = {};
        if (startDate) {
          where.timestamp.gte = startDate;
        }
        if (endDate) {
          where.timestamp.lte = endDate;
        }
      }

      // Get audit logs with pagination
      const [auditLogs, total] = await Promise.all([
        prisma.enrollmentAuditLog.findMany({
          where,
          skip,
          take: limit,
          orderBy: { timestamp: 'desc' },
          include: {
            giveaway: {
              select: {
                id: true,
                title: true
              }
            },
            user: {
              select: {
                id: true,
                email: true,
                firstName: true,
                lastName: true
              }
            }
          }
        }),
        prisma.enrollmentAuditLog.count({ where })
      ]);

      return {
        auditLogs,
        total,
        page,
        limit
      };
    } catch (error) {
      logger.error('Failed to get enrollment audit logs:', error);
      throw error;
    }
  }

  /**
   * Get user entries for a giveaway
   *
   * @param giveawayId - ID of the giveaway
   * @param userId - ID of the user
   * @returns The user's entries for the giveaway
   */
  async getUserEntries(giveawayId: string, userId: string): Promise<Entry | null> {
    try {
      // Get the user's entry for the giveaway
      const entry = await prisma.entry.findUnique({
        where: {
          userId_giveawayId: {
            userId,
            giveawayId
          }
        }
      });

      return entry;
    } catch (error) {
      logger.error(`Failed to get user entries for user ${userId} in giveaway ${giveawayId}:`, error);
      throw error;
    }
  }

  /**
   * Get entry statistics for a giveaway
   *
   * @param giveawayId - ID of the giveaway
   * @returns Statistics about entries for the giveaway
   */
  async getEntryStatistics(giveawayId: string): Promise<any> {
    try {
      // Get the giveaway
      const giveaway = await prisma.giveaway.findUnique({
        where: { id: giveawayId }
      });

      if (!giveaway) {
        throw new Error(`Giveaway with ID ${giveawayId} not found`);
      }

      // Get entry statistics
      const [totalEntries, uniqueUsers, entriesBySource, entriesByTier] = await Promise.all([
        // Total weighted entries
        prisma.entry.aggregate({
          where: { giveawayId },
          _sum: { quantity: true }
        }),

        // Unique users
        prisma.entry.count({
          where: { giveawayId }
        }),

        // Entries by source
        prisma.entry.groupBy({
          by: ['source'],
          where: { giveawayId },
          _sum: { quantity: true },
          _count: true
        }),

        // Entries by membership tier
        prisma.entry.groupBy({
          by: ['membershipTierId'],
          where: {
            giveawayId,
            membershipTierId: { not: null }
          },
          _sum: { quantity: true },
          _count: true
        })
      ]);

      // Get membership tier names for the tier IDs
      const tierIds = entriesByTier.map(tier => tier.membershipTierId).filter(Boolean) as string[];
      const tiers = tierIds.length > 0 ? await prisma.membershipTier.findMany({
        where: { id: { in: tierIds } },
        select: { id: true, name: true }
      }) : [];

      // Create a map of tier IDs to names
      const tierMap = new Map(tiers.map(tier => [tier.id, tier.name]));

      // Format the statistics
      return {
        totalWeightedEntries: totalEntries._sum.quantity || 0,
        uniqueUsers,
        entriesBySource: entriesBySource.map(source => ({
          source: source.source,
          count: source._count,
          weightedEntries: source._sum.quantity || 0
        })),
        entriesByTier: entriesByTier.map(tier => ({
          tierId: tier.membershipTierId,
          tierName: tierMap.get(tier.membershipTierId as string) || 'Unknown',
          count: tier._count,
          weightedEntries: tier._sum.quantity || 0
        }))
      };
    } catch (error) {
      logger.error(`Failed to get entry statistics for giveaway ${giveawayId}:`, error);
      throw error;
    }
  }

  /**
   * Update giveaway eligible tiers
   *
   * @param giveawayId - ID of the giveaway
   * @param eligibleMembershipTierIds - Array of eligible membership tier IDs
   * @returns The updated giveaway
   */
  async updateGiveawayEligibleTiers(
    giveawayId: string,
    eligibleMembershipTierIds: string[]
  ): Promise<Giveaway> {
    try {
      // Verify the giveaway exists
      const giveaway = await prisma.giveaway.findUnique({
        where: { id: giveawayId }
      });

      if (!giveaway) {
        throw new Error(`Giveaway with ID ${giveawayId} not found`);
      }

      // Verify the membership tiers exist
      if (eligibleMembershipTierIds.length > 0) {
        const tiers = await prisma.membershipTier.findMany({
          where: { id: { in: eligibleMembershipTierIds } }
        });

        if (tiers.length !== eligibleMembershipTierIds.length) {
          throw new Error('One or more membership tiers not found');
        }
      }

      // Update the giveaway
      const updatedGiveaway = await prisma.giveaway.update({
        where: { id: giveawayId },
        data: { eligibleMembershipTierIds }
      });

      return updatedGiveaway;
    } catch (error) {
      logger.error(`Failed to update eligible tiers for giveaway ${giveawayId}:`, error);
      throw error;
    }
  }

  /**
   * Toggle auto-enrollment for a giveaway
   *
   * @param giveawayId - ID of the giveaway
   * @param autoEnrollment - Whether to enable or disable auto-enrollment
   * @returns The updated giveaway
   */
  async toggleAutoEnrollment(
    giveawayId: string,
    autoEnrollment: boolean
  ): Promise<Giveaway> {
    try {
      // Verify the giveaway exists
      const giveaway = await prisma.giveaway.findUnique({
        where: { id: giveawayId }
      });

      if (!giveaway) {
        throw new Error(`Giveaway with ID ${giveawayId} not found`);
      }

      // Update the giveaway
      const updatedGiveaway = await prisma.giveaway.update({
        where: { id: giveawayId },
        data: { autoEnrollment }
      });

      return updatedGiveaway;
    } catch (error) {
      logger.error(`Failed to toggle auto-enrollment for giveaway ${giveawayId}:`, error);
      throw error;
    }
  }
}