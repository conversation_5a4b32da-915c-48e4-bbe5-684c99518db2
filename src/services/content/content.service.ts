/**
 * Content Service Implementation
 * 
 * This service manages content operations including retrieval, creation, updating, 
 * and access control based on user membership.
 */

import { Content, ContentStatus, AccessLevel, MembershipStatus } from '@prisma/client';
import { ContentServiceInterface } from './content-service.interface.js';
import {
  ContentResponseDto,
  ContentCreateDto,
  ContentUpdateDto,
  ContentFilterParamsDto
} from '../../dtos/content.dto.js';
import {
  FilterOptions,
  PaginatedResult,
  PaginationOptions
} from '../../repositories/base/base-repository.interface.js';
import { 
  ContentRepository,
  MembershipRepository
} from '../../repositories/index.js';
import { 
  ValidationError, 
  NotFoundError, 
  AuthorizationError,
  BusinessLogicError,
  AppError 
} from '../../utils/errors.js';

/**
 * Implementation of the Content Service
 */
export class ContentService implements ContentServiceInterface {
  /**
   * Constructor for ContentService
   */
  constructor(
    private readonly contentRepository: ContentRepository,
    private readonly membershipRepository: MembershipRepository
  ) {}

  /**
   * Convert a Content entity to a ContentResponseDto
   * 
   * @private
   * @param content - The content entity to convert
   * @returns ContentResponseDto
   */
  private toResponseDto(content: Content): ContentResponseDto {
    const dto: ContentResponseDto = {
      id: content.id,
      title: content.title,
      slug: content.slug,
      content: content.content,
      authorId: content.authorId,
      status: content.status,
      accessLevel: content.accessLevel,
      tags: content.tags as string[] || [],
      createdAt: content.createdAt,
      updatedAt: content.updatedAt
    };
    
    // Add optional fields only if they exist
    if (content.excerpt) {
      dto.excerpt = content.excerpt;
    }
    
    if (content.featuredImage) {
      dto.featuredImage = content.featuredImage;
    }
    
    if (content.publishDate) {
      dto.publishDate = content.publishDate;
    }
    
    if (content.category) {
      dto.category = content.category;
    }
    
    // Add author details if available (when findWithAuthor is used)
    if ((content as any).author) {
      const author = (content as any).author;
      dto.author = {
        id: author.id,
        firstName: author.firstName || '',
        lastName: author.lastName || ''
      };
    }
    
    return dto;
  }

  /**
   * Get a content item by ID
   * 
   * @param id - ID of the content to retrieve
   * @returns Promise with content or null if not found
   */
  async get(id: string): Promise<ContentResponseDto | null> {
    try {
      const contentWithAuthor = await this.contentRepository.findWithAuthor(id);
      
      if (!contentWithAuthor || contentWithAuthor.length === 0) {
        return null;
      }
      
      // contentWithAuthor[0] is definitely not undefined here since we checked length
      return this.toResponseDto(contentWithAuthor[0] as Content);
    } catch (error) {
      this.handleError(error, 'Failed to get content');
      return null;
    }
  }

  /**
   * List content with optional filtering and pagination
   * 
   * @param filter - Optional filter criteria
   * @param pagination - Optional pagination options
   * @returns Promise with array of content items
   */
  async list(
    filter?: FilterOptions,
    pagination?: PaginationOptions
  ): Promise<ContentResponseDto[]> {
    try {
      const content = await this.contentRepository.findMany(filter, pagination);
      return content.map(item => this.toResponseDto(item));
    } catch (error) {
      this.handleError(error, 'Failed to list content');
      return [];
    }
  }

  /**
   * List content with pagination metadata
   * 
   * @param filter - Optional filter criteria
   * @param pagination - Pagination options
   * @returns Promise with paginated result of content items
   */
  async listPaginated(
    filter?: FilterOptions,
    pagination?: PaginationOptions
  ): Promise<PaginatedResult<ContentResponseDto>> {
    try {
      const result = await this.contentRepository.findManyPaginated(filter, pagination);
      
      return {
        data: result.data.map(item => this.toResponseDto(item)),
        meta: result.meta
      };
    } catch (error) {
      this.handleError(error, 'Failed to list paginated content');
      throw error;
    }
  }

  /**
   * Count content items that match the filter criteria
   * 
   * @param filter - Optional filter criteria
   * @returns Promise with the count
   */
  async count(filter?: FilterOptions): Promise<number> {
    try {
      return await this.contentRepository.count(filter);
    } catch (error) {
      this.handleError(error, 'Failed to count content');
      throw error;
    }
  }

  /**
   * Create a new content item
   * 
   * @param data - Content data to create
   * @returns Promise with the created content
   */
  async create(data: ContentCreateDto): Promise<ContentResponseDto> {
    try {
      // Validate required fields
      if (!data.title) {
        throw new ValidationError('Title is required');
      }
      
      if (!data.content) {
        throw new ValidationError('Content is required');
      }
      
      if (!data.slug) {
        throw new ValidationError('Slug is required');
      }
      
      // Check if slug already exists
      const existingContent = await this.contentRepository.findBySlug(data.slug);
      if (existingContent) {
        throw new ValidationError(`Content with slug "${data.slug}" already exists`);
      }
      
      // Set default values if not provided
      const contentData = {
        ...data,
        status: data.status || ContentStatus.DRAFT,
        accessLevel: data.accessLevel || AccessLevel.PUBLIC,
        tags: data.tags || []
      };
      
      const createdContent = await this.contentRepository.create(contentData);
      return this.toResponseDto(createdContent);
    } catch (error) {
      this.handleError(error, 'Failed to create content');
      throw error;
    }
  }

  /**
   * Create multiple content items
   * 
   * @param dataArray - Array of content data to create
   * @returns Promise with array of created content items
   */
  async createMany(dataArray: ContentCreateDto[]): Promise<ContentResponseDto[]> {
    try {
      // Validate all items have required fields
      for (const data of dataArray) {
        if (!data.title || !data.content || !data.slug) {
          throw new ValidationError('All content items must have title, content, and slug');
        }
      }
      
      const createdContent = await this.contentRepository.createMany(dataArray);
      return createdContent.map(item => this.toResponseDto(item));
    } catch (error) {
      this.handleError(error, 'Failed to create multiple content items');
      throw error;
    }
  }

  /**
   * Update an existing content item
   * 
   * @param id - ID of the content to update
   * @param data - Data to update the content with
   * @returns Promise with the updated content
   */
  async update(id: string, data: ContentUpdateDto): Promise<ContentResponseDto> {
    try {
      // Verify content exists
      const existingContent = await this.contentRepository.findById(id);
      if (!existingContent) {
        throw new NotFoundError(`Content with ID ${id} not found`);
      }
      
      // If slug is updated, check it doesn't conflict
      if (data.slug && data.slug !== existingContent.slug) {
        const slugExists = await this.contentRepository.findBySlug(data.slug);
        if (slugExists && slugExists.id !== id) {
          throw new ValidationError(`Content with slug "${data.slug}" already exists`);
        }
      }
      
      const updatedContent = await this.contentRepository.update(id, data);
      return this.toResponseDto(updatedContent);
    } catch (error) {
      this.handleError(error, 'Failed to update content');
      throw error;
    }
  }

  /**
   * Delete a content item
   * 
   * @param id - ID of the content to delete
   * @returns Promise with the deleted content
   */
  async delete(id: string): Promise<ContentResponseDto> {
    try {
      // Verify content exists
      const existingContent = await this.contentRepository.findById(id);
      if (!existingContent) {
        throw new NotFoundError(`Content with ID ${id} not found`);
      }
      
      const deletedContent = await this.contentRepository.delete(id);
      return this.toResponseDto(deletedContent);
    } catch (error) {
      this.handleError(error, 'Failed to delete content');
      throw error;
    }
  }

  /**
   * Check if a content item exists
   * 
   * @param id - ID of the content to check
   * @returns Promise with boolean indicating existence
   */
  async exists(id: string): Promise<boolean> {
    try {
      return await this.contentRepository.exists(id);
    } catch (error) {
      this.handleError(error, 'Failed to check content existence');
      throw error;
    }
  }

  /**
   * Get content by slug
   * 
   * @param slug - The slug of the content to retrieve
   * @param userId - Optional user ID for access control
   * @returns Promise with content or null if not found or not accessible
   */
  async getBySlug(slug: string, userId?: string): Promise<ContentResponseDto | null> {
    try {
      if (!slug) {
        throw new ValidationError('Slug is required');
      }
      
      const content = await this.contentRepository.findBySlug(slug);
      if (!content) {
        return null;
      }
      
      // If content is published and public, return immediately
      if (content.status === ContentStatus.PUBLISHED && content.accessLevel === AccessLevel.PUBLIC) {
        return this.toResponseDto(content);
      }
      
      // If user ID is provided, check access
      if (userId) {
        const hasAccess = await this.checkAccess(content.id, userId);
        if (hasAccess) {
          return this.toResponseDto(content);
        }
      }
      
      // No access or not published
      return null;
    } catch (error) {
      this.handleError(error, 'Failed to get content by slug');
      throw error;
    }
  }

  /**
   * List content by category
   * 
   * @param category - The category to filter by
   * @param userId - Optional user ID for access control
   * @param pagination - Optional pagination options
   * @returns Promise with paginated result of content items
   */
  async getByCategory(
    category: string,
    userId?: string,
    pagination?: PaginationOptions
  ): Promise<PaginatedResult<ContentResponseDto>> {
    try {
      if (!category) {
        throw new ValidationError('Category is required');
      }
      
      // Build filter based on category and access control
      const filter: FilterOptions = {};
      filter['category'] = category;
      
      // Apply access control filter
      await this.applyAccessControlFilter(filter, userId);
      
      const result = await this.contentRepository.findManyPaginated(filter, pagination);
      
      return {
        data: result.data.map(item => this.toResponseDto(item)),
        meta: result.meta
      };
    } catch (error) {
      this.handleError(error, 'Failed to get content by category');
      throw error;
    }
  }

  /**
   * Search content
   * 
   * @param query - The search query
   * @param userId - Optional user ID for access control
   * @param pagination - Optional pagination options
   * @returns Promise with paginated result of content items
   */
  async search(
    query: string,
    userId?: string,
    pagination?: PaginationOptions
  ): Promise<PaginatedResult<ContentResponseDto>> {
    try {
      if (!query) {
        throw new ValidationError('Search query is required');
      }
      
      // Build search filter
      const filter: FilterOptions = {};
      filter['OR'] = [
        { title: { contains: query, mode: 'insensitive' } },
        { content: { contains: query, mode: 'insensitive' } },
        { excerpt: { contains: query, mode: 'insensitive' } }
      ];
      
      // Apply access control filter
      await this.applyAccessControlFilter(filter, userId);
      
      const result = await this.contentRepository.findManyPaginated(filter, pagination);
      
      return {
        data: result.data.map(item => this.toResponseDto(item)),
        meta: result.meta
      };
    } catch (error) {
      this.handleError(error, 'Failed to search content');
      throw error;
    }
  }

  /**
   * Filter content by multiple criteria
   * 
   * @param filterParams - Filter parameters
   * @param userId - Optional user ID for access control
   * @param pagination - Optional pagination options
   * @returns Promise with paginated result of content items
   */
  async filter(
    filterParams: ContentFilterParamsDto,
    userId?: string,
    pagination?: PaginationOptions
  ): Promise<PaginatedResult<ContentResponseDto>> {
    try {
      // Build filter from params
      const filter: FilterOptions = {};
      
      if (filterParams.status) {
        filter['status'] = filterParams.status;
      }
      
      if (filterParams.category) {
        filter['category'] = filterParams.category;
      }
      
      if (filterParams.tags && filterParams.tags.length > 0) {
        filter['tags'] = { hasEvery: filterParams.tags };
      }
      
      if (filterParams.authorId) {
        filter['authorId'] = filterParams.authorId;
      }
      
      if (filterParams.search) {
        filter['OR'] = [
          { title: { contains: filterParams.search, mode: 'insensitive' } },
          { content: { contains: filterParams.search, mode: 'insensitive' } },
          { excerpt: { contains: filterParams.search, mode: 'insensitive' } }
        ];
      }
      
      // Date range filters
      if (filterParams.publishDateStart || filterParams.publishDateEnd) {
        filter['publishDate'] = {};
        
        if (filterParams.publishDateStart) {
          filter['publishDate'].gte = filterParams.publishDateStart;
        }
        
        if (filterParams.publishDateEnd) {
          filter['publishDate'].lte = filterParams.publishDateEnd;
        }
      }
      
      // Apply access control filter
      if (!filterParams.accessLevel) {
        await this.applyAccessControlFilter(filter, userId);
      } else {
        filter['accessLevel'] = filterParams.accessLevel;
      }
      
      const result = await this.contentRepository.findManyPaginated(filter, pagination);
      
      return {
        data: result.data.map(item => this.toResponseDto(item)),
        meta: result.meta
      };
    } catch (error) {
      this.handleError(error, 'Failed to filter content');
      throw error;
    }
  }

  /**
   * Get content by tags
   * 
   * @param tags - Array of tags to filter by
   * @param userId - Optional user ID for access control
   * @param pagination - Optional pagination options
   * @returns Promise with paginated result of content items
   */
  async getByTags(
    tags: string[],
    userId?: string,
    pagination?: PaginationOptions
  ): Promise<PaginatedResult<ContentResponseDto>> {
    try {
      if (!tags || tags.length === 0) {
        throw new ValidationError('At least one tag is required');
      }
      
      // Build filter based on tags
      const filter: FilterOptions = {};
      filter['tags'] = { hasSome: tags };
      
      // Apply access control filter
      await this.applyAccessControlFilter(filter, userId);
      
      const result = await this.contentRepository.findManyPaginated(filter, pagination);
      
      return {
        data: result.data.map(item => this.toResponseDto(item)),
        meta: result.meta
      };
    } catch (error) {
      this.handleError(error, 'Failed to get content by tags');
      throw error;
    }
  }

  /**
   * Get published content
   * 
   * @param userId - Optional user ID for access control
   * @param pagination - Optional pagination options
   * @returns Promise with paginated result of published content items
   */
  async getPublished(
    userId?: string,
    pagination?: PaginationOptions
  ): Promise<PaginatedResult<ContentResponseDto>> {
    try {
      // Build filter for published content
      const filter: FilterOptions = {};
      filter['status'] = ContentStatus.PUBLISHED;
      filter['publishDate'] = { lte: new Date() };
      
      // Apply access control filter
      await this.applyAccessControlFilter(filter, userId);
      
      const result = await this.contentRepository.findManyPaginated(filter, pagination);
      
      return {
        data: result.data.map(item => this.toResponseDto(item)),
        meta: result.meta
      };
    } catch (error) {
      this.handleError(error, 'Failed to get published content');
      throw error;
    }
  }

  /**
   * Get featured content
   * 
   * @param limit - Number of featured items to retrieve
   * @param userId - Optional user ID for access control
   * @returns Promise with array of featured content items
   */
  async getFeatured(limit: number, userId?: string): Promise<ContentResponseDto[]> {
    try {
      // Build filter for featured content (published with featured tag)
      const filter: FilterOptions = {};
      filter['status'] = ContentStatus.PUBLISHED;
      filter['publishDate'] = { lte: new Date() };
      filter['tags'] = { hasSome: ['featured'] };
      
      // Apply access control filter
      await this.applyAccessControlFilter(filter, userId);
      
      // Get content with pagination to limit results
      const pagination: PaginationOptions = {
        limit,
        page: 1
      };
      
      const content = await this.contentRepository.findMany(filter, pagination);
      return content.map(item => this.toResponseDto(item));
    } catch (error) {
      this.handleError(error, 'Failed to get featured content');
      throw error;
    }
  }

  /**
   * Update content status (admin)
   * 
   * @param id - ID of the content to update
   * @param status - New status value
   * @returns Promise with the updated content
   */
  async updateStatus(id: string, status: ContentStatus): Promise<ContentResponseDto> {
    try {
      // Verify content exists
      const existingContent = await this.contentRepository.findById(id);
      if (!existingContent) {
        throw new NotFoundError(`Content with ID ${id} not found`);
      }
      
      // Handle special status transitions
      if (status === ContentStatus.PUBLISHED && existingContent.status !== ContentStatus.PUBLISHED) {
        // Set publishDate if not already set
        const updateData = {
          status,
          publishDate: existingContent.publishDate || new Date()
        };
        
        const updatedContent = await this.contentRepository.update(id, updateData);
        return this.toResponseDto(updatedContent);
      }
      
      // For other status transitions
      const updatedContent = await this.contentRepository.update(id, { status });
      return this.toResponseDto(updatedContent);
    } catch (error) {
      this.handleError(error, 'Failed to update content status');
      throw error;
    }
  }

  /**
   * Update content access level (admin)
   * 
   * @param id - ID of the content to update
   * @param accessLevel - New access level value
   * @returns Promise with the updated content
   */
  async updateAccessLevel(id: string, accessLevel: AccessLevel): Promise<ContentResponseDto> {
    try {
      // Verify content exists
      const existingContent = await this.contentRepository.findById(id);
      if (!existingContent) {
        throw new NotFoundError(`Content with ID ${id} not found`);
      }
      
      const updatedContent = await this.contentRepository.update(id, { accessLevel });
      return this.toResponseDto(updatedContent);
    } catch (error) {
      this.handleError(error, 'Failed to update content access level');
      throw error;
    }
  }

  /**
   * Check if a user has access to specific content
   * 
   * @param contentId - ID of the content to check
   * @param userId - ID of the user
   * @returns Promise with boolean indicating access permission
   */
  async checkAccess(contentId: string, userId: string): Promise<boolean> {
    try {
      if (!contentId) {
        throw new ValidationError('Content ID is required');
      }
      
      if (!userId) {
        throw new ValidationError('User ID is required');
      }
      
      // Get the content
      const content = await this.contentRepository.findById(contentId);
      if (!content) {
        throw new NotFoundError(`Content with ID ${contentId} not found`);
      }
      
      // If content is public, anyone can access
      if (content.accessLevel === AccessLevel.PUBLIC) {
        return true;
      }
      
      // If content is not published, only the author can access
      if (content.status !== ContentStatus.PUBLISHED) {
        return content.authorId === userId;
      }
      
      // For members-only content, check membership
      if (content.accessLevel === AccessLevel.MEMBERS_ONLY) {
        const activeMemberships = await this.membershipRepository.findActiveByUserId(userId);
        return activeMemberships.length > 0;
      }
      
      // For premium content, check premium membership
      if (content.accessLevel === AccessLevel.PREMIUM_MEMBERS) {
        const activeMemberships = await this.membershipRepository.findActiveByUserId(userId);
        
        // Check if any membership is premium (in a real app, you'd check tier level)
        // This is a simplified check
        return activeMemberships.some(membership => 
          membership.status === MembershipStatus.ACTIVE && 
          membership.membershipTierId !== null
        );
      }
      
      // Default to no access
      return false;
    } catch (error) {
      this.handleError(error, 'Failed to check content access');
      return false;
    }
  }

  /**
   * Get related content based on tags or category
   * 
   * @param contentId - ID of the reference content
   * @param limit - Number of related items to retrieve
   * @param userId - Optional user ID for access control
   * @returns Promise with array of related content items
   */
  async getRelated(contentId: string, limit: number, userId?: string): Promise<ContentResponseDto[]> {
    try {
      if (!contentId) {
        throw new ValidationError('Content ID is required');
      }
      
      // Get the reference content
      const content = await this.contentRepository.findById(contentId);
      if (!content) {
        throw new NotFoundError(`Content with ID ${contentId} not found`);
      }
      
      // Build filter for related content based on tags and category
      const filter: FilterOptions = {};
      filter['id'] = { not: contentId }; // Exclude the reference content
      filter['status'] = ContentStatus.PUBLISHED;
      filter['OR'] = [];
      
      // Add tags filter if available
      if (content.tags && (content.tags as string[]).length > 0) {
        filter['OR'].push({
          tags: { hasSome: content.tags as string[] }
        });
      }
      
      // Add category filter if available
      if (content.category) {
        filter['OR'].push({
          category: content.category
        });
      }
      
      // If no criteria for related content, return empty array
      if (filter['OR'].length === 0) {
        return [];
      }
      
      // Apply access control filter
      await this.applyAccessControlFilter(filter, userId);
      
      // Get the related content
      const pagination: PaginationOptions = {
        limit,
        page: 1
      };
      
      const relatedContent = await this.contentRepository.findMany(filter, pagination);
      return relatedContent.map(item => this.toResponseDto(item));
    } catch (error) {
      this.handleError(error, 'Failed to get related content');
      throw error;
    }
  }

  /**
   * Publish content (admin)
   * 
   * @param id - ID of the content to publish
   * @param publishDate - Optional specific publish date
   * @returns Promise with the published content
   */
  async publish(id: string, publishDate?: Date): Promise<ContentResponseDto> {
    try {
      // Verify content exists
      const existingContent = await this.contentRepository.findById(id);
      if (!existingContent) {
        throw new NotFoundError(`Content with ID ${id} not found`);
      }
      
      // If already published, just update the publishDate if provided
      if (existingContent.status === ContentStatus.PUBLISHED && publishDate) {
        const updatedContent = await this.contentRepository.update(id, { publishDate });
        return this.toResponseDto(updatedContent);
      }
      
      // Otherwise, update status and set publishDate
      const updateData = {
        status: ContentStatus.PUBLISHED,
        publishDate: publishDate || new Date()
      };
      
      const updatedContent = await this.contentRepository.update(id, updateData);
      return this.toResponseDto(updatedContent);
    } catch (error) {
      this.handleError(error, 'Failed to publish content');
      throw error;
    }
  }

  /**
   * Unpublish content (admin)
   * 
   * @param id - ID of the content to unpublish
   * @returns Promise with the unpublished content
   */
  async unpublish(id: string): Promise<ContentResponseDto> {
    try {
      // Verify content exists
      const existingContent = await this.contentRepository.findById(id);
      if (!existingContent) {
        throw new NotFoundError(`Content with ID ${id} not found`);
      }
      
      // Set status to draft
      const updatedContent = await this.contentRepository.update(id, {
        status: ContentStatus.DRAFT
      });
      
      return this.toResponseDto(updatedContent);
    } catch (error) {
      this.handleError(error, 'Failed to unpublish content');
      throw error;
    }
  }

  /**
   * Apply access control filter to a filter object
   * 
   * @private
   * @param filter - The filter to modify
   * @param userId - Optional user ID for access control
   */
  private async applyAccessControlFilter(filter: FilterOptions, userId?: string): Promise<void> {
    // If no user ID, only show public published content
    if (!userId) {
      filter['accessLevel'] = AccessLevel.PUBLIC;
      filter['status'] = ContentStatus.PUBLISHED;
      filter['publishDate'] = { lte: new Date() };
      return;
    }
    
    try {
      // Check user's memberships
      const activeMemberships = await this.membershipRepository.findActiveByUserId(userId);
      const hasMembership = activeMemberships.length > 0;
      
      // Check if user has a premium membership
      // In a real app, you'd check tier level. This is simplified.
      const hasPremiumMembership = activeMemberships.some(membership => 
        membership.status === MembershipStatus.ACTIVE && 
        membership.membershipTierId !== null
      );
      
      if (hasPremiumMembership) {
        // Premium users can see all content types
        // But still limit to published for non-owned content
        filter['OR'] = [
          { authorId: userId }, // User's own content regardless of status
          {
            status: ContentStatus.PUBLISHED,
            publishDate: { lte: new Date() }
          }
        ];
      } else if (hasMembership) {
        // Regular members can see public and members content
        filter['OR'] = [
          { authorId: userId }, // User's own content
          {
            status: ContentStatus.PUBLISHED,
            publishDate: { lte: new Date() },
            accessLevel: { in: [AccessLevel.PUBLIC, AccessLevel.MEMBERS_ONLY] }
          }
        ];
      } else {
        // Non-members can only see their own content and public content
        filter['OR'] = [
          { authorId: userId }, // User's own content
          {
            status: ContentStatus.PUBLISHED,
            publishDate: { lte: new Date() },
            accessLevel: AccessLevel.PUBLIC
          }
        ];
      }
    } catch (error) {
      // If there's an error checking membership, default to only public access
      console.error('Error checking membership for content access:', error);
      filter['OR'] = [
        { authorId: userId }, // User's own content
        {
          status: ContentStatus.PUBLISHED,
          publishDate: { lte: new Date() },
          accessLevel: AccessLevel.PUBLIC
        }
      ];
    }
  }

  /**
   * Handle errors from service operations
   * 
   * @private
   * @param error - The caught error
   * @param message - A message describing the operation that failed
   * @throws The appropriate error type
   */
  private handleError(error: unknown, message: string): never {
    // Log the error for debugging
    console.error(`${message}:`, error);
    
    // Re-throw domain errors
    if (
      error instanceof NotFoundError ||
      error instanceof ValidationError ||
      error instanceof AuthorizationError ||
      error instanceof BusinessLogicError ||
      error instanceof AppError
    ) {
      throw error;
    }
    
    // For unknown errors, throw a generic business logic error
    throw new BusinessLogicError(message);
  }

  /**
   * Get content by slug
   * 
   * @param slug - Content slug
   * @param userId - User ID (optional, for access control)
   * @returns Content item or null if not found/not accessible
   */
  async getContentBySlug(slug: string, userId?: string): Promise<ContentResponseDto | null> {
    return this.getBySlug(slug, userId);
  }

  /**
   * List content by category
   * 
   * @param category - Content category
   * @param userId - User ID (optional, for access control)
   * @param options - Query options
   * @returns Paginated list of content items
   */
  async listContentByCategory(
    category: string, 
    userId?: string, 
    options?: { page?: number; limit?: number; sort?: string }
  ): Promise<{
    data: ContentResponseDto[];
    total: number;
    page: number;
    limit: number;
  }> {
    const pagination: PaginationOptions = {
      page: options?.page || 1,
      limit: options?.limit || 10
    };
    
    // Create filter params for the filter method
    const filterParams: ContentFilterParamsDto = {
      category
    };
    
    // Use the existing filter method which handles sorting internally
    const result = await this.filter(filterParams, userId, pagination);
    
    return {
      data: result.data,
      total: result.meta.total,
      page: result.meta.page,
      limit: result.meta.limit
    };
  }

  /**
   * Create new content
   * 
   * @param data - Content creation data
   * @returns Created content item
   */
  async createContent(data: ContentCreateDto): Promise<ContentResponseDto> {
    return this.create(data);
  }

  /**
   * Update existing content
   * 
   * @param id - Content ID
   * @param data - Content update data
   * @returns Updated content item
   */
  async updateContent(id: string, data: ContentUpdateDto): Promise<ContentResponseDto> {
    return this.update(id, data);
  }

  /**
   * Delete content
   * 
   * @param id - Content ID
   */
  async deleteContent(id: string): Promise<void> {
    await this.delete(id);
  }
} 