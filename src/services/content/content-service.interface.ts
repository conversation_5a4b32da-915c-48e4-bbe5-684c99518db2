/**
 * Content Service Interface
 * 
 * This interface defines the standard operations for content management,
 * including retrieving, creating, updating, and deleting content items.
 */

import { BaseService } from '../base/base-service.interface.js';
import { AccessLevel, ContentStatus } from '@prisma/client';
import {
  ContentResponseDto,
  ContentCreateDto,
  ContentUpdateDto,
  ContentFilterParamsDto
} from '../../dtos/content.dto.js';
import { PaginatedResult, PaginationOptions } from '../../repositories/base/base-repository.interface.js';

/**
 * Content item model
 */
export interface Content {
  id: string;
  title: string;
  slug: string;
  content: string;
  category: string;
  authorId: string;
  accessLevel: string;
  status: string;
  metadata?: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Content creation options
 */
export interface ContentCreateOptions {
  title: string;
  slug: string;
  content: string;
  category: string;
  authorId: string;
  accessLevel?: string;
  status?: string;
  metadata?: Record<string, any>;
}

/**
 * Content update options
 */
export interface ContentUpdateOptions {
  title?: string;
  slug?: string;
  content?: string;
  category?: string;
  accessLevel?: string;
  status?: string;
  metadata?: Record<string, any>;
}

/**
 * Content query options
 */
export interface ContentQueryOptions {
  page?: number;
  limit?: number;
  sort?: string;
}

/**
 * Content service interface for content management operations
 */
export interface ContentServiceInterface extends BaseService<ContentResponseDto, ContentCreateDto, ContentUpdateDto, string> {
  /**
   * Get content by slug
   * 
   * @param slug - Content slug
   * @param userId - User ID (optional, for access control)
   * @returns Content item or null if not found/not accessible
   */
  getContentBySlug(slug: string, userId?: string): Promise<ContentResponseDto | null>;
  
  /**
   * List content by category
   * 
   * @param category - Content category
   * @param userId - User ID (optional, for access control)
   * @param options - Query options
   * @returns Paginated list of content items
   */
  listContentByCategory(category: string, userId?: string, options?: ContentQueryOptions): Promise<{
    data: ContentResponseDto[];
    total: number;
    page: number;
    limit: number;
  }>;
  
  /**
   * Create new content
   * 
   * @param data - Content creation data
   * @returns Created content item
   */
  createContent(data: ContentCreateDto): Promise<ContentResponseDto>;
  
  /**
   * Update existing content
   * 
   * @param id - Content ID
   * @param data - Content update data
   * @returns Updated content item
   */
  updateContent(id: string, data: ContentUpdateDto): Promise<ContentResponseDto>;
  
  /**
   * Delete content
   * 
   * @param id - Content ID
   */
  deleteContent(id: string): Promise<void>;
  
  /**
   * Get content by slug
   * 
   * @param slug - The slug of the content to retrieve
   * @param userId - Optional user ID for access control
   * @returns Promise with content or null if not found
   */
  getBySlug(slug: string, userId?: string): Promise<ContentResponseDto | null>;
  
  /**
   * List content by category
   * 
   * @param category - The category to filter by
   * @param userId - Optional user ID for access control
   * @param pagination - Optional pagination options
   * @returns Promise with paginated result of content items
   */
  getByCategory(category: string, userId?: string, pagination?: PaginationOptions): Promise<PaginatedResult<ContentResponseDto>>;
  
  /**
   * Search content
   * 
   * @param query - The search query
   * @param userId - Optional user ID for access control
   * @param pagination - Optional pagination options
   * @returns Promise with paginated result of content items
   */
  search(query: string, userId?: string, pagination?: PaginationOptions): Promise<PaginatedResult<ContentResponseDto>>;
  
  /**
   * Filter content by multiple criteria
   * 
   * @param filterParams - Filter parameters
   * @param userId - Optional user ID for access control
   * @param pagination - Optional pagination options
   * @returns Promise with paginated result of content items
   */
  filter(filterParams: ContentFilterParamsDto, userId?: string, pagination?: PaginationOptions): Promise<PaginatedResult<ContentResponseDto>>;
  
  /**
   * Get content by tags
   * 
   * @param tags - Array of tags to filter by
   * @param userId - Optional user ID for access control
   * @param pagination - Optional pagination options
   * @returns Promise with paginated result of content items
   */
  getByTags(tags: string[], userId?: string, pagination?: PaginationOptions): Promise<PaginatedResult<ContentResponseDto>>;
  
  /**
   * Get published content
   * 
   * @param userId - Optional user ID for access control
   * @param pagination - Optional pagination options
   * @returns Promise with paginated result of published content items
   */
  getPublished(userId?: string, pagination?: PaginationOptions): Promise<PaginatedResult<ContentResponseDto>>;
  
  /**
   * Get featured content
   * 
   * @param limit - Number of featured items to retrieve
   * @param userId - Optional user ID for access control
   * @returns Promise with array of featured content items
   */
  getFeatured(limit: number, userId?: string): Promise<ContentResponseDto[]>;
  
  /**
   * Update content status (admin)
   * 
   * @param id - ID of the content to update
   * @param status - New status value
   * @returns Promise with the updated content
   */
  updateStatus(id: string, status: ContentStatus): Promise<ContentResponseDto>;
  
  /**
   * Update content access level (admin)
   * 
   * @param id - ID of the content to update
   * @param accessLevel - New access level value
   * @returns Promise with the updated content
   */
  updateAccessLevel(id: string, accessLevel: AccessLevel): Promise<ContentResponseDto>;
  
  /**
   * Check if a user has access to specific content
   * 
   * @param contentId - ID of the content to check
   * @param userId - ID of the user
   * @returns Promise with boolean indicating access permission
   */
  checkAccess(contentId: string, userId: string): Promise<boolean>;
  
  /**
   * Get related content based on tags or category
   * 
   * @param contentId - ID of the reference content
   * @param limit - Number of related items to retrieve
   * @param userId - Optional user ID for access control
   * @returns Promise with array of related content items
   */
  getRelated(contentId: string, limit: number, userId?: string): Promise<ContentResponseDto[]>;
  
  /**
   * Publish content (admin)
   * 
   * @param id - ID of the content to publish
   * @param publishDate - Optional specific publish date
   * @returns Promise with the published content
   */
  publish(id: string, publishDate?: Date): Promise<ContentResponseDto>;
  
  /**
   * Unpublish content (admin)
   * 
   * @param id - ID of the content to unpublish
   * @returns Promise with the unpublished content
   */
  unpublish(id: string): Promise<ContentResponseDto>;
} 