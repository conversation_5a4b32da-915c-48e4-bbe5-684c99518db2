/**
 * Service Module
 *
 * This module serves as the central registry for all services in the application.
 * It exports all service interfaces and implementations, provides factory functions
 * to create services, and configures the dependency injection for service instances.
 *
 * This centralized approach simplifies service management and dependency injection
 * throughout the application.
 */

import {
  getRepositoryFactory,
  RepositoryFactory,
  createRepositoryProvider,
  getPrismaClient
} from '../repositories/index.js';

// Base services
export * from './base/base-service.interface.js';

// Auth services
export * from './auth/auth-service.interface.js';
export * from './auth/auth.service.js';

// User services
export * from './user/user-service.interface.js';
export * from './user/user.service.js';

// Membership services
export * from './membership/membership-service.interface.js';
export * from './membership/membership.service.js';

// Giveaway services
export * from './giveaway/giveaway-service.interface.js';
export * from './giveaway/giveaway.service.js';

// Prize services
export * from './prize/prize-service.interface.js';
export * from './prize/prize.service.js';

// Winner services
export * from './winner/winner-service.interface.js';
export * from './winner/winner.service.js';

// Payment services
export * from './payment/payment-service.interface.js';
export * from './payment/stripe-payment.service.js';

// Content services
export * from './content/content-service.interface.js';
export * from './content/content.service.js';

// Notification services
export * from './notification/notification-service.interface.js';
export * from './notification/notification.service.js';

// Admin services
export * from './admin/admin-service.interface.js';
export * from './admin/admin.service.js';

// Import service implementations
import { AuthServiceImpl } from './auth/auth.service.js';
import { UserServiceImpl } from './user/user.service.js';
import { MembershipServiceImpl } from './membership/membership.service.js';
import { GiveawayServiceImpl } from './giveaway/giveaway.service.js';
import { PrizeServiceImpl } from './prize/prize.service.js';
import { WinnerServiceImpl } from './winner/winner.service.js';
import { StripePaymentService } from './payment/stripe-payment.service.js';
import { ContentService } from './content/content.service.js';
import { NotificationService } from './notification/notification.service.js';
import { AdminService } from './admin/admin.service.js';

// Import service interfaces
import { AuthService } from './auth/auth-service.interface.js';
import { UserService } from './user/user-service.interface.js';
import { MembershipService } from './membership/membership-service.interface.js';
import { GiveawayService } from './giveaway/giveaway-service.interface.js';
import { PrizeService } from './prize/prize-service.interface.js';
import { WinnerService } from './winner/winner-service.interface.js';
import { PaymentServiceInterface } from './payment/payment-service.interface.js';
import { ContentServiceInterface } from './content/content-service.interface.js';
import { NotificationServiceInterface } from './notification/notification-service.interface.js';
import { AdminServiceInterface } from './admin/admin-service.interface.js';
import { EnrollmentService } from './enrollment.service.js';
import { EmailService } from './email/email-service.interface.js';
import { SendGridEmailService } from './email/sendgrid-email.service.js';

/**
 * Service factory interface
 * Defines the structure for factory objects that create services
 */
export interface ServiceFactory {
  createAuthService(): AuthService;
  createUserService(): UserService;
  createMembershipService(): MembershipService;
  createGiveawayService(): GiveawayService;
  createPrizeService(): PrizeService;
  createWinnerService(): WinnerService;
  createPaymentService(): PaymentServiceInterface;
  createContentService(): ContentServiceInterface;
  createNotificationService(): NotificationServiceInterface;
  createAdminService(): AdminServiceInterface;
  createEnrollmentService(): EnrollmentService;
  createEmailService(): EmailService;
}

/**
 * Implementation of the service factory
 * Creates service instances with proper dependency injection
 */
export class DefaultServiceFactory implements ServiceFactory {
  private repositories: ReturnType<typeof createRepositoryProvider>;
  private serviceCache: Map<string, any> = new Map();

  /**
   * Constructor
   * @param repositoryFactory Optional repository factory (will use default if not provided)
   */
  constructor(repositoryFactory?: RepositoryFactory) {
    this.repositories = createRepositoryProvider(repositoryFactory || getRepositoryFactory());
  }

  /**
   * Create EmailService
   * @returns EmailService implementation
   */
  createEmailService(): EmailService {
    if (!this.serviceCache.has('email')) {
      this.serviceCache.set(
        'email',
        new SendGridEmailService()
      );
    }
    return this.serviceCache.get('email');
  }

  /**
   * Create AuthService
   * @returns AuthService implementation
   */
  createAuthService(): AuthService {
    if (!this.serviceCache.has('auth')) {
      // Replace require with direct import from getPrismaClient
      const prisma = getPrismaClient();
      const emailService = this.createEmailService();

      this.serviceCache.set(
        'auth',
        new AuthServiceImpl(prisma, emailService)
      );
    }
    return this.serviceCache.get('auth');
  }

  /**
   * Create UserService
   * @returns UserService implementation
   */
  createUserService(): UserService {
    if (!this.serviceCache.has('user')) {
      this.serviceCache.set(
        'user',
        new UserServiceImpl(
          this.repositories.getUserRepository()
        )
      );
    }
    return this.serviceCache.get('user');
  }

  /**
   * Create MembershipService
   * @returns MembershipService implementation
   */
  createMembershipService(): MembershipService {
    if (!this.serviceCache.has('membership')) {
      this.serviceCache.set(
        'membership',
        new MembershipServiceImpl(
          this.repositories.getMembershipRepository(),
          this.repositories.getMembershipTierRepository(),
          this.repositories.getUserRepository()
        )
      );
    }
    return this.serviceCache.get('membership');
  }

  /**
   * Create GiveawayService
   * @returns GiveawayService implementation
   */
  createGiveawayService(): GiveawayService {
    if (!this.serviceCache.has('giveaway')) {
      this.serviceCache.set(
        'giveaway',
        new GiveawayServiceImpl(
          this.repositories.getGiveawayRepository(),
          this.repositories.getPrizeRepository(),
          this.repositories.getEntryRepository(),
          this.repositories.getWinnerRepository(),
          this.createMembershipService()
        )
      );
    }
    return this.serviceCache.get('giveaway');
  }

  /**
   * Create PrizeService
   * @returns PrizeService implementation
   */
  createPrizeService(): PrizeService {
    if (!this.serviceCache.has('prize')) {
      this.serviceCache.set(
        'prize',
        new PrizeServiceImpl(
          this.repositories.getPrizeRepository(),
          this.repositories.getWinnerRepository()
        )
      );
    }
    return this.serviceCache.get('prize');
  }

  /**
   * Create WinnerService
   * @returns WinnerService implementation
   */
  createWinnerService(): WinnerService {
    if (!this.serviceCache.has('winner')) {
      this.serviceCache.set(
        'winner',
        new WinnerServiceImpl(
          this.repositories.getWinnerRepository(),
          this.repositories.getUserRepository(),
          this.repositories.getPrizeRepository(),
          this.repositories.getGiveawayRepository(),
          this.repositories.getNotificationRepository()
        )
      );
    }
    return this.serviceCache.get('winner');
  }

  /**
   * Create PaymentService
   * @returns PaymentServiceInterface implementation
   */
  createPaymentService(): PaymentServiceInterface {
    if (!this.serviceCache.has('payment')) {
      const stripeSecretKey = process.env['STRIPE_SECRET_KEY'] || 'sk_test_default';

      this.serviceCache.set(
        'payment',
        new StripePaymentService(
          this.repositories.getTransactionRepository(),
          stripeSecretKey
        )
      );
    }
    return this.serviceCache.get('payment');
  }

  /**
   * Create ContentService
   * @returns ContentServiceInterface implementation
   */
  createContentService(): ContentServiceInterface {
    if (!this.serviceCache.has('content')) {
      this.serviceCache.set(
        'content',
        new ContentService(
          this.repositories.getContentRepository(),
          this.repositories.getMembershipRepository()
        )
      );
    }
    return this.serviceCache.get('content');
  }

  /**
   * Create NotificationService
   * @returns NotificationServiceInterface implementation
   */
  createNotificationService(): NotificationServiceInterface {
    if (!this.serviceCache.has('notification')) {
      this.serviceCache.set(
        'notification',
        new NotificationService(
          this.repositories.getNotificationRepository(),
          this.repositories.getUserRepository()
        )
      );
    }
    return this.serviceCache.get('notification');
  }

  /**
   * Create AdminService
   * @returns AdminServiceInterface implementation
   */
  createAdminService(): AdminServiceInterface {
    if (!this.serviceCache.has('admin')) {
      this.serviceCache.set(
        'admin',
        new AdminService(
          this.repositories.getUserRepository(),
          this.repositories.getMembershipRepository(),
          this.repositories.getMembershipTierRepository(),
          this.repositories.getGiveawayRepository(),
          this.repositories.getEntryRepository(),
          this.repositories.getWinnerRepository(),
          this.repositories.getTransactionRepository(),
          this.repositories.getContentRepository(),
          this.repositories.getSettingRepository(),
          this.repositories.getNotificationRepository()
        )
      );
    }
    return this.serviceCache.get('admin');
  }

  /**
   * Create EnrollmentService
   * @returns EnrollmentService implementation
   */
  createEnrollmentService(): EnrollmentService {
    if (!this.serviceCache.has('enrollment')) {
      this.serviceCache.set(
        'enrollment',
        new EnrollmentService()
      );
    }
    return this.serviceCache.get('enrollment');
  }
}

// Default service factory instance
const defaultServiceFactory = new DefaultServiceFactory();

/**
 * Get the default service factory
 * @returns Default ServiceFactory instance
 */
export function getServiceFactory(): ServiceFactory {
  return defaultServiceFactory;
}

/**
 * Dependency injection helper for controllers and other components
 * Creates a function to obtain services on demand
 * @param factory Service factory to use (uses default if not provided)
 * @returns Object with methods to get service instances
 */
export function createServiceProvider(factory: ServiceFactory = defaultServiceFactory) {
  return {
    getAuthService: () => factory.createAuthService(),
    getUserService: () => factory.createUserService(),
    getMembershipService: () => factory.createMembershipService(),
    getGiveawayService: () => factory.createGiveawayService(),
    getPrizeService: () => factory.createPrizeService(),
    getWinnerService: () => factory.createWinnerService(),
    getPaymentService: () => factory.createPaymentService(),
    getEmailService: () => factory.createEmailService(),
    getContentService: () => factory.createContentService(),
    getNotificationService: () => factory.createNotificationService(),
    getAdminService: () => factory.createAdminService(),
    getEnrollmentService: () => factory.createEnrollmentService()
  };
}

/**
 * Initialize all services
 * This ensures all services are created and properly initialized
 */
export function initializeServices(): void {
  const factory = getServiceFactory();

  // Initialize all services - this will trigger their creation and dependency injection
  factory.createEmailService();
  factory.createAuthService();
  factory.createUserService();
  factory.createMembershipService();
  factory.createGiveawayService();
  factory.createPrizeService();
  factory.createWinnerService();
  factory.createPaymentService();
  factory.createContentService();
  factory.createNotificationService();
  factory.createAdminService();
  factory.createEnrollmentService();

  // Log initialization in development mode
  if (process.env['NODE_ENV'] === 'development') {
    console.log('Service layer initialized');
  }
}