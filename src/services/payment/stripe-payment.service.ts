/**
 * Stripe Payment Service Implementation
 *
 * This service implements the Payment Service Interface using Stripe for payment processing.
 * It handles payment intents, payment processing, webhooks, transaction history, and refunds.
 */

import { Prisma, PaymentStatus, PaymentType, MembershipStatus } from '@prisma/client';
import Stripe from 'stripe';
import { TransactionRepository } from '../../repositories/transaction/transaction-repository.interface.js';
import {
  FilterOptions,
  PaginatedResult,
  PaginationOptions
} from '../../repositories/base/base-repository.interface.js';
import { PaymentServiceInterface, PaymentIntent, PaymentIntentOptions, PaymentProcessOptions, Transaction, TransactionQueryOptions, Refund, WebhookEvent } from './payment-service.interface.js';
import {
  NotFoundError,
  ValidationError,
  AuthorizationError,
  BusinessLogicError,
  AppError
} from '../../utils/errors.js';
import {
  TransactionFilterParamsDto,
  TransactionResponseDto
} from '../../dtos/transaction.dto.js';
import {
  PaymentMethodCreateDto,
  PaymentMethodResponseDto,
  PaymentMethodUpdateDto
} from '../../dtos/payment-method.dto.js';
import { PrismaClient } from '@prisma/client';

/**
 * StripePaymentService class implementing the payment service interface
 */
export class StripePaymentService implements PaymentServiceInterface {
  private stripe: Stripe;

  /**
   * Constructor for StripePaymentService
   *
   * @param transactionRepository - Repository for transaction data access
   * @param stripeSecretKey - Stripe API key
   */
  constructor(
    private readonly transactionRepository: TransactionRepository,
    stripeSecretKey: string
  ) {
    this.stripe = new Stripe(stripeSecretKey, {
      apiVersion: '2025-04-30.basil' // Use latest API version
    });
  }

  /**
   * Get a transaction by ID
   *
   * @param id - Transaction ID
   * @returns Promise with transaction response or null
   */
  async get(id: string): Promise<TransactionResponseDto | null> {
    try {
      const transaction = await this.transactionRepository.findById(id);

      if (!transaction) {
        return null;
      }

      return this.mapTransactionToDto(transaction);
    } catch (error) {
      this.handleError(error, 'Failed to get transaction');
      return null;
    }
  }

  /**
   * List transactions with optional filtering and pagination
   *
   * @param filter - Optional filter criteria
   * @param pagination - Optional pagination options
   * @returns Promise with array of transaction responses
   */
  async list(
    filter?: FilterOptions,
    pagination?: PaginationOptions
  ): Promise<TransactionResponseDto[]> {
    try {
      const transactions = await this.transactionRepository.findMany(filter, pagination);
      return transactions.map((transaction) => this.mapTransactionToDto(transaction));
    } catch (error) {
      this.handleError(error, 'Failed to list transactions');
      return [];
    }
  }

  /**
   * List transactions with pagination metadata
   *
   * @param filter - Optional filter criteria
   * @param pagination - Pagination options
   * @returns Promise with paginated result of transaction responses
   */
  async listPaginated(
    filter?: FilterOptions,
    pagination?: PaginationOptions
  ): Promise<PaginatedResult<TransactionResponseDto>> {
    try {
      const result = await this.transactionRepository.findManyPaginated(filter, pagination);

      return {
        data: result.data.map((transaction) => this.mapTransactionToDto(transaction)),
        meta: result.meta
      };
    } catch (error) {
      this.handleError(error, 'Failed to list paginated transactions');
      return {
        data: [],
        meta: {
          total: 0,
          page: 1,
          limit: 10,
          hasMore: false
        }
      };
    }
  }

  /**
   * Count transactions that match the filter criteria
   *
   * @param filter - Optional filter criteria
   * @returns Promise with the count
   */
  async count(filter?: FilterOptions): Promise<number> {
    try {
      return await this.transactionRepository.count(filter);
    } catch (error) {
      this.handleError(error, 'Failed to count transactions');
      return 0;
    }
  }

  /**
   * Create is not implemented for transactions as they are created through payment processing
   */
  async create(): Promise<TransactionResponseDto> {
    throw new BusinessLogicError('Transactions should be created through payment processing');
  }

  /**
   * Create many is not implemented for transactions
   */
  async createMany(): Promise<TransactionResponseDto[]> {
    throw new BusinessLogicError('Bulk transaction creation is not supported');
  }

  /**
   * Update is not implemented for transactions
   */
  async update(): Promise<TransactionResponseDto> {
    throw new BusinessLogicError('Direct transaction updates are not supported');
  }

  /**
   * Delete is not implemented for transactions
   */
  async delete(): Promise<void> {
    throw new BusinessLogicError('Transactions cannot be deleted');
  }

  /**
   * Check if a transaction exists
   *
   * @param id - Transaction ID
   * @returns Promise with boolean indicating existence
   */
  async exists(id: string): Promise<boolean> {
    try {
      return await this.transactionRepository.exists(id);
    } catch (error) {
      this.handleError(error, 'Failed to check transaction existence');
      return false;
    }
  }

  /**
   * Create a payment intent
   *
   * @param options - Payment intent options
   * @returns Payment intent with client secret
   */
  async createPaymentIntent(options: PaymentIntentOptions): Promise<PaymentIntent> {
    try {
      // Validate required fields
      if (!options.userId) throw new ValidationError('User ID is required');
      if (!options.planId) throw new ValidationError('planId is required');

      // Dynamically import the membership service factory
      const { getServiceFactory } = await import('../index.js');
      const membershipService = getServiceFactory().createMembershipService();
      const plan = await membershipService.getTierById(options.planId);
      if (!plan || !plan.isActive) {
        throw new NotFoundError('Membership plan not found or inactive');
      }

      // Fallback to USD if currency is missing
      const currency = (plan as any).currency || 'USD';

      // Create metadata
      const metadata: Record<string, any> = { userId: options.userId, planId: options.planId, ...options.metadata, planName: plan.name };

      // Create the payment intent via Stripe
      const paymentIntent = await this.stripe.paymentIntents.create({
        amount: Math.round(Number(plan.price) * 100), // Convert to cents
        currency: currency.toLowerCase(),
        // Configure payment methods to never redirect
        automatic_payment_methods: {
          enabled: true,
          allow_redirects: 'never',
        },
        // Add a return URL for any payment methods that might require it
        // This is a fallback and shouldn't be used with allow_redirects: 'never'
        confirm: false, // Don't confirm immediately
        description: options.description || `Membership plan: ${plan.name}`,
        metadata
      });

      return {
        id: paymentIntent.id,
        clientSecret: paymentIntent.client_secret || '',
        amount: paymentIntent.amount / 100, // Convert from cents
        currency: paymentIntent.currency,
        status: paymentIntent.status
      };
    } catch (error) {
      this.handleError(error, 'Failed to create payment intent');
      throw error;
    }
  }

  /**
   * Process a payment
   *
   * This method confirms a payment using a previously created payment intent.
   * The payment flow requires two steps:
   * 1. Create a payment intent using createPaymentIntent()
   * 2. Process the payment using this method with the payment intent ID and payment method ID
   *
   * @param userId - User ID
   * @param options - Payment processing options
   * @returns Transaction details
   */
  async processPayment(userId: string, options: PaymentProcessOptions): Promise<Transaction> {
    try {
      // Validate required fields
      if (!userId) throw new ValidationError('User ID is required');
      if (!options.paymentMethodId) throw new ValidationError('Payment method ID is required');
      if (!options.paymentIntentId) throw new ValidationError('Payment intent ID is required. Please create a payment intent first using createPaymentIntent().');

      let paymentIntent;

      // Retrieve and confirm the payment intent
      paymentIntent = await this.stripe.paymentIntents.retrieve(options.paymentIntentId);

      // Check if payment has already been processed
      if (paymentIntent.status === 'succeeded') {
        throw new BusinessLogicError('Payment has already been processed');
      }

      // Confirm the payment intent with the provided payment method
      paymentIntent = await this.stripe.paymentIntents.confirm(options.paymentIntentId, {
        payment_method: options.paymentMethodId
      });

      // Create a transaction record in our system
      const transaction = await this.transactionRepository.create({
        userId,
        membershipId: options.metadata && options.metadata['membershipId'] ? options.metadata['membershipId'] : null,
        amount: new Prisma.Decimal(paymentIntent.amount / 100), // Convert from cents
        currency: paymentIntent.currency,
        status: this.mapStripeStatusToPaymentStatus(paymentIntent.status),
        paymentMethod: options.paymentMethodId,
        paymentIntentId: paymentIntent.id,
        description: paymentIntent.description || 'Payment processed',
        metadata: options.metadata || {}
      });

      // --- Membership Activation Logic ---
      // Only activate membership if payment succeeded
      if (this.mapStripeStatusToPaymentStatus(paymentIntent.status) === PaymentStatus.COMPLETED) {
        // Extract planId from metadata
        const planId = (paymentIntent.metadata && paymentIntent.metadata['planId']) || (options.metadata && options.metadata['planId']);
        if (planId) {
          // First, ensure we have a PaymentMethod record in our database for this Stripe payment method
          const prisma = new PrismaClient();

          // Check if a payment method with this Stripe ID already exists
          let paymentMethod = await prisma.paymentMethod.findFirst({
            where: {
              userId,
              stripePaymentMethodId: options.paymentMethodId
            }
          });

          // If not, create a new payment method record
          if (!paymentMethod) {
            try {
              // Retrieve payment method details from Stripe
              const stripePaymentMethod = await this.stripe.paymentMethods.retrieve(options.paymentMethodId);

              // Extract card details if available
              let last4 = null;
              let brand = null;
              let expiryMonth = null;
              let expiryYear = null;

              if (stripePaymentMethod.type === 'card' && stripePaymentMethod.card) {
                last4 = stripePaymentMethod.card.last4;
                brand = stripePaymentMethod.card.brand;
                expiryMonth = stripePaymentMethod.card.exp_month;
                expiryYear = stripePaymentMethod.card.exp_year;
              }

              // Create payment method record
              paymentMethod = await prisma.paymentMethod.create({
                data: {
                  userId,
                  type: PaymentType.CREDIT_CARD,
                  stripePaymentMethodId: options.paymentMethodId,
                  last4,
                  brand,
                  expiryMonth,
                  expiryYear,
                  isDefault: true // Make this the default payment method
                }
              });

              // Update other payment methods to not be default
              await prisma.paymentMethod.updateMany({
                where: {
                  userId,
                  id: { not: paymentMethod.id }
                },
                data: {
                  isDefault: false
                }
              });
            } catch (error) {
              console.error('Error creating payment method record:', error);
              // Continue with the process even if payment method creation fails
            }
          }

          await prisma.$disconnect();

          // Dynamically import the membership service factory to avoid circular deps
          const { getServiceFactory } = await import('../index.js');
          const membershipService = getServiceFactory().createMembershipService();

          // Idempotency: check if a membership already exists for this user and plan
          const userWithMembership = await membershipService.getUserWithMembership(userId);
          let alreadyActive = false;
          let membership = userWithMembership && userWithMembership.membership;
          // If membership is an array, get the first element
          if (Array.isArray(membership)) membership = membership[0];
          if (membership) {
            if (membership.membershipTierId === planId && membership.status === MembershipStatus.ACTIVE) {
              alreadyActive = true;
            }
          }
          if (!alreadyActive) {
            // Only pass paymentMethodId if it exists
            const subscriptionData: any = {
              userId,
              tierId: planId,
              autoRenew: true
            };

            // Only add paymentMethodId if it exists
            if (paymentMethod) {
              subscriptionData.paymentMethodId = paymentMethod.id;
            }

            await membershipService.subscribe(subscriptionData);
          }
        }
      }

      return {
        id: transaction.id,
        userId: transaction.userId,
        amount: Number(transaction.amount),
        currency: transaction.currency,
        status: transaction.status,
        paymentIntentId: transaction.paymentIntentId || '',
        paymentMethodId: transaction.paymentMethod,
        description: transaction.description,
        metadata: transaction.metadata ? transaction.metadata as Record<string, any> : {},
        createdAt: transaction.createdAt,
        updatedAt: transaction.updatedAt
      };
    } catch (error) {
      this.handleError(error, 'Failed to process payment');
      throw error;
    }
  }

  /**
   * Map Stripe payment intent status to our internal payment status
   *
   * @param stripeStatus - The Stripe payment intent status
   * @returns The corresponding internal payment status
   * @private
   */
  private mapStripeStatusToPaymentStatus(stripeStatus: string): PaymentStatus {
    switch (stripeStatus) {
      case 'succeeded':
        return PaymentStatus.COMPLETED;
      case 'processing':
        return PaymentStatus.PENDING; // Use PENDING as fallback for PROCESSING
      case 'requires_payment_method':
      case 'requires_confirmation':
      case 'requires_action':
      case 'requires_capture':
        return PaymentStatus.PENDING;
      case 'canceled':
        return PaymentStatus.FAILED; // Use FAILED as fallback for CANCELLED
      default:
        return PaymentStatus.FAILED;
    }
  }

  /**
   * Handle webhook events from Stripe
   *
   * @param event - The webhook event data
   * @returns Promise with boolean indicating successful processing
   */
  async handleWebhook(event: Record<string, any>): Promise<boolean> {
    try {
      // Validate the event
      if (!event || !('type' in event)) throw new ValidationError('Invalid webhook event');

      // Handle different event types
      switch (event['type']) {
        case 'payment_intent.succeeded':
          await this.handlePaymentIntentSucceeded(event['data'].object);
          return true;

        case 'payment_intent.payment_failed':
          await this.handlePaymentIntentFailed(event['data'].object);
          return true;

        case 'charge.refunded':
          await this.handleChargeRefunded(event['data'].object);
          return true;

        default:
          // Log but don't throw an error for unhandled events
          console.log(`Unhandled webhook event type: ${event['type']}`);
          return true;
      }
    } catch (error) {
      this.handleError(error, 'Failed to process webhook event');
      return false;
    }
  }

  /**
   * Get transaction history for a user
   *
   * @param userId - ID of the user
   * @param filter - Optional filter parameters
   * @returns Promise with array of transaction responses
   */
  async getTransactionHistory(userId: string, filter?: TransactionFilterParamsDto): Promise<TransactionResponseDto[]> {
    try {
      // Validate required fields
      if (!userId) throw new ValidationError('User ID is required');

      // Combine user ID with filter params
      const combinedFilter: FilterOptions = {
        ...filter,
        userId
      };

      // Get transactions from repository
      const transactions = await this.transactionRepository.findMany(combinedFilter);

      return transactions.map((transaction) => this.mapTransactionToDto(transaction));
    } catch (error) {
      this.handleError(error, 'Failed to get transaction history');
      throw error;
    }
  }

  /**
   * Process a refund
   *
   * @param transactionId - Transaction ID
   * @param amount - Refund amount (optional, defaults to full amount)
   * @param reason - Refund reason (optional)
   * @returns Refund details
   */
  async processRefund(transactionId: string, amount?: number, reason?: string): Promise<Refund> {
    try {
      // Validate required fields
      if (!transactionId) throw new ValidationError('Transaction ID is required');

      // Get the transaction
      const transaction = await this.transactionRepository.findById(transactionId);

      if (!transaction) {
        throw new NotFoundError('Transaction not found');
      }

      if (!transaction.paymentIntentId) {
        throw new BusinessLogicError('Transaction has no associated payment intent');
      }

      // Process the refund through Stripe
      const refundParams: Stripe.RefundCreateParams = {
        payment_intent: transaction.paymentIntentId
      };

      // Add optional parameters if provided
      if (amount) {
        refundParams.amount = Math.round(amount * 100); // Convert to cents
      }

      if (reason) {
        refundParams.reason = reason as Stripe.RefundCreateParams.Reason;
      }

      const refund = await this.stripe.refunds.create(refundParams);

      // Update the transaction status
      // Since PARTIALLY_REFUNDED is not in the enum, use REFUNDED for all cases
      await this.transactionRepository.update(transaction.id, {
        status: PaymentStatus.REFUNDED
      });

      return {
        id: refund.id,
        transactionId: transaction.id,
        amount: (refund.amount || 0) / 100, // Convert from cents
        status: refund.status || 'unknown',
        reason: reason || '',
        createdAt: new Date(refund.created * 1000)
      };
    } catch (error) {
      this.handleError(error, 'Failed to process refund');
      throw error;
    }
  }

  /**
   * Handle payment intent succeeded event
   *
   * @param paymentIntent - The payment intent object from Stripe
   * @private
   */
  private async handlePaymentIntentSucceeded(paymentIntent: any): Promise<void> {
    // Find transaction by payment intent ID
    const transactions = await this.transactionRepository.findMany({
      paymentIntentId: paymentIntent.id
    }, { limit: 1 });

    const transaction = transactions.length > 0 ? transactions[0] : null;
    let userId = paymentIntent.metadata && paymentIntent.metadata['userId'];
    let planId = paymentIntent.metadata && paymentIntent.metadata['planId'];
    let paymentMethodId = paymentIntent.payment_method || (transaction ? transaction.paymentMethod : undefined);

    if (transaction) {
      // Update transaction status if needed
      if (transaction.status !== PaymentStatus.COMPLETED) {
        await this.transactionRepository.update(transaction.id, {
          status: PaymentStatus.COMPLETED
        });
      }
    } else {
      // Create a new transaction if one doesn't exist
      if (userId) {
        await this.transactionRepository.create({
          userId,
          membershipId: paymentIntent.metadata && paymentIntent.metadata['membershipId'] ? paymentIntent.metadata['membershipId'] : null,
          amount: new Prisma.Decimal(paymentIntent.amount / 100), // Convert from cents
          currency: paymentIntent.currency,
          status: PaymentStatus.COMPLETED,
          paymentMethod: paymentIntent.payment_method || 'unknown',
          paymentIntentId: paymentIntent.id,
          description: paymentIntent.description || 'Payment processed',
          metadata: paymentIntent.metadata || {}
        });
      }
    }

    // --- Membership Activation Logic (Webhook) ---
    // Only activate membership if payment succeeded and we have userId and planId
    if (userId && planId) {
      // First, ensure we have a PaymentMethod record in our database for this Stripe payment method
      let internalPaymentMethodId;
      if (paymentMethodId) {
        const prisma = new PrismaClient();

        // Check if a payment method with this Stripe ID already exists
        let paymentMethod = await prisma.paymentMethod.findFirst({
          where: {
            userId,
            stripePaymentMethodId: paymentMethodId
          }
        });

        // If not, create a new payment method record
        if (!paymentMethod) {
          try {
            // Retrieve payment method details from Stripe
            const stripePaymentMethod = await this.stripe.paymentMethods.retrieve(paymentMethodId);

            // Extract card details if available
            let last4 = null;
            let brand = null;
            let expiryMonth = null;
            let expiryYear = null;

            if (stripePaymentMethod.type === 'card' && stripePaymentMethod.card) {
              last4 = stripePaymentMethod.card.last4;
              brand = stripePaymentMethod.card.brand;
              expiryMonth = stripePaymentMethod.card.exp_month;
              expiryYear = stripePaymentMethod.card.exp_year;
            }

            // Create payment method record
            paymentMethod = await prisma.paymentMethod.create({
              data: {
                userId,
                type: PaymentType.CREDIT_CARD,
                stripePaymentMethodId: paymentMethodId,
                last4,
                brand,
                expiryMonth,
                expiryYear,
                isDefault: true // Make this the default payment method
              }
            });

            // Update other payment methods to not be default
            await prisma.paymentMethod.updateMany({
              where: {
                userId,
                id: { not: paymentMethod.id }
              },
              data: {
                isDefault: false
              }
            });
          } catch (error) {
            console.error('Error creating payment method record:', error);
            // Continue with the process even if payment method creation fails
          }
        }

        if (paymentMethod) {
          internalPaymentMethodId = paymentMethod.id;
        }

        await prisma.$disconnect();
      }

      // Dynamically import the membership service factory to avoid circular deps
      const { getServiceFactory } = await import('../index.js');
      const membershipService = getServiceFactory().createMembershipService();

      // Idempotency: check if a membership already exists for this user and plan
      const userWithMembership = await membershipService.getUserWithMembership(userId);
      let alreadyActive = false;
      let membership = userWithMembership && userWithMembership.membership;
      if (Array.isArray(membership)) membership = membership[0];
      if (membership) {
        if (membership.membershipTierId === planId && membership.status === MembershipStatus.ACTIVE) {
          alreadyActive = true;
        }
      }
      if (!alreadyActive) {
        // Only pass paymentMethodId if it exists
        const subscriptionData: any = {
          userId,
          tierId: planId,
          autoRenew: true
        };

        // Only add paymentMethodId if it exists
        if (internalPaymentMethodId) {
          subscriptionData.paymentMethodId = internalPaymentMethodId;
        }

        await membershipService.subscribe(subscriptionData);
      }
    }
  }

  /**
   * Handle payment intent failed event
   *
   * @param paymentIntent - The payment intent object from Stripe
   * @private
   */
  private async handlePaymentIntentFailed(paymentIntent: any): Promise<void> {
    // Find transaction by payment intent ID
    const transactions = await this.transactionRepository.findMany({
      paymentIntentId: paymentIntent.id
    }, { limit: 1 });

    const transaction = transactions.length > 0 ? transactions[0] : null;

    if (transaction) {
      // Update transaction status
      await this.transactionRepository.update(transaction.id, {
        status: PaymentStatus.FAILED
      });
    }
  }

  /**
   * Handle charge refunded event
   *
   * @param charge - The charge object from Stripe
   * @private
   */
  private async handleChargeRefunded(charge: any): Promise<void> {
    // Find transaction by payment intent ID
    const transactions = await this.transactionRepository.findMany({
      paymentIntentId: charge.payment_intent
    }, { limit: 1 });

    const transaction = transactions.length > 0 ? transactions[0] : null;

    if (transaction) {
      // Since we don't have PARTIALLY_REFUNDED, use REFUNDED for all cases
      await this.transactionRepository.update(transaction.id, {
        status: PaymentStatus.REFUNDED
      });
    }
  }

  /**
   * Map a transaction entity to a DTO
   *
   * @param transaction - The transaction entity
   * @returns TransactionResponseDto
   * @private
   */
  private mapTransactionToDto(transaction: any): TransactionResponseDto {
    return {
      id: transaction.id,
      createdAt: transaction.createdAt,
      updatedAt: transaction.updatedAt,
      userId: transaction.userId,
      membershipId: transaction.membershipId || undefined,
      amount: Number(transaction.amount),
      currency: transaction.currency,
      status: transaction.status,
      paymentMethod: transaction.paymentMethod,
      paymentIntentId: transaction.paymentIntentId || undefined,
      description: transaction.description || undefined,
      metadata: transaction.metadata || {}
    };
  }

  /**
   * Handle errors from service operations
   *
   * @param error - The caught error
   * @param message - A message describing the operation that failed
   * @throws The appropriate error type
   * @private
   */
  private handleError(error: any, message: string): never {
    // Log the error for debugging
    console.error(`${message}:`, error);

    // Re-throw domain errors
    if (
      error instanceof NotFoundError ||
      error instanceof ValidationError ||
      error instanceof AuthorizationError ||
      error instanceof BusinessLogicError ||
      error instanceof AppError
    ) {
      throw error;
    }

    // Handle Stripe-specific errors
    if (error.type && typeof error.type === 'string' && error.type.startsWith('Stripe')) {
      // Map Stripe error types to domain errors
      switch (error.type) {
        case 'StripeCardError':
          throw new ValidationError(`Payment failed: ${error.message}`);
        case 'StripeRateLimitError':
          throw new BusinessLogicError('Too many requests to payment processor');
        case 'StripeInvalidRequestError':
          throw new ValidationError(`Invalid payment request: ${error.message}`);
        case 'StripeAuthenticationError':
          throw new AuthorizationError('Failed to authenticate with payment processor');
        default:
          throw new BusinessLogicError(`Payment processing error: ${error.message}`);
      }
    }

    // For unknown errors, throw a generic business logic error
    throw new BusinessLogicError(message);
  }

  /**
   * Get transactions for a user
   *
   * @param userId - User ID
   * @param options - Query options
   * @returns List of transactions with pagination
   */
  async getTransactionsByUser(
    userId: string,
    options?: TransactionQueryOptions
  ): Promise<{
    data: Transaction[];
    total: number;
    page: number;
    limit: number;
  }> {
    try {
      if (!userId) throw new ValidationError('User ID is required');

      const page = options && options['page'] ? options['page'] : 1;
      const limit = options && options['limit'] ? options['limit'] : 10;
      const status = options && options['status'] ? options['status'] : undefined;

      const filter: any = { userId };
      if (status) {
        filter.status = status;
      }

      const pagination = { page, limit };

      const result = await this.transactionRepository.findManyPaginated(filter, pagination);

      // Convert to Transaction type
      const transactions = result.data.map(t => ({
        id: t.id,
        userId: t.userId,
        amount: Number(t.amount),
        currency: t.currency,
        status: t.status,
        paymentIntentId: t.paymentIntentId || '',
        paymentMethodId: t.paymentMethod || '',
        description: t.description,
        metadata: t.metadata ? t.metadata as Record<string, any> : {},
        refunded: t.status === PaymentStatus.REFUNDED,
        createdAt: t.createdAt,
        updatedAt: t.updatedAt
      })) as Transaction[];

      return {
        data: transactions,
        total: result.meta ? result.meta.total : 0,
        page: result.meta ? result.meta.page : page,
        limit: result.meta ? result.meta.limit : limit
      };
    } catch (error) {
      this.handleError(error, 'Failed to get transactions for user');
      throw error;
    }
  }

  /**
   * Get transaction details
   *
   * @param transactionId - Transaction ID
   * @param userId - User ID (for authorization)
   * @returns Transaction details or null if not found
   */
  async getTransactionDetails(
    transactionId: string,
    userId: string
  ): Promise<Transaction | null> {
    try {
      if (!transactionId) throw new ValidationError('Transaction ID is required');

      const transaction = await this.transactionRepository.findById(transactionId);

      if (!transaction) {
        return null;
      }

      // Check if the user is authorized to view this transaction
      if (transaction.userId !== userId) {
        throw new AuthorizationError('You are not authorized to view this transaction');
      }

      return {
        id: transaction.id,
        userId: transaction.userId,
        amount: Number(transaction.amount),
        currency: transaction.currency,
        status: transaction.status,
        paymentIntentId: transaction.paymentIntentId || '',
        paymentMethodId: transaction.paymentMethod || '',
        description: transaction.description,
        metadata: transaction.metadata ? transaction.metadata as Record<string, any> : {},
        refunded: transaction.status === PaymentStatus.REFUNDED,
        createdAt: transaction.createdAt,
        updatedAt: transaction.updatedAt
      };
    } catch (error) {
      this.handleError(error, 'Failed to get transaction details');
      throw error;
    }
  }

  /**
   * Process a webhook event
   *
   * @param payload - Raw webhook payload
   * @param signature - Webhook signature for verification
   * @returns Processed webhook event
   */
  async processWebhook(payload: any, signature: string): Promise<WebhookEvent> {
    try {
      if (!payload) throw new ValidationError('Webhook payload is required');
      if (!signature) throw new ValidationError('Webhook signature is required');

      // Create a Stripe webhook event
      const event = this.stripe.webhooks.constructEvent(
        payload,
        signature,
        process.env['STRIPE_WEBHOOK_SECRET'] || ''
      );

      // Process different event types
      switch (event.type) {
        case 'payment_intent.succeeded':
          await this.handlePaymentIntentSucceeded(event.data.object);
          break;
        case 'payment_intent.payment_failed':
          await this.handlePaymentIntentFailed(event.data.object);
          break;
        case 'charge.refunded':
          await this.handleChargeRefunded(event.data.object);
          break;
        // Handle other event types as needed
      }

      return {
        id: event.id,
        type: event.type,
        data: event.data.object
      };
    } catch (error) {
      this.handleError(error, 'Failed to process webhook');
      throw error;
    }
  }

  /**
   * Create a setup intent for adding a payment method
   *
   * @param userId - User ID
   * @returns Promise with the setup intent client secret
   */
  async createSetupIntent(userId: string): Promise<{ clientSecret: string }> {
    try {
      if (!userId) throw new ValidationError('User ID is required');

      const setupIntent = await this.stripe.setupIntents.create({
        usage: 'off_session', // Allow the payment method to be used for future payments
        metadata: { userId }
      });

      return {
        clientSecret: setupIntent.client_secret || ''
      };
    } catch (error) {
      this.handleError(error, 'Failed to create setup intent');
      throw error;
    }
  }

  /**
   * Create a payment method in the database
   *
   * @param userId - User ID
   * @param data - Payment method data
   * @returns Promise with the created payment method
   */
  async createPaymentMethod(userId: string, data: PaymentMethodCreateDto): Promise<PaymentMethodResponseDto> {
    try {
      if (!userId) throw new ValidationError('User ID is required');
      if (!data.stripePaymentMethodId) throw new ValidationError('Stripe payment method ID is required');

      // Verify the payment method exists in Stripe and belongs to the user
      const stripePaymentMethod = await this.stripe.paymentMethods.retrieve(data.stripePaymentMethodId);

      // Extract details to store in our database
      let last4 = null;
      let brand = null;
      let expiryMonth = null;
      let expiryYear = null;

      if (stripePaymentMethod.type === 'card' && stripePaymentMethod.card) {
        last4 = stripePaymentMethod.card.last4;
        brand = stripePaymentMethod.card.brand;
        expiryMonth = stripePaymentMethod.card.exp_month;
        expiryYear = stripePaymentMethod.card.exp_year;
      }

      // Create the payment method in our database
      const prisma = new PrismaClient();

      // Use type assertions for the entire operation
      const createData: any = {
        userId,
        type: PaymentType.CREDIT_CARD, // Default to credit card
        stripePaymentMethodId: data.stripePaymentMethodId,
        last4,
        brand,
        expiryMonth,
        expiryYear,
        isDefault: data.isDefault || false
      };

      const paymentMethod: any = await prisma.paymentMethod.create({
        data: createData
      });

      // If this is set as default, update all other payment methods to not be default
      if (data.isDefault) {
        await prisma.paymentMethod.updateMany({
          where: {
            userId,
            id: { not: paymentMethod.id }
          },
          data: {
            isDefault: false
          }
        });
      }

      await prisma.$disconnect();

      // Create a response object directly from known values
      return {
        id: paymentMethod.id,
        userId: paymentMethod.userId,
        type: paymentMethod.type,
        stripePaymentMethodId: data.stripePaymentMethodId,
        isDefault: Boolean(paymentMethod.isDefault),
        createdAt: paymentMethod.createdAt,
        updatedAt: paymentMethod.updatedAt,
        ...(last4 && { last4 }),
        ...(brand && { brand }),
        ...(expiryMonth && { expiryMonth }),
        ...(expiryYear && { expiryYear })
      };
    } catch (error) {
      this.handleError(error, 'Failed to create payment method');
      throw error;
    }
  }

  /**
   * Get payment methods for a user
   *
   * @param userId - User ID
   * @returns Promise with the list of payment methods
   */
  async getPaymentMethods(userId: string): Promise<PaymentMethodResponseDto[]> {
    try {
      if (!userId) throw new ValidationError('User ID is required');

      const prisma = new PrismaClient();
      const paymentMethods: any[] = await prisma.paymentMethod.findMany({
        where: { userId }
      });

      await prisma.$disconnect();

      // Map the payment methods to the response DTO format
      return paymentMethods.map(pm => ({
        id: pm.id,
        userId: pm.userId,
        type: pm.type,
        stripePaymentMethodId: pm.stripePaymentMethodId,
        isDefault: pm.isDefault,
        createdAt: pm.createdAt,
        updatedAt: pm.updatedAt,
        ...(pm.last4 && { last4: pm.last4 }),
        ...(pm.brand && { brand: pm.brand }),
        ...(pm.expiryMonth && { expiryMonth: pm.expiryMonth }),
        ...(pm.expiryYear && { expiryYear: pm.expiryYear }),
        ...(pm.holderName && { holderName: pm.holderName })
      }));
    } catch (error) {
      this.handleError(error, 'Failed to get payment methods');
      throw error;
    }
  }

  /**
   * Update a payment method
   *
   * @param userId - User ID
   * @param paymentMethodId - Payment method ID
   * @param data - Updated payment method data
   * @returns Promise with the updated payment method
   */
  async updatePaymentMethod(userId: string, paymentMethodId: string, data: PaymentMethodUpdateDto): Promise<PaymentMethodResponseDto> {
    try {
      if (!userId) throw new ValidationError('User ID is required');
      if (!paymentMethodId) throw new ValidationError('Payment method ID is required');

      const prisma = new PrismaClient();

      // Verify payment method belongs to the user
      const existingPaymentMethod: any = await prisma.paymentMethod.findUnique({
        where: { id: paymentMethodId }
      });

      if (!existingPaymentMethod) {
        throw new NotFoundError('Payment method not found');
      }

      if (existingPaymentMethod.userId !== userId) {
        throw new AuthorizationError('You are not authorized to update this payment method');
      }

      // Create a data object with only the fields that exist in the database schema
      const updateData: any = {};
      if (data.isDefault !== undefined) updateData.isDefault = data.isDefault;
      if (data.holderName !== undefined) updateData.holderName = data.holderName;

      // Update the payment method
      const paymentMethod: any = await prisma.paymentMethod.update({
        where: { id: paymentMethodId },
        data: updateData
      });

      // If this is set as default, update all other payment methods to not be default
      if (data.isDefault) {
        await prisma.paymentMethod.updateMany({
          where: {
            userId,
            id: { not: paymentMethodId }
          },
          data: {
            isDefault: false
          }
        });
      }

      await prisma.$disconnect();

      // Return a properly formatted response object
      return {
        id: paymentMethod.id,
        userId: paymentMethod.userId,
        type: paymentMethod.type,
        stripePaymentMethodId: paymentMethod.stripePaymentMethodId,
        isDefault: paymentMethod.isDefault,
        createdAt: paymentMethod.createdAt,
        updatedAt: paymentMethod.updatedAt,
        ...(paymentMethod.last4 && { last4: paymentMethod.last4 }),
        ...(paymentMethod.brand && { brand: paymentMethod.brand }),
        ...(paymentMethod.expiryMonth && { expiryMonth: paymentMethod.expiryMonth }),
        ...(paymentMethod.expiryYear && { expiryYear: paymentMethod.expiryYear }),
        ...(paymentMethod.holderName && { holderName: paymentMethod.holderName })
      };
    } catch (error) {
      this.handleError(error, 'Failed to update payment method');
      throw error;
    }
  }

  /**
   * Delete a payment method
   *
   * @param userId - User ID
   * @param paymentMethodId - Payment method ID
   * @returns Promise with boolean indicating success
   */
  async deletePaymentMethod(userId: string, paymentMethodId: string): Promise<boolean> {
    try {
      if (!userId) throw new ValidationError('User ID is required');
      if (!paymentMethodId) throw new ValidationError('Payment method ID is required');

      const prisma = new PrismaClient();

      // Verify payment method belongs to the user
      const paymentMethod = await prisma.paymentMethod.findUnique({
        where: { id: paymentMethodId }
      });

      if (!paymentMethod) {
        throw new NotFoundError('Payment method not found');
      }

      if (paymentMethod.userId !== userId) {
        throw new AuthorizationError('You are not authorized to delete this payment method');
      }

      // Delete the payment method from Stripe first
      /*
      try {
        await this.stripe.paymentMethods.detach(paymentMethod.stripePaymentMethodId);
      } catch (stripeError) {
        console.error('Error detaching payment method from Stripe:', stripeError);
        // Continue with deletion in our database
      }*/

      // Delete the payment method from our database
      await prisma.paymentMethod.delete({
        where: { id: paymentMethodId }
      });

      await prisma.$disconnect();

      return true;
    } catch (error) {
      this.handleError(error, 'Failed to delete payment method');
      throw error;
    }
  }
}