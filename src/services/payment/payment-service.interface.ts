/**
 * Payment Service Interface
 *
 * This interface defines the standard operations for payment processing,
 * including creating payment intents, processing payments, handling webhooks,
 * managing transaction history, and processing refunds.
 */

import { BaseService } from '../base/base-service.interface.js';
import {
  TransactionResponseDto
} from '../../dtos/transaction.dto.js';
import {
  PaymentMethodCreateDto,
  PaymentMethodResponseDto,
  PaymentMethodUpdateDto
} from '../../dtos/payment-method.dto.js';

/**
 * Payment Intent creation options
 */
export interface PaymentIntentOptions {
  planId: string;
  description?: string;
  userId: string;
  metadata?: Record<string, any>;
}

/**
 * Payment processing options
 *
 * Options for processing a payment with an existing payment intent.
 * The payment intent must be created first using createPaymentIntent.
 */
export interface PaymentProcessOptions {
  paymentMethodId: string;   // Required - ID of the payment method to use
  paymentIntentId: string;   // Required - ID of the previously created payment intent
  metadata?: Record<string, any>; // Optional metadata for the transaction
}

/**
 * Transaction query options
 */
export interface TransactionQueryOptions {
  page?: number;
  limit?: number;
  status?: string;
}

/**
 * Transaction model
 */
export interface Transaction {
  id: string;
  userId: string;
  amount: number;
  currency: string;
  status: string;
  paymentIntentId: string;
  paymentMethodId: string;
  description?: string;
  metadata?: Record<string, any>;
  refunded?: boolean;
  refundAmount?: number;
  refundReason?: string;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Payment Intent model
 */
export interface PaymentIntent {
  id: string;
  clientSecret: string;
  amount: number;
  currency: string;
  status: string;
}

/**
 * Refund model
 */
export interface Refund {
  id: string;
  transactionId: string;
  amount: number;
  status: string;
  reason?: string;
  createdAt: Date;
}

/**
 * Webhook event
 */
export interface WebhookEvent {
  id: string;
  type: string;
  data: any;
}

/**
 * Payment service interface for payment processing operations
 */
export interface PaymentServiceInterface extends BaseService<TransactionResponseDto, never, never, string> {
  /**
   * Create a payment intent
   *
   * @param options - Payment intent options
   * @returns Payment intent with client secret
   */
  createPaymentIntent(options: PaymentIntentOptions): Promise<PaymentIntent>;

  /**
   * Process a payment
   *
   * @param userId - User ID
   * @param options - Payment processing options
   * @returns Transaction details
   */
  processPayment(userId: string, options: PaymentProcessOptions): Promise<Transaction>;

  /**
   * Get transactions for a user
   *
   * @param userId - User ID
   * @param options - Query options
   * @returns List of transactions
   */
  getTransactionsByUser(userId: string, options?: TransactionQueryOptions): Promise<{
    data: Transaction[];
    total: number;
    page: number;
    limit: number;
  }>;

  /**
   * Get transaction details
   *
   * @param transactionId - Transaction ID
   * @param userId - User ID (for authorization)
   * @returns Transaction details or null if not found
   */
  getTransactionDetails(transactionId: string, userId: string): Promise<Transaction | null>;

  /**
   * Process a refund
   *
   * @param transactionId - Transaction ID
   * @param amount - Refund amount (optional, defaults to full amount)
   * @param reason - Refund reason (optional)
   * @returns Refund details
   */
  processRefund(transactionId: string, amount?: number, reason?: string): Promise<Refund>;

  /**
   * Process a webhook event
   *
   * @param payload - Raw webhook payload
   * @param signature - Webhook signature for verification
   * @returns Processed webhook event
   */
  processWebhook(payload: any, signature: string): Promise<WebhookEvent>;

  /**
   * Create a payment method setup intent for the client
   *
   * @param userId - User ID
   * @returns Setup intent with client secret
   */
  createSetupIntent(userId: string): Promise<{ clientSecret: string }>;

  /**
   * Create a payment method in the database
   *
   * @param userId - User ID
   * @param data - Payment method data
   * @returns Created payment method
   */
  createPaymentMethod(userId: string, data: PaymentMethodCreateDto): Promise<PaymentMethodResponseDto>;

  /**
   * Get payment methods for a user
   *
   * @param userId - User ID
   * @returns List of payment methods
   */
  getPaymentMethods(userId: string): Promise<PaymentMethodResponseDto[]>;

  /**
   * Update a payment method
   *
   * @param userId - User ID
   * @param paymentMethodId - Payment method ID
   * @param data - Updated payment method data
   * @returns Updated payment method
   */
  updatePaymentMethod(userId: string, paymentMethodId: string, data: PaymentMethodUpdateDto): Promise<PaymentMethodResponseDto>;

  /**
   * Delete a payment method
   *
   * @param userId - User ID
   * @param paymentMethodId - Payment method ID
   * @returns Success status
   */
  deletePaymentMethod(userId: string, paymentMethodId: string): Promise<boolean>;
}