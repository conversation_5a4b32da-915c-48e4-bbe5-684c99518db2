/**
 * Winner Service Interface
 * 
 * Defines methods for managing winners, including retrieval, prize claiming,
 * and status updates for giveaway winners.
 */

import { BaseService } from '../base/base-service.interface.js';
import { 
  WinnerResponseDto, 
  WinnerCreateDto, 
  WinnerUpdateDto,
  WinnerWithDetailsResponseDto,
  PrizeClaimDto,
  WinnerFilterParamsDto,
  WinnerSelectionResultDto
} from '../../dtos/winner.dto.js';
import { PaginatedResult, PaginationOptions } from '../../repositories/base/base-repository.interface.js';
import { WinnerStatus } from '@prisma/client';

/**
 * Winner service interface for managing winners in the giveaway platform
 */
export interface WinnerService extends BaseService<WinnerResponseDto, WinnerCreateDto, WinnerUpdateDto> {
  /**
   * Get winners for a specific giveaway
   * 
   * @param giveawayId - ID of the giveaway
   * @param pagination - Optional pagination options
   * @returns Promise with array of winners
   */
  getWinnersByGiveaway(giveawayId: string, pagination?: PaginationOptions): Promise<WinnerResponseDto[]>;

  /**
   * Get winners for a giveaway with pagination metadata
   * 
   * @param giveawayId - ID of the giveaway
   * @param pagination - Pagination options
   * @returns Promise with paginated result of winners
   */
  getWinnersByGiveawayPaginated(
    giveawayId: string, 
    pagination: PaginationOptions
  ): Promise<PaginatedResult<WinnerResponseDto>>;

  /**
   * Get winners for a specific user
   * 
   * @param userId - ID of the user
   * @param pagination - Optional pagination options
   * @returns Promise with array of winners
   */
  getWinnersByUser(userId: string, pagination?: PaginationOptions): Promise<WinnerResponseDto[]>;

  /**
   * Get winner details including user, prize, and giveaway information
   * 
   * @param winnerId - ID of the winner
   * @returns Promise with detailed winner information or null if not found
   */
  getWinnerDetails(winnerId: string): Promise<WinnerWithDetailsResponseDto | null>;

  /**
   * Claim a prize as a winner
   * 
   * @param winnerId - ID of the winner record
   * @param userId - ID of the user claiming the prize (for verification)
   * @param claimData - Shipping and claim details
   * @returns Promise with the updated winner record
   */
  claimPrize(winnerId: string, userId: string, claimData: PrizeClaimDto): Promise<WinnerResponseDto>;

  /**
   * Check claim eligibility for a prize
   * 
   * @param winnerId - ID of the winner record
   * @param userId - ID of the user checking eligibility
   * @returns Promise with eligibility status and any reason for ineligibility
   */
  checkClaimEligibility(winnerId: string, userId: string): Promise<{
    isEligible: boolean;
    reason?: string;
    expiresAt?: Date;
  }>;

  /**
   * Update winner status (admin function)
   * 
   * @param winnerId - ID of the winner to update
   * @param status - New status value
   * @returns Promise with the updated winner
   */
  updateWinnerStatus(winnerId: string, status: WinnerStatus): Promise<WinnerResponseDto>;

  /**
   * Get pending winners (selected but not claimed)
   * 
   * @param giveawayId - Optional giveaway ID to filter by
   * @param pagination - Optional pagination options
   * @returns Promise with array of pending winners
   */
  getPendingWinners(
    giveawayId?: string, 
    pagination?: PaginationOptions
  ): Promise<WinnerResponseDto[]>;

  /**
   * Filter winners by advanced criteria
   * 
   * @param filterParams - Filter parameters
   * @param pagination - Optional pagination options
   * @returns Promise with paginated result of winners
   */
  filterWinners(
    filterParams: WinnerFilterParamsDto, 
    pagination?: PaginationOptions
  ): Promise<PaginatedResult<WinnerResponseDto>>;

  /**
   * Create winners from a selection result (admin function)
   * 
   * @param selectionResult - Result of the winner selection process
   * @returns Promise with array of created winners
   */
  createWinnersFromSelection(selectionResult: WinnerSelectionResultDto): Promise<WinnerResponseDto[]>;

  /**
   * Notify winners of their selection (admin function)
   * 
   * @param winnerId - ID of the winner to notify, or all pending if not specified
   * @returns Promise with the count of winners notified
   */
  notifyWinners(winnerId?: string): Promise<number>;

  /**
   * Process expired claims (admin function)
   * 
   * @returns Promise with the count of expired claims processed
   */
  processExpiredClaims(): Promise<number>;

  /**
   * Reselect a winner for a forfeited prize (admin function)
   * 
   * @param winnerId - ID of the original winner record
   * @returns Promise with the new winner or null if no eligible entries
   */
  reselectWinner(winnerId: string): Promise<WinnerResponseDto | null>;
} 