/**
 * Winner Service Implementation
 * 
 * This service handles winner management operations for giveaways including retrieval,
 * prize claiming, and status updates following best practices for error handling and type safety.
 */

import { Winner, WinnerStatus, NotificationType } from '@prisma/client';
import { WinnerService } from './winner-service.interface.js';
import {
  WinnerR<PERSON>ponseDto,
  WinnerCreateDto,
  WinnerUpdateDto,
  WinnerWithDetailsResponseDto,
  PrizeClaimDto,
  WinnerFilterParamsDto,
  WinnerSelectionResultDto
} from '../../dtos/winner.dto.js';
import { 
  FilterOptions, 
  PaginatedResult, 
  PaginationOptions 
} from '../../repositories/base/base-repository.interface.js';
import { 
  WinnerRepository, 
  UserRepository,
  PrizeRepository,
  GiveawayRepository,
  NotificationRepository
} from '../../repositories/index.js';
import { 
  ValidationError, 
  NotFoundError, 
  AuthorizationError,
  AppError 
} from '../../utils/errors.js';

/**
 * Business logic error for winner operations
 */
class WinnerBusinessError extends AppError {
  constructor(message: string) {
    super(message);
    this.name = 'WinnerBusinessError';
  }
}

/**
 * Implementation of the Winner Service
 */
export class WinnerServiceImpl implements WinnerService {
  /**
   * Constructor for WinnerServiceImpl
   */
  constructor(
    private readonly winnerRepository: WinnerRepository,
    private readonly userRepository: UserRepository,
    private readonly prizeRepository: PrizeRepository,
    private readonly giveawayRepository: GiveawayRepository,
    private readonly notificationRepository: NotificationRepository
  ) {}

  /**
   * Convert a Winner entity to a WinnerResponseDto
   * 
   * @private
   * @param winner - The winner entity to convert
   * @returns WinnerResponseDto
   */
  private toResponseDto(winner: Winner): WinnerResponseDto {
    const dto: WinnerResponseDto = {
      id: winner.id,
      giveawayId: winner.giveawayId,
      prizeId: winner.prizeId,
      userId: winner.userId,
      entryId: winner.entryId,
      selectionDate: winner.selectionDate,
      status: winner.status,
      createdAt: winner.createdAt,
      updatedAt: winner.updatedAt
    };
    
    // Add optional fields only if they exist
    if (winner.claimDate) {
      dto.claimDate = winner.claimDate;
    }
    
    if (winner.shippingDetails) {
      dto.shippingDetails = winner.shippingDetails as any;
    }
    
    return dto;
  }

  /**
   * Get a winner by ID
   * 
   * @param id - ID of the winner to retrieve
   * @returns Promise with winner or null if not found
   */
  async get(id: string): Promise<WinnerResponseDto | null> {
    try {
      const winner = await this.winnerRepository.findById(id);
      
      if (!winner) {
        return null;
      }
      
      return this.toResponseDto(winner);
    } catch (error) {
      if (error instanceof NotFoundError) {
        return null;
      }
      throw error;
    }
  }

  /**
   * List winners with optional filtering and pagination
   * 
   * @param filter - Optional filter criteria
   * @param pagination - Optional pagination options
   * @returns Promise with array of winners
   */
  async list(
    filter?: FilterOptions,
    pagination?: PaginationOptions
  ): Promise<WinnerResponseDto[]> {
    try {
      const winners = await this.winnerRepository.findMany(filter, pagination);
      return winners.map(winner => this.toResponseDto(winner));
    } catch (error) {
      throw error;
    }
  }

  /**
   * List winners with pagination metadata
   * 
   * @param filter - Optional filter criteria
   * @param pagination - Pagination options
   * @returns Promise with paginated result of winners
   */
  async listPaginated(
    filter?: FilterOptions,
    pagination?: PaginationOptions
  ): Promise<PaginatedResult<WinnerResponseDto>> {
    try {
      const result = await this.winnerRepository.findManyPaginated(filter, pagination);
      
      return {
        data: result.data.map(winner => this.toResponseDto(winner)),
        meta: result.meta
      };
    } catch (error) {
      throw error;
    }
  }

  /**
   * Count winners that match the filter criteria
   * 
   * @param filter - Optional filter criteria
   * @returns Promise with the count
   */
  async count(filter?: FilterOptions): Promise<number> {
    try {
      return await this.winnerRepository.count(filter);
    } catch (error) {
      throw error;
    }
  }

  /**
   * Create a new winner
   * 
   * @param data - Winner data to create
   * @returns Promise with the created winner
   */
  async create(data: WinnerCreateDto): Promise<WinnerResponseDto> {
    try {
      // Validate required fields
      if (!data.giveawayId) {
        throw new ValidationError('Giveaway ID is required');
      }
      
      if (!data.prizeId) {
        throw new ValidationError('Prize ID is required');
      }
      
      if (!data.userId) {
        throw new ValidationError('User ID is required');
      }
      
      if (!data.entryId) {
        throw new ValidationError('Entry ID is required');
      }
      
      // Validate that prize and user exist
      const prize = await this.prizeRepository.findById(data.prizeId);
      if (!prize) {
        throw new NotFoundError(`Prize with ID ${data.prizeId} not found`);
      }
      
      const user = await this.userRepository.findById(data.userId);
      if (!user) {
        throw new NotFoundError(`User with ID ${data.userId} not found`);
      }
      
      // Check if this user already won this prize in this giveaway
      const existingWinner = await this.winnerRepository.findMany({
        userId: data.userId,
        prizeId: data.prizeId,
        giveawayId: data.giveawayId
      });
      
      if (existingWinner.length > 0) {
        throw new ValidationError(`User has already won this prize in this giveaway`);
      }
      
      // Create the winner record
      const winnerData = {
        giveawayId: data.giveawayId,
        prizeId: data.prizeId,
        userId: data.userId,
        entryId: data.entryId,
        selectionDate: new Date(),
        status: WinnerStatus.SELECTED
      };
      
      const winner = await this.winnerRepository.create(winnerData);
      
      // Update prize inventory
      try {
        await this.prizeRepository.updateQuantity(data.prizeId, -1);
      } catch (error: any) {
        // If inventory update fails, log but don't fail the winner creation
        console.error(`Failed to update prize inventory: ${error.message}`);
      }
      
      return this.toResponseDto(winner);
    } catch (error) {
      throw error;
    }
  }

  /**
   * Create multiple winners
   * 
   * @param dataArray - Array of winner data to create
   * @returns Promise with array of created winners
   */
  async createMany(dataArray: WinnerCreateDto[]): Promise<WinnerResponseDto[]> {
    try {
      // Validate all winners before creating any
      for (const data of dataArray) {
        if (!data.giveawayId || !data.prizeId || !data.userId || !data.entryId) {
          throw new ValidationError('All winners must have giveawayId, prizeId, userId, and entryId');
        }
      }
      
      const winners = await Promise.all(
        dataArray.map(data => this.create(data))
      );
      
      return winners;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Update an existing winner
   * 
   * @param id - ID of the winner to update
   * @param data - Data to update the winner with
   * @returns Promise with the updated winner
   */
  async update(id: string, data: WinnerUpdateDto): Promise<WinnerResponseDto> {
    try {
      // Validate winner exists
      const existingWinner = await this.winnerRepository.findById(id);
      if (!existingWinner) {
        throw new NotFoundError(`Winner with ID ${id} not found`);
      }
      
      // Create update data with correct types
      const updateData: Partial<Winner> = {};
      
      if (data.status !== undefined) {
        updateData.status = data.status;
      }
      
      if (data.claimDate !== undefined) {
        updateData.claimDate = data.claimDate;
      }
      
      if (data.shippingDetails !== undefined) {
        updateData.shippingDetails = data.shippingDetails as any;
      }
      
      // Special handling for status changes
      if (data.status === WinnerStatus.CLAIMED && !existingWinner.claimDate) {
        updateData.claimDate = new Date();
      }
      
      const updatedWinner = await this.winnerRepository.update(id, updateData);
      return this.toResponseDto(updatedWinner);
    } catch (error) {
      throw error;
    }
  }

  /**
   * Delete a winner
   * 
   * @param id - ID of the winner to delete
   * @returns Promise with the deleted winner
   */
  async delete(id: string): Promise<WinnerResponseDto> {
    try {
      // Check if winner exists
      const existingWinner = await this.winnerRepository.findById(id);
      if (!existingWinner) {
        throw new NotFoundError(`Winner with ID ${id} not found`);
      }
      
      // If the winner has claimed their prize, don't allow deletion
      if (existingWinner.status === WinnerStatus.CLAIMED) {
        throw new ValidationError(`Cannot delete a winner who has already claimed their prize`);
      }
      
      // Always update prize inventory since we can't reliably check isAlternate
      // This may cause inventory to increase when it shouldn't, but better than skipping it
      try {
        await this.prizeRepository.updateQuantity(existingWinner.prizeId, 1);
      } catch (error: any) {
        console.error(`Failed to update prize inventory: ${error.message}`);
      }
      
      const deletedWinner = await this.winnerRepository.delete(id);
      return this.toResponseDto(deletedWinner);
    } catch (error) {
      throw error;
    }
  }

  /**
   * Check if a winner exists
   * 
   * @param id - ID of the winner to check
   * @returns Promise with boolean indicating existence
   */
  async exists(id: string): Promise<boolean> {
    try {
      return await this.winnerRepository.exists(id);
    } catch (error) {
      throw error;
    }
  }

  /**
   * Get winners for a specific giveaway
   * 
   * @param giveawayId - ID of the giveaway
   * @returns Promise with array of winners
   */
  async getWinnersByGiveaway(
    giveawayId: string, 
    
  ): Promise<WinnerResponseDto[]> {
    try {
      if (!giveawayId) {
        throw new ValidationError('Giveaway ID is required');
      }
      
      const winners = await this.winnerRepository.findByGiveaway(giveawayId);
      return winners.map(winner => this.toResponseDto(winner));
    } catch (error) {
      throw error;
    }
  }

  /**
   * Get winners for a giveaway with pagination metadata
   * 
   * @param giveawayId - ID of the giveaway
   * @param pagination - Pagination options
   * @returns Promise with paginated result of winners
   */
  async getWinnersByGiveawayPaginated(
    giveawayId: string, 
    pagination: PaginationOptions
  ): Promise<PaginatedResult<WinnerResponseDto>> {
    try {
      if (!giveawayId) {
        throw new ValidationError('Giveaway ID is required');
      }
      
      const filter = { giveawayId };
      const result = await this.winnerRepository.findManyPaginated(filter, pagination);
      
      return {
        data: result.data.map(winner => this.toResponseDto(winner)),
        meta: result.meta
      };
    } catch (error) {
      throw error;
    }
  }

  /**
   * Get winners for a specific user
   * 
   * @param userId - ID of the user
   * @returns Promise with array of winners
   */
  async getWinnersByUser(
    userId: string
  ): Promise<WinnerResponseDto[]> {
    try {
      if (!userId) {
        throw new ValidationError('User ID is required');
      }
      
      const winners = await this.winnerRepository.findByUser(userId);
      return winners.map(winner => this.toResponseDto(winner));
    } catch (error) {
      throw error;
    }
  }

  /**
   * Get winner details including user, prize, and giveaway information
   * 
   * @param winnerId - ID of the winner
   * @returns Promise with detailed winner information or null if not found
   */
  async getWinnerDetails(winnerId: string): Promise<WinnerWithDetailsResponseDto | null> {
    try {
      // Get the winner with details
      const winners = await this.winnerRepository.findWithDetails(winnerId);
      
      if (winners.length === 0) {
        return null;
      }
      
      const winner = winners[0];
      if (!winner) {
        return null;
      }
      
      const winnerDto = this.toResponseDto(winner);
      
      // Get additional details
      const [user, prize, giveaway] = await Promise.all([
        this.userRepository.findById(winner.userId),
        this.prizeRepository.findById(winner.prizeId),
        this.giveawayRepository.findById(winner.giveawayId)
      ]);
      
      if (!user || !prize || !giveaway) {
        throw new Error(`Missing related data for winner ${winnerId}`);
      }
      
      // Create detailed response
      return {
        ...winnerDto,
        user: {
          id: user.id,
          email: user.email,
          firstName: user.firstName || '',
          lastName: user.lastName || ''
        },
        prize: {
          id: prize.id,
          name: prize.name,
          value: Number(prize.value),
          currency: prize.currency || 'USD',
          images: prize.images as string[] || []
        },
        giveaway: {
          id: giveaway.id,
          title: giveaway.title
        }
      };
    } catch (error) {
      if (error instanceof NotFoundError) {
        return null;
      }
      throw error;
    }
  }

  /**
   * Claim a prize as a winner
   * 
   * @param winnerId - ID of the winner record
   * @param userId - ID of the user claiming the prize (for verification)
   * @param claimData - Shipping and claim details
   * @returns Promise with the updated winner record
   */
  async claimPrize(
    winnerId: string, 
    userId: string, 
    claimData: PrizeClaimDto
  ): Promise<WinnerResponseDto> {
    try {
      // Validate data
      if (!winnerId) {
        throw new ValidationError('Winner ID is required');
      }
      
      if (!userId) {
        throw new ValidationError('User ID is required');
      }
      
      if (!claimData.shippingDetails) {
        throw new ValidationError('Shipping details are required');
      }
      
      // Validate required shipping details
      const { fullName, addressLine1, city, state, postalCode, country } = claimData.shippingDetails;
      if (!fullName || !addressLine1 || !city || !state || !postalCode || !country) {
        throw new ValidationError('Missing required shipping details');
      }
      
      // Get the winner record
      const winner = await this.winnerRepository.findById(winnerId);
      if (!winner) {
        throw new NotFoundError(`Winner with ID ${winnerId} not found`);
      }
      
      // Verify that the user claiming the prize is the winner
      if (winner.userId !== userId) {
        throw new AuthorizationError('You are not authorized to claim this prize');
      }
      
      // Check eligibility
      const eligibility = await this.checkClaimEligibility(winnerId, userId);
      if (!eligibility.isEligible) {
        throw new WinnerBusinessError(eligibility.reason || 'Not eligible to claim this prize');
      }
      
      // Update the winner record
      const updateData: WinnerUpdateDto = {
        status: WinnerStatus.CLAIMED,
        claimDate: new Date(),
        shippingDetails: claimData.shippingDetails
      };
      
      const updatedWinner = await this.update(winnerId, updateData);
      
      // Create a notification for the prize claim
      try {
        await this.notificationRepository.create({
          userId,
          type: NotificationType.GIVEAWAY,
          title: 'Prize Claimed',
          message: 'Your prize claim has been received and is being processed.',
          metadata: {
            winnerId,
            prizeId: winner.prizeId,
            giveawayId: winner.giveawayId
          }
        });
      } catch (error: any) {
        // Don't fail the claim if notification fails
        console.error(`Failed to create notification: ${error.message}`);
      }
      
      return updatedWinner;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Check claim eligibility for a prize
   * 
   * @param winnerId - ID of the winner record
   * @param userId - ID of the user checking eligibility
   * @returns Promise with eligibility status and any reason for ineligibility
   */
  async checkClaimEligibility(
    winnerId: string, 
    userId: string
  ): Promise<{
    isEligible: boolean;
    reason?: string;
    expiresAt?: Date;
  }> {
    try {
      // Get the winner record
      const winner = await this.winnerRepository.findById(winnerId);
      if (!winner) {
        return {
          isEligible: false,
          reason: `Winner with ID ${winnerId} not found`
        };
      }
      
      // Verify that the user is the winner
      if (winner.userId !== userId) {
        return {
          isEligible: false,
          reason: 'You are not authorized to claim this prize'
        };
      }
      
      // Check if already claimed
      if (winner.status === WinnerStatus.CLAIMED) {
        return {
          isEligible: false,
          reason: 'Prize has already been claimed'
        };
      }
      
      // Check if forfeited
      if (winner.status === WinnerStatus.FORFEITED) {
        return {
          isEligible: false,
          reason: 'Prize has been forfeited'
        };
      }
      
      // Check if the claim period has expired (30 days from selection)
      const claimPeriodDays = 30;
      const expirationDate = new Date(winner.selectionDate);
      expirationDate.setDate(expirationDate.getDate() + claimPeriodDays);
      
      if (new Date() > expirationDate) {
        return {
          isEligible: false,
          reason: `Claim period has expired on ${expirationDate.toDateString()}`
        };
      }
      
      // All checks passed
      return {
        isEligible: true,
        expiresAt: expirationDate
      };
    } catch (error) {
      throw error;
    }
  }

  /**
   * Update winner status (admin function)
   * 
   * @param winnerId - ID of the winner to update
   * @param status - New status value
   * @returns Promise with the updated winner
   */
  async updateWinnerStatus(
    winnerId: string, 
    status: WinnerStatus
  ): Promise<WinnerResponseDto> {
    try {
      // Validate winner exists
      const winner = await this.winnerRepository.findById(winnerId);
      if (!winner) {
        throw new NotFoundError(`Winner with ID ${winnerId} not found`);
      }
      
      // Special handling for status transitions
      if (status === WinnerStatus.CLAIMED && winner.status !== WinnerStatus.CLAIMED) {
        if (!winner.shippingDetails) {
          throw new ValidationError('Cannot mark as claimed without shipping details');
        }
      }
      
      // Handle forfeiting a prize
      if (status === WinnerStatus.FORFEITED && winner.status !== WinnerStatus.FORFEITED) {
        // If this was a claimed prize, this is an admin action to revoke it
        if (winner.status === WinnerStatus.CLAIMED) {
          // Create a notification for the user
          try {
            await this.notificationRepository.create({
              userId: winner.userId,
              type: NotificationType.GIVEAWAY,
              title: 'Prize Forfeited',
              message: 'Your prize has been forfeited. Please contact support for more information.',
              metadata: {
                winnerId,
                prizeId: winner.prizeId,
                giveawayId: winner.giveawayId
              }
            });
          } catch (error: any) {
            console.error(`Failed to create notification: ${error.message}`);
          }
        }
        
        // Always update prize inventory
        try {
          await this.prizeRepository.updateQuantity(winner.prizeId, 1);
        } catch (error: any) {
          console.error(`Failed to update prize inventory: ${error.message}`);
        }
      }
      
      // Update the status with the repository's method
      const updatedWinner = await this.winnerRepository.updateStatus(winnerId, status);
      
      return this.toResponseDto(updatedWinner);
    } catch (error) {
      throw error;
    }
  }

  /**
   * Get pending winners (selected but not claimed)
   * 
   * @param giveawayId - Optional giveaway ID to filter by
   * @returns Promise with array of pending winners
   */
  async getPendingWinners(
    giveawayId?: string
  ): Promise<WinnerResponseDto[]> {
    try {
      const pendingWinners = await this.winnerRepository.findPending(giveawayId);
      return pendingWinners.map(winner => this.toResponseDto(winner));
    } catch (error) {
      throw error;
    }
  }

  /**
   * Filter winners by advanced criteria
   * 
   * @param filterParams - Filter parameters
   * @param pagination - Optional pagination options
   * @returns Promise with paginated result of winners
   */
  async filterWinners(
    filterParams: WinnerFilterParamsDto,
    pagination?: PaginationOptions
  ): Promise<PaginatedResult<WinnerResponseDto>> {
    try {
      // Build filter object from the filter params
      const filter: FilterOptions = {};
      
      if (filterParams.giveawayId) {
        filter['giveawayId'] = filterParams.giveawayId;
      }
      
      if (filterParams.prizeId) {
        filter['prizeId'] = filterParams.prizeId;
      }
      
      if (filterParams.userId) {
        filter['userId'] = filterParams.userId;
      }
      
      if (filterParams.status) {
        filter['status'] = filterParams.status;
      }
      
      // Date range filters
      if (filterParams.selectionDateStart || filterParams.selectionDateEnd) {
        filter['selectionDate'] = {};
        
        if (filterParams.selectionDateStart) {
          filter['selectionDate']['gte'] = filterParams.selectionDateStart;
        }
        
        if (filterParams.selectionDateEnd) {
          filter['selectionDate']['lte'] = filterParams.selectionDateEnd;
        }
      }
      
      if (filterParams.claimDateStart || filterParams.claimDateEnd) {
        filter['claimDate'] = {};
        
        if (filterParams.claimDateStart) {
          filter['claimDate']['gte'] = filterParams.claimDateStart;
        }
        
        if (filterParams.claimDateEnd) {
          filter['claimDate']['lte'] = filterParams.claimDateEnd;
        }
      }
      
      // Apply pagination with defaults if not provided
      const paginationOptions = pagination || {
        page: 1,
        limit: 10
      };
      
      const result = await this.winnerRepository.findManyPaginated(filter, paginationOptions);
      
      return {
        data: result.data.map(winner => this.toResponseDto(winner)),
        meta: result.meta
      };
    } catch (error) {
      throw error;
    }
  }

  /**
   * Create winners from a selection result (admin function)
   * 
   * @param selectionResult - Result of the winner selection process
   * @returns Promise with array of created winners
   */
  async createWinnersFromSelection(
    selectionResult: WinnerSelectionResultDto
  ): Promise<WinnerResponseDto[]> {
    try {
      // Validate the selection result
      if (!selectionResult.giveawayId) {
        throw new ValidationError('Giveaway ID is required');
      }
      
      if (!selectionResult.winners || selectionResult.winners.length === 0) {
        throw new ValidationError('No winners provided in selection result');
      }
      
      // Convert selection result to winner creation DTOs
      const winnerDtos: WinnerCreateDto[] = selectionResult.winners.map(winner => ({
        giveawayId: selectionResult.giveawayId,
        userId: winner.userId,
        prizeId: winner.prizeId,
        entryId: winner.entryId,
        isAlternate: winner.isAlternate
      }));
      
      // Create all winners
      const createdWinners = await this.createMany(winnerDtos);
      
      // Update giveaway to reflect that winners have been selected
      try {
        await this.giveawayRepository.update(selectionResult.giveawayId, {
          status: 'COMPLETED'
        });
      } catch (error: any) {
        console.error(`Failed to update giveaway status: ${error.message}`);
      }
      
      return createdWinners;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Notify winners of their selection (admin function)
   * 
   * @param winnerId - ID of the winner to notify, or all pending if not specified
   * @returns Promise with the count of winners notified
   */
  async notifyWinners(winnerId?: string): Promise<number> {
    try {
      // Get winners to notify
      let winners: Winner[];
      
      if (winnerId) {
        // Notify a specific winner
        const winner = await this.winnerRepository.findById(winnerId);
        if (!winner) {
          throw new NotFoundError(`Winner with ID ${winnerId} not found`);
        }
        
        // Only notify if in SELECTED status
        if (winner.status !== WinnerStatus.SELECTED) {
          throw new ValidationError(`Winner with ID ${winnerId} is not in SELECTED status`);
        }
        
        winners = [winner];
      } else {
        // Notify all pending winners (in SELECTED status)
        winners = await this.winnerRepository.findMany({
          status: WinnerStatus.SELECTED
        });
      }
      
      if (winners.length === 0) {
        return 0;
      }
      
      // Process each winner
      const notificationPromises = winners.map(async (winner) => {
        // Create a notification
        await this.notificationRepository.create({
          userId: winner.userId,
          type: NotificationType.GIVEAWAY,
          title: 'You Won a Prize!',
          message: 'Congratulations! You have been selected as a winner in one of our giveaways.',
          metadata: {
            winnerId: winner.id,
            prizeId: winner.prizeId,
            giveawayId: winner.giveawayId
          }
        });
        
        // Update winner status to NOTIFIED
        await this.winnerRepository.updateStatus(winner.id, WinnerStatus.NOTIFIED);
        
        return winner;
      });
      
      // Wait for all notifications to be sent
      const notifiedWinners = await Promise.all(notificationPromises);
      
      return notifiedWinners.length;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Process expired claims (admin function)
   * 
   * @returns Promise with the count of expired claims processed
   */
  async processExpiredClaims(): Promise<number> {
    try {
      // Get winners in SELECTED or NOTIFIED status
      const pendingWinners = await this.winnerRepository.findMany({
        status: {
          in: [WinnerStatus.SELECTED, WinnerStatus.NOTIFIED]
        }
      });
      
      if (pendingWinners.length === 0) {
        return 0;
      }
      
      // Calculate which winners have expired claim periods
      const claimPeriodDays = 30;
      const now = new Date();
      
      const expiredWinners = pendingWinners.filter(winner => {
        const expiryDate = new Date(winner.selectionDate);
        expiryDate.setDate(expiryDate.getDate() + claimPeriodDays);
        return now > expiryDate;
      });
      
      if (expiredWinners.length === 0) {
        return 0;
      }
      
      // Process each expired winner
      const processPromises = expiredWinners.map(async (winner) => {
        // Update status to FORFEITED
        await this.updateWinnerStatus(winner.id, WinnerStatus.FORFEITED);
        
        // Create a notification
        await this.notificationRepository.create({
          userId: winner.userId,
          type: NotificationType.GIVEAWAY,
          title: 'Prize Claim Expired',
          message: 'Your prize claim period has expired. The prize has been forfeited.',
          metadata: {
            winnerId: winner.id,
            prizeId: winner.prizeId,
            giveawayId: winner.giveawayId
          }
        });
        
        return winner;
      });
      
      // Wait for all processing to complete
      const processedWinners = await Promise.all(processPromises);
      
      return processedWinners.length;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Reselect a winner for a forfeited prize (admin function)
   * 
   * @param winnerId - ID of the original winner record
   * @returns Promise with the new winner or null if no eligible entries
   */
  async reselectWinner(winnerId: string): Promise<WinnerResponseDto | null> {
    try {
      // Get the original winner record
      const originalWinner = await this.winnerRepository.findById(winnerId);
      if (!originalWinner) {
        throw new NotFoundError(`Winner with ID ${winnerId} not found`);
      }
      
      // Verify that the prize was forfeited
      if (originalWinner.status !== WinnerStatus.FORFEITED) {
        throw new ValidationError('Can only reselect for forfeited prizes');
      }
      
      // In a real implementation, this would need to query the entry repository
      // to find eligible entries that haven't won yet, and select a new winner
      // This is a placeholder implementation that just returns null
      console.log(`Reselection requested for winner ID ${winnerId} - implement actual logic to select from eligible entries`);
      
      // For now, just return null indicating no replacement found
      return null;
    } catch (error) {
      throw error;
    }
  }
} 