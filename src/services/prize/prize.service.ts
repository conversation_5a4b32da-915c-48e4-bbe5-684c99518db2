/**
 * Prize Service Implementation
 * 
 * This service handles prize management operations for giveaways including creation,
 * updates, and inventory management following best practices for error handling and type safety.
 */

import { Prize, Prisma } from '@prisma/client';
import { PrizeService } from './prize-service.interface.js';
import {
  PrizeResponseDto,
  PrizeCreateDto,
  PrizeUpdateDto,
  PrizeWithWinnersResponseDto
} from '../../dtos/prize.dto.js';
import { WinnerResponseDto } from '../../dtos/winner.dto.js';
import { 
  FilterOptions, 
  PaginatedResult, 
  PaginationOptions 
} from '../../repositories/base/base-repository.interface.js';
import { PrizeRepository, WinnerRepository } from '../../repositories/index.js';
import { 
  ValidationError, 
  NotFoundError, 
  AppError 
} from '../../utils/errors.js';

/**
 * Business logic error for prize inventory operations
 */
class PrizeInventoryError extends AppError {
  constructor(message: string) {
    super(message);
    this.name = 'PrizeInventoryError';
  }
}

/**
 * Implementation of the Prize Service
 */
export class PrizeServiceImpl implements PrizeService {
  /**
   * Constructor for PrizeServiceImpl
   * 
   * @param prizeRepository - Repository for prize data access
   * @param winnerRepository - Repository for winner data access
   */
  constructor(
    private readonly prizeRepository: PrizeRepository,
    private readonly winnerRepository: WinnerRepository
  ) {}

  /**
   * Convert a Prize entity to a PrizeResponseDto
   * 
   * @private
   * @param prize - The prize entity to convert
   * @returns PrizeResponseDto
   */
  private toResponseDto(prize: Prize): PrizeResponseDto {
    return {
      id: prize.id,
      giveawayId: prize.giveawayId,
      name: prize.name,
      description: prize.description || '',
      value: Number(prize.value),
      currency: prize.currency || 'USD',
      quantity: prize.quantity,
      images: prize.images as string[] || [],
      specifications: prize.specifications as Record<string, any> || {},
      createdAt: prize.createdAt,
      updatedAt: prize.updatedAt
    };
  }

  /**
   * Get a prize by ID
   * 
   * @param id - ID of the prize to retrieve
   * @returns Promise with prize or null if not found
   */
  async get(id: string): Promise<PrizeResponseDto | null> {
    try {
      const prize = await this.prizeRepository.findById(id);
      
      if (!prize) {
        return null;
      }
      
      return this.toResponseDto(prize);
    } catch (error) {
      if (error instanceof NotFoundError) {
        return null;
      }
      throw error;
    }
  }

  /**
   * List prizes with optional filtering and pagination
   * 
   * @param filter - Optional filter criteria
   * @param pagination - Optional pagination options
   * @returns Promise with array of prizes
   */
  async list(
    filter?: FilterOptions,
    pagination?: PaginationOptions
  ): Promise<PrizeResponseDto[]> {
    try {
      const prizes = await this.prizeRepository.findMany(filter, pagination);
      return prizes.map(prize => this.toResponseDto(prize));
    } catch (error) {
      throw error;
    }
  }

  /**
   * List prizes with pagination metadata
   * 
   * @param filter - Optional filter criteria
   * @param pagination - Pagination options
   * @returns Promise with paginated result of prizes
   */
  async listPaginated(
    filter?: FilterOptions,
    pagination?: PaginationOptions
  ): Promise<PaginatedResult<PrizeResponseDto>> {
    try {
      const result = await this.prizeRepository.findManyPaginated(filter, pagination);
      
      return {
        data: result.data.map(prize => this.toResponseDto(prize)),
        meta: result.meta
      };
    } catch (error) {
      throw error;
    }
  }

  /**
   * Count prizes that match the filter criteria
   * 
   * @param filter - Optional filter criteria
   * @returns Promise with the count
   */
  async count(filter?: FilterOptions): Promise<number> {
    try {
      return await this.prizeRepository.count(filter);
    } catch (error) {
      throw error;
    }
  }

  /**
   * Create a new prize
   * 
   * @param data - Prize data to create
   * @returns Promise with the created prize
   */
  async create(data: PrizeCreateDto): Promise<PrizeResponseDto> {
    try {
      // Validate required fields
      if (!data.giveawayId) {
        throw new ValidationError('Giveaway ID is required');
      }
      
      if (!data.name) {
        throw new ValidationError('Prize name is required');
      }
      
      if (data.value === undefined || data.value < 0) {
        throw new ValidationError('Prize value must be a non-negative number');
      }
      
      // Set default values and prepare data for Prisma
      const prizeData = {
        giveawayId: data.giveawayId,
        name: data.name,
        description: data.description,
        value: new Prisma.Decimal(data.value),
        currency: data.currency || 'USD',
        quantity: data.quantity || 1,
        images: data.images || [],
        specifications: data.specifications || {}
      };
      
      const prize = await this.prizeRepository.create(prizeData);
      return this.toResponseDto(prize);
    } catch (error) {
      throw error;
    }
  }

  /**
   * Create multiple prizes
   * 
   * @param dataArray - Array of prize data to create
   * @returns Promise with array of created prizes
   */
  async createMany(dataArray: PrizeCreateDto[]): Promise<PrizeResponseDto[]> {
    try {
      // Validate all prizes before creating any
      for (const data of dataArray) {
        if (!data.giveawayId || !data.name || data.value === undefined) {
          throw new ValidationError('All prizes must have giveawayId, name, and value');
        }
      }
      
      const prizes = await Promise.all(
        dataArray.map(data => this.create(data))
      );
      
      return prizes;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Update an existing prize
   * 
   * @param id - ID of the prize to update
   * @param data - Data to update the prize with
   * @returns Promise with the updated prize
   */
  async update(id: string, data: PrizeUpdateDto): Promise<PrizeResponseDto> {
    try {
      // Validate prize exists
      const existingPrize = await this.prizeRepository.findById(id);
      if (!existingPrize) {
        throw new NotFoundError(`Prize with ID ${id} not found`);
      }
      
      // Validate numeric values
      if (data.value !== undefined && data.value < 0) {
        throw new ValidationError('Prize value must be a non-negative number');
      }
      
      if (data.quantity !== undefined && data.quantity < 0) {
        throw new ValidationError('Prize quantity must be a non-negative number');
      }
      
      // Create a clean update object with the correct types
      const updateData: Partial<Prize> = {};
      
      // Only add properties that exist in the input
      if (data.name !== undefined) updateData.name = data.name;
      if (data.description !== undefined) updateData.description = data.description;
      if (data.currency !== undefined) updateData.currency = data.currency;
      if (data.quantity !== undefined) updateData.quantity = data.quantity;
      if (data.images !== undefined) updateData.images = data.images;
      if (data.specifications !== undefined) updateData.specifications = data.specifications;
      
      // Handle value conversion
      if (data.value !== undefined) {
        updateData.value = new Prisma.Decimal(data.value);
      }
      
      const updatedPrize = await this.prizeRepository.update(id, updateData);
      return this.toResponseDto(updatedPrize);
    } catch (error) {
      throw error;
    }
  }

  /**
   * Delete a prize
   * 
   * @param id - ID of the prize to delete
   * @returns Promise with the deleted prize
   */
  async delete(id: string): Promise<PrizeResponseDto> {
    try {
      // Check if prize exists
      const existingPrize = await this.prizeRepository.findById(id);
      if (!existingPrize) {
        throw new NotFoundError(`Prize with ID ${id} not found`);
      }
      
      // Check if prize has winners associated with it
      const winnerCount = await this.winnerRepository.count({
        prizeId: id
      });
      
      if (winnerCount > 0) {
        throw new ValidationError(`Cannot delete prize with ID ${id} as it has winners associated with it`);
      }
      
      const deletedPrize = await this.prizeRepository.delete(id);
      return this.toResponseDto(deletedPrize);
    } catch (error) {
      throw error;
    }
  }

  /**
   * Check if a prize exists
   * 
   * @param id - ID of the prize to check
   * @returns Promise with boolean indicating existence
   */
  async exists(id: string): Promise<boolean> {
    try {
      return await this.prizeRepository.exists(id);
    } catch (error) {
      throw error;
    }
  }

  /**
   * Get prizes for a specific giveaway
   * 
   * @param giveawayId - ID of the giveaway
   * @param pagination - Optional pagination options
   * @returns Promise with array of prizes
   */
  async getPrizesByGiveaway(
    giveawayId: string
  ): Promise<PrizeResponseDto[]> {
    try {
      if (!giveawayId) {
        throw new ValidationError('Giveaway ID is required');
      }
      
      const prizes = await this.prizeRepository.findByGiveawayId(giveawayId);
      return prizes.map(prize => this.toResponseDto(prize));
    } catch (error) {
      throw error;
    }
  }

  /**
   * Get prizes for a giveaway with pagination metadata
   * 
   * @param giveawayId - ID of the giveaway
   * @param pagination - Pagination options
   * @returns Promise with paginated result of prizes
   */
  async getPrizesByGiveawayPaginated(
    giveawayId: string, 
    pagination: PaginationOptions
  ): Promise<PaginatedResult<PrizeResponseDto>> {
    try {
      if (!giveawayId) {
        throw new ValidationError('Giveaway ID is required');
      }
      
      const filter = { giveawayId };
      const result = await this.prizeRepository.findManyPaginated(filter, pagination);
      
      return {
        data: result.data.map(prize => this.toResponseDto(prize)),
        meta: result.meta
      };
    } catch (error) {
      throw error;
    }
  }

  /**
   * Get a prize with its winners
   * 
   * @param prizeId - ID of the prize
   * @returns Promise with prize including winners or null if not found
   */
  async getPrizeWithWinners(prizeId: string): Promise<PrizeWithWinnersResponseDto | null> {
    try {
      // Get the prize
      const prize = await this.prizeRepository.findById(prizeId);
      if (!prize) {
        return null;
      }
      
      // Get winners for this prize
      const winners = await this.winnerRepository.findMany({
        prizeId
      });
      
      // Map winners to DTOs with required fields
      const winnerDtos: WinnerResponseDto[] = winners.map(winner => {
        // Create base dto with required fields
        const winnerDto: WinnerResponseDto = {
          id: winner.id,
          userId: winner.userId,
          giveawayId: winner.giveawayId,
          prizeId: winner.prizeId,
          entryId: winner.entryId,
          selectionDate: winner.selectionDate,
          status: winner.status,
          createdAt: winner.createdAt,
          updatedAt: winner.updatedAt,
          isAlternate: false // Default value if not present
        };
        
        // Only add optional fields if they exist
        if (winner.claimDate) {
          winnerDto.claimDate = winner.claimDate;
        }
        
        if (winner.shippingDetails) {
          winnerDto.shippingDetails = winner.shippingDetails as any;
        }
        
        return winnerDto;
      });
      
      // Return prize with winners
      return {
        ...this.toResponseDto(prize),
        winners: winnerDtos
      };
    } catch (error) {
      if (error instanceof NotFoundError) {
        return null;
      }
      throw error;
    }
  }

  /**
   * Update prize inventory quantity
   * 
   * @param prizeId - ID of the prize
   * @param quantity - New quantity value
   * @returns Promise with updated prize
   */
  async updateInventory(prizeId: string, quantity: number): Promise<PrizeResponseDto> {
    try {
      if (quantity < 0) {
        throw new ValidationError('Quantity cannot be negative');
      }
      
      // Get the current prize
      const existingPrize = await this.prizeRepository.findById(prizeId);
      if (!existingPrize) {
        throw new NotFoundError(`Prize with ID ${prizeId} not found`);
      }
      
      // Calculate the change in quantity
      const quantityChange = quantity - existingPrize.quantity;
      
      // Update the prize quantity
      const updatedPrize = await this.prizeRepository.updateQuantity(prizeId, quantityChange);
      return this.toResponseDto(updatedPrize);
    } catch (error) {
      throw error;
    }
  }

  /**
   * Check if a prize has available inventory
   * 
   * @param prizeId - ID of the prize
   * @returns Promise with boolean indicating inventory availability
   */
  async hasAvailableInventory(prizeId: string): Promise<boolean> {
    try {
      const prize = await this.prizeRepository.findById(prizeId);
      if (!prize) {
        throw new NotFoundError(`Prize with ID ${prizeId} not found`);
      }
      
      return prize.quantity > 0;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Decrement prize inventory by specified amount
   * 
   * @param prizeId - ID of the prize
   * @param amount - Amount to decrement (defaults to 1)
   * @returns Promise with updated prize
   */
  async decrementInventory(prizeId: string, amount = 1): Promise<PrizeResponseDto> {
    try {
      if (amount <= 0) {
        throw new ValidationError('Decrement amount must be positive');
      }
      
      // Get the current prize
      const prize = await this.prizeRepository.findById(prizeId);
      if (!prize) {
        throw new NotFoundError(`Prize with ID ${prizeId} not found`);
      }
      
      // Check if enough inventory is available
      if (prize.quantity < amount) {
        throw new PrizeInventoryError(`Not enough inventory for prize ${prizeId}. Available: ${prize.quantity}, Requested: ${amount}`);
      }
      
      // Update the prize quantity (negative for decrement)
      const updatedPrize = await this.prizeRepository.updateQuantity(prizeId, -amount);
      return this.toResponseDto(updatedPrize);
    } catch (error) {
      throw error;
    }
  }

  /**
   * Get total value of prizes for a giveaway
   * 
   * @param giveawayId - ID of the giveaway
   * @returns Promise with total value
   */
  async getTotalPrizeValue(giveawayId: string): Promise<number> {
    try {
      if (!giveawayId) {
        throw new ValidationError('Giveaway ID is required');
      }
      
      const prizes = await this.prizeRepository.findByGiveawayId(giveawayId);
      
      // Calculate total value
      const totalValue = prizes.reduce((sum, prize) => {
        return sum + Number(prize.value) * prize.quantity;
      }, 0);
      
      return totalValue;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Bulk update prize images
   * 
   * @param prizeId - ID of the prize
   * @param images - Array of image URLs
   * @returns Promise with updated prize
   */
  async updatePrizeImages(prizeId: string, images: string[]): Promise<PrizeResponseDto> {
    try {
      // Validate prize exists
      const existingPrize = await this.prizeRepository.findById(prizeId);
      if (!existingPrize) {
        throw new NotFoundError(`Prize with ID ${prizeId} not found`);
      }
      
      // Validate images array
      if (!Array.isArray(images)) {
        throw new ValidationError('Images must be an array of strings');
      }
      
      // Update the prize with new images
      const updatedPrize = await this.prizeRepository.update(prizeId, { images });
      return this.toResponseDto(updatedPrize);
    } catch (error) {
      throw error;
    }
  }
} 