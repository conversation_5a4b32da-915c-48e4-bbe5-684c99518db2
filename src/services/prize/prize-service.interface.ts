/**
 * Prize Service Interface
 * 
 * Defines methods for managing prizes, including retrieval, creation, updates, and inventory management.
 */

import { BaseService } from '../base/base-service.interface.js';
import { 
  PrizeResponseDto, 
  PrizeCreateDto, 
  PrizeUpdateDto,
  PrizeWithWinnersResponseDto
} from '../../dtos/prize.dto.js';
import {PaginatedResult, PaginationOptions } from '../../repositories/base/base-repository.interface.js';

/**
 * Prize service interface for managing prizes in the giveaway platform
 */
export interface PrizeService extends BaseService<PrizeResponseDto, PrizeCreateDto, PrizeUpdateDto> {
  /**
   * Get prizes for a specific giveaway
   * 
   * @param giveawayId - ID of the giveaway
   * @returns Promise with array of prizes
   */
  getPrizesByGiveaway(giveawayId: string): Promise<PrizeResponseDto[]>;

  /**
   * Get prizes for a giveaway with pagination metadata
   * 
   * @param giveawayId - ID of the giveaway
   * @param pagination - Pagination options
   * @returns Promise with paginated result of prizes
   */
  getPrizesByGiveawayPaginated(
    giveawayId: string, 
    pagination: PaginationOptions
  ): Promise<PaginatedResult<PrizeResponseDto>>;

  /**
   * Get a prize with its winners
   * 
   * @param prizeId - ID of the prize
   * @returns Promise with prize including winners or null if not found
   */
  getPrizeWithWinners(prizeId: string): Promise<PrizeWithWinnersResponseDto | null>;

  /**
   * Update prize inventory quantity
   * 
   * @param prizeId - ID of the prize
   * @param quantity - New quantity value
   * @returns Promise with updated prize
   */
  updateInventory(prizeId: string, quantity: number): Promise<PrizeResponseDto>;

  /**
   * Check if a prize has available inventory
   * 
   * @param prizeId - ID of the prize
   * @returns Promise with boolean indicating inventory availability
   */
  hasAvailableInventory(prizeId: string): Promise<boolean>;

  /**
   * Decrement prize inventory by specified amount
   * 
   * @param prizeId - ID of the prize
   * @param amount - Amount to decrement (defaults to 1)
   * @returns Promise with updated prize
   */
  decrementInventory(prizeId: string, amount?: number): Promise<PrizeResponseDto>;

  /**
   * Get total value of prizes for a giveaway
   * 
   * @param giveawayId - ID of the giveaway
   * @returns Promise with total value
   */
  getTotalPrizeValue(giveawayId: string): Promise<number>;

  /**
   * Bulk update prize images
   * 
   * @param prizeId - ID of the prize
   * @param images - Array of image URLs
   * @returns Promise with updated prize
   */
  updatePrizeImages(prizeId: string, images: string[]): Promise<PrizeResponseDto>;
} 