import express, { Request, Response, NextFunction } from 'express';
import cors from 'cors';
import helmet from 'helmet';
import morgan from 'morgan';
import { config } from 'dotenv';
import logger from './utils/logger.js';
import { setupRoutes } from './routes/index.js';
import { createAuthMiddleware } from './middleware/auth.middleware.js';
import { createAdminMiddleware } from './middleware/admin.middleware.js';

// Import dependency injection providers
import {
  initializeRepositories,
  getPrismaClient
} from './repositories/index.js';
import {
  initializeServices,
  getServiceFactory
} from './services/index.js';
import {
  initializeControllers
} from './controllers/index.js';

// Load environment variables
config();

// Initialize repositories, services, and controllers
initializeRepositories();
initializeServices();
initializeControllers();

// Get service factory instance
const serviceFactory = getServiceFactory();

// Create the auth middleware with auth service
const authService = serviceFactory.createAuthService();
const authenticateUser = createAuthMiddleware(authService);
const requireAdmin = createAdminMiddleware();

// Create Express application
const app = express();

// Middleware
app.use(express.json());
app.use(express.urlencoded({ extended: true }));
app.use(cors());
app.use(helmet());
app.use(morgan('dev'));

// Health check endpoint
app.get('/health', (_req: Request, res: Response) => {
  res.status(200).json({
    status: 'ok',
    timestamp: new Date().toISOString()
  });
});

// API routes - will be imported from route files
app.get('/api', (_req: Request, res: Response) => {
  res.status(200).json({
    message: 'Welcome to the Winners Society API',
    version: '1.0.0'
  });
});

// Setup all API routes using the centralized router
setupRoutes(app, {
  authService: serviceFactory.createAuthService(),
  userService: serviceFactory.createUserService(),
  membershipService: serviceFactory.createMembershipService(),
  giveawayService: serviceFactory.createGiveawayService(),
  prizeService: serviceFactory.createPrizeService(),
  winnerService: serviceFactory.createWinnerService(),
  paymentService: serviceFactory.createPaymentService(),
  contentService: serviceFactory.createContentService(),
  notificationService: serviceFactory.createNotificationService(),
  adminService: serviceFactory.createAdminService(),
  enrollmentService: serviceFactory.createEnrollmentService()
}, {
  authenticate: authenticateUser,
  requireAdmin
});

// Error handling middleware for unhandled routes
app.use((req: Request, res: Response) => {
  res.status(404).json({
    success: false,
    message: `Route ${req.method} ${req.url} not found`
  });
});

// Global error handler
app.use((err: Error, _req: Request, res: Response, _next: NextFunction) => {
  logger.error(err.stack);

  const statusCode = 'statusCode' in err ? (err as any).statusCode : 500;
  const message = err.message || 'Internal Server Error';

  res.status(statusCode).json({
    success: false,
    message: message,
    stack: process.env['NODE_ENV'] === 'development' ? err.stack : undefined
  });
});

// Get prisma client for graceful shutdown
const prisma = getPrismaClient();

// Graceful shutdown
process.on('SIGINT', async () => {
  await prisma.$disconnect();
  process.exit(0);
});

process.on('SIGTERM', async () => {
  await prisma.$disconnect();
  process.exit(0);
});

export default app;
