import app from './app.js';
import { config } from 'dotenv';
import logger from './utils/logger.js';
// import { initializeJobSystem, shutdownJobSystem } from './jobs/index.js';

// Load environment variables
config();

const PORT = process.env['PORT'] || 3000;

// Skip job system initialization
logger.info('Using PostgreSQL-based job processing system');

// Start the server
const server = app.listen(PORT, () => {
  logger.info(`🚀 Server running on port ${PORT} in ${process.env['NODE_ENV'] || 'development'} mode`);
  logger.info(`🔗 Health check available at http://localhost:${PORT}/health`);
  logger.info(`🔗 API available at http://localhost:${PORT}/api`);
  logger.info(`🔄 Background job system initialized`);
});

// Handle unhandled promise rejections
process.on('unhandledRejection', (err: Error) => {
  logger.error('UNHANDLED REJECTION! 💥 Shutting down...');
  logger.error(`${err.name}: ${err.message}`);

  // Close server & exit process
  server.close(async () => {
    try {
      // No job system to shut down
      logger.info('No job system to shut down');
    } catch (error) {
      logger.error('Error during shutdown:', error);
    }
    process.exit(1);
  });
});

// Handle SIGTERM
process.on('SIGTERM', () => {
  logger.info('SIGTERM RECEIVED. Shutting down gracefully');

  server.close(async () => {
    try {
      // No job system to shut down
      logger.info('No job system to shut down');
    } catch (error) {
      logger.error('Error during shutdown:', error);
    }
    logger.info('Process terminated!');
    process.exit(0);
  });
});
