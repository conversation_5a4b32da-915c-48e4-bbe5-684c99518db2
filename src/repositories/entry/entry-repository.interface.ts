/**
 * Entry Repository Interface
 * 
 * This interface defines the operations specific to giveaway entries,
 * extending the base repository interface.
 */

import { Entry } from '@prisma/client';
import { BaseRepository } from '../base/base-repository.interface.js';

/**
 * Entry repository interface for entry-specific operations
 * @extends BaseRepository<Entry, string>
 */
export interface EntryRepository extends BaseRepository<Entry, string> {
  /**
   * Find entries by giveaway and user
   * @param giveawayId - The giveaway ID to filter by
   * @param userId - The user ID to filter by
   * @returns Promise with array of entries for the specified giveaway and user
   */
  findByGiveawayAndUser(giveawayId: string, userId: string): Promise<Entry[]>;
  
  /**
   * Count entries by giveaway
   * @param giveawayId - The giveaway ID to count entries for
   * @returns Promise with the count of entries for the specified giveaway
   */
  countByGiveaway(giveawayId: string): Promise<number>;
  
  /**
   * Count entries by user
   * @param userId - The user ID to count entries for
   * @param giveawayId - Optional giveaway ID to filter by
   * @returns Promise with the count of entries for the specified user
   */
  countByUser(userId: string, giveawayId?: string): Promise<number>;
  
  /**
   * Find entries with user details
   * @param giveawayId - Optional giveaway ID to filter by
   * @returns Promise with array of entries including user information
   */
  findWithUserDetails(giveawayId?: string): Promise<Array<Entry & { user: any }>>;
  
  /**
   * Create multiple entries at once
   * @param entries - Array of entry data to create
   * @returns Promise with array of created entries
   */
  createBatch(entries: Array<Omit<Entry, 'id' | 'createdAt' | 'updatedAt'>>): Promise<Entry[]>;
}
