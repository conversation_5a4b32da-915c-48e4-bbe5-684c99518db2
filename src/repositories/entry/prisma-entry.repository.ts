/**
 * Prisma Entry Repository Implementation
 * 
 * This class implements the entry repository interface using Prisma ORM.
 * It provides functionality for managing giveaway entries, including methods to find,
 * count, and create entries, as well as specialized operations like batch creation.
 */

import { Entry, PrismaClient } from '@prisma/client';
import { EntryRepository } from './entry-repository.interface.js';
import { PrismaBaseRepository, RepositoryError, RepositoryErrorType } from '../base/prisma-base.repository.js';

/**
 * Prisma implementation of the entry repository
 */
export class PrismaEntryRepository extends PrismaBaseRepository<Entry, string> implements EntryRepository {
  /**
   * Constructor for PrismaEntryRepository
   * 
   * @param prisma - Prisma client instance
   */
  constructor(prisma: PrismaClient) {
    super(prisma, prisma.entry);
  }

  /**
   * Find entries by giveaway and user
   * 
   * @param giveawayId - Giveaway ID to filter by
   * @param userId - User ID to filter by
   * @returns Promise with array of entries for the specified giveaway and user
   */
  async findByGiveawayAndUser(giveawayId: string, userId: string): Promise<Entry[]> {
    try {
      return await this.prisma.entry.findMany({
        where: {
          giveawayId,
          userId
        },
        orderBy: {
          entryDate: 'desc' // Most recent first
        }
      });
    } catch (error) {
      this.handleError(error, `Failed to find entries for giveaway ID: ${giveawayId} and user ID: ${userId}`);
      return []; // This line will never be reached due to handleError throwing
    }
  }

  /**
   * Count entries by giveaway
   * 
   * @param giveawayId - Giveaway ID to count entries for
   * @returns Promise with the count of entries for the specified giveaway
   */
  async countByGiveaway(giveawayId: string): Promise<number> {
    try {
      return await this.prisma.entry.count({
        where: {
          giveawayId
        }
      });
    } catch (error) {
      this.handleError(error, `Failed to count entries for giveaway ID: ${giveawayId}`);
      return 0; // This line will never be reached due to handleError throwing
    }
  }

  /**
   * Count entries by user
   * 
   * @param userId - User ID to count entries for
   * @param giveawayId - Optional giveaway ID to filter by
   * @returns Promise with the count of entries for the specified user
   */
  async countByUser(userId: string, giveawayId?: string): Promise<number> {
    try {
      const whereCondition = {
        userId,
        ...(giveawayId !== undefined ? { giveawayId } : {})
      };
      
      return await this.prisma.entry.count({
        where: whereCondition
      });
    } catch (error) {
      this.handleError(error, `Failed to count entries for user ID: ${userId}`);
      return 0; // This line will never be reached due to handleError throwing
    }
  }

  /**
   * Find entries with user details
   * 
   * @param giveawayId - Optional giveaway ID to filter by
   * @returns Promise with array of entries including user information
   */
  async findWithUserDetails(giveawayId?: string): Promise<Array<Entry & { user: any }>> {
    try {
      const whereCondition = giveawayId !== undefined ? { giveawayId } : {};
      
      return await this.prisma.entry.findMany({
        where: whereCondition,
        include: {
          user: true
        },
        orderBy: {
          entryDate: 'desc' // Most recent first
        }
      });
    } catch (error) {
      this.handleError(error, 'Failed to find entries with user details');
      return []; // This line will never be reached due to handleError throwing
    }
  }

  /**
   * Create multiple entries at once
   * 
   * @param entries - Array of entry data to create
   * @returns Promise with array of created entries
   */
  async createBatch(entries: Array<Omit<Entry, 'id' | 'createdAt' | 'updatedAt'>>): Promise<Entry[]> {
    try {
      // Validate the entries array
      if (!entries || entries.length === 0) {
        throw new RepositoryError(
          'Entries array is empty or undefined',
          RepositoryErrorType.VALIDATION
        );
      }
      
      // Use a transaction to ensure all entries are created or none are
      const createdEntries = await this.prisma.$transaction(async (prisma) => {
        const results = [];
        
        // Create each entry
        for (const entry of entries) {
          const createdEntry = await prisma.entry.create({
            data: {
              ...entry,
              // Set default values if not provided
              entryDate: entry.entryDate || new Date()
            }
          });
          
          results.push(createdEntry);
        }
        
        return results;
      });
      
      return createdEntries;
    } catch (error) {
      if (error instanceof RepositoryError) {
        throw error; // Re-throw repository errors
      }
      this.handleError(error, 'Failed to create batch of entries');
      return []; // This line will never be reached due to handleError throwing
    }
  }
}
