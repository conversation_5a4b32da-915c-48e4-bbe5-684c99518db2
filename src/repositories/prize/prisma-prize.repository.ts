/**
 * Prisma Prize Repository Implementation
 * 
 * This class implements the prize repository interface using Prisma ORM.
 */

import { PrismaClient, Prize } from '@prisma/client';
import { PrizeRepository } from './prize-repository.interface.js';
import { PrismaBaseRepository, RepositoryError, RepositoryErrorType } from '../base/prisma-base.repository.js';

/**
 * Prisma implementation of the prize repository
 */
export class PrismaPrizeRepository extends PrismaBaseRepository<Prize, string> implements PrizeRepository {
  /**
   * Constructor for PrismaPrizeRepository
   * 
   * @param prisma - Prisma client instance
   */
  constructor(prisma: PrismaClient) {
    super(prisma, prisma.prize);
  }

  /**
   * Find prizes by giveaway ID
   * 
   * @param giveawayId - Giveaway ID to filter by
   * @returns Promise with array of prizes for the giveaway
   */
  async findByGiveawayId(giveawayId: string): Promise<Prize[]> {
    try {
      return await this.prisma.prize.findMany({
        where: {
          giveawayId
        },
        orderBy: {
          value: 'desc' // Highest value first
        }
      });
    } catch (error) {
      this.handleError(error, `Failed to find prizes for giveaway ID: ${giveawayId}`);
      return []; // This line will never be reached due to handleError throwing
    }
  }

  /**
   * Find available prizes (prizes with quantity > 0)
   * 
   * @param giveawayId - Optional giveaway ID to filter by
   * @returns Promise with array of available prizes
   */
  async findAvailable(giveawayId?: string): Promise<Prize[]> {
    try {
      const whereCondition = {
        quantity: {
          gt: 0 // Only prizes with quantity greater than 0
        },
        ...(giveawayId ? { giveawayId } : {})
      };
      
      return await this.prisma.prize.findMany({
        where: whereCondition,
        orderBy: [
          { giveawayId: 'asc' },
          { value: 'desc' } // Highest value first within each giveaway
        ]
      });
    } catch (error) {
      this.handleError(error, 'Failed to find available prizes');
      return []; // This line will never be reached due to handleError throwing
    }
  }

  /**
   * Update the quantity of a prize (when prizes are claimed)
   * 
   * @param prizeId - Prize ID
   * @param quantityChange - Amount to change the quantity by (negative for claiming)
   * @returns Promise with the updated prize
   * @throws Error if quantity would become negative
   */
  async updateQuantity(prizeId: string, quantityChange: number): Promise<Prize> {
    try {
      // First, get the current prize to check its quantity
      const prize = await this.prisma.prize.findUnique({
        where: { id: prizeId }
      });

      if (!prize) {
        throw new RepositoryError(
          `Prize with ID ${prizeId} not found`,
          RepositoryErrorType.NOT_FOUND
        );
      }

      // Calculate the new quantity
      const newQuantity = prize.quantity + quantityChange;

      // Check if the quantity would become negative
      if (newQuantity < 0) {
        throw new RepositoryError(
          `Cannot update prize quantity to a negative value (current: ${prize.quantity}, change: ${quantityChange})`,
          RepositoryErrorType.VALIDATION
        );
      }

      // Update the prize with the new quantity
      return await this.prisma.prize.update({
        where: { id: prizeId },
        data: { quantity: newQuantity }
      });
    } catch (error) {
      if (error instanceof RepositoryError) {
        throw error; // Re-throw repository errors
      }
      this.handleError(error, `Failed to update quantity for prize ID: ${prizeId}`);
      return {} as Prize; // This line will never be reached due to handleError throwing
    }
  }
}
