/**
 * Prize Repository Interface
 * 
 * This interface defines the operations specific to prize entities,
 * extending the base repository interface.
 */

import { Prize } from '@prisma/client';
import { BaseRepository } from '../base/base-repository.interface.js';

/**
 * Prize repository interface for prize-specific operations
 * @extends BaseRepository<Prize, string>
 */
export interface PrizeRepository extends BaseRepository<Prize, string> {
  /**
   * Find prizes by giveaway ID
   * @param giveawayId - The giveaway ID to filter by
   * @returns Promise with array of prizes for the giveaway
   */
  findByGiveawayId(giveawayId: string): Promise<Prize[]>;
  
  /**
   * Find available prizes (prizes with quantity > 0)
   * @param giveawayId - Optional giveaway ID to filter by
   * @returns Promise with array of available prizes
   */
  findAvailable(giveawayId?: string): Promise<Prize[]>;
  
  /**
   * Update the quantity of a prize (when prizes are claimed)
   * @param prizeId - The prize ID
   * @param quantityChange - The amount to change the quantity by (negative for claiming)
   * @returns Promise with the updated prize
   * @throws Error if quantity would become negative
   */
  updateQuantity(prizeId: string, quantityChange: number): Promise<Prize>;
}
