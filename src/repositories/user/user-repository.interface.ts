/**
 * User Repository Interface
 * 
 * This interface defines the operations specific to user entities,
 * extending the base repository interface.
 */

import { User } from '@prisma/client';
import { BaseRepository } from '../base/base-repository.interface.js';

/**
 * User repository interface for user-specific operations
 * @extends BaseRepository<User, string>
 */
export interface UserRepository extends BaseRepository<User, string> {
  /**
   * Find a user by email address
   * @param email - The email address to search for
   * @returns Promise with user or null if not found
   */
  findByEmail(email: string): Promise<User | null>;
  
  /**
   * Find a user by username (if applicable)
   * @param username - The username to search for
   * @returns Promise with user or null if not found
   */
  findByUsername?(username: string): Promise<User | null>;
  
  /**
   * Update a user's password
   * @param userId - The user ID
   * @param hashedPassword - The new hashed password
   * @returns Promise with the updated user
   */
  updatePassword(userId: string, hashedPassword: string): Promise<User>;
  
  /**
   * Mark a user's email as verified
   * @param userId - The user ID
   * @returns Promise with the updated user
   */
  verifyEmail(userId: string): Promise<User>;
  
  /**
   * Update a user's verification status
   * @param userId - The user ID
   * @param isVerified - The verification status to set
   * @returns Promise with the updated user
   */
  updateVerificationStatus(userId: string, isVerified: boolean): Promise<User>;
  
  /**
   * Find a user with their membership data
   * @param userId - The user ID
   * @returns Promise with user including membership data or null if not found
   */
  findWithMemberships(userId: string): Promise<User | null>;
}
