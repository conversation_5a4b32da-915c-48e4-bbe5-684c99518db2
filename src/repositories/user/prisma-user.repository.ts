/**
 * Prisma User Repository Implementation
 * 
 * This class implements the user repository interface using Prisma ORM.
 */

import { PrismaClient, User } from '@prisma/client';
import { UserRepository } from './user-repository.interface.js';
import { PrismaBaseRepository, RepositoryError, RepositoryErrorType } from '../base/prisma-base.repository.js';

/**
 * Prisma implementation of the user repository
 */
export class PrismaUserRepository extends PrismaBaseRepository<User, string> implements UserRepository {
  /**
   * Constructor for PrismaUserRepository
   * 
   * @param prisma - Prisma client instance
   */
  constructor(prisma: PrismaClient) {
    super(prisma, prisma.user);
  }

  /**
   * Find a user by email address
   * 
   * @param email - Email address to search for
   * @returns Promise with user or null if not found
   */
  async findByEmail(email: string): Promise<User | null> {
    try {
      return await this.prisma.user.findUnique({
        where: { email }
      });
    } catch (error) {
      this.handleError(error, `Failed to find user with email: ${email}`);
      return null; // This line will never be reached due to handleError throwing an exception
    }
  }

  /**
   * Update a user's password
   * 
   * @param userId - User ID
   * @param hashedPassword - New hashed password
   * @returns Promise with updated user
   */
  async updatePassword(userId: string, hashedPassword: string): Promise<User> {
    try {
      return await this.prisma.user.update({
        where: { id: userId },
        data: { 
          password: hashedPassword,
          updatedAt: new Date() 
        }
      });
    } catch (error) {
      if (error instanceof Error && error.name === 'PrismaClientKnownRequestError' && (error as any).code === 'P2025') {
        throw new RepositoryError(`User with ID ${userId} not found`, RepositoryErrorType.NOT_FOUND, error);
      }
      this.handleError(error, `Failed to update password for user with ID: ${userId}`);
      return {} as User; // Will never be reached due to handleError throwing
    }
  }

  /**
   * Mark a user's email as verified
   * 
   * @param userId - User ID
   * @returns Promise with updated user
   */
  async verifyEmail(userId: string): Promise<User> {
    try {
      return await this.prisma.user.update({
        where: { id: userId },
        data: { 
          isVerified: true,
          updatedAt: new Date() 
        }
      });
    } catch (error) {
      if (error instanceof Error && error.name === 'PrismaClientKnownRequestError' && (error as any).code === 'P2025') {
        throw new RepositoryError(`User with ID ${userId} not found`, RepositoryErrorType.NOT_FOUND, error);
      }
      this.handleError(error, `Failed to verify email for user with ID: ${userId}`);
      return {} as User; // Will never be reached due to handleError throwing
    }
  }

  /**
   * Update a user's verification status
   * 
   * @param userId - The user ID
   * @param isVerified - The verification status to set
   * @returns Promise with the updated user
   */
  async updateVerificationStatus(userId: string, isVerified: boolean): Promise<User> {
    try {
      return await this.prisma.user.update({
        where: { id: userId },
        data: { 
          isVerified,
          updatedAt: new Date() 
        }
      });
    } catch (error) {
      if (error instanceof Error && error.name === 'PrismaClientKnownRequestError' && (error as any).code === 'P2025') {
        throw new RepositoryError(`User with ID ${userId} not found`, RepositoryErrorType.NOT_FOUND, error);
      }
      this.handleError(error, `Failed to update verification status for user with ID: ${userId}`);
      return {} as User; // Will never be reached due to handleError throwing
    }
  }

  /**
   * Find a user with their membership data
   * 
   * @param userId - User ID
   * @returns Promise with user including membership data or null if not found
   */
  async findWithMemberships(userId: string): Promise<User | null> {
    try {
      return await this.prisma.user.findUnique({
        where: { id: userId },
        include: {
          memberships: {
            include: {
              membershipTier: true
            }
          }
        }
      });
    } catch (error) {
      this.handleError(error, `Failed to find user with memberships for ID: ${userId}`);
      return null; // Will never be reached due to handleError throwing
    }
  }
}
