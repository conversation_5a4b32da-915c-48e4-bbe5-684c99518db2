/**
 * Transaction Repository Interface
 * 
 * This interface defines the operations specific to payment transaction entities,
 * extending the base repository interface.
 */

import { PaymentStatus, Transaction } from '@prisma/client';
import { BaseRepository } from '../base/base-repository.interface.js';

/**
 * Transaction repository interface for transaction-specific operations
 * @extends BaseRepository<Transaction, string>
 */
export interface TransactionRepository extends BaseRepository<Transaction, string> {
  /**
   * Find transactions by user ID
   * @param userId - The user ID to filter by
   * @returns Promise with array of transactions for the user
   */
  findByUser(userId: string): Promise<Transaction[]>;
  
  /**
   * Find transactions by membership ID
   * @param membershipId - The membership ID to filter by
   * @returns Promise with array of transactions for the membership
   */
  findByMembership(membershipId: string): Promise<Transaction[]>;
  
  /**
   * Find transactions by status
   * @param status - The transaction status to filter by
   * @returns Promise with array of transactions with the specified status
   */
  findByStatus(status: PaymentStatus): Promise<Transaction[]>;
  
  /**
   * Find transactions within a date range
   * @param startDate - The start date of the range
   * @param endDate - The end date of the range
   * @returns Promise with array of transactions within the date range
   */
  findByDateRange(startDate: Date, endDate: Date): Promise<Transaction[]>;
  
  /**
   * Calculate the sum of transaction amounts within a date range
   * @param startDate - The start date of the range
   * @param endDate - The end date of the range
   * @param status - Optional status to filter by
   * @returns Promise with the sum of transaction amounts
   */
  sumByDateRange(startDate: Date, endDate: Date, status?: PaymentStatus): Promise<number>;
}
