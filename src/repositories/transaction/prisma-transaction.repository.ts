/**
 * Prisma Transaction Repository Implementation
 * 
 * This class implements the transaction repository interface using Prisma ORM.
 */

import { PaymentStatus, PrismaClient, Transaction } from '@prisma/client';
import { TransactionRepository } from './transaction-repository.interface.js';
import { PrismaBaseRepository, RepositoryError, RepositoryErrorType } from '../base/prisma-base.repository.js';

/**
 * Prisma implementation of the transaction repository
 */
export class PrismaTransactionRepository extends PrismaBaseRepository<Transaction, string> implements TransactionRepository {
  /**
   * Constructor for PrismaTransactionRepository
   * 
   * @param prisma - Prisma client instance
   */
  constructor(prisma: PrismaClient) {
    super(prisma, prisma.transaction);
  }

  /**
   * Find transactions by user ID
   * 
   * @param userId - User ID to filter by
   * @returns Promise with array of transactions for the user
   */
  async findByUser(userId: string): Promise<Transaction[]> {
    try {
      return await this.prisma.transaction.findMany({
        where: {
          userId
        },
        orderBy: {
          createdAt: 'desc' // Most recent first
        }
      });
    } catch (error) {
      this.handleError(error, `Failed to find transactions for user ID: ${userId}`);
      return []; // This line will never be reached due to handleError throwing
    }
  }

  /**
   * Find transactions by membership ID
   * 
   * @param membershipId - Membership ID to filter by
   * @returns Promise with array of transactions for the membership
   */
  async findByMembership(membershipId: string): Promise<Transaction[]> {
    try {
      return await this.prisma.transaction.findMany({
        where: {
          membershipId
        },
        orderBy: {
          createdAt: 'desc' // Most recent first
        }
      });
    } catch (error) {
      this.handleError(error, `Failed to find transactions for membership ID: ${membershipId}`);
      return []; // This line will never be reached due to handleError throwing
    }
  }

  /**
   * Find transactions by status
   * 
   * @param status - Transaction status to filter by
   * @returns Promise with array of transactions with the specified status
   */
  async findByStatus(status: PaymentStatus): Promise<Transaction[]> {
    try {
      return await this.prisma.transaction.findMany({
        where: {
          status
        },
        orderBy: {
          createdAt: 'desc' // Most recent first
        }
      });
    } catch (error) {
      this.handleError(error, `Failed to find transactions with status: ${status}`);
      return []; // This line will never be reached due to handleError throwing
    }
  }

  /**
   * Find transactions within a date range
   * 
   * @param startDate - Start date of the range
   * @param endDate - End date of the range
   * @returns Promise with array of transactions within the date range
   */
  async findByDateRange(startDate: Date, endDate: Date): Promise<Transaction[]> {
    try {
      // Validate date range
      if (startDate > endDate) {
        throw new RepositoryError(
          'Start date must be before end date',
          RepositoryErrorType.VALIDATION
        );
      }

      return await this.prisma.transaction.findMany({
        where: {
          createdAt: {
            gte: startDate,
            lte: endDate
          }
        },
        orderBy: {
          createdAt: 'desc' // Most recent first
        }
      });
    } catch (error) {
      if (error instanceof RepositoryError) {
        throw error; // Re-throw repository errors
      }
      this.handleError(error, `Failed to find transactions between ${startDate.toISOString()} and ${endDate.toISOString()}`);
      return []; // This line will never be reached due to handleError throwing
    }
  }

  /**
   * Calculate the sum of transaction amounts within a date range
   * 
   * @param startDate - Start date of the range
   * @param endDate - End date of the range
   * @param status - Optional status to filter by
   * @returns Promise with the sum of transaction amounts
   */
  async sumByDateRange(startDate: Date, endDate: Date, status?: PaymentStatus): Promise<number> {
    try {
      // Validate date range
      if (startDate > endDate) {
        throw new RepositoryError(
          'Start date must be before end date',
          RepositoryErrorType.VALIDATION
        );
      }

      // Build the where condition
      const whereCondition = {
        createdAt: {
          gte: startDate,
          lte: endDate
        },
        ...(status !== undefined ? { status } : {})
      };

      // Use Prisma's aggregation to calculate the sum
      const result = await this.prisma.transaction.aggregate({
        where: whereCondition,
        _sum: {
          amount: true
        }
      });

      // Return the sum, defaulting to 0 if no transactions found
      // Use optional chaining to safely access potentially undefined properties
      return result?._sum?.amount ? parseFloat(result._sum.amount.toString()) : 0;
    } catch (error) {
      if (error instanceof RepositoryError) {
        throw error; // Re-throw repository errors
      }
      this.handleError(error, `Failed to calculate sum of transactions between ${startDate.toISOString()} and ${endDate.toISOString()}`);
      return 0; // This line will never be reached due to handleError throwing
    }
  }
}
