/**
 * Repository Error Handling
 * 
 * This module defines custom error classes for repository operations and
 * provides functions to convert Prisma errors to application-specific errors.
 * It extends the base error handling system from utils/errors.ts and adds
 * repository-specific error types and handling logic.
 */

import { Prisma } from '@prisma/client';
import { AppError, NotFoundError as BaseNotFoundError } from '../utils/errors.js';

/**
 * Repository Error Type Enum 
 * 
 * Defines the different types of repository errors for better categorization
 */
export enum RepositoryErrorType {
  NOT_FOUND = 'NOT_FOUND',
  DUPLICATE = 'DUPLICATE',
  FOREIGN_KEY = 'FOREIGN_KEY',
  VALIDATION = 'VALIDATION',
  UNKNOWN = 'UNKNOWN'
}

/**
 * Base Repository Error Class
 * 
 * This is the base class for all repository-specific errors.
 * It extends the application's AppError class and adds repository-specific context.
 */
export class RepositoryError extends AppError {
  public readonly type: RepositoryErrorType;
  public readonly entityName: string | undefined;
  public override readonly cause: Error | undefined;
  public override readonly statusCode: number;
  public readonly isOperational: boolean;

  /**
   * Constructor for Repository Error
   * 
   * @param message - Human-readable error message
   * @param type - Type of repository error from RepositoryErrorType enum
   * @param statusCode - HTTP status code associated with this error
   * @param entityName - Optional name of the entity involved (e.g., 'User', 'Membership')
   * @param cause - Optional underlying error that caused this repository error
   * @param isOperational - Indicates if this is an expected operational error
   */
  constructor(
    message: string,
    type: RepositoryErrorType,
    statusCode: number = 500,
    entityName?: string,
    cause?: Error,
    isOperational: boolean = true
  ) {
    super(message);
    this.type = type;
    this.entityName = entityName;
    this.cause = cause;
    this.statusCode = statusCode;
    this.isOperational = isOperational;
    
    // Set name to the class name for better error identification
    this.name = this.constructor.name;
  }
}

/**
 * Not Found Repository Error
 * 
 * Used when an entity cannot be found in the database.
 */
export class NotFoundError extends RepositoryError {
  /**
   * Constructor for Not Found Repository Error
   * 
   * @param message - Human-readable error message
   * @param entityName - Optional name of the entity that wasn't found
   * @param cause - Optional underlying error
   */
  constructor(message: string, entityName?: string, cause?: Error) {
    super(
      message,
      RepositoryErrorType.NOT_FOUND,
      404,
      entityName,
      cause,
      true
    );
  }
}

/**
 * Duplicate Entity Repository Error
 * 
 * Used when a unique constraint violation occurs (e.g., duplicate email).
 */
export class DuplicateError extends RepositoryError {
  /**
   * Constructor for Duplicate Entity Repository Error
   * 
   * @param message - Human-readable error message
   * @param entityName - Optional name of the entity with duplication
   * @param cause - Optional underlying error
   */
  constructor(message: string, entityName?: string, cause?: Error) {
    super(
      message,
      RepositoryErrorType.DUPLICATE,
      409, // Conflict status code
      entityName,
      cause,
      true
    );
  }
}

/**
 * Foreign Key Repository Error
 * 
 * Used when a foreign key constraint violation occurs.
 */
export class ForeignKeyError extends RepositoryError {
  /**
   * Constructor for Foreign Key Repository Error
   * 
   * @param message - Human-readable error message
   * @param entityName - Optional name of the entity with foreign key violation
   * @param cause - Optional underlying error
   */
  constructor(message: string, entityName?: string, cause?: Error) {
    super(
      message,
      RepositoryErrorType.FOREIGN_KEY,
      400, // Bad Request status code
      entityName,
      cause,
      true
    );
  }
}

/**
 * Validation Repository Error
 * 
 * Used when validation fails for repository operations.
 */
export class ValidationError extends RepositoryError {
  /**
   * Constructor for Validation Repository Error
   * 
   * @param message - Human-readable error message
   * @param entityName - Optional name of the entity failing validation
   * @param cause - Optional underlying error
   */
  constructor(message: string, entityName?: string, cause?: Error) {
    super(
      message,
      RepositoryErrorType.VALIDATION,
      400, // Bad Request status code
      entityName,
      cause,
      true
    );
  }
}

/**
 * Type Guard function to check if an error is a Prisma error
 * 
 * @param error - Error to check
 * @returns True if the error is a Prisma client known error
 */
export function isPrismaError(error: any): error is Prisma.PrismaClientKnownRequestError {
  return (
    error instanceof Prisma.PrismaClientKnownRequestError ||
    error instanceof Prisma.PrismaClientValidationError ||
    error instanceof Prisma.PrismaClientRustPanicError ||
    error instanceof Prisma.PrismaClientInitializationError ||
    error instanceof Prisma.PrismaClientUnknownRequestError
  );
}

/**
 * Extract entity name from a Prisma error if possible
 * 
 * @param error - Prisma error to analyze
 * @returns Entity name or undefined if not found
 */
function extractEntityName(error: Prisma.PrismaClientKnownRequestError): string | undefined {
  // Try to extract entity name from error target
  if (error.meta?.['target'] && Array.isArray(error.meta['target'])) {
    return error.meta['target'][0] as string;
  }
  
  // Try to parse the model name from the error message
  const modelMatch = error.message.match(/\binvalid\s+`[^`]+\.[^`]+`\s+invocation.*\bon\s+(\w+)\b/i);
  if (modelMatch && modelMatch[1]) {
    return modelMatch[1];
  }
  
  return undefined;
}

/**
 * Convert Prisma errors to application-specific repository errors
 * 
 * This function takes a Prisma error and converts it to an appropriate
 * repository-specific error based on the error code and details.
 * 
 * @param error - Original error from Prisma or other source
 * @param defaultMessage - Default message to use if specific error can't be determined
 * @returns Repository-specific error
 */
export function convertPrismaError(error: unknown, defaultMessage: string = 'Database operation failed'): RepositoryError {
  // If it's already a repository error, return it as is
  if (error instanceof RepositoryError) {
    return error;
  }
  
  // Handle Prisma client errors
  if (error instanceof Prisma.PrismaClientKnownRequestError) {
    const entityName = extractEntityName(error);
    
    // Handle various Prisma error codes
    switch (error.code) {
      // Not Found errors
      case 'P2001': // Record does not exist
      case 'P2018': // Required relation not found
        return new NotFoundError(
          `Entity not found: ${error.message}`, 
          entityName, 
          error
        );
      
      // Unique Constraint Violations (Duplicates)
      case 'P2002': {
        const field = error.meta?.['target'] ? (error.meta['target'] as string[]).join(', ') : 'unknown field';
        return new DuplicateError(
          `Duplicate entry: The ${field} already exists`, 
          entityName,
          error
        );
      }
      
      // Foreign Key Constraint Violations
      case 'P2003': {
        const field = error.meta?.['field_name'] ? error.meta['field_name'] as string : 'unknown field';
        return new ForeignKeyError(
          `Foreign key constraint failed on field: ${field}`,
          entityName,
          error
        );
      }
      
      // Required Field Constraint Violations
      case 'P2011': // Null constraint violation
      case 'P2012': // Missing required value
        return new ValidationError(
          `Validation error: ${error.message}`,
          entityName,
          error
        );
      
      // Default case for other Prisma errors
      default:
        return new RepositoryError(
          error.message || defaultMessage,
          RepositoryErrorType.UNKNOWN,
          500,
          entityName,
          error,
          false
        );
    }
  }
  
  // Handle Prisma validation errors
  if (error instanceof Prisma.PrismaClientValidationError) {
    return new ValidationError(
      `Validation error: ${error.message}`,
      undefined,
      error
    );
  }
  
  // Handle standard NotFoundError from utils
  if (error instanceof BaseNotFoundError) {
    return new NotFoundError(
      error.message,
      undefined,
      error
    );
  }
  
  // Handle all other errors
  return new RepositoryError(
    error instanceof Error ? error.message : defaultMessage,
    RepositoryErrorType.UNKNOWN,
    500,
    undefined,
    error instanceof Error ? error : undefined,
    false
  );
}

/**
 * Helper function to create a NotFoundError for a specific entity
 * 
 * @param entityName - Name of the entity that wasn't found
 * @param id - ID that was searched for
 * @returns NotFoundError with a formatted message
 */
export function createNotFoundError(entityName: string, id: string | number): NotFoundError {
  return new NotFoundError(
    `${entityName} with ID '${id}' not found`,
    entityName
  );
}

/**
 * Map Prisma error to an appropriate HTTP status code
 * 
 * @param error - Prisma error to analyze
 * @returns Appropriate HTTP status code
 */
export function getStatusCodeForPrismaError(error: Prisma.PrismaClientKnownRequestError): number {
  switch (error.code) {
    case 'P2001': // Record does not exist
    case 'P2018': // Required relation not found
      return 404;
    
    case 'P2002': // Unique constraint violation
      return 409;
    
    case 'P2003': // Foreign key constraint violation
    case 'P2011': // Null constraint violation
    case 'P2012': // Missing required value
      return 400;
    
    default:
      return 500;
  }
}
