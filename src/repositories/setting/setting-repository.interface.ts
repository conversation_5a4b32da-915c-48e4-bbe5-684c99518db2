/**
 * Setting Repository Interface
 * 
 * This interface defines the operations specific to system settings,
 * extending the base repository interface.
 */

import { Setting } from '@prisma/client';
import { BaseRepository } from '../base/base-repository.interface.js';

/**
 * Setting repository interface for setting-specific operations
 * @extends BaseRepository<Setting, string>
 */
export interface SettingRepository extends BaseRepository<Setting, string> {
  /**
   * Find a setting by its key
   * @param key - The setting key to find
   * @returns Promise with setting or null if not found
   */
  findByKey(key: string): Promise<Setting | null>;
  
  /**
   * Find all public settings
   * @returns Promise with array of public settings
   */
  findPublic(): Promise<Setting[]>;
  
  /**
   * Update a setting if it exists, create if it doesn't
   * @param key - The setting key
   * @param value - The setting value
   * @param isPublic - Whether the setting is public (optional)
   * @returns Promise with the created or updated setting
   */
  upsert(key: string, value: string, isPublic?: boolean): Promise<Setting>;
  
  /**
   * Find a setting with cache support
   * This method tries to use a cache first before hitting the database
   * @param key - The setting key
   * @returns Promise with setting or null if not found
   */
  findWithCache(key: string): Promise<Setting | null>;
}
