/**
 * Prisma Setting Repository Implementation
 * 
 * This class implements the setting repository interface using Prisma ORM
 * with added support for caching settings.
 */

import { PrismaClient, Setting } from '@prisma/client';
import { SettingRepository } from './setting-repository.interface.js';
import { PrismaBaseRepository } from '../base/prisma-base.repository.js';

/**
 * Simple in-memory cache for settings
 */
class SettingsCache {
  private static instance: SettingsCache;
  private cache: Map<string, { value: Setting; timestamp: number }> = new Map();
  private readonly TTL_MS = 5 * 60 * 1000; // 5 minutes cache TTL
  
  /**
   * Get the singleton instance of the cache
   */
  public static getInstance(): SettingsCache {
    if (!SettingsCache.instance) {
      SettingsCache.instance = new SettingsCache();
    }
    return SettingsCache.instance;
  }
  
  /**
   * Get a value from the cache
   * @param key - The key to look up
   * @returns The setting or null if not found or expired
   */
  public get(key: string): Setting | null {
    const entry = this.cache.get(key);
    
    if (!entry) {
      return null;
    }
    
    // Check if the entry has expired
    if (Date.now() - entry.timestamp > this.TTL_MS) {
      this.cache.delete(key);
      return null;
    }
    
    return entry.value;
  }
  
  /**
   * Set a value in the cache
   * @param key - The key to store
   * @param value - The setting to cache
   */
  public set(key: string, value: Setting): void {
    this.cache.set(key, {
      value,
      timestamp: Date.now()
    });
  }
  
  /**
   * Remove a value from the cache
   * @param key - The key to remove
   */
  public invalidate(key: string): void {
    this.cache.delete(key);
  }
  
  /**
   * Clear all values from the cache
   */
  public clear(): void {
    this.cache.clear();
  }
}

/**
 * Prisma implementation of the setting repository
 */
export class PrismaSettingRepository extends PrismaBaseRepository<Setting, string> implements SettingRepository {
  private cache: SettingsCache;
  
  /**
   * Constructor for PrismaSettingRepository
   * 
   * @param prisma - Prisma client instance
   */
  constructor(prisma: PrismaClient) {
    super(prisma, prisma.setting);
    this.cache = SettingsCache.getInstance();
  }

  /**
   * Find a setting by its key
   * 
   * @param key - Setting key to find
   * @returns Promise with setting or null if not found
   */
  async findByKey(key: string): Promise<Setting | null> {
    try {
      return await this.prisma.setting.findUnique({
        where: { key }
      });
    } catch (error) {
      this.handleError(error, `Failed to find setting with key: ${key}`);
      return null; // This line will never be reached due to handleError throwing
    }
  }

  /**
   * Find all public settings
   * 
   * @returns Promise with array of public settings
   */
  async findPublic(): Promise<Setting[]> {
    try {
      return await this.prisma.setting.findMany({
        where: {
          isPublic: true
        },
        orderBy: {
          key: 'asc' // Alphabetical order by key
        }
      });
    } catch (error) {
      this.handleError(error, 'Failed to find public settings');
      return []; // This line will never be reached due to handleError throwing
    }
  }

  /**
   * Update a setting if it exists, create if it doesn't
   * 
   * @param key - Setting key
   * @param value - Setting value
   * @param isPublic - Whether the setting is public (optional)
   * @returns Promise with the created or updated setting
   */
  async upsert(key: string, value: string, isPublic: boolean = false): Promise<Setting> {
    try {
      const result = await this.prisma.setting.upsert({
        where: {
          key
        },
        update: {
          value,
          isPublic,
          updatedAt: new Date()
        },
        create: {
          key,
          value,
          isPublic
        }
      });
      
      // Invalidate the cache for this key
      this.cache.invalidate(key);
      
      return result;
    } catch (error) {
      this.handleError(error, `Failed to upsert setting with key: ${key}`);
      return {} as Setting; // This line will never be reached due to handleError throwing
    }
  }

  /**
   * Find a setting with cache support
   * This method tries to use a cache first before hitting the database
   * 
   * @param key - Setting key
   * @returns Promise with setting or null if not found
   */
  async findWithCache(key: string): Promise<Setting | null> {
    try {
      // Try to get from cache first
      const cachedSetting = this.cache.get(key);
      if (cachedSetting) {
        return cachedSetting;
      }
      
      // If not in cache, get from database
      const setting = await this.prisma.setting.findUnique({
        where: { key }
      });
      
      // Store in cache if found
      if (setting) {
        this.cache.set(key, setting);
      }
      
      return setting;
    } catch (error) {
      this.handleError(error, `Failed to find setting with key (cache): ${key}`);
      return null; // This line will never be reached due to handleError throwing
    }
  }
}
