/**
 * Unit of Work Pattern Implementation
 * 
 * This file implements the Unit of Work pattern to manage transactions across
 * multiple repositories while ensuring they all use the same Prisma client instance.
 * It provides methods to begin, commit, and rollback transactions, as well as
 * access to all repositories.
 */

import { PrismaClient } from '@prisma/client';
import { UserRepository } from './user/user-repository.interface.js';
import { MembershipTierRepository } from './membership-tier/membership-tier-repository.interface.js';
import { MembershipRepository } from './membership/membership-repository.interface.js';
import { GiveawayRepository } from './giveaway/giveaway-repository.interface.js';
import { PrizeRepository } from './prize/prize-repository.interface.js';
import { EntryRepository } from './entry/entry-repository.interface.js';
import { WinnerRepository } from './winner/winner-repository.interface.js';
import { TransactionRepository } from './transaction/transaction-repository.interface.js';
import { ContentRepository } from './content/content-repository.interface.js';
import { NotificationRepository } from './notification/notification-repository.interface.js';
import { SettingRepository } from './setting/setting-repository.interface.js';

import { PrismaUserRepository } from './user/prisma-user.repository.js';
import { PrismaMembershipTierRepository } from './membership-tier/prisma-membership-tier.repository.js';
import { PrismaMembershipRepository } from './membership/prisma-membership.repository.js';
import { PrismaGiveawayRepository } from './giveaway/prisma-giveaway.repository.js';
import { PrismaPrizeRepository } from './prize/prisma-prize.repository.js';
import { PrismaEntryRepository } from './entry/prisma-entry.repository.js';
import { PrismaWinnerRepository } from './winner/prisma-winner.repository.js';
import { PrismaTransactionRepository } from './transaction/prisma-transaction.repository.js';
import { PrismaContentRepository } from './content/prisma-content.repository.js';
import { PrismaNotificationRepository } from './notification/prisma-notification.repository.js';
import { PrismaSettingRepository } from './setting/prisma-setting.repository.js';

/**
 * Transaction Status Enum
 * Represents the current state of a transaction
 */
enum TransactionStatus {
  NONE = 'none',
  ACTIVE = 'active',
  COMMITTED = 'committed',
  ROLLED_BACK = 'rolledBack'
}

/**
 * Unit of Work Interface
 * Defines the contract for the Unit of Work implementation
 */
export interface IUnitOfWork {
  // Repository properties
  readonly users: UserRepository;
  readonly membershipTiers: MembershipTierRepository;
  readonly memberships: MembershipRepository;
  readonly giveaways: GiveawayRepository;
  readonly prizes: PrizeRepository;
  readonly entries: EntryRepository;
  readonly winners: WinnerRepository;
  readonly transactions: TransactionRepository;
  readonly content: ContentRepository;
  readonly notifications: NotificationRepository;
  readonly settings: SettingRepository;

  // Transaction methods
  beginTransaction(): Promise<void>;
  commitTransaction(): Promise<void>;
  rollbackTransaction(): Promise<void>;
  
  // Method to execute code in a transaction
  withTransaction<T>(
    work: (unitOfWork: IUnitOfWork) => Promise<T>
  ): Promise<T>;
  
  // Cleanup method
  dispose(): Promise<void>;
}

/**
 * Unit of Work Implementation
 * Implements the Unit of Work pattern for transaction management
 */
class UnitOfWork implements IUnitOfWork {
  private prisma: PrismaClient;
  private _transactionStatus: TransactionStatus = TransactionStatus.NONE;
  
  // Repository instances
  private _users: UserRepository;
  private _membershipTiers: MembershipTierRepository;
  private _memberships: MembershipRepository;
  private _giveaways: GiveawayRepository;
  private _prizes: PrizeRepository;
  private _entries: EntryRepository;
  private _winners: WinnerRepository;
  private _transactions: TransactionRepository;
  private _content: ContentRepository;
  private _notifications: NotificationRepository;
  private _settings: SettingRepository;

  /**
   * Constructor
   * Initialize the Unit of Work with a PrismaClient instance
   * @param prisma - PrismaClient instance
   */
  constructor(prisma: PrismaClient) {
    this.prisma = prisma;
    
    // Initialize all repositories with the same Prisma client instance
    this._users = new PrismaUserRepository(this.prisma);
    this._membershipTiers = new PrismaMembershipTierRepository(this.prisma);
    this._memberships = new PrismaMembershipRepository(this.prisma);
    this._giveaways = new PrismaGiveawayRepository(this.prisma);
    this._prizes = new PrismaPrizeRepository(this.prisma);
    this._entries = new PrismaEntryRepository(this.prisma);
    this._winners = new PrismaWinnerRepository(this.prisma);
    this._transactions = new PrismaTransactionRepository(this.prisma);
    this._content = new PrismaContentRepository(this.prisma);
    this._notifications = new PrismaNotificationRepository(this.prisma);
    this._settings = new PrismaSettingRepository(this.prisma);
  }

  // Repository getters
  get users(): UserRepository {
    return this._users;
  }

  get membershipTiers(): MembershipTierRepository {
    return this._membershipTiers;
  }

  get memberships(): MembershipRepository {
    return this._memberships;
  }

  get giveaways(): GiveawayRepository {
    return this._giveaways;
  }

  get prizes(): PrizeRepository {
    return this._prizes;
  }

  get entries(): EntryRepository {
    return this._entries;
  }

  get winners(): WinnerRepository {
    return this._winners;
  }

  get transactions(): TransactionRepository {
    return this._transactions;
  }

  get content(): ContentRepository {
    return this._content;
  }

  get notifications(): NotificationRepository {
    return this._notifications;
  }

  get settings(): SettingRepository {
    return this._settings;
  }

  /**
   * Begin a new transaction
   * @throws Error if a transaction is already active
   */
  async beginTransaction(): Promise<void> {
    if (this._transactionStatus === TransactionStatus.ACTIVE) {
      throw new Error('A transaction is already active');
    }
    
    await this.prisma.$transaction(async (prisma) => {
      // Update the prisma client in transaction context
      this.prisma = prisma as unknown as PrismaClient;
      this._transactionStatus = TransactionStatus.ACTIVE;
      
      // This empty transaction is just to initiate the transaction
      // The actual operations will be performed by the repositories
    });
  }

  /**
   * Commit the current transaction
   * @throws Error if no transaction is active
   */
  async commitTransaction(): Promise<void> {
    if (this._transactionStatus !== TransactionStatus.ACTIVE) {
      throw new Error('No active transaction to commit');
    }
    
    // In Prisma, the transaction is automatically committed at the end of the
    // transaction callback, so we just need to update our status
    this._transactionStatus = TransactionStatus.COMMITTED;
  }

  /**
   * Rollback the current transaction
   * @throws Error if no transaction is active
   */
  async rollbackTransaction(): Promise<void> {
    if (this._transactionStatus !== TransactionStatus.ACTIVE) {
      throw new Error('No active transaction to rollback');
    }
    
    // In Prisma, we need to throw an error to trigger a rollback
    this._transactionStatus = TransactionStatus.ROLLED_BACK;
    throw new Error('ROLLBACK_TRANSACTION');
  }

  /**
   * Execute a function within a transaction
   * @param work - Function to execute within the transaction
   * @returns Result of the function execution
   */
  async withTransaction<T>(
    work: (unitOfWork: IUnitOfWork) => Promise<T>
  ): Promise<T> {
    return this.prisma.$transaction(async (prisma) => {
      // Create a new Unit of Work with the transaction-specific Prisma client
      const transactionUnitOfWork = new UnitOfWork(prisma as unknown as PrismaClient);
      transactionUnitOfWork._transactionStatus = TransactionStatus.ACTIVE;
      
      try {
        // Execute the work function with the transaction-specific Unit of Work
        const result = await work(transactionUnitOfWork);
        transactionUnitOfWork._transactionStatus = TransactionStatus.COMMITTED;
        return result;
      } catch (error) {
        // If the error is our rollback signal, we just set the status and re-throw
        if (error instanceof Error && error.message === 'ROLLBACK_TRANSACTION') {
          transactionUnitOfWork._transactionStatus = TransactionStatus.ROLLED_BACK;
        }
        throw error; // Re-throw the error to trigger Prisma's rollback
      }
    });
  }

  /**
   * Clean up resources used by this Unit of Work
   */
  async dispose(): Promise<void> {
    // Nothing to do here for now, as Prisma client will be disposed elsewhere
    // If we add any resources that need explicit cleanup, they would go here
  }
}

/**
 * Factory function to create a new Unit of Work instance
 * @param prisma - Optional PrismaClient instance (will create a new one if not provided)
 * @returns A new Unit of Work instance
 */
export function createUnitOfWork(prisma?: PrismaClient): IUnitOfWork {
  return new UnitOfWork(prisma || new PrismaClient());
}

// Export the UnitOfWork class and interface
export { UnitOfWork };
