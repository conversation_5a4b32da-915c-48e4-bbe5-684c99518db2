/**
 * Prisma Membership Tier Repository Implementation
 * 
 * This class implements the membership tier repository interface using Prisma ORM.
 */

import { PrismaClient, MembershipTier } from '@prisma/client';
import { MembershipTierRepository } from './membership-tier-repository.interface.js';
import { PrismaBaseRepository } from '../base/prisma-base.repository.js';

/**
 * Prisma implementation of the membership tier repository
 */
export class PrismaMembershipTierRepository extends PrismaBaseRepository<MembershipTier, string> implements MembershipTierRepository {
  /**
   * Constructor for PrismaMembershipTierRepository
   * 
   * @param prisma - Prisma client instance
   */
  constructor(prisma: PrismaClient) {
    super(prisma, prisma.membershipTier);
  }

  /**
   * Find only active membership tiers
   * 
   * @returns Promise with array of active membership tiers
   */
  async findActive(): Promise<MembershipTier[]> {
    try {
      return await this.prisma.membershipTier.findMany({
        where: { isActive: true }
      });
    } catch (error) {
      this.handleError(error, 'Failed to find active membership tiers');
    }
  }

  /**
   * Find membership tiers sorted by display order
   * 
   * @returns Promise with array of sorted membership tiers
   */
  async findByDisplayOrder(): Promise<MembershipTier[]> {
    try {
      return await this.prisma.membershipTier.findMany({
        orderBy: { displayOrder: 'asc' }
      });
    } catch (error) {
      this.handleError(error, 'Failed to find membership tiers by display order');
    }
  }

  /**
   * Find membership tiers with detailed feature data
   * 
   * @param tierIds - Optional array of tier IDs to filter by
   * @returns Promise with array of membership tiers including feature details
   */
  async findWithFeatures(tierIds?: string[]): Promise<MembershipTier[]> {
    try {
      const where = tierIds?.length 
        ? { id: { in: tierIds } }
        : {};
        
      return await this.prisma.membershipTier.findMany({
        where,
        orderBy: { displayOrder: 'asc' }
      });
    } catch (error) {
      this.handleError(error, 'Failed to find membership tiers with features');
    }
  }

  /**
   * Find membership tiers by active status
   * 
   * @param isActive - Whether to find active or inactive tiers
   * @returns Promise with array of tiers
   */
  async findByActiveStatus(isActive: boolean): Promise<any[]> {
    try {
      return await this.prisma.membershipTier.findMany({
        where: { isActive }
      });
    } catch (error) {
      this.handleError(error, `Failed to find membership tiers with active status: ${isActive}`);
      return []; // This line will never be reached but satisfies TypeScript
    }
  }
}
