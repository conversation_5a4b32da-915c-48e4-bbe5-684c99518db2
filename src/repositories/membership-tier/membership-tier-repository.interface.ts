/**
 * Membership Tier Repository Interface
 * 
 * This interface defines the operations specific to membership tier entities,
 * extending the base repository interface.
 */

import { MembershipTier } from '@prisma/client';
import { BaseRepository } from '../base/base-repository.interface.js';

/**
 * Membership Tier repository interface for tier-specific operations
 * @extends BaseRepository<MembershipTier, string>
 */
export interface MembershipTierRepository extends BaseRepository<MembershipTier, string> {
  /**
   * Find only active membership tiers
   * @returns Promise with array of active membership tiers
   */
  findActive(): Promise<MembershipTier[]>;
  
  /**
   * Find membership tiers sorted by display order
   * @returns Promise with array of sorted membership tiers
   */
  findByDisplayOrder(): Promise<MembershipTier[]>;
  
  /**
   * Find membership tiers with detailed feature data
   * @param tierIds - Optional array of tier IDs to filter by
   * @returns Promise with array of membership tiers including feature details
   */
  findWithFeatures(tierIds?: string[]): Promise<MembershipTier[]>;
  
  /**
   * Find membership tiers by active status
   * @param isActive - Whether to find active or inactive tiers
   * @returns Promise with array of tiers filtered by active status
   */
  findByActiveStatus(isActive: boolean): Promise<MembershipTier[]>;
}
