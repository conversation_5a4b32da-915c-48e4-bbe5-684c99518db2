/**
 * Membership Repository Interface
 * 
 * This interface defines the operations specific to membership entities,
 * extending the base repository interface.
 */

import { Membership } from '@prisma/client';
import { BaseRepository } from '../base/base-repository.interface.js';

/**
 * Membership repository interface for membership-specific operations
 * @extends BaseRepository<Membership, string>
 */
export interface MembershipRepository extends BaseRepository<Membership, string> {
  /**
   * Find memberships by user ID
   * @param userId - The user ID to find memberships for
   * @returns Promise with array of memberships
   */
  findByUserId(userId: string): Promise<Membership[]>;
  
  /**
   * Find active memberships by user ID
   * @param userId - The user ID to find active memberships for
   * @returns Promise with array of active memberships
   */
  findActiveByUserId(userId: string): Promise<Membership[]>;
  
  /**
   * Update membership history for tracking changes
   * @param membershipId - The membership ID
   * @param historyData - The history data to record
   * @returns Promise with the updated membership
   */
  updateMembershipHistory(membershipId: string, historyData: any): Promise<Membership>;
  
  /**
   * Find memberships with tier details
   * @param membershipId - The membership ID
   * @returns Promise with membership including tier details or null if not found
   */
  findWithTierDetails(membershipId: string): Promise<Membership | null>;
  
  /**
   * Find memberships that are expiring soon
   * @param daysThreshold - Number of days to consider as "soon" (default: 7)
   * @returns Promise with array of memberships expiring soon
   */
  findExpiringSoon(daysThreshold?: number): Promise<Membership[]>;
}
