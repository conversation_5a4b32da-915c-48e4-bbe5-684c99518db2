import { FilterOptions } from '../base/base-repository.interface.js';
import { BaseRepository } from '../base/base-repository.interface.js';

/**
 * Repository interface for membership tiers
 */
export interface MembershipTierRepository extends BaseRepository<any, string> {
  /**
   * Find a membership tier by ID
   * 
   * @param id - The ID of the tier to find
   * @returns Promise with the tier or null if not found
   */
  findById(id: string): Promise<any>;
  
  /**
   * Find multiple membership tiers with optional filtering
   * 
   * @param filter - Optional filter criteria
   * @returns Promise with array of tiers
   */
  findMany(filter?: FilterOptions): Promise<any[]>;

  /**
   * Find membership tiers by active status
   * 
   * @param isActive - Whether to find active or inactive tiers
   * @returns Promise with array of tiers
   */
  findByActiveStatus(isActive: boolean): Promise<any[]>;
} 