/**
 * Prisma Membership Repository Implementation
 * 
 * This class implements the membership repository interface using Prisma ORM.
 */

import { PrismaClient, Membership, MembershipStatus } from '@prisma/client';
import { MembershipRepository } from './membership-repository.interface.js';
import { PrismaBaseRepository, RepositoryError, RepositoryErrorType } from '../base/prisma-base.repository.js';

/**
 * Prisma implementation of the membership repository
 */
export class PrismaMembershipRepository extends PrismaBaseRepository<Membership, string> implements MembershipRepository {
  /**
   * Constructor for PrismaMembershipRepository
   * 
   * @param prisma - Prisma client instance
   */
  constructor(prisma: PrismaClient) {
    super(prisma, prisma.membership);
  }

  /**
   * Find memberships by user ID
   * 
   * @param userId - User ID to find memberships for
   * @returns Promise with array of memberships
   */
  async findByUserId(userId: string): Promise<Membership[]> {
    try {
      return await this.prisma.membership.findMany({
        where: { userId }
      });
    } catch (error) {
      this.handleError(error, `Failed to find memberships for user ID: ${userId}`);
    }
  }

  /**
   * Find active memberships by user ID
   * 
   * @param userId - User ID to find active memberships for
   * @returns Promise with array of active memberships
   */
  async findActiveByUserId(userId: string): Promise<Membership[]> {
    try {
      return await this.prisma.membership.findMany({
        where: { 
          userId,
          status: MembershipStatus.ACTIVE,
          endDate: {
            gte: new Date()  // End date is in the future
          }
        }
      });
    } catch (error) {
      this.handleError(error, `Failed to find active memberships for user ID: ${userId}`);
    }
  }

  /**
   * Update membership history for tracking changes
   * 
   * @param membershipId - Membership ID
   * @param historyData - History data to record
   * @returns Promise with updated membership
   */
  async updateMembershipHistory(membershipId: string, historyData: any): Promise<Membership> {
    try {
      // First get the current membership to access existing history
      const membership = await this.prisma.membership.findUnique({
        where: { id: membershipId }
      });

      if (!membership) {
        throw new RepositoryError(
          `Membership with ID ${membershipId} not found`,
          RepositoryErrorType.NOT_FOUND
        );
      }

      // Prepare the new history
      const currentHistory = membership.membershipHistory || [];
      const updatedHistory = Array.isArray(currentHistory)
        ? [...currentHistory, { ...historyData, timestamp: new Date() }]
        : [{ ...historyData, timestamp: new Date() }];

      // Update the membership with the new history
      return await this.prisma.membership.update({
        where: { id: membershipId },
        data: {
          membershipHistory: updatedHistory,
          updatedAt: new Date()
        }
      });
    } catch (error) {
      this.handleError(error, `Failed to update history for membership ID: ${membershipId}`);
    }
  }

  /**
   * Find membership with tier details
   * 
   * @param membershipId - Membership ID
   * @returns Promise with membership including tier details or null if not found
   */
  async findWithTierDetails(membershipId: string): Promise<Membership | null> {
    try {
      return await this.prisma.membership.findUnique({
        where: { id: membershipId },
        include: {
          membershipTier: true
        }
      });
    } catch (error) {
      this.handleError(error, `Failed to find membership with tier details for ID: ${membershipId}`);
    }
  }

  /**
   * Find memberships that are expiring soon
   * 
   * @param daysThreshold - Number of days to consider as "soon" (default: 7)
   * @returns Promise with array of memberships expiring soon
   */
  async findExpiringSoon(daysThreshold: number = 7): Promise<Membership[]> {
    try {
      const today = new Date();
      const thresholdDate = new Date(today);
      thresholdDate.setDate(today.getDate() + daysThreshold);

      return await this.prisma.membership.findMany({
        where: {
          status: MembershipStatus.ACTIVE,
          endDate: {
            gte: today,        // End date is after today
            lte: thresholdDate // But before the threshold date
          }
        },
        include: {
          user: true,
          membershipTier: true
        },
        orderBy: {
          endDate: 'asc'       // Show soonest expiring first
        }
      });
    } catch (error) {
      this.handleError(error, `Failed to find memberships expiring in the next ${daysThreshold} days`);
    }
  }
}
