/**
 * Prisma Notification Repository Implementation
 * 
 * This class implements the notification repository interface using Prisma ORM.
 */

import { Notification, NotificationStatus, PrismaClient } from '@prisma/client';
import { NotificationRepository } from './notification-repository.interface.js';
import { PrismaBaseRepository, RepositoryError, RepositoryErrorType } from '../base/prisma-base.repository.js';

/**
 * Prisma implementation of the notification repository
 */
export class PrismaNotificationRepository extends PrismaBaseRepository<Notification, string> implements NotificationRepository {
  /**
   * Constructor for PrismaNotificationRepository
   * 
   * @param prisma - Prisma client instance
   */
  constructor(prisma: PrismaClient) {
    super(prisma, prisma.notification);
  }

  /**
   * Find notifications by user ID
   * 
   * @param userId - User ID to filter by
   * @returns Promise with array of notifications for the user
   */
  async findByUser(userId: string): Promise<Notification[]> {
    try {
      return await this.prisma.notification.findMany({
        where: {
          userId
        },
        orderBy: {
          createdAt: 'desc' // Most recent first
        }
      });
    } catch (error) {
      this.handleError(error, `Failed to find notifications for user ID: ${userId}`);
      return []; // This line will never be reached due to handleError throwing
    }
  }

  /**
   * Find unread notifications by user ID
   * 
   * @param userId - User ID to filter by
   * @returns Promise with array of unread notifications for the user
   */
  async findUnreadByUser(userId: string): Promise<Notification[]> {
    try {
      return await this.prisma.notification.findMany({
        where: {
          userId,
          status: NotificationStatus.UNREAD
        },
        orderBy: {
          createdAt: 'desc' // Most recent first
        }
      });
    } catch (error) {
      this.handleError(error, `Failed to find unread notifications for user ID: ${userId}`);
      return []; // This line will never be reached due to handleError throwing
    }
  }

  /**
   * Mark a notification as read
   * 
   * @param notificationId - Notification ID to mark as read
   * @returns Promise with the updated notification
   */
  async markAsRead(notificationId: string): Promise<Notification> {
    try {
      // Find the notification first to ensure it exists
      const notification = await this.prisma.notification.findUnique({
        where: { id: notificationId }
      });

      if (!notification) {
        throw new RepositoryError(
          `Notification with ID ${notificationId} not found`,
          RepositoryErrorType.NOT_FOUND
        );
      }

      // Update the notification status to READ
      return await this.prisma.notification.update({
        where: { id: notificationId },
        data: { 
          status: NotificationStatus.READ,
          updatedAt: new Date()
        }
      });
    } catch (error) {
      if (error instanceof RepositoryError) {
        throw error; // Re-throw repository errors
      }
      this.handleError(error, `Failed to mark notification as read: ${notificationId}`);
      return {} as Notification; // This line will never be reached due to handleError throwing
    }
  }

  /**
   * Mark all notifications as read for a user
   * 
   * @param userId - User ID to mark all notifications as read for
   * @returns Promise with the number of notifications updated
   */
  async markAllAsRead(userId: string): Promise<number> {
    try {
      const result = await this.prisma.notification.updateMany({
        where: {
          userId,
          status: NotificationStatus.UNREAD
        },
        data: {
          status: NotificationStatus.READ,
          updatedAt: new Date()
        }
      });

      return result.count;
    } catch (error) {
      this.handleError(error, `Failed to mark all notifications as read for user ID: ${userId}`);
      return 0; // This line will never be reached due to handleError throwing
    }
  }

  /**
   * Delete notifications older than a specified date
   * 
   * @param date - Cutoff date for deletion
   * @returns Promise with the number of notifications deleted
   */
  async deleteOlderThan(date: Date): Promise<number> {
    try {
      const result = await this.prisma.notification.deleteMany({
        where: {
          createdAt: {
            lt: date // Less than the specified date
          }
        }
      });

      return result.count;
    } catch (error) {
      this.handleError(error, `Failed to delete notifications older than: ${date.toISOString()}`);
      return 0; // This line will never be reached due to handleError throwing
    }
  }
}
