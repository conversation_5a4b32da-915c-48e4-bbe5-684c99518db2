/**
 * Notification Repository Interface
 * 
 * This interface defines the operations specific to user notifications,
 * extending the base repository interface.
 */

import { Notification } from '@prisma/client';
import { BaseRepository } from '../base/base-repository.interface.js';

/**
 * Notification repository interface for notification-specific operations
 * @extends BaseRepository<Notification, string>
 */
export interface NotificationRepository extends BaseRepository<Notification, string> {
  /**
   * Find notifications by user ID
   * @param userId - The user ID to filter by
   * @returns Promise with array of notifications for the user
   */
  findByUser(userId: string): Promise<Notification[]>;
  
  /**
   * Find unread notifications by user ID
   * @param userId - The user ID to filter by
   * @returns Promise with array of unread notifications for the user
   */
  findUnreadByUser(userId: string): Promise<Notification[]>;
  
  /**
   * Mark a notification as read
   * @param notificationId - The notification ID to mark as read
   * @returns Promise with the updated notification
   */
  markAsRead(notificationId: string): Promise<Notification>;
  
  /**
   * Mark all notifications as read for a user
   * @param userId - The user ID to mark all notifications as read for
   * @returns Promise with the number of notifications updated
   */
  markAllAsRead(userId: string): Promise<number>;
  
  /**
   * Delete notifications older than a specified date
   * @param date - The cutoff date
   * @returns Promise with the number of notifications deleted
   */
  deleteOlderThan(date: Date): Promise<number>;
}
