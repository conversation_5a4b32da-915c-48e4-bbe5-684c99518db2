/**
 * Winner Repository Interface
 * 
 * This interface defines the operations specific to winner entities,
 * extending the base repository interface.
 */

import { Winner } from '@prisma/client';
import { BaseRepository } from '../base/base-repository.interface.js';

/**
 * Winner repository interface for winner-specific operations
 * @extends BaseRepository<Winner, string>
 */
export interface WinnerRepository extends BaseRepository<Winner, string> {
  /**
   * Find winners by giveaway ID
   * @param giveawayId - The giveaway ID to filter by
   * @returns Promise with array of winners for the giveaway
   */
  findByGiveaway(giveawayId: string): Promise<Winner[]>;
  
  /**
   * Find winners by user ID
   * @param userId - The user ID to filter by
   * @returns Promise with array of winners where the user won
   */
  findByUser(userId: string): Promise<Winner[]>;
  
  /**
   * Find pending winners (selected but not claimed)
   * @param giveawayId - Optional giveaway ID to filter by
   * @returns Promise with array of pending winners
   */
  findPending(giveawayId?: string): Promise<Winner[]>;
  
  /**
   * Find winners with detailed information (user, prize, and giveaway details)
   * @param winnerId - Optional winner ID to filter by
   * @returns Promise with array of winners including related details
   */
  findWithDetails(winnerId?: string): Promise<Winner[]>;
  
  /**
   * Update the status of a winner (for tracking claim status)
   * @param winnerId - The winner ID
   * @param status - The new status (SELECTED, NOTIFIED, CLAIMED, FORFEITED)
   * @returns Promise with the updated winner
   */
  updateStatus(winnerId: string, status: string): Promise<Winner>;
}
