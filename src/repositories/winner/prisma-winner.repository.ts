/**
 * Prisma Winner Repository Implementation
 * 
 * This class implements the winner repository interface using Prisma ORM.
 */

import { PrismaClient, Winner, WinnerStatus } from '@prisma/client';
import { WinnerRepository } from './winner-repository.interface.js';
import { PrismaBaseRepository, RepositoryError, RepositoryErrorType } from '../base/prisma-base.repository.js';

/**
 * Prisma implementation of the winner repository
 */
export class PrismaWinnerRepository extends PrismaBaseRepository<Winner, string> implements WinnerRepository {
  /**
   * Constructor for PrismaWinnerRepository
   * 
   * @param prisma - Prisma client instance
   */
  constructor(prisma: PrismaClient) {
    super(prisma, prisma.winner);
  }

  /**
   * Find winners by giveaway ID
   * 
   * @param giveawayId - Giveaway ID to filter by
   * @returns Promise with array of winners for the giveaway
   */
  async findByGiveaway(giveawayId: string): Promise<Winner[]> {
    try {
      return await this.prisma.winner.findMany({
        where: {
          giveawayId
        },
        orderBy: {
          createdAt: 'desc' // Most recent first
        }
      });
    } catch (error) {
      this.handleError(error, `Failed to find winners for giveaway ID: ${giveawayId}`);
      return []; // This line will never be reached due to handleError throwing
    }
  }

  /**
   * Find winners by user ID
   * 
   * @param userId - User ID to filter by
   * @returns Promise with array of winners where the user won
   */
  async findByUser(userId: string): Promise<Winner[]> {
    try {
      return await this.prisma.winner.findMany({
        where: {
          userId
        },
        orderBy: {
          createdAt: 'desc' // Most recent first
        }
      });
    } catch (error) {
      this.handleError(error, `Failed to find winners for user ID: ${userId}`);
      return []; // This line will never be reached due to handleError throwing
    }
  }

  /**
   * Find pending winners (selected but not claimed)
   * 
   * @param giveawayId - Optional giveaway ID to filter by
   * @returns Promise with array of pending winners
   */
  async findPending(giveawayId?: string): Promise<Winner[]> {
    try {
      const whereCondition = {
        status: {
          in: ['SELECTED', 'NOTIFIED'] as WinnerStatus[] // Pending statuses
        },
        ...(giveawayId ? { giveawayId } : {})
      };
      
      return await this.prisma.winner.findMany({
        where: whereCondition,
        orderBy: {
          createdAt: 'asc' // Oldest pending first
        }
      });
    } catch (error) {
      this.handleError(error, 'Failed to find pending winners');
      return []; // This line will never be reached due to handleError throwing
    }
  }

  /**
   * Find winners with detailed information (user, prize, and giveaway details)
   * 
   * @param winnerId - Optional winner ID to filter by
   * @returns Promise with array of winners including related details
   */
  async findWithDetails(winnerId?: string): Promise<Winner[]> {
    try {
      const whereCondition = winnerId ? { id: winnerId } : {};
      
      return await this.prisma.winner.findMany({
        where: whereCondition,
        include: {
          user: true,
          prize: true,
          giveaway: true
        },
        orderBy: {
          createdAt: 'desc' // Most recent first
        }
      });
    } catch (error) {
      this.handleError(error, 'Failed to find winners with details');
      return []; // This line will never be reached due to handleError throwing
    }
  }

  /**
   * Update the status of a winner (for tracking claim status)
   * 
   * @param winnerId - Winner ID
   * @param status - New status (SELECTED, NOTIFIED, CLAIMED, FORFEITED)
   * @returns Promise with the updated winner
   */
  async updateStatus(winnerId: string, status: string): Promise<Winner> {
    try {
      // Validate that the status is valid
      if (!['SELECTED', 'NOTIFIED', 'CLAIMED', 'FORFEITED'].includes(status)) {
        throw new RepositoryError(
          `Invalid winner status: ${status}. Must be one of: SELECTED, NOTIFIED, CLAIMED, FORFEITED`,
          RepositoryErrorType.VALIDATION
        );
      }

      // Find the winner first to ensure it exists
      const winner = await this.prisma.winner.findUnique({
        where: { id: winnerId }
      });

      if (!winner) {
        throw new RepositoryError(
          `Winner with ID ${winnerId} not found`,
          RepositoryErrorType.NOT_FOUND
        );
      }

      // Update the winner status
      return await this.prisma.winner.update({
        where: { id: winnerId },
        data: { 
          status: status as WinnerStatus,
          updatedAt: new Date()
        }
      });
    } catch (error) {
      if (error instanceof RepositoryError) {
        throw error; // Re-throw repository errors
      }
      this.handleError(error, `Failed to update status for winner ID: ${winnerId}`);
      return {} as Winner; // This line will never be reached due to handleError throwing
    }
  }
}
