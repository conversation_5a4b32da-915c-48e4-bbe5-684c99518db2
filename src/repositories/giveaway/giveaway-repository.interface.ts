/**
 * Giveaway Repository Interface
 * 
 * This interface defines the operations specific to giveaway entities,
 * extending the base repository interface.
 */

import { Giveaway } from '@prisma/client';
import { BaseRepository } from '../base/base-repository.interface.js';

/**
 * Giveaway repository interface for giveaway-specific operations
 * @extends BaseRepository<Giveaway, string>
 */
export interface GiveawayRepository extends BaseRepository<Giveaway, string> {
  /**
   * Find currently active giveaways
   * @returns Promise with array of active giveaways
   */
  findActive(): Promise<Giveaway[]>;
  
  /**
   * Find upcoming future giveaways
   * @param limit - Optional limit on the number of records to return
   * @returns Promise with array of upcoming giveaways
   */
  findUpcoming(limit?: number): Promise<Giveaway[]>;
  
  /**
   * Find past/completed giveaways
   * @param limit - Optional limit on the number of records to return
   * @returns Promise with array of past giveaways
   */
  findPast(limit?: number): Promise<Giveaway[]>;
  
  /**
   * Find giveaways by category
   * @param category - The category to filter by
   * @returns Promise with array of giveaways matching the category
   */
  findByCategory(category: string): Promise<Giveaway[]>;
  
  /**
   * Find giveaways with their prize details
   * @param giveawayId - Optional giveaway ID to filter by
   * @returns Promise with array of giveaways including prize details
   */
  findWithPrizes(giveawayId?: string): Promise<(Giveaway & { prizes: any[] })[]>;
  
  /**
   * Find giveaways with entry count information
   * @param giveawayId - Optional giveaway ID to filter by
   * @returns Promise with array of giveaways including entry count
   */
  findWithEntryCount(giveawayId?: string): Promise<Array<Giveaway & { entryCount: number }>>;
}
