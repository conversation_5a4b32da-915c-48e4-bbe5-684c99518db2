/**
 * Prisma Giveaway Repository Implementation
 * 
 * This class implements the giveaway repository interface using Prisma ORM.
 */

import { PrismaClient, Giveaway } from '@prisma/client';
import { GiveawayRepository } from './giveaway-repository.interface.js';
import { PrismaBaseRepository } from '../base/prisma-base.repository.js';

/**
 * Prisma implementation of the giveaway repository
 */
export class PrismaGiveawayRepository extends PrismaBaseRepository<Giveaway, string> implements GiveawayRepository {
  /**
   * Constructor for PrismaGiveawayRepository
   * 
   * @param prisma - Prisma client instance
   */
  constructor(prisma: PrismaClient) {
    super(prisma, prisma.giveaway);
  }

  /**
   * Find currently active giveaways (ongoing giveaways where current date is between start and end dates)
   * 
   * @returns Promise with array of active giveaways
   */
  async findActive(): Promise<Giveaway[]> {
    try {
      const now = new Date();
      
      return await this.prisma.giveaway.findMany({
        where: {
          startDate: {
            lte: now // Start date is in the past or now
          },
          endDate: {
            gte: now // End date is in the future or now
          },
          status: 'ACTIVE',
          isActive: true
        },
        orderBy: {
          endDate: 'asc' // Soonest ending first
        }
      });
    } catch (error) {
      this.handleError(error, 'Failed to find active giveaways');
      return []; // This line will never be reached due to handleError throwing
    }
  }

  /**
   * Find upcoming future giveaways (giveaways that haven't started yet)
   * 
   * @param limit - Optional limit on the number of records to return
   * @returns Promise with array of upcoming giveaways
   */
  async findUpcoming(limit?: number): Promise<Giveaway[]> {
    try {
      const now = new Date();
      
      return await this.prisma.giveaway.findMany({
        where: {
          startDate: {
            gt: now // Start date is in the future
          },
          status: 'ACTIVE',
          isActive: true
        },
        orderBy: {
          startDate: 'asc' // Soonest starting first
        },
        ...(limit !== undefined ? { take: limit } : {})
      });
    } catch (error) {
      this.handleError(error, 'Failed to find upcoming giveaways');
      return []; // This line will never be reached due to handleError throwing
    }
  }

  /**
   * Find past/completed giveaways (giveaways that have ended)
   * 
   * @param limit - Optional limit on the number of records to return
   * @returns Promise with array of past giveaways
   */
  async findPast(limit?: number): Promise<Giveaway[]> {
    try {
      const now = new Date();
      
      return await this.prisma.giveaway.findMany({
        where: {
          endDate: {
            lt: now // End date is in the past
          },
          // Note: We don't filter by status or isActive here since past giveaways 
          // might have different statuses (COMPLETED, CANCELLED, etc.)
        },
        orderBy: {
          endDate: 'desc' // Most recently ended first
        },
        ...(limit !== undefined ? { take: limit } : {})
      });
    } catch (error) {
      this.handleError(error, 'Failed to find past giveaways');
      return []; // This line will never be reached due to handleError throwing
    }
  }

  /**
   * Find giveaways by category
   * 
   * @param category - The category to filter by
   * @returns Promise with array of giveaways matching the category
   */
  async findByCategory(category: string): Promise<Giveaway[]> {
    try {
      return await this.prisma.giveaway.findMany({
        where: {
          category: category
        },
        orderBy: {
          startDate: 'desc' // Most recent first
        }
      });
    } catch (error) {
      this.handleError(error, `Failed to find giveaways for category: ${category}`);
      return []; // This line will never be reached due to handleError throwing
    }
  }

  /**
   * Find giveaways with their prize details
   * 
   * @param giveawayId - Optional giveaway ID to filter by
   * @returns Promise with array of giveaways including prize details
   */
  async findWithPrizes(giveawayId?: string): Promise<(Giveaway & { prizes: any[] })[]> {
    try {
      const where = giveawayId ? { id: giveawayId } : {};
      
      return await this.prisma.giveaway.findMany({
        where,
        include: {
          prizes: true
        },
        orderBy: {
          startDate: 'desc' // Most recent first
        }
      });
    } catch (error) {
      this.handleError(error, 'Failed to find giveaways with prizes');
      return []; // This line will never be reached due to handleError throwing
    }
  }

  /**
   * Find giveaways with entry count information
   * 
   * @param giveawayId - Optional giveaway ID to filter by
   * @returns Promise with array of giveaways including entry count
   */
  async findWithEntryCount(giveawayId?: string): Promise<Array<Giveaway & { entryCount: number }>> {
    try {
      const where = giveawayId ? { id: giveawayId } : {};
      
      // Using Prisma's count feature to get entry counts
      const giveaways = await this.prisma.giveaway.findMany({
        where,
        include: {
          _count: {
            select: {
              entries: true,
            },
          },
        },
        orderBy: {
          startDate: 'desc', // Most recent first
        },
      });
      
      // Map the results to the expected format
      return giveaways.map(giveaway => {
        const { _count, ...giveawayData } = giveaway;
        return {
          ...giveawayData,
          entryCount: _count.entries,
        };
      });
    } catch (error) {
      this.handleError(error, 'Failed to find giveaways with entry counts');
      return []; // This line will never be reached due to handleError throwing
    }
  }
}
