/**
 * Prisma Content Repository Implementation
 * 
 * This class implements the content repository interface using Prisma ORM.
 */

import { AccessLevel, Content, ContentStatus, PrismaClient } from '@prisma/client';
import { ContentRepository } from './content-repository.interface.js';
import { PrismaBaseRepository } from '../base/prisma-base.repository.js';

/**
 * Prisma implementation of the content repository
 */
export class PrismaContentRepository extends PrismaBaseRepository<Content, string> implements ContentRepository {
  /**
   * Constructor for PrismaContentRepository
   * 
   * @param prisma - Prisma client instance
   */
  constructor(prisma: PrismaClient) {
    super(prisma, prisma.content);
  }

  /**
   * Find content by slug
   * 
   * @param slug - Slug to search for
   * @returns Promise with content or null if not found
   */
  async findBySlug(slug: string): Promise<Content | null> {
    try {
      return await this.prisma.content.findUnique({
        where: {
          slug
        }
      });
    } catch (error) {
      this.handleError(error, `Failed to find content with slug: ${slug}`);
      return null; // This line will never be reached due to handleError throwing
    }
  }

  /**
   * Find all published content
   * 
   * @param limit - Optional limit on the number of records to return
   * @returns Promise with array of published content
   */
  async findPublished(limit?: number): Promise<Content[]> {
    try {
      return await this.prisma.content.findMany({
        where: {
          status: ContentStatus.PUBLISHED
        },
        orderBy: {
          updatedAt: 'desc' // Most recently updated first
        },
        ...(limit !== undefined ? { take: limit } : {})
      });
    } catch (error) {
      this.handleError(error, 'Failed to find published content');
      return []; // This line will never be reached due to handleError throwing
    }
  }

  /**
   * Find content by category
   * 
   * @param category - Category to filter by
   * @returns Promise with array of content in the specified category
   */
  async findByCategory(category: string): Promise<Content[]> {
    try {
      return await this.prisma.content.findMany({
        where: {
          category
        },
        orderBy: {
          updatedAt: 'desc' // Most recently updated first
        }
      });
    } catch (error) {
      this.handleError(error, `Failed to find content in category: ${category}`);
      return []; // This line will never be reached due to handleError throwing
    }
  }

  /**
   * Find content by access level
   * 
   * @param accessLevel - Access level to filter by
   * @returns Promise with array of content with the specified access level
   */
  async findByAccessLevel(accessLevel: AccessLevel): Promise<Content[]> {
    try {
      return await this.prisma.content.findMany({
        where: {
          accessLevel
        },
        orderBy: {
          updatedAt: 'desc' // Most recently updated first
        }
      });
    } catch (error) {
      this.handleError(error, `Failed to find content with access level: ${accessLevel}`);
      return []; // This line will never be reached due to handleError throwing
    }
  }

  /**
   * Find content with author details
   * 
   * @param contentId - Optional content ID to filter by
   * @returns Promise with array of content including author details
   */
  async findWithAuthor(contentId?: string): Promise<Content[]> {
    try {
      const whereCondition = contentId ? { id: contentId } : {};
      
      return await this.prisma.content.findMany({
        where: whereCondition,
        include: {
          author: true
        },
        orderBy: {
          updatedAt: 'desc' // Most recently updated first
        }
      });
    } catch (error) {
      this.handleError(error, 'Failed to find content with author details');
      return []; // This line will never be reached due to handleError throwing
    }
  }
}
