/**
 * Content Repository Interface
 * 
 * This interface defines the operations specific to content pages and articles,
 * extending the base repository interface.
 */

import { Content, AccessLevel } from '@prisma/client';
import { BaseRepository } from '../base/base-repository.interface.js';

/**
 * Content repository interface for content-specific operations
 * @extends BaseRepository<Content, string>
 */
export interface ContentRepository extends BaseRepository<Content, string> {
  /**
   * Find content by slug
   * @param slug - The slug to search for
   * @returns Promise with content or null if not found
   */
  findBySlug(slug: string): Promise<Content | null>;
  
  /**
   * Find all published content
   * @param limit - Optional limit on the number of records to return
   * @returns Promise with array of published content
   */
  findPublished(limit?: number): Promise<Content[]>;
  
  /**
   * Find content by category
   * @param category - The category to filter by
   * @returns Promise with array of content in the specified category
   */
  findByCategory(category: string): Promise<Content[]>;
  
  /**
   * Find content by access level
   * @param accessLevel - The access level to filter by
   * @returns Promise with array of content with the specified access level
   */
  findByAccessLevel(accessLevel: AccessLevel): Promise<Content[]>;
  
  /**
   * Find content with author details
   * @param contentId - Optional content ID to filter by
   * @returns Promise with array of content including author details
   */
  findWithAuthor(contentId?: string): Promise<Content[]>;
}
