/**
 * Prisma Base Repository Implementation
 * 
 * This class implements the base repository interface using Prisma ORM.
 * It provides generic CRUD operations for any Prisma model.
 */

import { PrismaClient, Prisma } from '@prisma/client';
import { 
  BaseRepository, 
  FilterOptions, 
  PaginationOptions, 
  PaginatedResult 
} from './base-repository.interface.js';

/**
 * Prisma database error codes
 */
export enum PrismaErrorCode {
  UNIQUE_CONSTRAINT_VIOLATION = 'P2002',
  FOREIGN_KEY_CONSTRAINT_VIOLATION = 'P2003',
  RECORD_NOT_FOUND = 'P2001',
  REQUIRED_FIELD_MISSING = 'P2012',
}

/**
 * Repository error types
 */
export enum RepositoryErrorType {
  NOT_FOUND = 'NOT_FOUND',
  DUPLICATE = 'DUPLICATE',
  FOREIGN_KEY = 'FOREIGN_KEY',
  VALIDATION = 'VALIDATION',
  UNKNOWN = 'UNKNOWN',
}

/**
 * Custom repository error class
 */
export class RepositoryError extends Error {
  type: RepositoryErrorType;
  override cause?: unknown;

  constructor(message: string, type: RepositoryErrorType, cause?: unknown) {
    super(message);
    this.name = 'RepositoryError';
    this.type = type;
    this.cause = cause;
  }
}

/**
 * Type for a model with an ID field
 */
type ModelWithId<ID> = {
  id: ID;
  [key: string]: any;
};

/**
 * Type for database query operation function
 */
type QueryOperation<TResult> = () => Promise<TResult>;

/**
 * Base Prisma repository implementation
 * 
 * @template T - Entity type
 * @template ID - ID type (default: string)
 */
export abstract class PrismaBaseRepository<T extends ModelWithId<ID>, ID = string> implements BaseRepository<T, ID> {
  
  /**
   * Constructor for PrismaBaseRepository
   * 
   * @param prisma - Prisma client instance
   * @param modelName - Name of the Prisma model to use
   */
  constructor(
    protected readonly prisma: PrismaClient,
    protected readonly model: any
  ) {}

  /**
   * Execute a database query with standardized error handling
   * 
   * This is a central method for executing Prisma operations with consistent
   * error handling. All repository methods should use this internally.
   * 
   * @param operation - Database query operation to execute
   * @param errorMessage - Error message to use if the operation fails
   * @returns Promise with operation result
   */
  protected async executeQuery<TResult>(
    operation: QueryOperation<TResult>,
    errorMessage: string
  ): Promise<TResult> {
    try {
      return await operation();
    } catch (error) {
      this.handleError(error, errorMessage);
      
      // This line is never reached because handleError always throws
      // It's here to satisfy TypeScript's return type checking
      throw new Error('Unreachable code');
    }
  }

  /**
   * Find an entity by its ID
   * 
   * @param id - Entity ID
   * @returns Promise with entity or null
   */
  async findById(id: ID): Promise<T | null> {
    return this.executeQuery(
      () => this.model.findUnique({
        where: { id },
      }) as Promise<T | null>,
      `Failed to find entity with ID: ${String(id)}`
    );
  }

  /**
   * Find multiple entities by their IDs
   * 
   * @param ids - Array of entity IDs to find
   * @returns Promise with array of found entities
   */
  async findByIds(ids: ID[]): Promise<T[]> {
    return this.executeQuery(
      () => this.model.findMany({
        where: {
          id: {
            in: ids
          }
        }
      }) as Promise<T[]>,
      `Failed to find entities with IDs: ${JSON.stringify(ids)}`
    );
  }

  /**
   * Find multiple entities with optional filtering and pagination
   * 
   * @param filter - Optional filter criteria
   * @param pagination - Optional pagination options
   * @returns Promise with array of entities
   */
  async findMany(filter?: FilterOptions, pagination?: PaginationOptions): Promise<T[]> {
    const { skip, take } = this.getPaginationParams(pagination);
    
    return this.executeQuery(
      () => this.model.findMany({
        where: this.transformFilter(filter),
        skip,
        take,
      }) as Promise<T[]>,
      `Failed to find entities with filter: ${JSON.stringify(filter)}`
    );
  }

  /**
   * Find multiple entities with pagination metadata
   * 
   * @param filter - Optional filter criteria
   * @param pagination - Optional pagination options
   * @returns Promise with paginated result
   */
  async findManyPaginated(filter?: FilterOptions, pagination?: PaginationOptions): Promise<PaginatedResult<T>> {
    const { skip, take, page, limit } = this.getPaginationParams(pagination);
    const where = this.transformFilter(filter);

    return this.executeQuery(
      async () => {
        const [data, total] = await Promise.all([
          this.model.findMany({
            where,
            skip,
            take,
          }) as Promise<T[]>,
          this.model.count({ where })
        ]);

        return {
          data,
          meta: {
            total,
            page,
            limit,
            hasMore: skip + take < total,
          }
        };
      },
      `Failed to find paginated entities with filter: ${JSON.stringify(filter)}`
    );
  }

  /**
   * Count entities that match the filter criteria
   * 
   * @param filter - Optional filter criteria
   * @returns Promise with count
   */
  async count(filter?: FilterOptions): Promise<number> {
    return this.executeQuery(
      () => this.model.count({
        where: this.transformFilter(filter),
      }),
      `Failed to count entities with filter: ${JSON.stringify(filter)}`
    );
  }

  /**
   * Create a new entity
   * 
   * @param data - Entity data
   * @returns Promise with created entity
   */
  async create(data: Partial<T>): Promise<T> {
    return this.executeQuery(
      () => this.model.create({
        data,
      }) as Promise<T>,
      `Failed to create entity with data: ${JSON.stringify(data)}`
    );
  }

  /**
   * Create multiple entities
   * 
   * @param data - Array of entity data
   * @returns Promise with array of created entities
   */
  async createMany(data: Partial<T>[]): Promise<T[]> {
    return this.executeQuery(
      async () => {
        await this.model.createMany({
          data,
        });
        
        // Prisma's createMany doesn't return the created entities, so we need to fetch them separately
        // This approach assumes the data has some unique identifier to match against
        // In a real implementation, you might want to fetch by a timestamp range or other criteria
        return await this.findMany() as T[];
      },
      `Failed to create multiple entities with data: ${JSON.stringify(data)}`
    );
  }

  /**
   * Update an existing entity
   * 
   * @param id - Entity ID
   * @param data - Update data
   * @returns Promise with updated entity
   */
  async update(id: ID, data: Partial<T>): Promise<T> {
    try {
      return await this.model.update({
        where: { id },
        data,
      }) as T;
    } catch (error) {
      if (error instanceof Prisma.PrismaClientKnownRequestError && error.code === PrismaErrorCode.RECORD_NOT_FOUND) {
        throw new RepositoryError(`Entity with ID ${String(id)} not found`, RepositoryErrorType.NOT_FOUND, error);
      }
      this.handleError(error, `Failed to update entity with ID: ${String(id)}`);
      return {} as T; // Will never be reached due to handleError throwing
    }
  }

  /**
   * Update multiple entities that match the filter
   * 
   * @param filter - Filter to select entities to update
   * @param data - Update data
   * @returns Promise with number of updated entities
   */
  async updateMany(filter: FilterOptions, data: Partial<T>): Promise<number> {
    return this.executeQuery(
      async () => {
        const result = await this.model.updateMany({
          where: this.transformFilter(filter),
          data,
        });
        return result.count;
      },
      `Failed to update multiple entities with filter: ${JSON.stringify(filter)}`
    );
  }

  /**
   * Delete an entity by its ID
   * 
   * @param id - Entity ID
   * @returns Promise with deleted entity
   */
  async delete(id: ID): Promise<T> {
    try {
      return await this.model.delete({
        where: { id },
      }) as T;
    } catch (error) {
      if (error instanceof Prisma.PrismaClientKnownRequestError && error.code === PrismaErrorCode.RECORD_NOT_FOUND) {
        throw new RepositoryError(`Entity with ID ${String(id)} not found`, RepositoryErrorType.NOT_FOUND, error);
      }
      this.handleError(error, `Failed to delete entity with ID: ${String(id)}`);
      return {} as T; // Will never be reached due to handleError throwing
    }
  }

  /**
   * Delete multiple entities that match the filter
   * 
   * @param filter - Filter to select entities to delete
   * @returns Promise with number of deleted entities
   */
  async deleteMany(filter: FilterOptions): Promise<number> {
    return this.executeQuery(
      async () => {
        const result = await this.model.deleteMany({
          where: this.transformFilter(filter),
        });
        return result.count;
      },
      `Failed to delete multiple entities with filter: ${JSON.stringify(filter)}`
    );
  }

  /**
   * Check if an entity with the given ID exists
   * 
   * @param id - Entity ID
   * @returns Promise with boolean
   */
  async exists(id: ID): Promise<boolean> {
    return this.executeQuery(
      async () => {
        const count = await this.model.count({
          where: { id },
        });
        return count > 0;
      },
      `Failed to check if entity exists with ID: ${String(id)}`
    );
  }

  /**
   * Check if any entities exist matching the filter
   */
  async existsWhere(filter: FilterOptions): Promise<boolean> {
    return this.executeQuery(
      async () => {
        const count = await this.model.count({
          where: this.transformFilter(filter),
        });
        return count > 0;
      },
      `Failed to check if entities exist with filter: ${JSON.stringify(filter)}`
    );
  }

  /**
   * Find one entity that matches the filter
   * 
   * @param filter - Filter criteria
   * @returns Promise with entity or null
   */
  async findOne(filter: FilterOptions): Promise<T | null> {
    return this.executeQuery(
      () => this.model.findFirst({
        where: this.transformFilter(filter),
      }) as Promise<T | null>,
      `Failed to find entity with filter: ${JSON.stringify(filter)}`
    );
  }

  /**
   * Transform filter object for Prisma
   * 
   * @param filter - Filter options
   * @returns Transformed filter for Prisma
   */
  protected transformFilter(filter?: FilterOptions): any {
    if (!filter) return {};

    // Default implementation passes filter as-is
    // Subclasses can override this to provide custom filter transformation
    return filter;
  }

  /**
   * Get pagination parameters with defaults
   * 
   * @param options - Pagination options
   * @returns Processed pagination parameters
   */
  protected getPaginationParams(options?: PaginationOptions): {
    skip: number;
    take: number;
    page: number;
    limit: number;
  } {
    const page = options?.page || 1;
    const limit = options?.limit || 10;
    
    // Allow explicit skip/take to override page/limit
    const skip = options?.skip !== undefined ? options.skip : (page - 1) * limit;
    const take = options?.take !== undefined ? options.take : limit;

    return { skip, take, page, limit };
  }

  /**
   * Handle Prisma errors
   * 
   * @param error - Error object
   * @param message - Error message
   */
  protected handleError(error: unknown, message: string): never {
    if (error instanceof Prisma.PrismaClientKnownRequestError) {
      switch (error.code) {
        case PrismaErrorCode.UNIQUE_CONSTRAINT_VIOLATION:
          throw new RepositoryError(
            `Duplicate entry: ${error.meta?.['target'] || 'Unknown field'} already exists`,
            RepositoryErrorType.DUPLICATE,
            error
          );
        case PrismaErrorCode.FOREIGN_KEY_CONSTRAINT_VIOLATION:
          throw new RepositoryError(
            `Foreign key constraint violation: ${error.meta?.['field_name'] || 'Unknown field'}`,
            RepositoryErrorType.FOREIGN_KEY,
            error
          );
        case PrismaErrorCode.RECORD_NOT_FOUND:
          throw new RepositoryError(
            `Record not found`,
            RepositoryErrorType.NOT_FOUND,
            error
          );
        case PrismaErrorCode.REQUIRED_FIELD_MISSING:
          throw new RepositoryError(
            `Validation error: ${error.meta?.['field_name'] || 'A required field'} is missing`,
            RepositoryErrorType.VALIDATION,
            error
          );
        default:
          throw new RepositoryError(
            `Database error: ${message}`,
            RepositoryErrorType.UNKNOWN,
            error
          );
      }
    } else if (error instanceof Prisma.PrismaClientValidationError) {
      throw new RepositoryError(
        `Validation error: ${message}`,
        RepositoryErrorType.VALIDATION,
        error
      );
    } else if (error instanceof Error) {
      throw new RepositoryError(
        `Repository error: ${message} - ${error.message}`,
        RepositoryErrorType.UNKNOWN,
        error
      );
    } else {
      throw new RepositoryError(
        `Unknown error: ${message}`,
        RepositoryErrorType.UNKNOWN,
        error
      );
    }
  }
}
