/**
 * Base Repository Interface
 * 
 * This interface defines the standard CRUD operations that all repositories
 * should implement. It uses generic type parameters for flexibility across
 * different entity types.
 */

/**
 * Filter options interface for querying entities
 */
export interface FilterOptions {
  [key: string]: any;
}

/**
 * Pagination options interface
 */
export interface PaginationOptions {
  page?: number;
  limit?: number;
  skip?: number;
  take?: number;
}

/**
 * Pagination result interface that includes the data and metadata
 */
export interface PaginatedResult<T> {
  data: T[];
  meta: {
    total: number;
    page: number;
    limit: number;
    hasMore: boolean;
  };
}

/**
 * Base repository interface for CRUD operations
 * @template T - The entity type
 * @template ID - The entity ID type
 */
export interface BaseRepository<T, ID> {
  /**
   * Find an entity by its ID
   * @param id - The ID of the entity to find
   * @returns A promise that resolves to the entity or null if not found
   */
  findById(id: ID): Promise<T | null>;

  /**
   * Find multiple entities by their IDs
   * @param ids - Array of IDs to find
   * @returns A promise that resolves to an array of found entities
   */
  findByIds(ids: ID[]): Promise<T[]>;

  /**
   * Find multiple entities with optional filtering and pagination
   * @param filter - Optional filter criteria
   * @param pagination - Optional pagination options
   * @returns A promise that resolves to an array of entities
   */
  findMany(filter?: FilterOptions, pagination?: PaginationOptions): Promise<T[]>;

  /**
   * Find multiple entities with pagination metadata
   * @param filter - Optional filter criteria
   * @param pagination - Pagination options
   * @returns A promise that resolves to a paginated result
   */
  findManyPaginated(filter?: FilterOptions, pagination?: PaginationOptions): Promise<PaginatedResult<T>>;

  /**
   * Count entities that match the filter criteria
   * @param filter - Optional filter criteria
   * @returns A promise that resolves to the count
   */
  count(filter?: FilterOptions): Promise<number>;

  /**
   * Create a new entity
   * @param data - The data to create the entity with
   * @returns A promise that resolves to the created entity
   */
  create(data: Partial<T>): Promise<T>;

  /**
   * Create multiple entities
   * @param data - Array of entity data to create
   * @returns A promise that resolves to an array of created entities
   */
  createMany(data: Partial<T>[]): Promise<T[]>;

  /**
   * Update an existing entity
   * @param id - The ID of the entity to update
   * @param data - The data to update the entity with
   * @returns A promise that resolves to the updated entity
   */
  update(id: ID, data: Partial<T>): Promise<T>;

  /**
   * Update multiple entities that match the filter
   * @param filter - Filter to select entities to update
   * @param data - The data to update the entities with
   * @returns A promise that resolves to the number of updated entities
   */
  updateMany(filter: FilterOptions, data: Partial<T>): Promise<number>;

  /**
   * Delete an entity by its ID
   * @param id - The ID of the entity to delete
   * @returns A promise that resolves to the deleted entity
   */
  delete(id: ID): Promise<T>;

  /**
   * Delete multiple entities that match the filter
   * @param filter - Filter to select entities to delete
   * @returns A promise that resolves to the number of deleted entities
   */
  deleteMany(filter: FilterOptions): Promise<number>;

  /**
   * Check if an entity with the specified ID exists
   * @param id - The ID to check
   * @returns A promise that resolves to a boolean indicating existence
   */
  exists(id: ID): Promise<boolean>;

  /**
   * Check if any entities match the filter
   * @param filter - Filter criteria
   * @returns A promise that resolves to a boolean indicating existence
   */
  existsWhere(filter: FilterOptions): Promise<boolean>;

  /**
   * Find one entity that matches the filter
   * @param filter - Filter criteria
   * @returns A promise that resolves to the entity or null if not found
   */
  findOne(filter: FilterOptions): Promise<T | null>;
}
