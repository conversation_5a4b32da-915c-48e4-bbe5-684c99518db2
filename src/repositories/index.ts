/**
 * Repository Module
 *
 * This module serves as the central registry for all repositories in the application.
 * It exports all repository interfaces and implementations, provides factory functions
 * to create repositories, and configures the unit of work for dependency injection.
 *
 * This centralized approach simplifies repository management and dependency injection
 * throughout the application.
 */

import { PrismaClient } from '@prisma/client';

// Import all repository interfaces
import { BaseRepository } from './base/base-repository.interface.js';
import { UserRepository } from './user/user-repository.interface.js';
import { MembershipTierRepository } from './membership-tier/membership-tier-repository.interface.js';
import { MembershipRepository } from './membership/membership-repository.interface.js';
import { GiveawayRepository } from './giveaway/giveaway-repository.interface.js';
import { PrizeRepository } from './prize/prize-repository.interface.js';
import { EntryRepository } from './entry/entry-repository.interface.js';
import { WinnerRepository } from './winner/winner-repository.interface.js';
import { TransactionRepository } from './transaction/transaction-repository.interface.js';
import { ContentRepository } from './content/content-repository.interface.js';
import { NotificationRepository } from './notification/notification-repository.interface.js';
import { SettingRepository } from './setting/setting-repository.interface.js';

// Import all repository implementations
import { PrismaBaseRepository } from './base/prisma-base.repository.js';
import { PrismaUserRepository } from './user/prisma-user.repository.js';
import { PrismaMembershipTierRepository } from './membership-tier/prisma-membership-tier.repository.js';
import { PrismaMembershipRepository } from './membership/prisma-membership.repository.js';
import { PrismaGiveawayRepository } from './giveaway/prisma-giveaway.repository.js';
import { PrismaPrizeRepository } from './prize/prisma-prize.repository.js';
import { PrismaEntryRepository } from './entry/prisma-entry.repository.js';
import { PrismaWinnerRepository } from './winner/prisma-winner.repository.js';
import { PrismaTransactionRepository } from './transaction/prisma-transaction.repository.js';
import { PrismaContentRepository } from './content/prisma-content.repository.js';
import { PrismaNotificationRepository } from './notification/prisma-notification.repository.js';
import { PrismaSettingRepository } from './setting/prisma-setting.repository.js';

// Import unit of work
import { IUnitOfWork, UnitOfWork, createUnitOfWork } from './unit-of-work.js';

// Export all repository interfaces
export {
  BaseRepository,
  UserRepository,
  MembershipTierRepository,
  MembershipRepository,
  GiveawayRepository,
  PrizeRepository,
  EntryRepository,
  WinnerRepository,
  TransactionRepository,
  ContentRepository,
  NotificationRepository,
  SettingRepository
};

// Export all repository implementations
export {
  PrismaBaseRepository,
  PrismaUserRepository,
  PrismaMembershipTierRepository,
  PrismaMembershipRepository,
  PrismaGiveawayRepository,
  PrismaPrizeRepository,
  PrismaEntryRepository,
  PrismaWinnerRepository,
  PrismaTransactionRepository,
  PrismaContentRepository,
  PrismaNotificationRepository,
  PrismaSettingRepository
};

// Export unit of work
export { IUnitOfWork, UnitOfWork, createUnitOfWork };

/**
 * Singleton PrismaClient instance that will be shared across the application
 * This ensures we don't create multiple database connections unnecessarily
 */
let prismaClient: PrismaClient | null = null;

/**
 * Get or create the shared Prisma client instance
 * @returns Shared PrismaClient instance
 */
export function getPrismaClient(): PrismaClient {
  if (!prismaClient) {
    prismaClient = new PrismaClient({
      datasources: {
        db: {
          url: process.env['DATABASE_URL'] || 'postgresql://postgres@localhost:5432/winnerssociety?connect_timeout=10&sslmode=prefer&schema=public'
        }
      },
      log: process.env['NODE_ENV'] === 'development'
        ? ['query', 'error', 'warn']
        : ['error']
    });
  }
  return prismaClient;
}

/**
 * Repository factory interface
 * Defines the structure for factory objects that create repositories
 */
export interface RepositoryFactory {
  createUserRepository(): UserRepository;
  createMembershipTierRepository(): MembershipTierRepository;
  createMembershipRepository(): MembershipRepository;
  createGiveawayRepository(): GiveawayRepository;
  createPrizeRepository(): PrizeRepository;
  createEntryRepository(): EntryRepository;
  createWinnerRepository(): WinnerRepository;
  createTransactionRepository(): TransactionRepository;
  createContentRepository(): ContentRepository;
  createNotificationRepository(): NotificationRepository;
  createSettingRepository(): SettingRepository;
  createUnitOfWork(): IUnitOfWork;
}

/**
 * Prisma implementation of the repository factory
 * Creates Prisma-based repositories using a shared Prisma client
 */
export class PrismaRepositoryFactory implements RepositoryFactory {
  private prisma: PrismaClient;

  /**
   * Constructor
   * @param prisma Optional PrismaClient instance (will use the shared instance if not provided)
   */
  constructor(prisma?: PrismaClient) {
    this.prisma = prisma || getPrismaClient();
  }

  /**
   * Create a User repository
   * @returns UserRepository implementation
   */
  createUserRepository(): UserRepository {
    return new PrismaUserRepository(this.prisma);
  }

  /**
   * Create a Membership Tier repository
   * @returns MembershipTierRepository implementation
   */
  createMembershipTierRepository(): MembershipTierRepository {
    return new PrismaMembershipTierRepository(this.prisma);
  }

  /**
   * Create a Membership repository
   * @returns MembershipRepository implementation
   */
  createMembershipRepository(): MembershipRepository {
    return new PrismaMembershipRepository(this.prisma);
  }

  /**
   * Create a Giveaway repository
   * @returns GiveawayRepository implementation
   */
  createGiveawayRepository(): GiveawayRepository {
    return new PrismaGiveawayRepository(this.prisma);
  }

  /**
   * Create a Prize repository
   * @returns PrizeRepository implementation
   */
  createPrizeRepository(): PrizeRepository {
    return new PrismaPrizeRepository(this.prisma);
  }

  /**
   * Create an Entry repository
   * @returns EntryRepository implementation
   */
  createEntryRepository(): EntryRepository {
    return new PrismaEntryRepository(this.prisma);
  }

  /**
   * Create a Winner repository
   * @returns WinnerRepository implementation
   */
  createWinnerRepository(): WinnerRepository {
    return new PrismaWinnerRepository(this.prisma);
  }

  /**
   * Create a Transaction repository
   * @returns TransactionRepository implementation
   */
  createTransactionRepository(): TransactionRepository {
    return new PrismaTransactionRepository(this.prisma);
  }

  /**
   * Create a Content repository
   * @returns ContentRepository implementation
   */
  createContentRepository(): ContentRepository {
    return new PrismaContentRepository(this.prisma);
  }

  /**
   * Create a Notification repository
   * @returns NotificationRepository implementation
   */
  createNotificationRepository(): NotificationRepository {
    return new PrismaNotificationRepository(this.prisma);
  }

  /**
   * Create a Setting repository
   * @returns SettingRepository implementation
   */
  createSettingRepository(): SettingRepository {
    return new PrismaSettingRepository(this.prisma);
  }

  /**
   * Create a Unit of Work
   * @returns IUnitOfWork implementation
   */
  createUnitOfWork(): IUnitOfWork {
    return new UnitOfWork(this.prisma);
  }
}

// Default repository factory instance using the shared Prisma client
const defaultRepositoryFactory = new PrismaRepositoryFactory();

/**
 * Get the default repository factory
 * @returns Default RepositoryFactory instance
 */
export function getRepositoryFactory(): RepositoryFactory {
  return defaultRepositoryFactory;
}

/**
 * Dependency injection helper for services
 * Creates a function to obtain repositories or unit of work on demand
 * @param factory Repository factory to use (uses default if not provided)
 * @returns Object with methods to get repositories and unit of work
 */
export function createRepositoryProvider(factory: RepositoryFactory = defaultRepositoryFactory) {
  return {
    getUserRepository: () => factory.createUserRepository(),
    getMembershipTierRepository: () => factory.createMembershipTierRepository(),
    getMembershipRepository: () => factory.createMembershipRepository(),
    getGiveawayRepository: () => factory.createGiveawayRepository(),
    getPrizeRepository: () => factory.createPrizeRepository(),
    getEntryRepository: () => factory.createEntryRepository(),
    getWinnerRepository: () => factory.createWinnerRepository(),
    getTransactionRepository: () => factory.createTransactionRepository(),
    getContentRepository: () => factory.createContentRepository(),
    getNotificationRepository: () => factory.createNotificationRepository(),
    getSettingRepository: () => factory.createSettingRepository(),
    getUnitOfWork: () => factory.createUnitOfWork()
  };
}

/**
 * Initialize the repository module
 * This sets up any configurations or validations needed for the repository layer
 */
export function initializeRepositories(): void {
  // Currently this just ensures the PrismaClient is created
  // Could be extended with more setup logic as needed
  getPrismaClient();

  // Log initialization in development mode
  if (process.env['NODE_ENV'] === 'development') {
    console.log('Repository layer initialized');
  }
}

/**
 * Clean up repository resources
 * Should be called when shutting down the application
 */
export async function cleanupRepositories(): Promise<void> {
  if (prismaClient) {
    await prismaClient.$disconnect();
    prismaClient = null;

    if (process.env['NODE_ENV'] === 'development') {
      console.log('Repository layer cleaned up');
    }
  }
}
