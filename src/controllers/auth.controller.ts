import { Request, Response, NextFunction } from 'express';
import { AuthService } from '../services/auth/auth-service.interface.js';
import {
  UserLoginDto,
  PasswordResetRequestDto,
  PasswordResetDto,
  TokenRefreshDto,
  EmailVerificationDto
} from '../dtos/auth.dto.js';
import { UserRegistrationDto } from '../dtos/user.dto.js';

// Request with user property for authenticated routes
interface AuthenticatedRequest extends Request {
  user?: {
    id: string;
  };
}

/**
 * Authentication controller
 */
export class AuthController {
  private authService: AuthService;

  /**
   * Create a new auth controller
   *
   * @param authService - Authentication service
   */
  constructor(authService: AuthService) {
    this.authService = authService;
  }

  /**
   * Register a new user
   *
   * @param req - Express request
   * @param res - Express response
   */
  async register(req: Request, res: Response): Promise<void> {
    const userData: UserRegistrationDto = req.body;

    // Validate required fields
    if (!userData.email || !userData.password || !userData.firstName || !userData.lastName) {
      res.status(400).json({
        success: false,
        message: 'Missing required fields for registration'
      });
      return;
    }

    try {
      const result = await this.authService.register(userData);

      res.status(201).json({
        success: true,
        data: result
      });
    } catch (error) {
      console.error('Registration error:', error);

      // Handle specific registration errors
      if (error instanceof Error) {
        if (error.message.includes('already exists')) {
          res.status(409).json({
            success: false,
            message: 'User with this email already exists'
          });
          return;
        }
      }

      // Handle other errors
      res.status(500).json({
        success: false,
        message: 'An error occurred during registration'
      });
    }
  }

  /**
   * Login a user
   *
   * @param req - Express request
   * @param res - Express response
   */
  async login(req: Request, res: Response): Promise<void> {
    const credentials: UserLoginDto = req.body;

    // Validate required fields
    if (!credentials.email || !credentials.password) {
      res.status(400).json({
        success: false,
        message: 'Email and password are required'
      });
      return;
    }

    try {
      const result = await this.authService.login(credentials);

      res.status(200).json({
        success: true,
        data: result
      });
    } catch (error) {
      console.error('Login error:', error);

      // Handle authentication errors with proper status codes
      if (error instanceof Error) {
        if (error.message.includes('Invalid email or password')) {
          res.status(401).json({
            success: false,
            message: 'Invalid email or password'
          });
          return;
        }
      }

      // Handle other errors
      res.status(500).json({
        success: false,
        message: 'An error occurred during login'
      });
    }
  }

  /**
   * Verify a user's email (redirect endpoint for email links)
   *
   * @param req - Express request
   * @param res - Express response
   */
  async verifyEmail(req: Request, res: Response): Promise<void> {
    const verificationToken = req.params['token'] || '';

    // Validate token
    if (!verificationToken) {
      const frontendUrl = process.env['FRONTEND_URL'] || 'http://localhost:4200';
      res.redirect(`${frontendUrl}/auth/verify-email?error=missing_token`);
      return;
    }

    try {
      const data = { token: verificationToken } as EmailVerificationDto;
      const result = await this.authService.verifyEmail(data);

      // Redirect to frontend with success parameter
      const frontendUrl = process.env['FRONTEND_URL'] || 'http://localhost:4200';
      res.redirect(`${frontendUrl}/auth/verify-email?success=true&email=${encodeURIComponent(result.email)}`);
    } catch (error) {
      const frontendUrl = process.env['FRONTEND_URL'] || 'http://localhost:4200';

      // Determine error type and redirect with appropriate error parameter
      let errorType = 'unknown';
      if (error instanceof Error) {
        if (error.message.includes('expired')) {
          errorType = 'expired';
        } else if (error.message.includes('already verified')) {
          errorType = 'already_verified';
        } else if (error.message.includes('invalid')) {
          errorType = 'invalid';
        } else if (error.message.includes('not found')) {
          errorType = 'not_found';
        }
      }

      res.redirect(`${frontendUrl}/auth/verify-email?error=${errorType}`);
    }
  }

  /**
   * Verify a user's email (JSON API endpoint)
   *
   * @param req - Express request
   * @param res - Express response
   */
  async verifyEmailAPI(req: Request, res: Response): Promise<void> {
    const verificationToken = req.params['token'] || '';

    // Validate token
    if (!verificationToken) {
      res.status(400).json({
        success: false,
        error: 'missing_token',
        message: 'Verification token is required'
      });
      return;
    }

    try {
      const data = { token: verificationToken } as EmailVerificationDto;
      const result = await this.authService.verifyEmail(data);

      res.status(200).json({
        success: true,
        message: 'Email verified successfully',
        data: {
          email: result.email,
          isVerified: true
        }
      });
    } catch (error) {
      console.error('Email verification failed:', error);

      // Determine error type and status code
      let statusCode = 400;
      let errorType = 'unknown';
      let message = 'Email verification failed';

      if (error instanceof Error) {
        if (error.message.includes('expired')) {
          statusCode = 410; // Gone
          errorType = 'expired';
          message = 'Verification token has expired';
        } else if (error.message.includes('already verified')) {
          statusCode = 409; // Conflict
          errorType = 'already_verified';
          message = 'Email address is already verified';
        } else if (error.message.includes('invalid')) {
          statusCode = 400; // Bad Request
          errorType = 'invalid';
          message = 'Invalid verification token';
        } else if (error.message.includes('not found')) {
          statusCode = 404; // Not Found
          errorType = 'not_found';
          message = 'Verification token not found';
        }
      }

      res.status(statusCode).json({
        success: false,
        error: errorType,
        message: message
      });
    }
  }

  /**
   * Request a password reset
   *
   * @param req - Express request
   * @param res - Express response
   */
  async forgotPassword(req: Request, res: Response): Promise<void> {
    const data: PasswordResetRequestDto = req.body;

    // Validate email
    if (!data.email) {
      res.status(400).json({
        success: false,
        message: 'Email is required'
      });
      return;
    }

    try {
      await this.authService.requestPasswordReset(data);

      // Always return success even if email not found (for security)
      res.status(200).json({
        success: true,
        message: 'Password reset instructions sent to email'
      });
    } catch (error) {
      console.error('Password reset request error:', error);

      // Always return success for security (don't reveal if email exists)
      res.status(200).json({
        success: true,
        message: 'Password reset instructions sent to email'
      });
    }
  }

  /**
   * Reset a user's password
   *
   * @param req - Express request
   * @param res - Express response
   */
  async resetPassword(req: Request, res: Response): Promise<void> {
    const data: PasswordResetDto = req.body;

    // Validate required fields
    if (!data.token || !data.newPassword) {
      res.status(400).json({
        success: false,
        message: 'Token and new password are required'
      });
      return;
    }

    try {
      await this.authService.confirmPasswordReset(data);

      res.status(200).json({
        success: true,
        message: 'Password reset successfully'
      });
    } catch (error) {
      console.error('Password reset error:', error);

      // Handle password reset errors
      if (error instanceof Error) {
        if (error.message.includes('Invalid or expired')) {
          res.status(400).json({
            success: false,
            message: 'Invalid or expired password reset token'
          });
          return;
        }
      }

      res.status(500).json({
        success: false,
        message: 'An error occurred while resetting password'
      });
    }
  }

  /**
   * Refresh authentication tokens
   *
   * @param req - Express request
   * @param res - Express response
   */
  async refreshToken(req: Request, res: Response): Promise<void> {
    const data: TokenRefreshDto = req.body;

    // Validate refresh token
    if (!data.refreshToken) {
      res.status(400).json({
        success: false,
        message: 'Refresh token is required'
      });
      return;
    }

    try {
      const result = await this.authService.refreshToken(data);

      res.status(200).json({
        success: true,
        data: result
      });
    } catch (error) {
      console.error('Token refresh error:', error);

      // Handle token refresh errors
      if (error instanceof Error) {
        if (error.message.includes('Invalid refresh token') || error.message.includes('expired')) {
          res.status(401).json({
            success: false,
            message: 'Invalid or expired refresh token'
          });
          return;
        }
      }

      res.status(500).json({
        success: false,
        message: 'An error occurred while refreshing token'
      });
    }
  }

  /**
   * Logout a user
   *
   * @param req - Express request
   * @param res - Express response
   */
  async logout(req: AuthenticatedRequest, res: Response): Promise<void> {
    const userId = req.user?.id;
    const { refreshToken } = req.body;

    // Validate user and refresh token
    if (!userId) {
      res.status(401).json({
        success: false,
        message: 'User not authenticated'
      });
      return;
    }

    if (!refreshToken) {
      res.status(400).json({
        success: false,
        message: 'Refresh token is required'
      });
      return;
    }

    try {
      await this.authService.logout(userId, refreshToken);

      res.status(204).send();
    } catch (error) {
      console.error('Logout error:', error);

      // Even if logout fails, return success for security
      res.status(204).send();
    }
  }

  /**
   * Resend verification email (authenticated)
   *
   * @param req - Express request
   * @param res - Express response
   */
  async resendVerificationEmail(req: AuthenticatedRequest, res: Response): Promise<void> {
    const userId = req.user?.id;

    if (!userId) {
      res.status(401).json({
        success: false,
        message: 'User not authenticated'
      });
      return;
    }

    try {
      await this.authService.resendVerificationEmail(userId);

      res.status(200).json({
        success: true,
        message: 'Verification email sent successfully'
      });
    } catch (error) {
      console.error('Resend verification email error:', error);

      if (error instanceof Error) {
        if (error.message.includes('already verified')) {
          res.status(409).json({
            success: false,
            message: 'Email is already verified'
          });
          return;
        }
        if (error.message.includes('not found')) {
          res.status(404).json({
            success: false,
            message: 'User not found'
          });
          return;
        }
      }

      res.status(500).json({
        success: false,
        message: 'An error occurred while sending verification email'
      });
    }
  }

  /**
   * Resend verification email by email (public endpoint)
   *
   * @param req - Express request
   * @param res - Express response
   */
  async resendVerificationEmailByEmail(req: Request, res: Response): Promise<void> {
    const { email } = req.body;

    // Validate email
    if (!email) {
      res.status(400).json({
        success: false,
        message: 'Email is required'
      });
      return;
    }

    try {
      await this.authService.resendVerificationEmailByEmail(email);

      // Always return success even if email not found (for security)
      res.status(200).json({
        success: true,
        message: 'Verification email sent successfully'
      });
    } catch (error) {
      console.error('Resend verification email by email error:', error);

      // Always return success for security (don't reveal if email exists)
      res.status(200).json({
        success: true,
        message: 'Verification email sent successfully'
      });
    }
  }
}

/**
 * Create route handlers for auth controller
 */
export function createAuthHandlers(authService: AuthService) {
  const controller = new AuthController(authService);

  return {
    register: asyncHandler(controller.register.bind(controller)),
    login: asyncHandler(controller.login.bind(controller)),
    verifyEmail: asyncHandler(controller.verifyEmail.bind(controller)),
    verifyEmailAPI: asyncHandler(controller.verifyEmailAPI.bind(controller)),
    forgotPassword: asyncHandler(controller.forgotPassword.bind(controller)),
    resetPassword: asyncHandler(controller.resetPassword.bind(controller)),
    refreshToken: asyncHandler(controller.refreshToken.bind(controller)),
    logout: asyncHandler(controller.logout.bind(controller)),
    resendVerificationEmail: asyncHandler(controller.resendVerificationEmail.bind(controller)),
    resendVerificationEmailByEmail: asyncHandler(controller.resendVerificationEmailByEmail.bind(controller))
  };
}

/**
 * Async handler wrapper for controller methods
 */
function asyncHandler(fn: (req: Request, res: Response, next: NextFunction) => Promise<void>) {
  return async (req: Request, res: Response, next: NextFunction) => {
    try {
      await fn(req, res, next);
    } catch (error) {
      next(error);
    }
  };
}