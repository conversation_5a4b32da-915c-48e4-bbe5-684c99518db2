import { Request, Response } from 'express';
import { PrizeService } from '../services/prize/prize-service.interface.js';
import { 
  PrizeCreateDto,
  PrizeUpdateDto
} from '../dtos/index.js';

/**
 * Prize Controller
 * 
 * Handles prize management for giveaways.
 */
export class PrizeController {
  /**
   * Create a new prize controller
   * 
   * @param prizeService - Prize service
   */
  constructor(private prizeService: PrizeService) {}

  /**
   * Get prizes for a giveaway
   * 
   * @param req - Express request
   * @param res - Express response
   */
  async getPrizes(req: Request, res: Response): Promise<void> {
    const giveawayId = req.params['id'];
    
    if (!giveawayId) {
      res.status(400).json({
        success: false,
        message: 'Giveaway ID is required'
      });
      return;
    }
    
    try {
      const prizes = await this.prizeService.getPrizesByGiveaway(giveawayId);
      
      res.status(200).json({
        success: true,
        data: prizes
      });
    } catch (error) {
      // If no giveaway found
      res.status(404).json({
        success: false,
        message: 'Giveaway not found'
      });
    }
  }

  /**
   * Get prize details by ID
   * 
   * @param req - Express request
   * @param res - Express response
   */
  async getPrizeDetails(req: Request, res: Response): Promise<void> {
    const prizeId = req.params['id'];
    
    if (!prizeId) {
      res.status(400).json({
        success: false,
        message: 'Prize ID is required'
      });
      return;
    }
    
    const prize = await this.prizeService.get(prizeId);
    
    if (!prize) {
      res.status(404).json({
        success: false,
        message: 'Prize not found'
      });
      return;
    }
    
    res.status(200).json({
      success: true,
      data: prize
    });
  }

  /**
   * Create a new prize (admin only)
   * 
   * @param req - Express request
   * @param res - Express response
   */
  async createPrize(req: Request, res: Response): Promise<void> {
    const giveawayId = req.params['giveawayId'];
    const prizeData: PrizeCreateDto = req.body;
    
    if (!giveawayId) {
      res.status(400).json({
        success: false,
        message: 'Giveaway ID is required'
      });
      return;
    }
    
    // Basic validation
    if (!prizeData.name || !prizeData.description) {
      res.status(400).json({
        success: false,
        message: 'Name and description are required'
      });
      return;
    }
    
    // Add giveaway ID to prize data
    const createData = {
      ...prizeData,
      giveawayId
    };
    
    try {
      const newPrize = await this.prizeService.create(createData);
      
      res.status(201).json({
        success: true,
        data: newPrize
      });
    } catch (error) {
      // Handle giveaway not found
      const err = error as Error;
      
      if (err.message.includes('not found')) {
        res.status(404).json({
          success: false,
          message: 'Giveaway not found'
        });
      } else {
        res.status(400).json({
          success: false,
          message: err.message || 'Error creating prize'
        });
      }
    }
  }

  /**
   * Update an existing prize (admin only)
   * 
   * @param req - Express request
   * @param res - Express response
   */
  async updatePrize(req: Request, res: Response): Promise<void> {
    const prizeId = req.params['id'];
    const updateData: PrizeUpdateDto = req.body;
    
    if (!prizeId) {
      res.status(400).json({
        success: false,
        message: 'Prize ID is required'
      });
      return;
    }
    
    try {
      const updatedPrize = await this.prizeService.update(prizeId, updateData);
      
      res.status(200).json({
        success: true,
        data: updatedPrize
      });
    } catch (error) {
      // Handle prize not found
      const err = error as Error;
      
      if (err.message.includes('not found')) {
        res.status(404).json({
          success: false,
          message: 'Prize not found'
        });
      } else {
        res.status(400).json({
          success: false,
          message: err.message || 'Error updating prize'
        });
      }
    }
  }

  /**
   * Delete a prize (admin only)
   * 
   * @param req - Express request
   * @param res - Express response
   */
  async deletePrize(req: Request, res: Response): Promise<void> {
    const prizeId = req.params['id'];
    
    if (!prizeId) {
      res.status(400).json({
        success: false,
        message: 'Prize ID is required'
      });
      return;
    }
    
    try {
      await this.prizeService.delete(prizeId);
      
      res.status(204).send();
    } catch (error) {
      // Handle specific error cases
      const err = error as Error;
      
      if (err.message.includes('not found')) {
        res.status(404).json({
          success: false,
          message: 'Prize not found'
        });
      } else if (err.message.includes('has winners')) {
        res.status(400).json({
          success: false,
          message: 'Cannot delete prize with winners'
        });
      } else {
        res.status(400).json({
          success: false,
          message: err.message || 'Error deleting prize'
        });
      }
    }
  }
}

/**
 * Create route handlers for prize controller
 */
export function createPrizeRouteHandlers(prizeController: PrizeController) {
  return {
    getPrizes: asyncHandler(prizeController.getPrizes.bind(prizeController)),
    getPrizeDetails: asyncHandler(prizeController.getPrizeDetails.bind(prizeController)),
    createPrize: asyncHandler(prizeController.createPrize.bind(prizeController)),
    updatePrize: asyncHandler(prizeController.updatePrize.bind(prizeController)),
    deletePrize: asyncHandler(prizeController.deletePrize.bind(prizeController))
  };
}

/**
 * Helper function to wrap controller methods with try/catch
 */
function asyncHandler(fn: (req: Request, res: Response) => Promise<void>) {
  return async (req: Request, res: Response, next: Function) => {
    try {
      await fn(req, res);
    } catch (error) {
      next(error);
    }
  };
}

/**
 * Factory function to create the controller
 */
export const createPrizeController = (prizeService: PrizeService): PrizeController => {
  return new PrizeController(prizeService);
}; 