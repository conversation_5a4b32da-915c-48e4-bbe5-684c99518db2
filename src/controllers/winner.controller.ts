import { Request, Response } from 'express';
import { WinnerService } from '../services/winner/winner-service.interface.js';

/**
 * Winner Controller
 * 
 * Handles winner management and prize claiming.
 */
export class WinnerController {
  /**
   * Create a new winner controller
   * 
   * @param winnerService - Winner service
   */
  constructor(private winnerService: WinnerService) {}

  /**
   * Get winners for a giveaway
   * 
   * @param req - Express request
   * @param res - Express response
   */
  async getWinners(req: Request, res: Response): Promise<void> {
    const giveawayId = req.params['id'];
    
    if (!giveawayId) {
      res.status(400).json({
        success: false,
        message: 'Giveaway ID is required'
      });
      return;
    }
    
    try {
      const winners = await this.winnerService.getWinnersByGiveaway(giveawayId);
      
      res.status(200).json({
        success: true,
        data: winners
      });
    } catch (error) {
      // Handle specific error cases
      const err = error as Error;
      
      if (err.message.includes('not found')) {
        res.status(404).json({
          success: false,
          message: 'Giveaway not found'
        });
      } else {
        res.status(400).json({
          success: false,
          message: err.message || 'Error retrieving winners'
        });
      }
    }
  }

  /**
   * Get winner details
   * 
   * @param req - Express request
   * @param res - Express response
   */
  async getWinnerDetails(req: Request, res: Response): Promise<void> {
    const winnerId = req.params['id'];
    
    if (!winnerId) {
      res.status(400).json({
        success: false,
        message: 'Winner ID is required'
      });
      return;
    }
    
    const winner = await this.winnerService.get(winnerId);
    
    if (!winner) {
      res.status(404).json({
        success: false,
        message: 'Winner not found'
      });
      return;
    }
    
    res.status(200).json({
      success: true,
      data: winner
    });
  }

  /**
   * Claim a prize
   * 
   * @param req - Express request
   * @param res - Express response
   */
  async claimPrize(req: Request, res: Response): Promise<void> {
    const winnerId = req.params['id'];
    const userId = (req as any).user.id;
    const shippingDetails = req.body.shippingDetails;
    
    if (!winnerId) {
      res.status(400).json({
        success: false,
        message: 'Winner ID is required'
      });
      return;
    }
    
    if (!shippingDetails) {
      res.status(400).json({
        success: false,
        message: 'Shipping details are required'
      });
      return;
    }
    
    try {
      const updatedWinner = await this.winnerService.claimPrize(winnerId, userId, shippingDetails);
      
      res.status(200).json({
        success: true,
        data: updatedWinner,
        message: 'Prize claimed successfully'
      });
    } catch (error) {
      // Handle specific error cases
      const err = error as Error;
      
      if (err.message.includes('not found')) {
        res.status(404).json({
          success: false,
          message: 'Winner not found'
        });
      } else if (err.message.includes('not the winner')) {
        res.status(403).json({
          success: false,
          message: 'You are not authorized to claim this prize'
        });
      } else if (err.message.includes('already claimed')) {
        res.status(400).json({
          success: false,
          message: 'Prize has already been claimed'
        });
      } else {
        res.status(400).json({
          success: false,
          message: err.message || 'Error claiming prize'
        });
      }
    }
  }

  /**
   * Update winner status (admin only)
   * 
   * @param req - Express request
   * @param res - Express response
   */
  async updateWinnerStatus(req: Request, res: Response): Promise<void> {
    const winnerId = req.params['id'];
    const statusUpdate = req.body;
    
    if (!winnerId) {
      res.status(400).json({
        success: false,
        message: 'Winner ID is required'
      });
      return;
    }
    
    if (!statusUpdate || !statusUpdate.status) {
      res.status(400).json({
        success: false,
        message: 'Status update is required'
      });
      return;
    }
    
    try {
      const updatedWinner = await this.winnerService.updateWinnerStatus(winnerId, statusUpdate.status);
      
      res.status(200).json({
        success: true,
        data: updatedWinner
      });
    } catch (error) {
      // Handle specific error cases
      const err = error as Error;
      
      if (err.message.includes('not found')) {
        res.status(404).json({
          success: false,
          message: 'Winner not found'
        });
      } else if (err.message.includes('invalid status')) {
        res.status(400).json({
          success: false,
          message: 'Invalid status value'
        });
      } else {
        res.status(400).json({
          success: false,
          message: err.message || 'Error updating winner status'
        });
      }
    }
  }
}

/**
 * Create route handlers for winner controller
 */
export function createWinnerRouteHandlers(winnerController: WinnerController) {
  return {
    getWinners: asyncHandler(winnerController.getWinners.bind(winnerController)),
    getWinnerDetails: asyncHandler(winnerController.getWinnerDetails.bind(winnerController)),
    claimPrize: asyncHandler(winnerController.claimPrize.bind(winnerController)),
    updateWinnerStatus: asyncHandler(winnerController.updateWinnerStatus.bind(winnerController))
  };
}

/**
 * Helper function to wrap controller methods with try/catch
 */
function asyncHandler(fn: (req: Request, res: Response) => Promise<void>) {
  return async (req: Request, res: Response, next: Function) => {
    try {
      await fn(req, res);
    } catch (error) {
      next(error);
    }
  };
}

/**
 * Factory function to create the controller
 */
export const createWinnerController = (winnerService: WinnerService): WinnerController => {
  return new WinnerController(winnerService);
}; 