import { Request, Response } from 'express';
import { MembershipService } from '../services/membership/membership-service.interface.js';
import { MembershipTierDto } from '../services/membership/membership-service.interface.js';
import { BillingPeriod } from '@prisma/client';

/**
 * Membership Tiers Controller
 * 
 * Handles both public and admin operations for membership tier management.
 */
export class MembershipTiersController {
  /**
   * Create a new membership tiers controller
   * 
   * @param membershipService - Membership service
   */
  constructor(private membershipService: MembershipService) {}

  /**
   * Get publicly available membership tiers
   * 
   * @param req - Express request
   * @param res - Express response
   */
  async getPublicTiers(_req: Request, res: Response): Promise<void> {
    try {
      // Direct database call as a temporary workaround
      const prisma = (this.membershipService as any).membershipTierRepository.prisma;
      
      // Make direct Prisma call to bypass abstraction layers that might have issues
      const tiers = await prisma.membershipTier.findMany({
        where: { isActive: true }
      });
      
      // Manual mapping
      const mappedTiers = tiers.map((tier: any) => ({
        id: tier.id,
        name: tier.name,
        description: tier.description,
        price: Number(tier.price),
        billingPeriod: tier.billingPeriod,
        features: Array.isArray(tier.features) ? tier.features : 
                 typeof tier.features === 'object' ? Object.values(tier.features) : [],
        entryAllocation: tier.entryAllocation,
        duration: tier.duration,
        isActive: tier.isActive
      }));
      
      res.status(200).json({
        success: true,
        data: mappedTiers
      });
    } catch (error) {
      console.error('Error retrieving tiers:', error);
      const err = error as Error;
      
      res.status(400).json({
        success: false,
        message: err.message || 'Error retrieving membership tiers'
      });
    }
  }

  /**
   * Get a public membership tier by ID
   * 
   * @param req - Express request
   * @param res - Express response
   */
  async getPublicTierById(req: Request, res: Response): Promise<void> {
    const tierId = req.params['id'];
    
    if (!tierId) {
      res.status(400).json({
        success: false,
        message: 'Tier ID is required'
      });
      return;
    }
    
    try {
      const tier = await this.membershipService.getTierById(tierId);
      
      if (!tier) {
        res.status(404).json({
          success: false,
          message: `Membership tier with ID ${tierId} not found`
        });
        return;
      }
      
      // For public access, only return active tiers
      if (!tier.isActive) {
        res.status(404).json({
          success: false,
          message: `Membership tier with ID ${tierId} not found`
        });
        return;
      }
      
      res.status(200).json({
        success: true,
        data: tier
      });
    } catch (error) {
      const err = error as Error;
      
      res.status(400).json({
        success: false,
        message: err.message || `Error retrieving membership tier with ID ${tierId}`
      });
    }
  }

  /**
   * [ADMIN] Get all membership tiers (including inactive ones)
   * 
   * @param req - Express request
   * @param res - Express response
   */
  async getAllTiers(_req: Request, res: Response): Promise<void> {
    try {
      // Include inactive tiers for admin view
      const includeInactive = true;
      const tiers = await this.membershipService.getAvailableTiers(includeInactive);
      
      res.status(200).json({
        success: true,
        data: tiers
      });
    } catch (error) {
      const err = error as Error;
      
      res.status(400).json({
        success: false,
        message: err.message || 'Error retrieving membership tiers'
      });
    }
  }

  /**
   * [ADMIN] Get a membership tier by ID
   * 
   * @param req - Express request
   * @param res - Express response
   */
  async getTierById(req: Request, res: Response): Promise<void> {
    const tierId = req.params['id'];
    
    if (!tierId) {
      res.status(400).json({
        success: false,
        message: 'Tier ID is required'
      });
      return;
    }
    
    try {
      const tier = await this.membershipService.getTierById(tierId);
      
      if (!tier) {
        res.status(404).json({
          success: false,
          message: `Membership tier with ID ${tierId} not found`
        });
        return;
      }
      
      res.status(200).json({
        success: true,
        data: tier
      });
    } catch (error) {
      const err = error as Error;
      
      res.status(400).json({
        success: false,
        message: err.message || `Error retrieving membership tier with ID ${tierId}`
      });
    }
  }

  /**
   * [ADMIN] Create a new membership tier
   * 
   * @param req - Express request
   * @param res - Express response
   */
  async createTier(req: Request, res: Response): Promise<void> {
    try {
      const tierData = req.body;
      
      // Validate required fields
      if (!tierData.name || !tierData.description || tierData.price === undefined || !tierData.billingPeriod) {
        res.status(400).json({
          success: false,
          message: 'Name, description, price, and billing period are required'
        });
        return;
      }
      
      const newTier = await this.membershipService.createTier(tierData);
      
      res.status(201).json({
        success: true,
        data: newTier
      });
    } catch (error) {
      const err = error as Error;
      
      res.status(400).json({
        success: false,
        message: err.message || 'Error creating membership tier'
      });
    }
  }

  /**
   * [ADMIN] Update a membership tier
   * 
   * @param req - Express request
   * @param res - Express response
   */
  async updateTier(req: Request, res: Response): Promise<void> {
    const tierId = req.params['id'];
    
    if (!tierId) {
      res.status(400).json({
        success: false,
        message: 'Tier ID is required'
      });
      return;
    }
    
    try {
      const tierData = req.body;
      
      const updatedTier = await this.membershipService.updateTier(tierId, tierData);
      
      res.status(200).json({
        success: true,
        data: updatedTier
      });
    } catch (error) {
      const err = error as Error;
      
      res.status(400).json({
        success: false,
        message: err.message || `Error updating membership tier with ID ${tierId}`
      });
    }
  }

  /**
   * [ADMIN] Add seed data for initial membership tiers
   * This is a temporary solution until proper admin UI is built
   * 
   * @param req - Express request
   * @param res - Express response
   */
  async seedTiers(_req: Request, res: Response): Promise<void> {
    try {
      // Create sample tiers
      const basicTier: MembershipTierDto = {
        id: '', // will be set by the database
        name: 'Basic',
        description: 'Entry level membership with basic features',
        price: 9.99,
        billingPeriod: BillingPeriod.MONTHLY,
        features: ['Access to basic giveaways', 'Monthly newsletter'],
        entryAllocation: 5,
        duration: 30, // 30 days
        isActive: true
      };
      
      const premiumTier: MembershipTierDto = {
        id: '', // will be set by the database
        name: 'Premium',
        description: 'Enhanced membership with more entries and features',
        price: 19.99,
        billingPeriod: BillingPeriod.MONTHLY,
        features: ['Access to all giveaways', 'Priority customer support', 'Exclusive content'],
        entryAllocation: 15,
        duration: 30, // 30 days
        isActive: true
      };
      
      const vipTier: MembershipTierDto = {
        id: '', // will be set by the database
        name: 'VIP',
        description: 'Ultimate membership with maximum benefits',
        price: 39.99,
        billingPeriod: BillingPeriod.MONTHLY,
        features: ['Unlimited entries', 'VIP-only giveaways', 'Early access to new giveaways', '24/7 premium support'],
        entryAllocation: 50,
        duration: 30, // 30 days
        isActive: true
      };
      
      await this.membershipService.createTier(basicTier);
      await this.membershipService.createTier(premiumTier);
      await this.membershipService.createTier(vipTier);
      
      res.status(201).json({
        success: true,
        message: 'Membership tiers seeded successfully'
      });
    } catch (error) {
      const err = error as Error;
      
      res.status(400).json({
        success: false,
        message: err.message || 'Error seeding membership tiers'
      });
    }
  }
}

/**
 * Helper function to wrap controller methods with try/catch
 */
function asyncHandler(fn: (req: Request, res: Response) => Promise<void>) {
  return async (req: Request, res: Response, next: Function) => {
    try {
      await fn(req, res);
    } catch (error) {
      next(error);
    }
  };
}

/**
 * Create route handlers for membership tiers controller
 */
export function createMembershipTiersRouteHandlers(membershipTiersController: MembershipTiersController) {
  return {
    // Public handlers
    getPublicTiers: asyncHandler(membershipTiersController.getPublicTiers.bind(membershipTiersController)),
    getPublicTierById: asyncHandler(membershipTiersController.getPublicTierById.bind(membershipTiersController)),
    
    // Admin handlers
    getAllTiers: asyncHandler(membershipTiersController.getAllTiers.bind(membershipTiersController)),
    getTierById: asyncHandler(membershipTiersController.getTierById.bind(membershipTiersController)),
    createTier: asyncHandler(membershipTiersController.createTier.bind(membershipTiersController)),
    updateTier: asyncHandler(membershipTiersController.updateTier.bind(membershipTiersController)),
    seedTiers: asyncHandler(membershipTiersController.seedTiers.bind(membershipTiersController))
  };
}

/**
 * Create membership tiers controller factory
 */
export const createMembershipTiersController = (membershipService: MembershipService): MembershipTiersController => {
  return new MembershipTiersController(membershipService);
}; 