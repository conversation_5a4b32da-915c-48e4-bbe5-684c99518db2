import { Request, Response } from 'express';
import { GiveawayService } from '../services/giveaway/giveaway-service.interface.js';
import { GiveawayEntryDto } from '../dtos/index.js';

/**
 * Giveaway Controller
 *
 * Handles giveaway listing, details, entries, and admin operations.
 */
export class GiveawayController {
  /**
   * Create a new giveaway controller
   *
   * @param giveawayService - Giveaway service
   */
  constructor(private giveawayService: GiveawayService) {}

  /**
   * List all giveaways with filtering and pagination
   *
   * @param req - Express request
   * @param res - Express response
   */
  async listGiveaways(req: Request, res: Response): Promise<void> {
    // Extract query parameters for pagination, filtering, and sorting
    const page = req.query['page'] ? parseInt(req.query['page'] as string) : 1;
    const limit = req.query['limit'] ? parseInt(req.query['limit'] as string) : 10;

    const paginationOptions = { page, limit };

    // Check if we want active, upcoming, or past giveaways
    const status = req.query['status'] as string;
    let giveaways;

    if (status === 'active') {
      giveaways = await this.giveawayService.getActiveGiveaways(paginationOptions);
    } else if (status === 'upcoming') {
      giveaways = await this.giveawayService.getUpcomingGiveaways(paginationOptions);
    } else if (status === 'past') {
      giveaways = await this.giveawayService.getPastGiveaways(paginationOptions);
    } else {
      // Use active giveaways as a default
      giveaways = await this.giveawayService.getActiveGiveaways(paginationOptions);
    }

    res.status(200).json({
      success: true,
      data: giveaways.data,
      meta: giveaways.meta
    });
  }

  /**
   * Get giveaway details by ID
   *
   * @param req - Express request
   * @param res - Express response
   */
  async getGiveawayDetails(req: Request, res: Response): Promise<void> {
    const giveawayId = req.params['id'];

    if (!giveawayId) {
      res.status(400).json({
        success: false,
        message: 'Giveaway ID is required'
      });
      return;
    }

    // There's no getById in the interface, so we need to find a similar method
    // Let's check for active giveaways and then find the one we need
    const paginationOptions = { page: 1, limit: 100 }; // Get a large batch to search
    const allGiveaways = await this.giveawayService.getActiveGiveaways(paginationOptions);

    const giveaway = allGiveaways.data.find(g => g.id === giveawayId);

    if (!giveaway) {
      // Check past giveaways if not found in active
      const pastGiveaways = await this.giveawayService.getPastGiveaways(paginationOptions);
      const pastGiveaway = pastGiveaways.data.find(g => g.id === giveawayId);

      if (!pastGiveaway) {
        res.status(404).json({
          success: false,
          message: 'Giveaway not found'
        });
        return;
      }

      res.status(200).json({
        success: true,
        data: pastGiveaway
      });
      return;
    }

    res.status(200).json({
      success: true,
      data: giveaway
    });
  }

  // Admin-specific methods have been moved to AdminGiveawayController

  // Admin-specific methods have been moved to AdminGiveawayController

  // Admin-specific methods have been moved to AdminGiveawayController

  /**
   * Enter a giveaway
   *
   * @param req - Express request
   * @param res - Express response
   */
  async enterGiveaway(req: Request, res: Response): Promise<void> {
    const giveawayId = req.params['id'];
    const userId = (req as any).user.id;

    if (!giveawayId) {
      res.status(400).json({
        success: false,
        message: 'Giveaway ID is required'
      });
      return;
    }

    // Create entry data with proper fields needed by the service
    const entryData: GiveawayEntryDto = {
      giveawayId,
      userId,
      quantity: req.body.quantity || 1,
      ...req.body
    };

    try {
      // First, check eligibility
      const eligibility = await this.giveawayService.checkEligibility(userId, giveawayId);

      if (!eligibility.isEligible) {
        res.status(403).json({
          success: false,
          message: eligibility.reason || 'You are not eligible to enter this giveaway'
        });
        return;
      }

      const success = await this.giveawayService.enterGiveaway(entryData);

      if (success) {
        // Get the user's entries for this giveaway
        const entries = await this.giveawayService.getUserEntries(userId, giveawayId);

        res.status(201).json({
          success: true,
          data: {
            entries,
            message: 'Successfully entered giveaway'
          }
        });
      } else {
        res.status(400).json({
          success: false,
          message: 'Failed to enter giveaway'
        });
      }
    } catch (error) {
      // Handle specific error types
      const err = error as Error;

      if (err.message.includes('not active') || err.message.includes('already ended')) {
        res.status(400).json({
          success: false,
          message: 'Giveaway is not active or has already ended'
        });
      } else if (err.message.includes('maximum entries') || err.message.includes('entry limit')) {
        res.status(400).json({
          success: false,
          message: 'You have reached the maximum number of entries for this giveaway'
        });
      } else if (err.message.includes('not eligible')) {
        res.status(403).json({
          success: false,
          message: 'You are not eligible to enter this giveaway'
        });
      } else {
        res.status(400).json({
          success: false,
          message: err.message || 'Error entering giveaway'
        });
      }
    }
  }

  // Admin-specific methods have been moved to AdminGiveawayController

  // Admin-specific methods have been moved to AdminGiveawayController

  // Admin-specific methods have been moved to AdminGiveawayController
}

/**
 * Create route handlers for giveaway controller
 */
export function createGiveawayRouteHandlers(giveawayController: GiveawayController) {
  return {
    listGiveaways: asyncHandler(giveawayController.listGiveaways.bind(giveawayController)),
    getGiveawayDetails: asyncHandler(giveawayController.getGiveawayDetails.bind(giveawayController)),
    enterGiveaway: asyncHandler(giveawayController.enterGiveaway.bind(giveawayController))
  };
}

/**
 * Helper function to wrap controller methods with try/catch
 */
function asyncHandler(fn: (req: Request, res: Response) => Promise<void>) {
  return async (req: Request, res: Response, next: Function) => {
    try {
      await fn(req, res);
    } catch (error) {
      next(error);
    }
  };
}

/**
 * Factory function to create the controller
 */
export const createGiveawayController = (giveawayService: GiveawayService): GiveawayController => {
  return new GiveawayController(giveawayService);
};