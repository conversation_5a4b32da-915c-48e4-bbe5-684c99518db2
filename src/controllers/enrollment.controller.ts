/**
 * Enrollment Controller
 * 
 * This controller handles HTTP requests related to giveaway enrollment.
 * It provides endpoints for managing automatic enrollment and manual enrollment.
 */

import { Request, Response } from 'express';
import { EnrollmentService } from '../services/enrollment.service.js';
import logger from '../utils/logger.js';

/**
 * Enrollment Controller class
 */
export class EnrollmentController {
  /**
   * Constructor
   * @param enrollmentService - Enrollment service instance
   */
  constructor(private enrollmentService: EnrollmentService) {}

  /**
   * Update giveaway eligible tiers
   * @param req - Express request
   * @param res - Express response
   */
  async updateGiveawayEligibleTiers(req: Request, res: Response): Promise<void> {
    try {
      const { giveawayId } = req.params;
      const { eligibleMembershipTierIds } = req.body;

      if (!giveawayId) {
        res.status(400).json({ error: 'Giveaway ID is required' });
        return;
      }

      if (!Array.isArray(eligibleMembershipTierIds)) {
        res.status(400).json({ error: 'Eligible membership tier IDs must be an array' });
        return;
      }

      const updatedGiveaway = await this.enrollmentService.updateGiveawayEligibleTiers(
        giveawayId,
        eligibleMembershipTierIds
      );

      res.status(200).json({
        success: true,
        data: updatedGiveaway
      });
    } catch (error) {
      logger.error('Error updating giveaway eligible tiers:', error);
      res.status(500).json({
        success: false,
        error: error instanceof Error ? error.message : 'An unknown error occurred'
      });
    }
  }

  /**
   * Toggle auto-enrollment for a giveaway
   * @param req - Express request
   * @param res - Express response
   */
  async toggleAutoEnrollment(req: Request, res: Response): Promise<void> {
    try {
      const { giveawayId } = req.params;
      const { autoEnrollment } = req.body;

      if (!giveawayId) {
        res.status(400).json({ error: 'Giveaway ID is required' });
        return;
      }

      if (typeof autoEnrollment !== 'boolean') {
        res.status(400).json({ error: 'Auto-enrollment must be a boolean' });
        return;
      }

      const updatedGiveaway = await this.enrollmentService.toggleAutoEnrollment(
        giveawayId,
        autoEnrollment
      );

      res.status(200).json({
        success: true,
        data: updatedGiveaway
      });
    } catch (error) {
      logger.error('Error toggling auto-enrollment:', error);
      res.status(500).json({
        success: false,
        error: error instanceof Error ? error.message : 'An unknown error occurred'
      });
    }
  }

  /**
   * Manually enroll a user in a giveaway
   * @param req - Express request
   * @param res - Express response
   */
  async manuallyEnrollUser(req: Request, res: Response): Promise<void> {
    try {
      const { giveawayId } = req.params;
      const { 
        userId, 
        membershipTierId, 
        numberOfEntries, 
        updateStrategy = 'REPLACE_OR_CREATE',
        notes 
      } = req.body;
      
      // Get admin ID from authenticated user
      const adminId = (req as any).user?.id;

      if (!giveawayId || !userId || !membershipTierId) {
        res.status(400).json({ 
          error: 'Giveaway ID, user ID, and membership tier ID are required' 
        });
        return;
      }

      const entry = await this.enrollmentService.manuallyEnrollUser(
        giveawayId,
        userId,
        membershipTierId,
        numberOfEntries,
        updateStrategy as any,
        adminId,
        notes
      );

      res.status(200).json({
        success: true,
        data: entry
      });
    } catch (error) {
      logger.error('Error manually enrolling user:', error);
      res.status(500).json({
        success: false,
        error: error instanceof Error ? error.message : 'An unknown error occurred'
      });
    }
  }

  /**
   * Retry failed enrollments
   * @param req - Express request
   * @param res - Express response
   */
  async retryFailedEnrollments(req: Request, res: Response): Promise<void> {
    try {
      const { giveawayId } = req.params;
      const { failedEnrollmentIds } = req.body;
      
      // Get admin ID from authenticated user
      const adminId = (req as any).user?.id;

      if (!giveawayId || !failedEnrollmentIds || !Array.isArray(failedEnrollmentIds)) {
        res.status(400).json({ 
          error: 'Giveaway ID and an array of failed enrollment IDs are required' 
        });
        return;
      }

      const jobId = await this.enrollmentService.retryFailedEnrollments(
        giveawayId,
        failedEnrollmentIds,
        adminId
      );

      res.status(200).json({
        success: true,
        data: { jobId }
      });
    } catch (error) {
      logger.error('Error retrying failed enrollments:', error);
      res.status(500).json({
        success: false,
        error: error instanceof Error ? error.message : 'An unknown error occurred'
      });
    }
  }

  /**
   * Get failed enrollments for a giveaway
   * @param req - Express request
   * @param res - Express response
   */
  async getFailedEnrollments(req: Request, res: Response): Promise<void> {
    try {
      const { giveawayId } = req.params;
      const { status, page = '1', limit = '10' } = req.query;

      if (!giveawayId) {
        res.status(400).json({ error: 'Giveaway ID is required' });
        return;
      }

      const result = await this.enrollmentService.getFailedEnrollments(
        giveawayId,
        status as string | undefined,
        parseInt(page as string, 10),
        parseInt(limit as string, 10)
      );

      res.status(200).json({
        success: true,
        data: result
      });
    } catch (error) {
      logger.error('Error getting failed enrollments:', error);
      res.status(500).json({
        success: false,
        error: error instanceof Error ? error.message : 'An unknown error occurred'
      });
    }
  }

  /**
   * Get entry statistics for a giveaway
   * @param req - Express request
   * @param res - Express response
   */
  async getEntryStatistics(req: Request, res: Response): Promise<void> {
    try {
      const { giveawayId } = req.params;

      if (!giveawayId) {
        res.status(400).json({ error: 'Giveaway ID is required' });
        return;
      }

      const statistics = await this.enrollmentService.getEntryStatistics(giveawayId);

      res.status(200).json({
        success: true,
        data: statistics
      });
    } catch (error) {
      logger.error('Error getting entry statistics:', error);
      res.status(500).json({
        success: false,
        error: error instanceof Error ? error.message : 'An unknown error occurred'
      });
    }
  }
}

/**
 * Create an enrollment controller
 * @param enrollmentService - Enrollment service instance
 * @returns Enrollment controller instance
 */
export function createEnrollmentController(
  enrollmentService: EnrollmentService
): EnrollmentController {
  return new EnrollmentController(enrollmentService);
}
