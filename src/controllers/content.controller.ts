import { Request, Response } from 'express';
import { ContentServiceInterface } from '../services/content/content-service.interface.js';

/**
 * Content Controller
 * 
 * Handles content pages and articles.
 */
export class ContentController {
  /**
   * Create a new content controller
   * 
   * @param contentService - Content service
   */
  constructor(private contentService: ContentServiceInterface) {}

  /**
   * Get content by slug
   * 
   * @param req - Express request
   * @param res - Express response
   */
  async getContentBySlug(req: Request, res: Response): Promise<void> {
    const slug = req.params['slug'];
    // User ID is optional - used for access control checks
    const userId = (req as any).user?.id;
    
    if (!slug) {
      res.status(400).json({
        success: false,
        message: 'Slug is required'
      });
      return;
    }
    
    try {
      const content = await this.contentService.getContentBySlug(slug, userId);
      
      if (!content) {
        res.status(404).json({
          success: false,
          message: 'Content not found'
        });
        return;
      }
      
      res.status(200).json({
        success: true,
        data: content
      });
    } catch (error) {
      const err = error as Error;
      
      if (err.message.includes('access')) {
        res.status(403).json({
          success: false,
          message: 'You do not have access to this content'
        });
      } else {
        res.status(400).json({
          success: false,
          message: err.message || 'Error retrieving content'
        });
      }
    }
  }

  /**
   * List content by category
   * 
   * @param req - Express request
   * @param res - Express response
   */
  async listContentByCategory(req: Request, res: Response): Promise<void> {
    const category = req.params['category'];
    // User ID is optional - used for access control checks
    const userId = (req as any).user?.id;
    const options: Record<string, any> = {};
    
    if (req.query['page']) {
      options['page'] = parseInt(req.query['page'] as string);
    }
    
    if (req.query['limit']) {
      options['limit'] = parseInt(req.query['limit'] as string);
    }
    
    if (req.query['sort']) {
      options['sort'] = req.query['sort'] as string;
    }
    
    if (!category) {
      res.status(400).json({
        success: false,
        message: 'Category is required'
      });
      return;
    }
    
    try {
      const content = await this.contentService.listContentByCategory(category, userId, options);
      
      res.status(200).json({
        success: true,
        data: content
      });
    } catch (error) {
      const err = error as Error;
      
      res.status(400).json({
        success: false,
        message: err.message || 'Error retrieving content'
      });
    }
  }

  /**
   * Create content (admin only)
   * 
   * @param req - Express request
   * @param res - Express response
   */
  async createContent(req: Request, res: Response): Promise<void> {
    const contentData = req.body;
    const authorId = (req as any).user.id;
    
    if (!contentData) {
      res.status(400).json({
        success: false,
        message: 'Content data is required'
      });
      return;
    }
    
    try {
      const content = await this.contentService.createContent({
        ...contentData,
        authorId
      });
      
      res.status(201).json({
        success: true,
        data: content
      });
    } catch (error) {
      const err = error as Error;
      
      if (err.message.includes('slug already exists')) {
        res.status(409).json({
          success: false,
          message: 'A content item with this slug already exists'
        });
      } else if (err.message.includes('validation')) {
        res.status(400).json({
          success: false,
          message: err.message
        });
      } else {
        res.status(400).json({
          success: false,
          message: err.message || 'Error creating content'
        });
      }
    }
  }

  /**
   * Update content (admin only)
   * 
   * @param req - Express request
   * @param res - Express response
   */
  async updateContent(req: Request, res: Response): Promise<void> {
    const contentId = req.params['id'];
    const contentData = req.body;
    
    if (!contentId) {
      res.status(400).json({
        success: false,
        message: 'Content ID is required'
      });
      return;
    }
    
    if (!contentData) {
      res.status(400).json({
        success: false,
        message: 'Content data is required'
      });
      return;
    }
    
    try {
      const content = await this.contentService.updateContent(contentId, contentData);
      
      res.status(200).json({
        success: true,
        data: content
      });
    } catch (error) {
      const err = error as Error;
      
      if (err.message.includes('not found')) {
        res.status(404).json({
          success: false,
          message: 'Content not found'
        });
      } else if (err.message.includes('slug already exists')) {
        res.status(409).json({
          success: false,
          message: 'A content item with this slug already exists'
        });
      } else {
        res.status(400).json({
          success: false,
          message: err.message || 'Error updating content'
        });
      }
    }
  }

  /**
   * Delete content (admin only)
   * 
   * @param req - Express request
   * @param res - Express response
   */
  async deleteContent(req: Request, res: Response): Promise<void> {
    const contentId = req.params['id'];
    
    if (!contentId) {
      res.status(400).json({
        success: false,
        message: 'Content ID is required'
      });
      return;
    }
    
    try {
      await this.contentService.deleteContent(contentId);
      
      res.status(204).send();
    } catch (error) {
      const err = error as Error;
      
      if (err.message.includes('not found')) {
        res.status(404).json({
          success: false,
          message: 'Content not found'
        });
      } else {
        res.status(400).json({
          success: false,
          message: err.message || 'Error deleting content'
        });
      }
    }
  }
}

/**
 * Create route handlers for content controller
 */
export function createContentRouteHandlers(contentController: ContentController) {
  return {
    getContentBySlug: asyncHandler(contentController.getContentBySlug.bind(contentController)),
    listContentByCategory: asyncHandler(contentController.listContentByCategory.bind(contentController)),
    createContent: asyncHandler(contentController.createContent.bind(contentController)),
    updateContent: asyncHandler(contentController.updateContent.bind(contentController)),
    deleteContent: asyncHandler(contentController.deleteContent.bind(contentController))
  };
}

/**
 * Helper function to wrap controller methods with try/catch
 */
function asyncHandler(fn: (req: Request, res: Response) => Promise<void>) {
  return async (req: Request, res: Response, next: Function) => {
    try {
      await fn(req, res);
    } catch (error) {
      next(error);
    }
  };
}

/**
 * Factory function to create the controller
 */
export const createContentController = (contentService: ContentServiceInterface): ContentController => {
  return new ContentController(contentService);
}; 