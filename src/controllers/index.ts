/**
 * Controller Module
 *
 * This module serves as the central registry for all controllers in the application.
 * It exports all controller interfaces and implementations, provides factory functions
 * to create controllers, and configures the dependency injection for controller instances.
 *
 * This centralized approach simplifies controller management and dependency injection
 * throughout the application.
 */

import {
  getServiceFactory,
  ServiceFactory,
  createServiceProvider
} from '../services/index.js';

// Import controller implementations
import { AuthController } from './auth.controller.js';
import { UserController, createUserController } from './user.controller.js';
import { MembershipController, createMembershipController } from './membership.controller.js';
import { GiveawayController, createGiveawayController } from './giveaway.controller.js';
import { PrizeController, createPrizeController } from './prize.controller.js';
import { WinnerController, createWinnerController } from './winner.controller.js';
import { PaymentController, createPaymentController } from './payment.controller.js';
import { ContentController, createContentController } from './content.controller.js';
import { NotificationController, createNotificationController } from './notification.controller.js';
import { AdminDashboardController, createAdminDashboardController } from './admin/dashboard.controller.js';
import { EnrollmentController, createEnrollmentController } from './enrollment.controller.js';

// Export controller implementations
export {
  AuthController,
  UserController,
  MembershipController,
  GiveawayController,
  PrizeController,
  WinnerController,
  PaymentController,
  ContentController,
  NotificationController,
  AdminDashboardController,
  EnrollmentController
};

// Export controller factory functions
export {
  createUserController,
  createMembershipController,
  createGiveawayController,
  createPrizeController,
  createWinnerController,
  createPaymentController,
  createContentController,
  createNotificationController,
  createAdminDashboardController,
  createEnrollmentController
};

/**
 * Create a new auth controller instance
 * @param authService The auth service implementation
 * @returns A new auth controller instance
 */
export function createAuthController(authService: any): AuthController {
  return new AuthController(authService);
}

/**
 * Controller factory interface
 * Defines the structure for factory objects that create controllers
 */
export interface ControllerFactory {
  createAuthController(): AuthController;
  createUserController(): UserController;
  createMembershipController(): MembershipController;
  createGiveawayController(): GiveawayController;
  createPrizeController(): PrizeController;
  createWinnerController(): WinnerController;
  createPaymentController(): PaymentController;
  createContentController(): ContentController;
  createNotificationController(): NotificationController;
  createAdminDashboardController(): AdminDashboardController;
  createEnrollmentController(): EnrollmentController;
}

/**
 * Implementation of the controller factory
 * Creates controller instances with proper dependency injection
 */
export class DefaultControllerFactory implements ControllerFactory {
  private services: ReturnType<typeof createServiceProvider>;
  private controllerCache: Map<string, any> = new Map();

  /**
   * Constructor
   * @param serviceFactory Optional service factory (will use default if not provided)
   */
  constructor(serviceFactory?: ServiceFactory) {
    this.services = createServiceProvider(serviceFactory || getServiceFactory());
  }

  /**
   * Create AuthController
   * @returns AuthController implementation
   */
  createAuthController(): AuthController {
    if (!this.controllerCache.has('auth')) {
      this.controllerCache.set(
        'auth',
        new AuthController(this.services.getAuthService())
      );
    }
    return this.controllerCache.get('auth');
  }

  /**
   * Create UserController
   * @returns UserController implementation
   */
  createUserController(): UserController {
    if (!this.controllerCache.has('user')) {
      this.controllerCache.set(
        'user',
        createUserController(this.services.getUserService())
      );
    }
    return this.controllerCache.get('user');
  }

  /**
   * Create MembershipController
   * @returns MembershipController implementation
   */
  createMembershipController(): MembershipController {
    if (!this.controllerCache.has('membership')) {
      this.controllerCache.set(
        'membership',
        createMembershipController(this.services.getMembershipService())
      );
    }
    return this.controllerCache.get('membership');
  }

  /**
   * Create GiveawayController
   * @returns GiveawayController implementation
   */
  createGiveawayController(): GiveawayController {
    if (!this.controllerCache.has('giveaway')) {
      this.controllerCache.set(
        'giveaway',
        createGiveawayController(this.services.getGiveawayService())
      );
    }
    return this.controllerCache.get('giveaway');
  }

  /**
   * Create PrizeController
   * @returns PrizeController implementation
   */
  createPrizeController(): PrizeController {
    if (!this.controllerCache.has('prize')) {
      this.controllerCache.set(
        'prize',
        createPrizeController(this.services.getPrizeService())
      );
    }
    return this.controllerCache.get('prize');
  }

  /**
   * Create WinnerController
   * @returns WinnerController implementation
   */
  createWinnerController(): WinnerController {
    if (!this.controllerCache.has('winner')) {
      this.controllerCache.set(
        'winner',
        createWinnerController(this.services.getWinnerService())
      );
    }
    return this.controllerCache.get('winner');
  }

  /**
   * Create PaymentController
   * @returns PaymentController implementation
   */
  createPaymentController(): PaymentController {
    if (!this.controllerCache.has('payment')) {
      this.controllerCache.set(
        'payment',
        createPaymentController(this.services.getPaymentService())
      );
    }
    return this.controllerCache.get('payment');
  }

  /**
   * Create ContentController
   * @returns ContentController implementation
   */
  createContentController(): ContentController {
    if (!this.controllerCache.has('content')) {
      this.controllerCache.set(
        'content',
        createContentController(this.services.getContentService())
      );
    }
    return this.controllerCache.get('content');
  }

  /**
   * Create NotificationController
   * @returns NotificationController implementation
   */
  createNotificationController(): NotificationController {
    if (!this.controllerCache.has('notification')) {
      this.controllerCache.set(
        'notification',
        createNotificationController(this.services.getNotificationService())
      );
    }
    return this.controllerCache.get('notification');
  }

  /**
   * Create AdminDashboardController
   * @returns AdminDashboardController implementation
   */
  createAdminDashboardController(): AdminDashboardController {
    if (!this.controllerCache.has('adminDashboard')) {
      this.controllerCache.set(
        'adminDashboard',
        createAdminDashboardController(this.services.getAdminService())
      );
    }
    return this.controllerCache.get('adminDashboard');
  }

  /**
   * Create EnrollmentController
   * @returns EnrollmentController implementation
   */
  createEnrollmentController(): EnrollmentController {
    if (!this.controllerCache.has('enrollment')) {
      this.controllerCache.set(
        'enrollment',
        createEnrollmentController(this.services.getEnrollmentService())
      );
    }
    return this.controllerCache.get('enrollment');
  }
}

// Default controller factory instance
const defaultControllerFactory = new DefaultControllerFactory();

/**
 * Get the default controller factory
 * @returns Default ControllerFactory instance
 */
export function getControllerFactory(): ControllerFactory {
  return defaultControllerFactory;
}

/**
 * Dependency injection helper for routes
 * Creates a function to obtain controllers on demand
 * @param factory Controller factory to use (uses default if not provided)
 * @returns Object with methods to get controllers
 */
export function createControllerProvider(factory: ControllerFactory = defaultControllerFactory) {
  return {
    getAuthController: () => factory.createAuthController(),
    getUserController: () => factory.createUserController(),
    getMembershipController: () => factory.createMembershipController(),
    getGiveawayController: () => factory.createGiveawayController(),
    getPrizeController: () => factory.createPrizeController(),
    getWinnerController: () => factory.createWinnerController(),
    getPaymentController: () => factory.createPaymentController(),
    getContentController: () => factory.createContentController(),
    getNotificationController: () => factory.createNotificationController(),
    getAdminDashboardController: () => factory.createAdminDashboardController(),
    getEnrollmentController: () => factory.createEnrollmentController()
  };
}

/**
 * Initialize all controllers
 * This is a placeholder for any initialization logic that might be needed for controllers
 */
export function initializeControllers(): void {
  const factory = getControllerFactory();

  // Initialize all controllers - this will trigger their creation and dependency injection
  factory.createAuthController();
  factory.createUserController();
  factory.createMembershipController();
  factory.createGiveawayController();
  factory.createPrizeController();
  factory.createWinnerController();
  factory.createPaymentController();
  factory.createContentController();
  factory.createNotificationController();
  factory.createAdminDashboardController();
  factory.createEnrollmentController();

  // Log initialization in development mode
  if (process.env['NODE_ENV'] === 'development') {
    console.log('Controller layer initialized');
  }
}

/**
 * Cleanup all controllers
 * This is a placeholder for any cleanup logic that might be needed for controllers
 */
export async function cleanupControllers(): Promise<void> {
  // Placeholder for any controller cleanup
}