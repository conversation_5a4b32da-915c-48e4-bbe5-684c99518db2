import { Request, Response } from 'express';
import { PaymentServiceInterface } from '../services/payment/payment-service.interface.js';

/**
 * Payment Controller
 *
 * Handles payment processing and transaction management.
 */
export class PaymentController {
  /**
   * Create a new payment controller
   *
   * @param paymentService - Payment service
   */
  constructor(private paymentService: PaymentServiceInterface) {}

  /**
   * Create a payment intent
   *
   * @param req - Express request
   * @param res - Express response
   */
  async createPaymentIntent(req: Request, res: Response): Promise<void> {
    const userId = (req as any).user.id;
    const { planId, description, metadata } = req.body;

    if (!planId) {
      res.status(400).json({
        success: false,
        message: 'planId is required'
      });
      return;
    }

    // Import the membership service dynamically to avoid circular deps
    const { getServiceFactory } = await import('../services/index.js');
    const membershipService = getServiceFactory().createMembershipService();

    try {
      const plan = await membershipService.getTierById(planId);
      if (!plan || !plan.isActive) {
        res.status(404).json({
          success: false,
          message: 'Membership plan not found or inactive'
        });
        return;
      }

      const paymentIntent = await this.paymentService.createPaymentIntent({
        planId,
        description,
        userId,
        metadata: {
          ...metadata,
          planName: plan.name
        }
      });

      res.status(200).json({
        success: true,
        data: paymentIntent
      });
    } catch (error) {
      const err = error as Error;
      res.status(400).json({
        success: false,
        message: err.message || 'Error creating payment intent'
      });
    }
  }

  /**
   * Process a payment
   *
   * This endpoint confirms a payment using a previously created payment intent.
   * The payment flow requires two steps:
   * 1. Create a payment intent using /api/payments/payment-intent
   * 2. Process the payment using this endpoint with the payment intent ID and payment method ID
   *
   * @param req - Express request
   * @param res - Express response
   */
  async processPayment(req: Request, res: Response): Promise<void> {
    const userId = (req as any).user.id;
    const paymentDetails = req.body;

    // Validate required parameters
    if (!paymentDetails) {
      res.status(400).json({
        success: false,
        message: 'Payment details are required'
      });
      return;
    }

    if (!paymentDetails.paymentIntentId) {
      res.status(400).json({
        success: false,
        message: 'Payment intent ID is required. Please create a payment intent first using the /api/payments/payment-intent endpoint.'
      });
      return;
    }

    if (!paymentDetails.paymentMethodId) {
      res.status(400).json({
        success: false,
        message: 'Payment method ID is required'
      });
      return;
    }

    try {
      const transaction = await this.paymentService.processPayment(userId, paymentDetails);

      res.status(200).json({
        success: true,
        data: transaction
      });
    } catch (error) {
      const err = error as Error;

      res.status(400).json({
        success: false,
        message: err.message || 'Error processing payment'
      });
    }
  }

  /**
   * Get user transactions
   *
   * @param req - Express request
   * @param res - Express response
   */
  async getTransactions(req: Request, res: Response): Promise<void> {
    const userId = (req as any).user.id;
    const options: Record<string, any> = {};

    if (req.query['page']) {
      options['page'] = parseInt(req.query['page'] as string);
    }

    if (req.query['limit']) {
      options['limit'] = parseInt(req.query['limit'] as string);
    }

    if (req.query['status']) {
      options['status'] = req.query['status'] as string;
    }

    try {
      const transactions = await this.paymentService.getTransactionsByUser(userId, options);

      res.status(200).json({
        success: true,
        data: transactions
      });
    } catch (error) {
      const err = error as Error;

      res.status(400).json({
        success: false,
        message: err.message || 'Error retrieving transactions'
      });
    }
  }

  /**
   * Get transaction details
   *
   * @param req - Express request
   * @param res - Express response
   */
  async getTransactionDetails(req: Request, res: Response): Promise<void> {
    const transactionId = req.params['id'];
    const userId = (req as any).user.id;

    if (!transactionId) {
      res.status(400).json({
        success: false,
        message: 'Transaction ID is required'
      });
      return;
    }

    try {
      const transaction = await this.paymentService.getTransactionDetails(transactionId, userId);

      if (!transaction) {
        res.status(404).json({
          success: false,
          message: 'Transaction not found'
        });
        return;
      }

      res.status(200).json({
        success: true,
        data: transaction
      });
    } catch (error) {
      const err = error as Error;

      if (err.message.includes('unauthorized')) {
        res.status(403).json({
          success: false,
          message: 'You are not authorized to view this transaction'
        });
      } else {
        res.status(400).json({
          success: false,
          message: err.message || 'Error retrieving transaction details'
        });
      }
    }
  }

  /**
   * Process a refund (admin only)
   *
   * @param req - Express request
   * @param res - Express response
   */
  async processRefund(req: Request, res: Response): Promise<void> {
    const { transactionId, amount, reason } = req.body;

    if (!transactionId) {
      res.status(400).json({
        success: false,
        message: 'Transaction ID is required'
      });
      return;
    }

    try {
      const refund = await this.paymentService.processRefund(transactionId, amount, reason);

      res.status(200).json({
        success: true,
        data: refund
      });
    } catch (error) {
      const err = error as Error;

      if (err.message.includes('not found')) {
        res.status(404).json({
          success: false,
          message: 'Transaction not found'
        });
      } else if (err.message.includes('already refunded')) {
        res.status(400).json({
          success: false,
          message: 'Transaction has already been refunded'
        });
      } else {
        res.status(400).json({
          success: false,
          message: err.message || 'Error processing refund'
        });
      }
    }
  }

  /**
   * Handle payment webhook
   *
   * @param req - Express request
   * @param res - Express response
   */
  async handleWebhook(req: Request, res: Response): Promise<void> {
    const signature = req.headers['stripe-signature'] as string;

    if (!signature) {
      res.status(400).json({
        success: false,
        message: 'Webhook signature is required'
      });
      return;
    }

    try {
      const event = await this.paymentService.processWebhook(req.body, signature);

      res.status(200).json({
        success: true,
        data: { received: true, eventType: event.type }
      });
    } catch (error) {
      const err = error as Error;

      res.status(400).json({
        success: false,
        message: err.message || 'Error processing webhook'
      });
    }
  }

  /**
   * Create a setup intent for adding a payment method
   *
   * @param req - Express request
   * @param res - Express response
   */
  async createSetupIntent(req: Request, res: Response): Promise<void> {
    const userId = (req as any).user.id;

    try {
      const setupIntent = await this.paymentService.createSetupIntent(userId);

      res.status(200).json({
        success: true,
        data: setupIntent
      });
    } catch (error) {
      const err = error as Error;

      res.status(400).json({
        success: false,
        message: err.message || 'Error creating setup intent'
      });
    }
  }

  /**
   * Create a payment method
   *
   * @param req - Express request
   * @param res - Express response
   */
  async createPaymentMethod(req: Request, res: Response): Promise<void> {
    const userId = (req as any).user.id;
    const paymentMethodData = req.body;

    if (!paymentMethodData || !paymentMethodData.stripePaymentMethodId) {
      res.status(400).json({
        success: false,
        message: 'Stripe payment method ID is required'
      });
      return;
    }

    try {
      const paymentMethod = await this.paymentService.createPaymentMethod(userId, paymentMethodData);

      res.status(201).json({
        success: true,
        data: paymentMethod
      });
    } catch (error) {
      const err = error as Error;

      res.status(400).json({
        success: false,
        message: err.message || 'Error creating payment method'
      });
    }
  }

  /**
   * Get user's payment methods
   *
   * @param req - Express request
   * @param res - Express response
   */
  async getPaymentMethods(req: Request, res: Response): Promise<void> {
    const userId = (req as any).user.id;

    try {
      const paymentMethods = await this.paymentService.getPaymentMethods(userId);

      res.status(200).json({
        success: true,
        data: paymentMethods
      });
    } catch (error) {
      const err = error as Error;

      res.status(400).json({
        success: false,
        message: err.message || 'Error retrieving payment methods'
      });
    }
  }

  /**
   * Update a payment method
   *
   * @param req - Express request
   * @param res - Express response
   */
  async updatePaymentMethod(req: Request, res: Response): Promise<void> {
    const userId = (req as any).user.id;
    const paymentMethodId = req.params['id'];
    const updateData = req.body;

    if (!paymentMethodId) {
      res.status(400).json({
        success: false,
        message: 'Payment method ID is required'
      });
      return;
    }

    try {
      const paymentMethod = await this.paymentService.updatePaymentMethod(userId, paymentMethodId, updateData);

      res.status(200).json({
        success: true,
        data: paymentMethod
      });
    } catch (error) {
      const err = error as Error;

      if (err.message.includes('not found')) {
        res.status(404).json({
          success: false,
          message: 'Payment method not found'
        });
      } else if (err.message.includes('not authorized')) {
        res.status(403).json({
          success: false,
          message: 'You are not authorized to update this payment method'
        });
      } else {
        res.status(400).json({
          success: false,
          message: err.message || 'Error updating payment method'
        });
      }
    }
  }

  /**
   * Delete a payment method
   *
   * @param req - Express request
   * @param res - Express response
   */
  async deletePaymentMethod(req: Request, res: Response): Promise<void> {
    const userId = (req as any).user.id;
    const paymentMethodId = req.params['id'];

    if (!paymentMethodId) {
      res.status(400).json({
        success: false,
        message: 'Payment method ID is required'
      });
      return;
    }

    try {
      await this.paymentService.deletePaymentMethod(userId, paymentMethodId);

      res.status(200).json({
        success: true,
        message: 'Payment method deleted successfully'
      });
    } catch (error) {
      const err = error as Error;

      if (err.message.includes('not found')) {
        res.status(404).json({
          success: false,
          message: 'Payment method not found'
        });
      } else if (err.message.includes('not authorized')) {
        res.status(403).json({
          success: false,
          message: 'You are not authorized to delete this payment method'
        });
      } else {
        res.status(400).json({
          success: false,
          message: err.message || 'Error deleting payment method'
        });
      }
    }
  }
}

/**
 * Create route handlers for payment controller
 */
export function createPaymentRouteHandlers(paymentController: PaymentController) {
  return {
    createPaymentIntent: asyncHandler(paymentController.createPaymentIntent.bind(paymentController)),
    processPayment: asyncHandler(paymentController.processPayment.bind(paymentController)),
    getTransactions: asyncHandler(paymentController.getTransactions.bind(paymentController)),
    getTransactionDetails: asyncHandler(paymentController.getTransactionDetails.bind(paymentController)),
    processRefund: asyncHandler(paymentController.processRefund.bind(paymentController)),
    handleWebhook: asyncHandler(paymentController.handleWebhook.bind(paymentController)),
    createSetupIntent: asyncHandler(paymentController.createSetupIntent.bind(paymentController)),
    createPaymentMethod: asyncHandler(paymentController.createPaymentMethod.bind(paymentController)),
    getPaymentMethods: asyncHandler(paymentController.getPaymentMethods.bind(paymentController)),
    updatePaymentMethod: asyncHandler(paymentController.updatePaymentMethod.bind(paymentController)),
    deletePaymentMethod: asyncHandler(paymentController.deletePaymentMethod.bind(paymentController))
  };
}

/**
 * Helper function to wrap controller methods with try/catch
 */
function asyncHandler(fn: (req: Request, res: Response) => Promise<void>) {
  return async (req: Request, res: Response, next: Function) => {
    try {
      await fn(req, res);
    } catch (error) {
      next(error);
    }
  };
}

/**
 * Factory function to create the controller
 */
export const createPaymentController = (paymentService: PaymentServiceInterface): PaymentController => {
  return new PaymentController(paymentService);
};