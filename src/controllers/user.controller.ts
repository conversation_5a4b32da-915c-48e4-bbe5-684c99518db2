import { Request, Response, NextFunction } from 'express';
import { UserService } from '../services/user/user-service.interface.js';
import { 
  UserUpdateDto, 
  PasswordChangeDto 
} from '../dtos/user.dto.js';
import 'multer';

// Request with user property for authenticated routes
interface AuthenticatedRequest extends Request {
  user: {
    id: string;
  };
}

// Extended request with file upload
interface FileUploadRequest extends AuthenticatedRequest {
  file?: Express.Multer.File;
}

/**
 * User Controller
 * 
 * Handles user profile management and related operations.
 */
export class UserController {
  /**
   * Create a new user controller
   * 
   * @param userService - User service
   */
  constructor(private userService: UserService) {}

  /**
   * Get user profile
   * 
   * @param req - Express request
   * @param res - Express response
   */
  async getProfile(req: Request, res: Response): Promise<void> {
    const authReq = req as AuthenticatedRequest;
    const userId = authReq.user.id;
    
    const profile = await this.userService.getProfile(userId);
    
    res.status(200).json({
      success: true,
      data: profile
    });
  }

  /**
   * Get user profile (User + Profile)
   * 
   * @param req - Express request
   * @param res - Express response
   */
  async getFullProfile(req: Request, res: Response): Promise<void> {
    const authReq = req as AuthenticatedRequest;
    const userId = authReq.user.id;
    // Fetch user and profile
    const user = await this.userService.get(userId);
    const profile = await this.userService.getProfile(userId);
    res.status(200).json({
      success: true,
      data: {
        user,
        profile
      }
    });
  }

  /**
   * Update user profile
   * 
   * @param req - Express request
   * @param res - Express response
   */
  async updateProfile(req: Request, res: Response): Promise<void> {
    const authReq = req as AuthenticatedRequest;
    const userId = authReq.user.id;
    const profileData: UserUpdateDto = req.body;
    
    // Validate required fields
    if (!profileData || Object.keys(profileData).length === 0) {
      res.status(400).json({
        success: false,
        message: 'No update data provided'
      });
      return;
    }
    
    const updatedProfile = await this.userService.updateProfile(userId, profileData);
    
    res.status(200).json({
      success: true,
      data: updatedProfile
    });
  }

  /**
   * Change user password
   * 
   * @param req - Express request
   * @param res - Express response
   */
  async changePassword(req: Request, res: Response): Promise<void> {
    const authReq = req as AuthenticatedRequest;
    const userId = authReq.user.id;
    const passwordData: PasswordChangeDto = req.body;
    
    // Validate required fields
    if (!passwordData.currentPassword || !passwordData.newPassword || !passwordData.confirmPassword) {
      res.status(400).json({
        success: false,
        message: 'Current password, new password, and confirm password are required'
      });
      return;
    }
    
    // Check if passwords match
    if (passwordData.newPassword !== passwordData.confirmPassword) {
      res.status(400).json({
        success: false,
        message: 'New passwords do not match'
      });
      return;
    }
    
    await this.userService.changePassword(userId, passwordData);
    
    res.status(204).send();
  }

  /**
   * Get user preferences
   * 
   * @param req - Express request
   * @param res - Express response
   */
  async getPreferences(req: Request, res: Response): Promise<void> {
    const authReq = req as AuthenticatedRequest;
    const userId = authReq.user.id;
    
    const preferences = await this.userService.getUserPreferences(userId);
    
    res.status(200).json({
      success: true,
      data: preferences
    });
  }

  /**
   * Update user preferences
   * 
   * @param req - Express request
   * @param res - Express response
   */
  async updatePreferences(req: Request, res: Response): Promise<void> {
    const authReq = req as AuthenticatedRequest;
    const userId = authReq.user.id;
    const preferences = req.body;
    
    // Validate preferences
    if (!preferences || Object.keys(preferences).length === 0) {
      res.status(400).json({
        success: false,
        message: 'No preferences data provided'
      });
      return;
    }
    
    const updatedPreferences = await this.userService.updateUserPreferences(userId, preferences);
    
    res.status(200).json({
      success: true,
      data: updatedPreferences
    });
  }

  /**
   * Get user entries
   * 
   * @param req - Express request
   * @param res - Express response
   */
  async getEntries(req: Request, res: Response): Promise<void> {
    const authReq = req as AuthenticatedRequest;
    const userId = authReq.user.id;
    const giveawayId = req.query['giveawayId'] as string | undefined;
    
    const entries = await this.userService.getUserEntries(userId, giveawayId);
    
    res.status(200).json({
      success: true,
      data: entries
    });
  }

  /**
   * Get user wins
   * 
   * @param req - Express request
   * @param res - Express response
   */
  async getWins(req: Request, res: Response): Promise<void> {
    const authReq = req as AuthenticatedRequest;
    const userId = authReq.user.id;
    
    const wins = await this.userService.getUserWins(userId);
    
    res.status(200).json({
      success: true,
      data: wins
    });
  }

  /**
   * Upload profile image
   * 
   * @param req - Express request
   * @param res - Express response
   */
  async uploadProfileImage(req: Request, res: Response): Promise<void> {
    const authReq = req as FileUploadRequest;
    const userId = authReq.user.id;
    const imageFile = authReq.file;
    
    if (!imageFile) {
      res.status(400).json({
        success: false,
        message: 'No image file provided'
      });
      return;
    }
    
    const updatedProfile = await this.userService.uploadProfileImage(userId, imageFile);
    
    res.status(200).json({
      success: true,
      data: updatedProfile
    });
  }
}

/**
 * Create route handlers for user controller
 */
export function createUserRouteHandlers(userController: UserController) {
  return {
    getProfile: asyncHandler(userController.getProfile.bind(userController)),
    getFullProfile: asyncHandler(userController.getFullProfile.bind(userController)),
    updateProfile: asyncHandler(userController.updateProfile.bind(userController)),
    changePassword: asyncHandler(userController.changePassword.bind(userController)),
    getPreferences: asyncHandler(userController.getPreferences.bind(userController)),
    updatePreferences: asyncHandler(userController.updatePreferences.bind(userController)),
    getEntries: asyncHandler(userController.getEntries.bind(userController)),
    getWins: asyncHandler(userController.getWins.bind(userController)),
    uploadProfileImage: asyncHandler(userController.uploadProfileImage.bind(userController))
  };
}

/**
 * Helper function to wrap controller methods with try/catch
 */
function asyncHandler(fn: (req: Request, res: Response, next: NextFunction) => Promise<void>) {
  return async (req: Request, res: Response, next: NextFunction) => {
    try {
      await fn(req, res, next);
    } catch (error) {
      next(error);
    }
  };
}

/**
 * Factory function to create the controller
 */
export const createUserController = (userService: UserService): UserController => {
  return new UserController(userService);
};