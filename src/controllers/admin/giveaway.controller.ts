import { Request, Response } from 'express';
import { GiveawayService } from '../../services/giveaway/giveaway-service.interface.js';
import {
  GiveawayCreateDto,
  GiveawayUpdateDto,
  WinnerSelectionDto
} from '../../dtos/index.js';
import { getRepositoryFactory } from '../../repositories/index.js';

/**
 * Admin Giveaway Controller
 *
 * Handles admin-specific giveaway operations like creation, updates, deletion,
 * entry management, and winner selection.
 */
export class AdminGiveawayController {
  /**
   * Create a new admin giveaway controller
   *
   * @param giveawayService - Giveaway service
   */
  constructor(private giveawayService: GiveawayService) {}

  /**
   * List all giveaways with pagination (admin only)
   *
   * @param req - Express request
   * @param res - Express response
   */
  async listGiveaways(req: Request, res: Response): Promise<void> {
    // Extract pagination parameters
    const page = req.query['page'] ? parseInt(req.query['page'] as string) : 1;
    const limit = req.query['limit'] ? parseInt(req.query['limit'] as string) : 10;

    // Extract filter parameters if provided
    const filters: any = {};
    if (req.query['status']) {
      filters.status = req.query['status'];
    }

    try {
      // Get all giveaways with pagination
      const paginationOptions = { page, limit };
      const result = await this.giveawayService.listPaginated(filters, paginationOptions);

      // Calculate total pages
      const totalPages = Math.ceil(result.meta.total / limit);

      res.status(200).json({
        success: true,
        data: result.data,
        meta: {
          total: result.meta.total,
          page,
          limit,
          pages: totalPages
        }
      });
    } catch (error) {
      const err = error as Error;
      res.status(500).json({
        success: false,
        message: err.message || 'Error retrieving giveaways'
      });
    }
  }

  /**
   * Get a giveaway by ID (admin only)
   *
   * @param req - Express request
   * @param res - Express response
   */
  async getGiveawayById(req: Request, res: Response): Promise<void> {
    const giveawayId = req.params['id'];

    if (!giveawayId) {
      res.status(400).json({
        success: false,
        message: 'Giveaway ID is required'
      });
      return;
    }

    try {
      // Get the giveaway by ID
      const giveaway = await this.giveawayService.get(giveawayId);

      if (!giveaway) {
        res.status(404).json({
          success: false,
          message: 'Giveaway not found'
        });
        return;
      }

      res.status(200).json({
        success: true,
        data: giveaway
      });
    } catch (error) {
      const err = error as Error;
      res.status(500).json({
        success: false,
        message: err.message || 'Error retrieving giveaway'
      });
    }
  }

  /**
   * Create a new giveaway (admin only)
   *
   * @param req - Express request
   * @param res - Express response
   */
  async createGiveaway(req: Request, res: Response): Promise<void> {
    const giveawayData: GiveawayCreateDto = req.body;

    // Basic validation
    if (!giveawayData.title || !giveawayData.description || !giveawayData.startDate || !giveawayData.endDate) {
      res.status(400).json({
        success: false,
        message: 'Missing required fields'
      });
      return;
    }

    // Ensure dates are valid Date objects
    giveawayData.startDate = new Date(giveawayData.startDate);
    giveawayData.endDate = new Date(giveawayData.endDate);

    // Validate date order
    if (giveawayData.startDate >= giveawayData.endDate) {
      res.status(400).json({
        success: false,
        message: 'End date must be after start date'
      });
      return;
    }

    const newGiveaway = await this.giveawayService.createGiveaway(giveawayData);

    res.status(201).json({
      success: true,
      data: newGiveaway
    });
  }

  /**
   * Update an existing giveaway (admin only)
   *
   * @param req - Express request
   * @param res - Express response
   */
  async updateGiveaway(req: Request, res: Response): Promise<void> {
    const giveawayId = req.params['id'];
    const updateData: GiveawayUpdateDto = req.body;

    // Convert date strings to Date objects if provided
    if (updateData.startDate) {
      updateData.startDate = new Date(updateData.startDate);
    }

    if (updateData.endDate) {
      updateData.endDate = new Date(updateData.endDate);
    }

    // If both dates are provided, validate their order
    if (updateData.startDate && updateData.endDate && updateData.startDate >= updateData.endDate) {
      res.status(400).json({
        success: false,
        message: 'End date must be after start date'
      });
      return;
    }

    if (!giveawayId) {
      res.status(400).json({
        success: false,
        message: 'Giveaway ID is required'
      });
      return;
    }

    const updatedGiveaway = await this.giveawayService.updateGiveaway(giveawayId, updateData);

    if (!updatedGiveaway) {
      res.status(404).json({
        success: false,
        message: 'Giveaway not found'
      });
      return;
    }

    res.status(200).json({
      success: true,
      data: updatedGiveaway
    });
  }

  /**
   * Delete a giveaway (admin only)
   *
   * @param req - Express request
   * @param res - Express response
   */
  async deleteGiveaway(req: Request, res: Response): Promise<void> {
    const giveawayId = req.params['id'];

    if (!giveawayId) {
      res.status(400).json({
        success: false,
        message: 'Giveaway ID is required'
      });
      return;
    }

    try {
      // There's no explicit delete method, so we'll use cancel
      await this.giveawayService.cancelGiveaway(giveawayId);

      res.status(204).send();
    } catch (error) {
      res.status(404).json({
        success: false,
        message: 'Giveaway not found or cannot be deleted'
      });
    }
  }

  /**
   * Get entries for a giveaway (admin only)
   *
   * @param req - Express request
   * @param res - Express response
   */
  async getEntries(req: Request, res: Response): Promise<void> {
    const giveawayId = req.params['id'];

    if (!giveawayId) {
      res.status(400).json({
        success: false,
        message: 'Giveaway ID is required'
      });
      return;
    }

    // Extract pagination parameters
    const page = req.query['page'] ? parseInt(req.query['page'] as string) : 1;
    const limit = req.query['limit'] ? parseInt(req.query['limit'] as string) : 10;

    try {
      // Get entries with user details for this giveaway
      const entryRepository = getRepositoryFactory().createEntryRepository();
      const entries = await entryRepository.findWithUserDetails(giveawayId);

      // Get total entry count
      const totalEntries = await this.giveawayService.getEntryCount(giveawayId);

      // Apply pagination manually
      const startIndex = (page - 1) * limit;
      const endIndex = page * limit;
      const paginatedEntries = entries.slice(startIndex, endIndex);

      // Calculate total pages
      const totalPages = Math.ceil(totalEntries / limit);

      res.status(200).json({
        success: true,
        data: paginatedEntries,
        meta: {
          total: totalEntries,
          page,
          limit,
          pages: totalPages
        }
      });
    } catch (error) {
      const err = error as Error;
      res.status(500).json({
        success: false,
        message: err.message || 'Error retrieving entries'
      });
    }
  }

  /**
   * Conduct draw for a giveaway (admin only)
   *
   * @param req - Express request
   * @param res - Express response
   */
  async conductDraw(req: Request, res: Response): Promise<void> {
    const giveawayId = req.params['id'];

    if (!giveawayId) {
      res.status(400).json({
        success: false,
        message: 'Giveaway ID is required'
      });
      return;
    }

    try {
      const selectionData: WinnerSelectionDto = {
        giveawayId,
        numberOfWinners: req.body.numberOfWinners || 1
      };

      const drawResult = await this.giveawayService.conductDraw(selectionData);

      // Get the winners after the draw
      const winners = await this.giveawayService.getWinners(giveawayId);

      res.status(200).json({
        success: true,
        data: {
          drawResult,
          winners
        }
      });
    } catch (error) {
      const err = error as Error;

      // Check for specific error conditions
      if (err.message.includes('already drawn') || err.message.includes('winners already selected')) {
        res.status(400).json({
          success: false,
          message: 'Draw has already been conducted for this giveaway'
        });
      } else if (err.message.includes('still active') || err.message.includes('not ended')) {
        res.status(400).json({
          success: false,
          message: 'Giveaway is still active and cannot be drawn yet'
        });
      } else if (err.message.includes('no entries')) {
        res.status(400).json({
          success: false,
          message: 'Giveaway has no entries to draw from'
        });
      } else {
        res.status(400).json({
          success: false,
          message: err.message || 'Error conducting draw'
        });
      }
    }
  }

  /**
   * Get prizes for a giveaway (admin only)
   *
   * @param req - Express request
   * @param res - Express response
   */
  async getPrizes(req: Request, res: Response): Promise<void> {
    const giveawayId = req.params['id'];

    if (!giveawayId) {
      res.status(400).json({
        success: false,
        message: 'Giveaway ID is required'
      });
      return;
    }

    try {
      // Get the giveaway by ID to verify it exists
      const giveaway = await this.giveawayService.get(giveawayId);

      if (!giveaway) {
        res.status(404).json({
          success: false,
          message: 'Giveaway not found'
        });
        return;
      }

      // Get prizes for the giveaway
      const prizeRepository = getRepositoryFactory().createPrizeRepository();
      const prizes = await prizeRepository.findByGiveawayId(giveawayId);

      res.status(200).json({
        success: true,
        data: prizes
      });
    } catch (error) {
      const err = error as Error;
      res.status(500).json({
        success: false,
        message: err.message || 'Error retrieving prizes'
      });
    }
  }

  /**
   * Publish a giveaway (admin only)
   *
   * @param req - Express request
   * @param res - Express response
   */
  async publishGiveaway(req: Request, res: Response): Promise<void> {
    const giveawayId = req.params['id'];

    if (!giveawayId) {
      res.status(400).json({
        success: false,
        message: 'Giveaway ID is required'
      });
      return;
    }

    try {
      const publishedGiveaway = await this.giveawayService.publishGiveaway(giveawayId);

      res.status(200).json({
        success: true,
        data: publishedGiveaway
      });
    } catch (error) {
      const err = error as Error;

      res.status(400).json({
        success: false,
        message: err.message || 'Error publishing giveaway'
      });
    }
  }
}

/**
 * Create route handlers for admin giveaway controller
 */
export function createAdminGiveawayRouteHandlers(adminGiveawayController: AdminGiveawayController) {
  return {
    listGiveaways: asyncHandler(adminGiveawayController.listGiveaways.bind(adminGiveawayController)),
    getGiveawayById: asyncHandler(adminGiveawayController.getGiveawayById.bind(adminGiveawayController)),
    createGiveaway: asyncHandler(adminGiveawayController.createGiveaway.bind(adminGiveawayController)),
    updateGiveaway: asyncHandler(adminGiveawayController.updateGiveaway.bind(adminGiveawayController)),
    deleteGiveaway: asyncHandler(adminGiveawayController.deleteGiveaway.bind(adminGiveawayController)),
    getEntries: asyncHandler(adminGiveawayController.getEntries.bind(adminGiveawayController)),
    getPrizes: asyncHandler(adminGiveawayController.getPrizes.bind(adminGiveawayController)),
    conductDraw: asyncHandler(adminGiveawayController.conductDraw.bind(adminGiveawayController)),
    publishGiveaway: asyncHandler(adminGiveawayController.publishGiveaway.bind(adminGiveawayController))
  };
}

/**
 * Helper function to wrap controller methods with try/catch
 */
function asyncHandler(fn: (req: Request, res: Response) => Promise<void>) {
  return async (req: Request, res: Response, next: Function) => {
    try {
      await fn(req, res);
    } catch (error) {
      next(error);
    }
  };
}

/**
 * Factory function to create the controller
 */
export const createAdminGiveawayController = (giveawayService: GiveawayService): AdminGiveawayController => {
  return new AdminGiveawayController(giveawayService);
};
