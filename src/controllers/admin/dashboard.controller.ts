import { Request, Response } from 'express';
import { AdminServiceInterface } from '../../services/admin/admin-service.interface.js';
import { 
  DashboardStatsParamsDto,
  ReportGenerationParamsDto,
  ReportFormat
} from '../../dtos/admin.dto.js';

/**
 * Admin Dashboard Controller
 * 
 * Handles admin dashboard statistics and reports.
 */
export class AdminDashboardController {
  /**
   * Create a new admin dashboard controller
   * 
   * @param adminService - Admin service
   */
  constructor(private adminService: AdminServiceInterface) {}

  /**
   * Get dashboard statistics
   * 
   * @param req - Express request
   * @param res - Express response
   */
  async getDashboardStats(req: Request, res: Response): Promise<void> {
    // Extract query parameters
    const params: DashboardStatsParamsDto = {};
    
    if (req.query['startDate']) {
      params.startDate = new Date(req.query['startDate'] as string);
    }
    
    if (req.query['endDate']) {
      params.endDate = new Date(req.query['endDate'] as string);
    }
    
    if (req.query['dateRange']) {
      params.dateRange = req.query['dateRange'] as 'day' | 'week' | 'month' | 'year' | 'all';
    }
    
    try {
      const stats = await this.adminService.getDashboardStats(params);
      
      res.status(200).json({
        success: true,
        data: stats
      });
    } catch (error) {
      const err = error as Error;
      
      res.status(400).json({
        success: false,
        message: err.message || 'Error retrieving dashboard statistics'
      });
    }
  }

  /**
   * Get user statistics
   * 
   * @param req - Express request
   * @param res - Express response
   */
  async getUserStats(req: Request, res: Response): Promise<void> {
    // Parse pagination options
    const pagination = {
      page: req.query['page'] ? parseInt(req.query['page'] as string) : 1,
      limit: req.query['limit'] ? parseInt(req.query['limit'] as string) : 10
    };
    
    // Build filter object
    const filter: Record<string, any> = {};
    
    if (req.query['role']) {
      filter['role'] = req.query['role'] as string;
    }
    
    if (req.query['membershipStatus']) {
      filter['membershipStatus'] = req.query['membershipStatus'] as string;
    }
    
    if (req.query['createdAfter']) {
      filter['createdAfter'] = new Date(req.query['createdAfter'] as string);
    }
    
    if (req.query['createdBefore']) {
      filter['createdBefore'] = new Date(req.query['createdBefore'] as string);
    }
    
    try {
      // Get users with the filter and pagination
      const users = await this.adminService.getUsers(filter, pagination);
      
      res.status(200).json({
        success: true,
        data: users.data,
        meta: users.meta
      });
    } catch (error) {
      const err = error as Error;
      
      res.status(400).json({
        success: false,
        message: err.message || 'Error retrieving user statistics'
      });
    }
  }

  /**
   * Get membership statistics
   * 
   * @param req - Express request
   * @param res - Express response
   */
  async getMembershipStats(req: Request, res: Response): Promise<void> {
    // Extract date range parameters for membership stats
    const params: DashboardStatsParamsDto = {};
    
    if (req.query['startDate']) {
      params.startDate = new Date(req.query['startDate'] as string);
    }
    
    if (req.query['endDate']) {
      params.endDate = new Date(req.query['endDate'] as string);
    }
    
    if (req.query['dateRange']) {
      params.dateRange = req.query['dateRange'] as 'day' | 'week' | 'month' | 'year' | 'all';
    }
    
    try {
      // Get general dashboard statistics
      const stats = await this.adminService.getDashboardStats(params);
      
      // Return only the membership portion of the statistics
      res.status(200).json({
        success: true,
        data: stats.membershipStats
      });
    } catch (error) {
      const err = error as Error;
      
      res.status(400).json({
        success: false,
        message: err.message || 'Error retrieving membership statistics'
      });
    }
  }

  /**
   * Get giveaway statistics
   * 
   * @param req - Express request
   * @param res - Express response
   */
  async getGiveawayStats(req: Request, res: Response): Promise<void> {
    // Extract date range parameters for giveaway stats
    const params: DashboardStatsParamsDto = {};
    
    if (req.query['startDate']) {
      params.startDate = new Date(req.query['startDate'] as string);
    }
    
    if (req.query['endDate']) {
      params.endDate = new Date(req.query['endDate'] as string);
    }
    
    if (req.query['dateRange']) {
      params.dateRange = req.query['dateRange'] as 'day' | 'week' | 'month' | 'year' | 'all';
    }
    
    try {
      // Get general dashboard statistics
      const stats = await this.adminService.getDashboardStats(params);
      
      // Return only the giveaway portion of the statistics
      res.status(200).json({
        success: true,
        data: stats.giveawayStats
      });
    } catch (error) {
      const err = error as Error;
      
      res.status(400).json({
        success: false,
        message: err.message || 'Error retrieving giveaway statistics'
      });
    }
  }

  /**
   * Get revenue statistics
   * 
   * @param req - Express request
   * @param res - Express response
   */
  async getRevenueStats(req: Request, res: Response): Promise<void> {
    // Extract date range parameters for revenue stats
    const params: DashboardStatsParamsDto = {};
    
    if (req.query['startDate']) {
      params.startDate = new Date(req.query['startDate'] as string);
    }
    
    if (req.query['endDate']) {
      params.endDate = new Date(req.query['endDate'] as string);
    }
    
    if (req.query['dateRange']) {
      params.dateRange = req.query['dateRange'] as 'day' | 'week' | 'month' | 'year' | 'all';
    }
    
    try {
      // Get general dashboard statistics
      const stats = await this.adminService.getDashboardStats(params);
      
      // Return only the transaction/revenue portion of the statistics
      res.status(200).json({
        success: true,
        data: stats.transactionStats
      });
    } catch (error) {
      const err = error as Error;
      
      res.status(400).json({
        success: false,
        message: err.message || 'Error retrieving revenue statistics'
      });
    }
  }

  /**
   * Generate reports
   * 
   * @param req - Express request
   * @param res - Express response
   */
  async generateReport(req: Request, res: Response): Promise<void> {
    const adminId = (req as any).user.id;
    const reportParams: ReportGenerationParamsDto = req.body;
    
    if (!reportParams.type) {
      res.status(400).json({
        success: false,
        message: 'Report type is required'
      });
      return;
    }
    
    if (!reportParams.format) {
      // Set default format if not provided
      reportParams.format = ReportFormat.CSV;
    }
    
    try {
      const report = await this.adminService.generateReport(reportParams, adminId);
      
      res.status(200).json({
        success: true,
        data: report
      });
    } catch (error) {
      const err = error as Error;
      
      res.status(400).json({
        success: false,
        message: err.message || 'Error generating report'
      });
    }
  }
}

/**
 * Create route handlers for admin dashboard controller
 */
export function createAdminDashboardRouteHandlers(adminDashboardController: AdminDashboardController) {
  return {
    getDashboardStats: asyncHandler(adminDashboardController.getDashboardStats.bind(adminDashboardController)),
    getUserStats: asyncHandler(adminDashboardController.getUserStats.bind(adminDashboardController)),
    getMembershipStats: asyncHandler(adminDashboardController.getMembershipStats.bind(adminDashboardController)),
    getGiveawayStats: asyncHandler(adminDashboardController.getGiveawayStats.bind(adminDashboardController)),
    getRevenueStats: asyncHandler(adminDashboardController.getRevenueStats.bind(adminDashboardController)),
    generateReport: asyncHandler(adminDashboardController.generateReport.bind(adminDashboardController))
  };
}

/**
 * Helper function to wrap controller methods with try/catch
 */
function asyncHandler(fn: (req: Request, res: Response) => Promise<void>) {
  return async (req: Request, res: Response, next: Function) => {
    try {
      await fn(req, res);
    } catch (error) {
      next(error);
    }
  };
}

/**
 * Factory function to create the controller
 */
export const createAdminDashboardController = (adminService: AdminServiceInterface): AdminDashboardController => {
  return new AdminDashboardController(adminService);
}; 