import { Request, Response } from 'express';
import { AdminServiceInterface } from '../../services/admin/admin-service.interface.js';
import { UserRoleUpdateDto } from '../../dtos/admin.dto.js';
import { AdminUserFilterParamsDto } from '../../dtos/admin.dto.js';

/**
 * Admin Users Controller
 * 
 * Handles admin operations for user management.
 */
export class AdminUsersController {
  /**
   * Create a new admin users controller
   * 
   * @param adminService - Admin service
   */
  constructor(private adminService: AdminServiceInterface) {}

  /**
   * Get a user by ID
   * 
   * @param req - Express request
   * @param res - Express response
   */
  async getUserById(req: Request, res: Response): Promise<void> {
    const userId = req.params['id'];
    
    if (!userId) {
      res.status(400).json({
        success: false,
        message: 'User ID is required'
      });
      return;
    }
    
    try {
      // Get user with filtering to get a single user
      const filter: AdminUserFilterParamsDto = {};
      // Using bracket notation to avoid linter errors
      filter['id'] = userId;
      
      const users = await this.adminService.getUsers(filter);
      
      if (!users.data || users.data.length === 0) {
        res.status(404).json({
          success: false,
          message: `User with ID ${userId} not found`
        });
        return;
      }
      
      const user = users.data[0];
      
      res.status(200).json({
        success: true,
        data: user
      });
    } catch (error) {
      const err = error as Error;
      
      res.status(400).json({
        success: false,
        message: err.message || `Error retrieving user with ID ${userId}`
      });
    }
  }

  /**
   * Update a user's role
   * 
   * @param req - Express request
   * @param res - Express response
   */
  async updateUserRole(req: Request, res: Response): Promise<void> {
    const userId = req.params['id'];
    const user = (req as any).user;
    const adminId = user['id']; // Using bracket notation to avoid linter errors
    const roleData: UserRoleUpdateDto = req.body;
    
    if (!userId) {
      res.status(400).json({
        success: false,
        message: 'User ID is required'
      });
      return;
    }
    
    if (!roleData.role) {
      res.status(400).json({
        success: false,
        message: 'Role is required'
      });
      return;
    }
    
    try {
      const updatedUser = await this.adminService.updateUserRole(userId, roleData, adminId);
      
      res.status(200).json({
        success: true,
        data: updatedUser
      });
    } catch (error) {
      const err = error as Error;
      
      res.status(400).json({
        success: false,
        message: err.message || 'Error updating user role'
      });
    }
  }

  /**
   * Delete a user account
   * 
   * @param req - Express request
   * @param res - Express response
   */
  async deleteUser(req: Request, res: Response): Promise<void> {
    const userId = req.params['id'];
    const user = (req as any).user;
    const adminId = user['id']; // Using bracket notation to avoid linter errors
    
    if (!userId) {
      res.status(400).json({
        success: false,
        message: 'User ID is required'
      });
      return;
    }
    
    try {
      const deletedUser = await this.adminService.deleteUser(userId, adminId);
      
      res.status(200).json({
        success: true,
        data: deletedUser
      });
    } catch (error) {
      const err = error as Error;
      
      res.status(400).json({
        success: false,
        message: err.message || 'Error deleting user'
      });
    }
  }
}

/**
 * Create route handlers for admin users controller
 */
export function createAdminUsersRouteHandlers(adminUsersController: AdminUsersController) {
  return {
    getUserById: asyncHandler(adminUsersController.getUserById.bind(adminUsersController)),
    updateUserRole: asyncHandler(adminUsersController.updateUserRole.bind(adminUsersController)),
    deleteUser: asyncHandler(adminUsersController.deleteUser.bind(adminUsersController))
  };
}

/**
 * Helper function to wrap controller methods with try/catch
 */
function asyncHandler(fn: (req: Request, res: Response) => Promise<void>) {
  return async (req: Request, res: Response, next: Function) => {
    try {
      await fn(req, res);
    } catch (error) {
      next(error);
    }
  };
}

/**
 * Factory function to create the controller
 */
export const createAdminUsersController = (adminService: AdminServiceInterface): AdminUsersController => {
  return new AdminUsersController(adminService);
}; 