import { Request, Response } from 'express';
import { NotificationServiceInterface } from '../services/notification/notification-service.interface.js';
import { NotificationFilterParamsDto } from '../dtos/notification.dto.js';

/**
 * Notification Controller
 * 
 * Handles user notifications.
 */
export class NotificationController {
  /**
   * Create a new notification controller
   * 
   * @param notificationService - Notification service
   */
  constructor(private notificationService: NotificationServiceInterface) {}

  /**
   * Get notifications for the authenticated user
   * 
   * @param req - Express request
   * @param res - Express response
   */
  async getNotifications(req: Request, res: Response): Promise<void> {
    const userId = (req as any).user.id;
    const options: Record<string, any> = {};
    
    // Parse pagination options from query params
    if (req.query['page']) {
      options['page'] = parseInt(req.query['page'] as string);
    }
    
    if (req.query['limit']) {
      options['limit'] = parseInt(req.query['limit'] as string);
    }
    
    // Apply filters if provided
    if (req.query['read'] !== undefined) {
      options['read'] = req.query['read'] === 'true';
    }
    
    if (req.query['type']) {
      options['type'] = req.query['type'] as string;
    }
    
    try {
      // If read flag is set to false, use getUnreadNotifications
      if (options['read'] === false) {
        const notifications = await this.notificationService.getUnreadNotifications(
          userId,
          options
        );
        
        res.status(200).json({
          success: true,
          data: notifications.data,
          meta: notifications.meta
        });
      } else {
        // Otherwise get all notifications with or without additional filters
        const filterParams: NotificationFilterParamsDto = {
          userId
        };
        
        if (options['type']) {
          filterParams['type'] = options['type'];
        }
        
        const notifications = await this.notificationService.filterNotifications(
          filterParams,
          {
            page: options['page'] || 1,
            limit: options['limit'] || 10
          }
        );
        
        res.status(200).json({
          success: true,
          data: notifications.data,
          meta: notifications.meta
        });
      }
    } catch (error) {
      const err = error as Error;
      
      res.status(400).json({
        success: false,
        message: err.message || 'Error retrieving notifications'
      });
    }
  }

  /**
   * Get specific notification details
   * 
   * @param req - Express request
   * @param res - Express response
   */
  async getNotificationDetails(req: Request, res: Response): Promise<void> {
    const notificationId = req.params['id'];
    const userId = (req as any).user.id;
    
    if (!notificationId) {
      res.status(400).json({
        success: false,
        message: 'Notification ID is required'
      });
      return;
    }
    
    try {
      const notification = await this.notificationService.get(notificationId);
      
      if (!notification) {
        res.status(404).json({
          success: false,
          message: 'Notification not found'
        });
        return;
      }
      
      // Check if notification belongs to the user
      if (notification.userId !== userId) {
        res.status(403).json({
          success: false,
          message: 'You do not have access to this notification'
        });
        return;
      }
      
      res.status(200).json({
        success: true,
        data: notification
      });
    } catch (error) {
      const err = error as Error;
      
      res.status(400).json({
        success: false,
        message: err.message || 'Error retrieving notification'
      });
    }
  }

  /**
   * Mark notification as read
   * 
   * @param req - Express request
   * @param res - Express response
   */
  async markAsRead(req: Request, res: Response): Promise<void> {
    const notificationId = req.params['id'];
    const userId = (req as any).user.id;
    
    if (!notificationId) {
      res.status(400).json({
        success: false,
        message: 'Notification ID is required'
      });
      return;
    }
    
    try {
      const notification = await this.notificationService.markAsRead(notificationId, userId);
      
      res.status(200).json({
        success: true,
        data: notification
      });
    } catch (error) {
      const err = error as Error;
      
      if (err.message.includes('not found')) {
        res.status(404).json({
          success: false,
          message: 'Notification not found'
        });
      } else if (err.message.includes('access')) {
        res.status(403).json({
          success: false,
          message: 'You do not have access to this notification'
        });
      } else {
        res.status(400).json({
          success: false,
          message: err.message || 'Error marking notification as read'
        });
      }
    }
  }

  /**
   * Mark all notifications as read
   * 
   * @param req - Express request
   * @param res - Express response
   */
  async markAllAsRead(req: Request, res: Response): Promise<void> {
    const userId = (req as any).user.id;
    
    try {
      const count = await this.notificationService.markAllAsRead(userId);
      
      res.status(200).json({
        success: true,
        data: {
          count
        }
      });
    } catch (error) {
      const err = error as Error;
      
      res.status(400).json({
        success: false,
        message: err.message || 'Error marking notifications as read'
      });
    }
  }
}

/**
 * Create route handlers for notification controller
 */
export function createNotificationRouteHandlers(notificationController: NotificationController) {
  return {
    getNotifications: asyncHandler(notificationController.getNotifications.bind(notificationController)),
    getNotificationDetails: asyncHandler(notificationController.getNotificationDetails.bind(notificationController)),
    markAsRead: asyncHandler(notificationController.markAsRead.bind(notificationController)),
    markAllAsRead: asyncHandler(notificationController.markAllAsRead.bind(notificationController))
  };
}

/**
 * Helper function to wrap controller methods with try/catch
 */
function asyncHandler(fn: (req: Request, res: Response) => Promise<void>) {
  return async (req: Request, res: Response, next: Function) => {
    try {
      await fn(req, res);
    } catch (error) {
      next(error);
    }
  };
}

/**
 * Factory function to create the controller
 */
export const createNotificationController = (notificationService: NotificationServiceInterface): NotificationController => {
  return new NotificationController(notificationService);
}; 