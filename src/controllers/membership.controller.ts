import { Request, Response } from 'express';
import { MembershipService } from '../services/membership/membership-service.interface.js';
import { 
  MembershipSubscribeDto, 
  MembershipUpgradeDto,
  MembershipAutoRenewDto
} from '../dtos/membership.dto.js';

/**
 * Membership Controller
 * 
 * Handles membership subscription, cancellation, and management.
 */
export class MembershipController {
  /**
   * Create a new membership controller
   * 
   * @param membershipService - Membership service
   */
  constructor(private membershipService: MembershipService) {}

  /**
   * Get the current user's membership
   * 
   * @param req - Express request
   * @param res - Express response
   */
  async getCurrentMembership(req: Request, res: Response): Promise<void> {
    const userId = (req as any).user.id;
    
    const userWithMembership = await this.membershipService.getUserWithMembership(userId);
    
    if (!userWithMembership || !userWithMembership.membership) {
      res.status(404).json({
        success: false,
        message: 'No active membership found'
      });
      return;
    }
    
    res.status(200).json({
      success: true,
      data: userWithMembership.membership
    });
  }

  /**
   * Subscribe to a membership tier
   * 
   * @param req - Express request
   * @param res - Express response
   */
  async subscribe(req: Request, res: Response): Promise<void> {
    const userId = (req as any).user.id;
    const subscriptionData: MembershipSubscribeDto = req.body;
    
    // Validate required fields
    if (!subscriptionData.membershipTierId) {
      res.status(400).json({
        success: false,
        message: 'Membership tier ID is required'
      });
      return;
    }
    
    // Create subscription data with proper field mapping
    const serviceSubscriptionData = {
      userId,
      tierId: subscriptionData.membershipTierId,
      autoRenew: subscriptionData.autoRenew ?? true,
      paymentMethodId: subscriptionData.paymentMethodId || '' // Provide default empty string if undefined
    };
    
    const newMembership = await this.membershipService.subscribe(serviceSubscriptionData);
    
    res.status(201).json({
      success: true,
      data: newMembership
    });
  }

  /**
   * Cancel the current membership
   * 
   * @param req - Express request
   * @param res - Express response
   */
  async cancelMembership(req: Request, res: Response): Promise<void> {
    const userId = (req as any).user.id;
    const cancelImmediately = req.query['immediate'] === 'true';
    
    try {
      await this.membershipService.cancelMembership(userId, cancelImmediately);
      
      res.status(200).json({
        success: true,
        message: cancelImmediately
          ? 'Membership cancelled immediately'
          : 'Membership will be cancelled at the end of the billing period'
      });
    } catch (error) {
      // If the service throws an error due to no active membership
      res.status(404).json({
        success: false,
        message: 'No active membership found'
      });
    }
  }

  /**
   * Update auto-renew setting
   * 
   * @param req - Express request
   * @param res - Express response
   */
  async updateAutoRenew(req: Request, res: Response): Promise<void> {
    const userId = (req as any).user.id;
    const autoRenewData: MembershipAutoRenewDto = req.body;
    
    // Validate required fields
    if (typeof autoRenewData.autoRenew !== 'boolean') {
      res.status(400).json({
        success: false,
        message: 'Auto-renew setting must be a boolean value'
      });
      return;
    }
    
    // Create renewal data that matches the service interface
    const renewalSettings = {
      autoRenew: autoRenewData.autoRenew
    };
    
    const updatedSettings = await this.membershipService.updateAutoRenew(userId, renewalSettings);
    
    res.status(200).json({
      success: true,
      data: updatedSettings
    });
  }

  /**
   * Upgrade to a higher membership tier
   * 
   * @param req - Express request
   * @param res - Express response
   */
  async upgradeMembership(req: Request, res: Response): Promise<void> {
    const userId = (req as any).user.id;
    const upgradeData: MembershipUpgradeDto = req.body;
    
    // Validate required fields
    if (!upgradeData.newMembershipTierId) {
      res.status(400).json({
        success: false,
        message: 'New membership tier ID is required'
      });
      return;
    }
    
    // Get current membership to determine current tier
    const userWithMembership = await this.membershipService.getUserWithMembership(userId);
    
    if (!userWithMembership || !userWithMembership.membership) {
      res.status(404).json({
        success: false,
        message: 'No active membership found'
      });
      return;
    }
    
    // Create complete upgrade data that matches the service interface
    const serviceUpgradeData = {
      userId,
      currentTierId: userWithMembership.membership.membershipTierId,
      newTierId: upgradeData.newMembershipTierId,
      paymentMethodId: upgradeData.paymentMethodId || '', // Provide default empty string if undefined
      applyProration: true
    };
    
    const updatedMembership = await this.membershipService.upgradeMembership(serviceUpgradeData);
    
    res.status(200).json({
      success: true,
      data: updatedMembership
    });
  }

  /**
   * Get all available membership tiers
   * 
   * @param req - Express request
   * @param res - Express response
   */
  async getAvailableMemberships(_req: Request, res: Response): Promise<void> {
    try {
      // Only get active tiers for members - a separate admin endpoint exists for all tiers
      const includeInactive = false;
      const availableTiers = await this.membershipService.getAvailableTiers(includeInactive);
      
      res.status(200).json({
        success: true,
        data: availableTiers
      });
    } catch (error) {
      const err = error as Error;
      
      res.status(400).json({
        success: false,
        message: err.message || 'Error retrieving available memberships'
      });
    }
  }

  /**
   * Get membership history
   * 
   * @param req - Express request
   * @param res - Express response
   */
  async getMembershipHistory(req: Request, res: Response): Promise<void> {
    const userId = (req as any).user.id;
    
    const history = await this.membershipService.getMembershipHistory(userId);
    
    res.status(200).json({
      success: true,
      data: history
    });
  }
}

/**
 * Create route handlers for membership controller
 */
export function createMembershipRouteHandlers(membershipController: MembershipController) {
  return {
    getCurrentMembership: asyncHandler(membershipController.getCurrentMembership.bind(membershipController)),
    subscribe: asyncHandler(membershipController.subscribe.bind(membershipController)),
    cancelMembership: asyncHandler(membershipController.cancelMembership.bind(membershipController)),
    updateAutoRenew: asyncHandler(membershipController.updateAutoRenew.bind(membershipController)),
    upgradeMembership: asyncHandler(membershipController.upgradeMembership.bind(membershipController)),
    getMembershipHistory: asyncHandler(membershipController.getMembershipHistory.bind(membershipController)),
    getAvailableMemberships: asyncHandler(membershipController.getAvailableMemberships.bind(membershipController))
  };
}

/**
 * Helper function to wrap controller methods with try/catch
 */
function asyncHandler(fn: (req: Request, res: Response) => Promise<void>) {
  return async (req: Request, res: Response, next: Function) => {
    try {
      await fn(req, res);
    } catch (error) {
      next(error);
    }
  };
}

/**
 * Create membership controller factory
 */
export const createMembershipController = (membershipService: MembershipService): MembershipController => {
  return new MembershipController(membershipService);
}; 