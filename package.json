{"name": "winners-society", "version": "1.0.0", "description": "A membership-based giveaway platform", "main": "dist/server.js", "type": "module", "compilerOptions": {"target": "ES2020", "module": "NodeNext", "moduleResolution": "NodeNext", "esModuleInterop": true, "sourceMap": true, "outDir": "./dist", "rootDir": "./src", "strict": true}, "include": ["src/**/*"], "exclude": ["node_modules"], "scripts": {"start": "node dist/server.js", "dev": "ts-node-dev --respawn --transpile-only --esm src/server.ts", "dev:debug": "nodemon --exec node --inspect -r ts-node/register src/server.ts", "build": "tsc", "build:clean": "rimraf dist && tsc", "lint": "eslint . --ext .ts", "lint:fix": "eslint . --ext .ts --fix", "format": "prettier --write \"src/**/*.ts\"", "format:check": "prettier --check \"src/**/*.ts\"", "test": "jest --coverage", "test:watch": "jest --watch", "test:unit": "jest --testPathPattern=unit", "test:integration": "jest --testPathPattern=integration", "prepare": "husky install", "prisma:generate": "prisma generate", "prisma:migrate": "prisma migrate dev", "prisma:migrate:prod": "prisma migrate deploy", "prisma:studio": "prisma studio", "prisma:seed": "ts-node prisma/seed.ts", "db:reset": "prisma migrate reset --force", "docs:generate": "typedoc --out docs/api src"}, "keywords": ["membership", "giveaway", "platform", "express", "prisma", "postgresql"], "author": "", "license": "ISC", "dependencies": {"@prisma/client": "^6.8.2", "@sendgrid/mail": "^8.1.5", "@types/bull": "^3.15.9", "bcrypt": "^5.1.1", "bull": "^4.16.5", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-validator": "^7.0.1", "helmet": "^7.1.0", "http-status-codes": "^2.3.0", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "multer": "^1.4.5-lts.2", "pg": "^8.11.3", "stripe": "^18.1.0", "winston": "^3.11.0", "zod": "^3.24.3"}, "devDependencies": {"@types/bcrypt": "^5.0.2", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jest": "^29.5.11", "@types/jsonwebtoken": "^9.0.5", "@types/morgan": "^1.9.9", "@types/multer": "^1.4.12", "@types/node": "^20.10.5", "@types/supertest": "^2.0.16", "@typescript-eslint/eslint-plugin": "^6.15.0", "@typescript-eslint/parser": "^6.15.0", "eslint": "^8.56.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-airbnb-typescript": "^18.0.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import": "^2.31.0", "eslint-plugin-prettier": "^5.1.1", "jest": "^29.7.0", "nodemon": "^3.0.2", "prettier": "^3.1.1", "prisma": "^6.8.2", "supertest": "^6.3.3", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "ts-node-dev": "^2.0.0", "typescript": "^5.3.3"}}